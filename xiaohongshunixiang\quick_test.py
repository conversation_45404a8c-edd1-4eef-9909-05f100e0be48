#!/usr/bin/env python3
"""
快速测试搜索接口 - 重点检查 x-s-common
"""

import requests
import json
import base64

def quick_test():
    print("🔍 快速测试搜索接口")
    
    # 1. 测试签名服务
    try:
        health = requests.get('http://localhost:5107/health', timeout=5)
        print(f"✅ 签名服务状态: {health.json()}")
    except Exception as e:
        print(f"❌ 签名服务不可用: {e}")
        return
    
    # 2. 生成签名
    try:
        sig_response = requests.post(
            'http://localhost:5107/signature/generate',
            json={
                'url': '/api/sns/web/v1/search/notes',
                'data': {
                    'keyword': '美食',
                    'page': 1,
                    'page_size': 20
                }
            },
            timeout=30
        )
        
        if sig_response.status_code == 200:
            sig_data = sig_response.json()
            if sig_data.get('success'):
                signature = sig_data['signature']
                print(f"✅ 签名生成成功")
                print(f"   X-s: {signature['X-s'][:50]}...")
                print(f"   X-t: {signature['X-t']}")

                # 检查 x-s-common
                if 'x-s-common' in signature and signature['x-s-common']:
                    print(f"   x-s-common: {signature['x-s-common'][:50]}...")

                    # 尝试解析 x-s-common
                    try:
                        decoded = base64.b64decode(signature['x-s-common']).decode('utf-8')
                        common_data = json.loads(decoded)
                        print(f"   📊 x-s-common 解析成功:")
                        for key, value in list(common_data.items())[:5]:  # 只显示前5个
                            print(f"      {key}: {value}")
                    except Exception as e:
                        print(f"   ⚠️ x-s-common 解析失败: {e}")
                else:
                    print(f"   ❌ 缺少 x-s-common!")
                    return
            else:
                print(f"❌ 签名生成失败: {sig_data}")
                return
        else:
            print(f"❌ 签名服务错误: {sig_response.status_code}")
            return
            
    except Exception as e:
        print(f"❌ 签名生成异常: {e}")
        return
    
    # 3. 测试 API 调用
    try:
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json;charset=UTF-8',
            'Origin': 'https://www.xiaohongshu.com',
            'Referer': 'https://www.xiaohongshu.com/',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'X-s': signature['X-s'],
            'X-t': str(signature['X-t']),  # 确保是字符串
            'x-s-common': signature['x-s-common'],
            'Cookie': 'a1=1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399;webId=77532cb79e595615769bcc5df41c0386;web_session=040069b937bf843c7b6e1952713a4befbda53e'
        }
        
        api_data = {
            'keyword': '美食',
            'page': 1,
            'page_size': 20,
            'search_id': '',
            'sort': 'general',
            'note_type': 0
        }

        # 验证请求头完整性
        print(f"📋 请求头检查:")
        required_headers = ['X-s', 'X-t', 'x-s-common']
        for header in required_headers:
            if header in headers and headers[header]:
                print(f"   ✅ {header}: 存在")
            else:
                print(f"   ❌ {header}: 缺失")

        print(f"🚀 调用搜索 API...")
        print(f"   URL: https://edith.xiaohongshu.com/api/sns/web/v1/search/notes")
        print(f"   数据: {json.dumps(api_data, ensure_ascii=False)}")

        api_response = requests.post(
            'https://edith.xiaohongshu.com/api/sns/web/v1/search/notes',
            json=api_data,
            headers=headers,
            timeout=15
        )
        
        print(f"📊 API 响应状态: {api_response.status_code}")
        
        if api_response.status_code == 200:
            try:
                result = api_response.json()
                print(f"✅ API 调用成功!")
                
                if 'data' in result and 'items' in result['data']:
                    items = result['data']['items']
                    print(f"   找到 {len(items)} 条笔记")
                    if items:
                        print(f"   第一条: {items[0].get('display_title', 'N/A')}")
                else:
                    print(f"   响应结构: {list(result.keys())}")
                    
            except json.JSONDecodeError:
                print(f"⚠️ 响应不是 JSON: {api_response.text[:200]}")
        else:
            print(f"❌ API 调用失败")
            print(f"   错误: {api_response.text[:300]}")

            if api_response.status_code == 461:
                print("💡 461 错误分析:")
                print("   - 通常表示签名验证失败")
                print("   - 检查 X-s, X-t, x-s-common 是否都存在")
                print("   - 确认请求参数与签名时完全一致")
                print("   - 验证时间戳是否在有效范围内")
            elif api_response.status_code == 403:
                print("💡 403 错误分析:")
                print("   - 可能是反爬虫检测")
                print("   - 检查 User-Agent 和 Referer")
                print("   - 验证 Cookie 是否有效")
            elif api_response.status_code == 400:
                print("💡 400 错误分析:")
                print("   - 请求参数格式错误")
                print("   - 检查 JSON 数据结构")
            else:
                print(f"💡 {api_response.status_code} 错误:")
                print("   - 未知错误类型，需要进一步分析")
                
    except Exception as e:
        print(f"❌ API 调用异常: {e}")

if __name__ == '__main__':
    quick_test()
