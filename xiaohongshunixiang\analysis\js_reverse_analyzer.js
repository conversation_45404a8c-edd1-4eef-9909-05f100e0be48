/**
 * JavaScript 逆向分析脚本
 * 专门用于分析 xhs.js 中的签名算法
 */

class JSReverseAnalyzer {
    constructor() {
        this.results = {
            functions: {},
            variables: {},
            algorithms: {},
            signatures: {}
        };
    }

    /**
     * 开始分析
     */
    analyze() {
        console.log('🚀 开始 JavaScript 逆向分析...');
        
        // 1. 分析全局函数
        this.analyzeGlobalFunctions();
        
        // 2. 分析 o() 函数
        this.analyzeOFunction();
        
        // 3. 分析 getXs() 函数
        this.analyzeGetXsFunction();
        
        // 4. 测试签名生成
        this.testSignatureGeneration();
        
        // 5. 分析算法模式
        this.analyzeAlgorithmPatterns();
        
        // 6. 生成报告
        this.generateReport();
        
        return this.results;
    }

    /**
     * 分析全局函数
     */
    analyzeGlobalFunctions() {
        console.log('🔍 分析全局函数...');
        
        const globalFunctions = [];
        
        for (let key in window) {
            try {
                if (typeof window[key] === 'function') {
                    const func = window[key];
                    const source = func.toString();
                    
                    globalFunctions.push({
                        name: key,
                        length: source.length,
                        isNative: source.includes('[native code]'),
                        isObfuscated: this.isObfuscated(source),
                        complexity: this.calculateComplexity(source)
                    });
                }
            } catch (e) {
                // 忽略无法访问的函数
            }
        }
        
        // 按复杂度排序
        globalFunctions.sort((a, b) => b.complexity - a.complexity);
        
        this.results.functions.global = globalFunctions.slice(0, 20);
        console.log(`✅ 发现 ${globalFunctions.length} 个全局函数`);
    }

    /**
     * 分析 o() 函数
     */
    analyzeOFunction() {
        console.log('🔍 分析 o() 函数...');
        
        if (typeof o === 'function') {
            const source = o.toString();
            
            this.results.functions.o = {
                exists: true,
                length: source.length,
                isObfuscated: this.isObfuscated(source),
                complexity: this.calculateComplexity(source),
                patterns: this.extractPatterns(source),
                variables: this.extractVariables(source),
                strings: this.extractStrings(source)
            };
            
            // 尝试执行 o() 函数
            try {
                const testResult = o();
                this.results.functions.o.testResult = {
                    success: true,
                    result: testResult,
                    type: typeof testResult
                };
            } catch (e) {
                this.results.functions.o.testResult = {
                    success: false,
                    error: e.message
                };
            }
            
            console.log('✅ o() 函数分析完成');
        } else {
            this.results.functions.o = { exists: false };
            console.log('⚠️ o() 函数不存在');
        }
    }

    /**
     * 分析 getXs() 函数
     */
    analyzeGetXsFunction() {
        console.log('🔍 分析 getXs() 函数...');
        
        if (typeof getXs === 'function') {
            const source = getXs.toString();
            
            this.results.functions.getXs = {
                exists: true,
                length: source.length,
                source: source,
                parameters: this.extractParameters(source)
            };
            
            console.log('✅ getXs() 函数分析完成');
        } else {
            this.results.functions.getXs = { exists: false };
            console.log('⚠️ getXs() 函数不存在');
        }
    }

    /**
     * 测试签名生成
     */
    testSignatureGeneration() {
        console.log('🧪 测试签名生成...');
        
        const testCases = [
            {
                url: 'https://edith.xiaohongshu.com/api/sns/web/v2/user/me',
                data: '{}',
                a1: '1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399'
            },
            {
                url: 'https://edith.xiaohongshu.com/api/sns/web/v1/homefeed',
                data: '{"cursor_score":"","num":31,"refresh_type":1}',
                a1: '1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399'
            }
        ];
        
        this.results.signatures.tests = [];
        
        testCases.forEach((testCase, index) => {
            try {
                console.log(`🔬 测试用例 ${index + 1}: ${testCase.url}`);
                
                let result = null;
                
                // 测试 getXs 函数
                if (typeof getXs === 'function') {
                    result = getXs(testCase.url, testCase.data, testCase.a1);
                }
                
                // 测试 window._webmsxyw 函数
                let webmsxywResult = null;
                if (typeof window._webmsxyw === 'function') {
                    webmsxywResult = window._webmsxyw(testCase.url, testCase.data);
                }
                
                this.results.signatures.tests.push({
                    testCase: testCase,
                    getXsResult: result,
                    webmsxywResult: webmsxywResult,
                    success: result !== null || webmsxywResult !== null
                });
                
                if (result) {
                    console.log(`✅ getXs 签名生成成功: ${JSON.stringify(result).substring(0, 100)}...`);
                }
                
                if (webmsxywResult) {
                    console.log(`✅ _webmsxyw 签名生成成功: ${JSON.stringify(webmsxywResult).substring(0, 100)}...`);
                }
                
            } catch (e) {
                console.log(`❌ 测试用例 ${index + 1} 失败: ${e.message}`);
                this.results.signatures.tests.push({
                    testCase: testCase,
                    error: e.message,
                    success: false
                });
            }
        });
    }

    /**
     * 分析算法模式
     */
    analyzeAlgorithmPatterns() {
        console.log('🔍 分析算法模式...');
        
        // 查找可能的加密算法
        const cryptoPatterns = [
            'md5', 'sha1', 'sha256', 'hmac', 'aes', 'des', 'rsa',
            'base64', 'crc32', 'hash', 'encrypt', 'decrypt', 'sign'
        ];
        
        const foundPatterns = [];
        
        // 在 o() 函数中查找
        if (this.results.functions.o && this.results.functions.o.exists) {
            const oSource = o.toString().toLowerCase();
            cryptoPatterns.forEach(pattern => {
                if (oSource.includes(pattern)) {
                    foundPatterns.push({
                        pattern: pattern,
                        location: 'o() function',
                        context: this.extractContext(oSource, pattern)
                    });
                }
            });
        }
        
        // 在全局作用域中查找
        for (let key in window) {
            if (typeof window[key] === 'function') {
                try {
                    const source = window[key].toString().toLowerCase();
                    cryptoPatterns.forEach(pattern => {
                        if (source.includes(pattern) && key !== 'o') {
                            foundPatterns.push({
                                pattern: pattern,
                                location: `${key}() function`,
                                context: this.extractContext(source, pattern)
                            });
                        }
                    });
                } catch (e) {
                    // 忽略错误
                }
            }
        }
        
        this.results.algorithms.patterns = foundPatterns;
        console.log(`✅ 发现 ${foundPatterns.length} 个算法模式`);
    }

    /**
     * 生成报告
     */
    generateReport() {
        console.log('📊 生成分析报告...');
        
        const report = {
            summary: {
                totalFunctions: this.results.functions.global ? this.results.functions.global.length : 0,
                oFunctionExists: this.results.functions.o ? this.results.functions.o.exists : false,
                getXsFunctionExists: this.results.functions.getXs ? this.results.functions.getXs.exists : false,
                successfulTests: this.results.signatures.tests ? 
                    this.results.signatures.tests.filter(t => t.success).length : 0,
                totalTests: this.results.signatures.tests ? this.results.signatures.tests.length : 0,
                algorithmPatterns: this.results.algorithms.patterns ? this.results.algorithms.patterns.length : 0
            },
            recommendations: this.generateRecommendations()
        };
        
        this.results.report = report;
        
        console.log('📋 分析报告摘要:');
        console.log(`   - 全局函数数量: ${report.summary.totalFunctions}`);
        console.log(`   - o() 函数存在: ${report.summary.oFunctionExists}`);
        console.log(`   - getXs() 函数存在: ${report.summary.getXsFunctionExists}`);
        console.log(`   - 成功测试: ${report.summary.successfulTests}/${report.summary.totalTests}`);
        console.log(`   - 算法模式: ${report.summary.algorithmPatterns}`);
        
        console.log('💡 优化建议:');
        report.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
    }

    /**
     * 生成优化建议
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (this.results.functions.o && this.results.functions.o.exists) {
            if (this.results.functions.o.isObfuscated) {
                recommendations.push('o() 函数存在混淆，建议使用反混淆工具');
            }
            if (this.results.functions.o.complexity > 1000) {
                recommendations.push('o() 函数复杂度很高，建议分段分析');
            }
        }
        
        if (this.results.functions.getXs && this.results.functions.getXs.exists) {
            recommendations.push('getXs() 函数可用，建议深入分析其调用链');
        }
        
        const successRate = this.results.signatures.tests ? 
            this.results.signatures.tests.filter(t => t.success).length / this.results.signatures.tests.length : 0;
        
        if (successRate < 0.5) {
            recommendations.push('签名生成成功率较低，需要检查环境配置');
        } else if (successRate === 1) {
            recommendations.push('签名生成成功率100%，可以开始实际应用');
        }
        
        if (this.results.algorithms.patterns && this.results.algorithms.patterns.length > 0) {
            recommendations.push('发现加密算法模式，建议重点分析这些函数');
        }
        
        recommendations.push('建议使用浏览器开发者工具进行动态调试');
        recommendations.push('建议对比真实网站的签名生成过程');
        
        return recommendations;
    }

    // 辅助方法
    isObfuscated(source) {
        const obfuscationIndicators = [
            /[a-zA-Z]{1,2}\d+/g,  // 短变量名 + 数字
            /0x[0-9a-f]+/gi,      // 十六进制数字
            /\\x[0-9a-f]{2}/gi,   // 十六进制转义
            /eval\s*\(/gi,        // eval 调用
            /String\.fromCharCode/gi // 字符编码
        ];
        
        return obfuscationIndicators.some(pattern => pattern.test(source));
    }

    calculateComplexity(source) {
        const lines = source.split('\n').length;
        const functions = (source.match(/function/g) || []).length;
        const loops = (source.match(/for\s*\(|while\s*\(/g) || []).length;
        const conditions = (source.match(/if\s*\(/g) || []).length;
        
        return lines + functions * 10 + loops * 5 + conditions * 3;
    }

    extractPatterns(source) {
        const patterns = [];
        
        // 提取正则表达式
        const regexMatches = source.match(/\/[^\/\n]+\/[gimuy]*/g) || [];
        patterns.push(...regexMatches.map(r => ({ type: 'regex', value: r })));
        
        // 提取数字常量
        const numberMatches = source.match(/\b\d+\b/g) || [];
        patterns.push(...numberMatches.slice(0, 10).map(n => ({ type: 'number', value: n })));
        
        return patterns;
    }

    extractVariables(source) {
        const variables = source.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g) || [];
        return [...new Set(variables)].slice(0, 20);
    }

    extractStrings(source) {
        const strings = source.match(/'[^']*'|"[^"]*"/g) || [];
        return strings.slice(0, 10);
    }

    extractParameters(source) {
        const match = source.match(/function[^(]*\(([^)]*)\)/);
        if (match && match[1]) {
            return match[1].split(',').map(p => p.trim()).filter(p => p);
        }
        return [];
    }

    extractContext(source, pattern) {
        const index = source.indexOf(pattern);
        if (index !== -1) {
            const start = Math.max(0, index - 50);
            const end = Math.min(source.length, index + pattern.length + 50);
            return source.substring(start, end);
        }
        return '';
    }
}

// 自动执行分析
console.log('🎯 启动 JavaScript 逆向分析器...');
const analyzer = new JSReverseAnalyzer();
const results = analyzer.analyze();

// 将结果保存到全局变量，便于后续访问
window.reverseAnalysisResults = results;

console.log('🎉 JavaScript 逆向分析完成！');
console.log('📊 完整结果已保存到 window.reverseAnalysisResults');
