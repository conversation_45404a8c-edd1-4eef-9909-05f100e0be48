#!/usr/bin/env python3
"""
JavaScript 逆向工程工具 - 专门用于小红书 x-s-common 签名逆向
支持处理压缩混淆的 JS 代码，提取签名生成算法
"""

import re
import json
import ast
import base64
import hashlib
import time
from typing import Dict, List, Any, Optional, Tuple

class JSDeobfuscator:
    """JavaScript 解混淆器"""

    def __init__(self):
        self.variable_map = {}
        self.function_map = {}
        self.string_map = {}

    def beautify_js(self, js_content: str) -> str:
        """增强的 JavaScript 美化函数"""

        # 预处理：处理字符串字面量
        js_content = self._protect_strings(js_content)

        # 基本格式化
        js_content = js_content.replace(';', ';\n')
        js_content = js_content.replace('{', ' {\n')
        js_content = js_content.replace('}', '\n}\n')
        js_content = js_content.replace(',', ',\n')

        # 处理函数定义
        js_content = re.sub(r'function\s*([^{]*)\{', r'function \1 {\n', js_content)

        # 处理控制结构
        js_content = re.sub(r'(if|for|while|switch)\s*\([^)]*\)\s*\{',
                           lambda m: f'{m.group(1)} ({self._extract_condition(m.group(0))}) {{\n',
                           js_content)

        # 处理对象字面量
        js_content = re.sub(r'(\w+):', r'\n  \1:', js_content)

        # 缩进处理
        return self._apply_indentation(js_content)

    def _protect_strings(self, js_content: str) -> str:
        """保护字符串字面量不被格式化破坏"""
        string_pattern = r'(["\'])(?:(?=(\\?))\2.)*?\1'
        strings = re.findall(string_pattern, js_content)

        for i, (quote, _) in enumerate(strings):
            placeholder = f'__STRING_{i}__'
            js_content = js_content.replace(strings[i][0], placeholder, 1)
            self.string_map[placeholder] = strings[i][0]

        return js_content

    def _extract_condition(self, condition_block: str) -> str:
        """提取条件表达式"""
        match = re.search(r'\(([^)]*)\)', condition_block)
        return match.group(1) if match else '...'

    def _apply_indentation(self, js_content: str) -> str:
        """应用缩进"""
        lines = js_content.split('\n')
        formatted_lines = []
        indent_level = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 减少缩进
            if line.startswith('}') or line.startswith(']') or line.startswith(')'):
                indent_level = max(0, indent_level - 1)

            # 添加缩进
            formatted_lines.append('  ' * indent_level + line)

            # 增加缩进
            if (line.endswith('{') or line.endswith('[') or
                line.endswith('(') and not line.strip().startswith('if')):
                indent_level += 1

        # 恢复字符串
        result = '\n'.join(formatted_lines)
        for placeholder, original in self.string_map.items():
            result = result.replace(placeholder, original)

        return result

def extract_functions_with_keywords(js_content, keywords):
    """提取包含特定关键词的函数"""
    functions = []
    
    # 查找函数定义
    function_pattern = r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\([^)]*\)\s*\{[^}]*\}'
    matches = re.finditer(function_pattern, js_content, re.IGNORECASE | re.DOTALL)
    
    for match in matches:
        func_content = match.group(0)
        func_name = match.group(1)
        
        # 检查是否包含关键词
        for keyword in keywords:
            if keyword.lower() in func_content.lower():
                functions.append({
                    'name': func_name,
                    'content': func_content,
                    'keyword': keyword
                })
                break
                
    return functions

def search_for_signatures(js_content):
    """搜索签名相关的代码"""
    
    keywords = [
        'x-s-common', 'X-S-Common', 'xs-common', 'XS-Common',
        'signature', 'sign', 'encrypt', 'encode', 'decode',
        'webmsxyw', '_webmsxyw', 'base64', 'btoa', 'atob',
        'md5', 'sha', 'crc', 'hash'
    ]
    
    results = {
        'functions': extract_functions_with_keywords(js_content, keywords),
        'keyword_matches': {}
    }
    
    # 搜索关键词出现的位置
    for keyword in keywords:
        pattern = re.compile(keyword, re.IGNORECASE)
        matches = []
        
        for match in pattern.finditer(js_content):
            start = max(0, match.start() - 100)
            end = min(len(js_content), match.end() + 100)
            context = js_content[start:end]
            
            matches.append({
                'position': match.start(),
                'context': context
            })
            
        if matches:
            results['keyword_matches'][keyword] = matches
            
    return results

def main():
    """主函数"""
    input_file = "xiaohongshunixiang/data/vendor-dynamic.f0f5c43a.js"
    
    print("🔍 分析 JavaScript 文件...")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
            
        print(f"📄 文件大小: {len(js_content)} 字符")
        
        # 搜索签名相关内容
        print("\n🔍 搜索签名相关内容...")
        results = search_for_signatures(js_content)
        
        # 输出结果
        print(f"\n📊 搜索结果:")
        print(f"   找到的函数: {len(results['functions'])}")
        print(f"   关键词匹配: {len(results['keyword_matches'])}")
        
        # 显示函数
        if results['functions']:
            print(f"\n🔧 找到的相关函数:")
            for i, func in enumerate(results['functions'][:5]):  # 只显示前5个
                print(f"   {i+1}. {func['name']} (包含关键词: {func['keyword']})")
                
        # 显示关键词匹配
        if results['keyword_matches']:
            print(f"\n🔑 关键词匹配:")
            for keyword, matches in results['keyword_matches'].items():
                if matches:
                    print(f"   {keyword}: {len(matches)} 次匹配")
                    # 显示第一个匹配的上下文
                    if matches:
                        context = matches[0]['context'].replace('\n', ' ')
                        print(f"      上下文: ...{context[:100]}...")
                        
        # 保存详细结果
        output_file = "xiaohongshunixiang/output/js_analysis_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 详细结果已保存到: {output_file}")
        
        # 尝试美化部分代码
        if results['keyword_matches']:
            print(f"\n🎨 尝试美化相关代码片段...")
            for keyword, matches in list(results['keyword_matches'].items())[:3]:  # 只处理前3个
                if matches:
                    match = matches[0]
                    start = max(0, match['position'] - 500)
                    end = min(len(js_content), match['position'] + 500)
                    code_snippet = js_content[start:end]
                    
                    beautified = beautify_js(code_snippet)
                    
                    snippet_file = f"xiaohongshunixiang/output/beautified_{keyword.replace('-', '_')}.js"
                    with open(snippet_file, 'w', encoding='utf-8') as f:
                        f.write(beautified)
                        
                    print(f"   {keyword} -> {snippet_file}")
                    
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == '__main__':
    main()
