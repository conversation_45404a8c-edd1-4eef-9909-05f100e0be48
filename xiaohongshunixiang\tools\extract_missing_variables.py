#!/usr/bin/env python3
"""
提取 xsCommon 函数缺失的变量和常量的工具
"""

import os
import re
import json
from datetime import datetime


def extract_missing_variables(file_path):
    """从JS文件中提取缺失的变量和常量"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        results = {}
        
        # 1. 搜索 PlatformCode 枚举定义
        platform_patterns = [
            r'PlatformCode\s*=\s*\{[^}]*\}',
            r'var\s+PlatformCode\s*=\s*\{[^}]*\}',
            r'const\s+PlatformCode\s*=\s*\{[^}]*\}',
            r'let\s+PlatformCode\s*=\s*\{[^}]*\}',
            r'Android\s*:\s*\d+.*iOS\s*:\s*\d+.*MacOs\s*:\s*\d+',
        ]
        
        for pattern in platform_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'PlatformCode' not in results:
                    results['PlatformCode'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['PlatformCode'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 2. 搜索变量 C 的定义
        c_patterns = [
            r'var\s+C\s*=\s*[^;]+;',
            r'let\s+C\s*=\s*[^;]+;',
            r'const\s+C\s*=\s*[^;]+;',
            r'C\s*=\s*["\'][^"\']*["\']',
            r'C\s*=\s*\d+',
        ]
        
        for pattern in c_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if 'variable_C' not in results:
                    results['variable_C'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['variable_C'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 3. 搜索数组 S 的定义
        s_patterns = [
            r'var\s+S\s*=\s*\[[^\]]*\]',
            r'let\s+S\s*=\s*\[[^\]]*\]',
            r'const\s+S\s*=\s*\[[^\]]*\]',
            r'S\s*=\s*\[[^\]]*\]',
            r'NEED_XSCOMMON_URLS\s*=\s*\[[^\]]*\]',
        ]
        
        for pattern in s_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'variable_S' not in results:
                    results['variable_S'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['variable_S'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 4. 搜索数组 k 的定义
        k_patterns = [
            r'var\s+k\s*=\s*\[[^\]]*\]',
            r'let\s+k\s*=\s*\[[^\]]*\]',
            r'const\s+k\s*=\s*\[[^\]]*\]',
            r'k\s*=\s*\[[^\]]*\]',
            r'NEED_REAL_TIME_XSCOMMON_URLS\s*=\s*\[[^\]]*\]',
        ]
        
        for pattern in k_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'variable_k' not in results:
                    results['variable_k'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['variable_k'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 5. 搜索数组 g 的定义
        g_patterns = [
            r'var\s+g\s*=\s*\[[^\]]*\]',
            r'let\s+g\s*=\s*\[[^\]]*\]',
            r'const\s+g\s*=\s*\[[^\]]*\]',
            r'g\s*=\s*\[[^\]]*\]',
            r'BLOCKED_HOSTS\s*=\s*\[[^\]]*\]',
        ]
        
        for pattern in g_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'variable_g' not in results:
                    results['variable_g'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['variable_g'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 6. 搜索 O 函数的定义
        o_patterns = [
            r'function\s+O\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'var\s+O\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'const\s+O\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'let\s+O\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'O\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
        ]
        
        for pattern in o_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'function_O' not in results:
                    results['function_O'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['function_O'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 7. 搜索 mcr 函数的定义
        mcr_patterns = [
            r'function\s+mcr\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'var\s+mcr\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'const\s+mcr\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'let\s+mcr\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'mcr\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
        ]
        
        for pattern in mcr_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'function_mcr' not in results:
                    results['function_mcr'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['function_mcr'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 8. 搜索 P 数组（Base64查找表）
        p_patterns = [
            r'var\s+P\s*=\s*["\'][A-Za-z0-9+/=]+["\']',
            r'const\s+P\s*=\s*["\'][A-Za-z0-9+/=]+["\']',
            r'let\s+P\s*=\s*["\'][A-Za-z0-9+/=]+["\']',
            r'P\s*=\s*["\'][A-Za-z0-9+/=]+["\']',
            r'lookup\s*=\s*["\'][A-Za-z0-9+/=]+["\']',
        ]
        
        for pattern in p_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if 'variable_P' not in results:
                    results['variable_P'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['variable_P'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 9. 搜索 encodeChunk 函数
        encode_chunk_patterns = [
            r'function\s+encodeChunk\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'var\s+encodeChunk\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'const\s+encodeChunk\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
            r'let\s+encodeChunk\s*=\s*function\s*\([^)]*\)\s*\{[^}]*(?:\{[^}]*\}[^}]*)*\}',
        ]
        
        for pattern in encode_chunk_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if 'function_encodeChunk' not in results:
                    results['function_encodeChunk'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['function_encodeChunk'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        # 10. 搜索常量定义
        constant_patterns = [
            r'MINI_BROSWER_INFO_KEY\s*=\s*["\'][^"\']*["\']',
            r'RC4_SECRET_VERSION_KEY\s*=\s*["\'][^"\']*["\']',
            r'RC4_SECRET_VERSION\s*=\s*["\'][^"\']*["\']',
            r'LOCAL_ID_KEY\s*=\s*["\'][^"\']*["\']',
            r'version\s*=\s*["\'][^"\']*["\']',
        ]
        
        for pattern in constant_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if 'constants' not in results:
                    results['constants'] = []
                
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results['constants'].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
        
        return results
        
    except Exception as e:
        print(f"❌ 处理文件失败: {e}")
        return {}


def main():
    """主函数"""
    print("🔍 提取 xsCommon 函数缺失的变量和常量")
    print("="*60)
    
    # 搜索目录
    js_dir = os.path.join(os.getcwd(), "data", "js")
    output_dir = os.path.join(os.getcwd(), "analysis")
    
    if not os.path.exists(js_dir):
        print(f"❌ JS文件目录不存在: {js_dir}")
        return
    
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = {
        'PlatformCode': [],
        'variable_C': [],
        'variable_S': [],
        'variable_k': [],
        'variable_g': [],
        'variable_P': [],
        'function_O': [],
        'function_mcr': [],
        'function_encodeChunk': [],
        'constants': [],
        'analysis_time': datetime.now().isoformat()
    }
    
    # 搜索所有JS文件
    js_files = []
    for root, _, files in os.walk(js_dir):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    
    print(f"📁 搜索 {len(js_files)} 个JS文件...")
    
    for js_file in js_files:
        print(f"🔍 分析文件: {os.path.basename(js_file)}")
        
        # 提取变量和常量
        results = extract_missing_variables(js_file)
        
        for key, values in results.items():
            if values:
                print(f"  ✅ 找到 {len(values)} 个 {key}")
                for value in values:
                    value['file'] = os.path.basename(js_file)
                    value['full_path'] = js_file
                    all_results[key].extend(values)
    
    # 输出结果
    print("\n📊 分析结果:")
    for key, values in all_results.items():
        if key != 'analysis_time' and values:
            print(f"  {key}: {len(values)} 个")
    
    # 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存JSON格式
    json_file = os.path.join(output_dir, f"missing_variables_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # 保存可读格式
    txt_file = os.path.join(output_dir, f"missing_variables_{timestamp}.txt")
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("xsCommon 缺失变量和常量提取报告\n")
        f.write("="*60 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for key, values in all_results.items():
            if key != 'analysis_time' and values:
                f.write(f"🔍 {key}:\n")
                f.write("-"*40 + "\n")
                for i, item in enumerate(values, 1):
                    f.write(f"\n{i}. 文件: {item['file']} (行 {item['line_number']})\n")
                    f.write(f"定义: {item['definition']}\n")
                    f.write("="*80 + "\n")
    
    print(f"\n✅ 分析完成！结果已保存到:")
    print(f"  📄 详细数据: {json_file}")
    print(f"  📝 分析报告: {txt_file}")


if __name__ == "__main__":
    main()
