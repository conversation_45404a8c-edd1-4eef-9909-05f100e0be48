{"webmsxyw_source": "function(){var bc=bt;m={'_sabo_c097b':this||R,'_sabo_57ecb':m,'_sabo_10b1e':arguments,'_sabo_6da36':F1};M(F0);m=m[bc(0x354)];return i[bc(WL.h)](j,u[0x0]);}", "signature_functions": [{"name": "fetch", "source": "function(url, options = {}) {\n                console.log('🌐 Fetch请求:', url);\n                \n                if (options.headers) {\n                    for (let [key, value] of Object.entries(options.headers)) {\n                        if (key.toLowerCase() === 'x-s-common') {\n                            console.log('🔑 发现 x-s-common:', value);\n                            \n                            // 尝试获取调用栈\n                            try {\n                                throw new Error('Stack trace');\n                            } catch (e) {\n                                console.log('📍 调用栈:', e.stack);\n                            }\n                        }\n                    }\n                }\n                \n                return originalFetch.apply(this, arguments);\n            }"}, {"name": "n", "source": "function assign() { [native code] }"}, {"name": "initCaptcha", "source": "function(ie){const T=Object.assign({},y,ie);return m(T)}"}], "relevant_scripts": []}