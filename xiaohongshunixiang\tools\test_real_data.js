#!/usr/bin/env node
/**
 * 使用真实Cookie数据测试 x-s-common 生成器
 */

const { generateXSCommon, setUserData, mcr, b64Encode, encodeUtf8 } = require('./xs_common_generator');

// 真实的Cookie数据
const REAL_COOKIE = "abRequestId=41b545ec-e397-57bd-9d91-3569da43d3d0; xsecappid=xhs-pc-web; a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; webId=00ec9cba945a033e639b90fd1084ed06; gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf; x-hng=lang=zh-CN&domain=edith.xiaohongshu.com; web_session=040069b2f9b2fb2f11df169d7f3a4bba9874fc; webBuild=4.68.0; acw_tc=0a4add3517500006000197379e2d9dab37f158557fee88b1e97b2160e31b06; loadts=1750000601796; websectiga=cf46039d1971c7b9a650d87269f31ac8fe3bf71d61ebf9d9a0a87efb414b816c; sec_poison_id=accf80f2-1df7-43fd-8187-351ca062b405; unread={%22ub%22:%22684cec01000000000f03ba8d%22%2C%22ue%22:%22684e4f2c00000000230012dd%22%2C%22uc%22:15}";

/**
 * 解析Cookie字符串
 */
function parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
            cookies[name] = decodeURIComponent(value);
        }
    });
    return cookies;
}

/**
 * 生成模拟的X-t和X-s值
 */
function generateMockHeaders() {
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 15);
    
    return {
        xT: timestamp.toString(),
        xS: `XYW_${randomStr}_${timestamp}`,
        timestamp: timestamp
    };
}

/**
 * 主测试函数
 */
function testWithRealData() {
    console.log("🚀 使用真实Cookie数据测试 x-s-common 生成器");
    console.log("============================================================");
    
    // 解析Cookie
    const cookies = parseCookie(REAL_COOKIE);
    console.log("\n📋 解析的Cookie数据:");
    console.log(`  a1: ${cookies.a1}`);
    console.log(`  webId: ${cookies.webId}`);
    console.log(`  gid: ${cookies.gid}`);
    console.log(`  webBuild: ${cookies.webBuild}`);
    console.log(`  xsecappid: ${cookies.xsecappid}`);
    
    // 设置用户数据
    setUserData({
        a1: cookies.a1,
        b1: cookies.webId, // 使用webId作为设备指纹
        b1b1: "1"
    });
    
    console.log("\n✅ 用户数据设置完成");
    
    // 测试URL列表
    const testUrls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post", 
        "https://www.xiaohongshu.com/api/sec/v1/shield/webprofile",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/user/keyInfo",
        "https://www.xiaohongshu.com/api/sns/web/v1/user/otherinfo"
    ];
    
    console.log("\n🧪 开始测试生成 x-s-common...");
    
    testUrls.forEach((url, index) => {
        console.log(`\n📝 测试 ${index + 1}: ${url}`);
        
        // 生成模拟的请求头
        const mockHeaders = generateMockHeaders();
        console.log(`   X-t: ${mockHeaders.xT}`);
        console.log(`   X-s: ${mockHeaders.xS}`);
        
        try {
            // 生成 x-s-common
            const xsCommon = generateXSCommon(url, {
                platform: "PC",
                xT: mockHeaders.xT,
                xS: mockHeaders.xS
            });
            
            if (xsCommon) {
                console.log(`✅ 生成成功: ${xsCommon.substring(0, 50)}...`);
                console.log(`   完整长度: ${xsCommon.length} 字符`);
                
                // 尝试解码验证
                try {
                    const decoded = Buffer.from(xsCommon, 'base64').toString('utf-8');
                    const parsed = JSON.parse(decoded);
                    
                    console.log(`📊 解码验证成功:`);
                    console.log(`   s0 (平台代码): ${parsed.s0}`);
                    console.log(`   x1 (版本): ${parsed.x1}`);
                    console.log(`   x2 (平台): ${parsed.x2}`);
                    console.log(`   x3 (应用): ${parsed.x3}`);
                    console.log(`   x4 (应用版本): ${parsed.x4}`);
                    console.log(`   x5 (a1): ${parsed.x5 ? parsed.x5.substring(0, 20) + '...' : '空'}`);
                    console.log(`   x8 (设备指纹): ${parsed.x8}`);
                    console.log(`   x9 (CRC32): ${parsed.x9}`);
                    console.log(`   x10 (计数): ${parsed.x10}`);
                    console.log(`   x11 (状态): ${parsed.x11}`);
                    
                    // 验证CRC32计算
                    const expectedCrc = mcr(`${mockHeaders.xT}${mockHeaders.xS}${cookies.webId}`);
                    console.log(`🔍 CRC32验证: 期望=${expectedCrc}, 实际=${parsed.x9}, ${expectedCrc === parsed.x9 ? '✅匹配' : '❌不匹配'}`);
                    
                } catch (decodeError) {
                    console.log(`❌ 解码失败: ${decodeError.message}`);
                }
            } else {
                console.log(`❌ 生成失败 - 可能URL不在支持列表中`);
            }
            
        } catch (error) {
            console.log(`❌ 生成异常: ${error.message}`);
        }
    });
    
    // 测试CRC32算法
    console.log("\n🔧 测试CRC32算法:");
    const testStrings = [
        "test",
        "hello world",
        `${Date.now()}test${cookies.webId}`,
        ""
    ];
    
    testStrings.forEach(str => {
        const crc = mcr(str);
        console.log(`   "${str}" -> ${crc}`);
    });
    
    // 测试Base64编码
    console.log("\n🔧 测试自定义Base64编码:");
    const testData = "Hello, 小红书!";
    const utf8Bytes = encodeUtf8(testData);
    const base64Result = b64Encode(utf8Bytes);
    console.log(`   原文: ${testData}`);
    console.log(`   UTF8字节: [${utf8Bytes.slice(0, 10).join(', ')}...]`);
    console.log(`   Base64: ${base64Result}`);
    
    // 与标准Base64对比
    const standardBase64 = Buffer.from(testData, 'utf-8').toString('base64');
    console.log(`   标准Base64: ${standardBase64}`);
    console.log(`   是否相同: ${base64Result === standardBase64 ? '✅是' : '❌否'}`);
    
    console.log("\n🎯 测试完成！");
    console.log("\n💡 使用建议:");
    console.log("1. 确保a1 cookie值是最新的");
    console.log("2. webId应该保持一致作为设备指纹");
    console.log("3. X-t和X-s值需要与实际请求匹配");
    console.log("4. 生成的x-s-common应该在请求中作为请求头使用");
}

/**
 * 单独测试特定URL
 */
function testSingleUrl(url, options = {}) {
    console.log(`\n🎯 单独测试URL: ${url}`);
    
    const cookies = parseCookie(REAL_COOKIE);
    setUserData({
        a1: cookies.a1,
        b1: cookies.webId,
        b1b1: "1"
    });
    
    const mockHeaders = generateMockHeaders();
    const result = generateXSCommon(url, {
        platform: options.platform || "PC",
        xT: options.xT || mockHeaders.xT,
        xS: options.xS || mockHeaders.xS
    });
    
    if (result) {
        console.log(`✅ 生成成功:`);
        console.log(`X-S-Common: ${result}`);
        
        // 生成完整的请求头示例
        console.log(`\n📋 完整请求头示例:`);
        console.log(`X-S-Common: ${result}`);
        console.log(`X-t: ${options.xT || mockHeaders.xT}`);
        console.log(`X-s: ${options.xS || mockHeaders.xS}`);
        console.log(`Cookie: ${REAL_COOKIE}`);
        console.log(`User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36`);
        
    } else {
        console.log(`❌ 生成失败`);
    }
    
    return result;
}

// 命令行参数处理
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length > 0) {
        const url = args[0];
        const platform = args[1] || "PC";
        testSingleUrl(url, { platform });
    } else {
        testWithRealData();
    }
}

module.exports = {
    testWithRealData,
    testSingleUrl,
    parseCookie,
    generateMockHeaders,
    REAL_COOKIE
};
