#!/usr/bin/env python3
"""
JavaScript代码分析工具 - 专门搜索x-s-common相关代码
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import os
import re
import json
import threading
from datetime import datetime
import ast


class JSAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 JavaScript代码分析工具 - x-s-common搜索")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)
        
        # 搜索配置
        self.search_patterns = {
            'x-s-common': [
                r'["\']x-s-common["\']',
                r'x-s-common',
                r'xscommon',
                r'xs_common',
                r'XS_COMMON',
                r'X_S_COMMON'
            ],
            'signature': [
                r'signature',
                r'sign\s*\(',
                r'getSign',
                r'generateSign',
                r'createSign'
            ],
            'encrypt': [
                r'encrypt',
                r'encode',
                r'hash',
                r'md5',
                r'sha',
                r'crypto'
            ]
        }
        
        # 结果存储
        self.analysis_results = []
        self.current_directory = ""
        
        self.setup_ui()
        
        # 设置默认目录
        default_dir = os.path.join(os.getcwd(), "data", "js")
        if os.path.exists(default_dir):
            self.directory_var.set(default_dir)
            self.current_directory = default_dir

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 JavaScript代码分析工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        subtitle_label = ttk.Label(main_frame, text="专门搜索 x-s-common 相关代码", 
                                  font=('Arial', 12), foreground="gray")
        subtitle_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 目录选择区域
        ttk.Label(main_frame, text="JS文件目录:").grid(row=2, column=0, sticky=tk.W, pady=5)
        self.directory_var = tk.StringVar()
        directory_entry = ttk.Entry(main_frame, textvariable=self.directory_var, width=80)
        directory_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        browse_btn = ttk.Button(main_frame, text="📁 浏览", command=self.browse_directory)
        browse_btn.grid(row=2, column=2, pady=5, padx=(5, 0))
        
        # 搜索选项区域
        options_frame = ttk.LabelFrame(main_frame, text="🔧 搜索选项", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 搜索模式选择
        self.search_x_s_common = tk.BooleanVar(value=True)
        self.search_signature = tk.BooleanVar(value=True)
        self.search_encrypt = tk.BooleanVar(value=True)
        self.case_sensitive = tk.BooleanVar(value=False)
        self.show_context = tk.BooleanVar(value=True)
        
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(options_row1, text="搜索 x-s-common", variable=self.search_x_s_common).pack(side=tk.LEFT)
        ttk.Checkbutton(options_row1, text="搜索签名相关", variable=self.search_signature).pack(side=tk.LEFT, padx=(20, 0))
        ttk.Checkbutton(options_row1, text="搜索加密相关", variable=self.search_encrypt).pack(side=tk.LEFT, padx=(20, 0))
        
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X)
        
        ttk.Checkbutton(options_row2, text="区分大小写", variable=self.case_sensitive).pack(side=tk.LEFT)
        ttk.Checkbutton(options_row2, text="显示上下文", variable=self.show_context).pack(side=tk.LEFT, padx=(20, 0))
        
        # 自定义搜索模式
        custom_frame = ttk.Frame(options_frame)
        custom_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(custom_frame, text="自定义搜索:").pack(side=tk.LEFT)
        self.custom_pattern = tk.StringVar()
        custom_entry = ttk.Entry(custom_frame, textvariable=self.custom_pattern, width=40)
        custom_entry.pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(custom_frame, text="(支持正则表达式)", foreground="gray").pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.analyze_btn = ttk.Button(button_frame, text="🔍 开始分析", 
                                     command=self.start_analysis)
        self.analyze_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止分析", 
                                  command=self.stop_analysis, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空结果", 
                                   command=self.clear_results)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        self.export_btn = ttk.Button(button_frame, text="💾 导出结果", 
                                    command=self.export_results)
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        self.extract_btn = ttk.Button(button_frame, text="📋 提取关键代码", 
                                     command=self.extract_key_code)
        self.extract_btn.pack(side=tk.LEFT, padx=5)
        
        # 结果显示区域
        results_frame = ttk.LabelFrame(main_frame, text="📊 分析结果", padding="5")
        results_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview来显示结果
        columns = ('文件', '行号', '匹配类型', '匹配内容', '上下文')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # 设置列标题和宽度
        self.results_tree.heading('文件', text='文件名')
        self.results_tree.heading('行号', text='行号')
        self.results_tree.heading('匹配类型', text='匹配类型')
        self.results_tree.heading('匹配内容', text='匹配内容')
        self.results_tree.heading('上下文', text='上下文')
        
        self.results_tree.column('文件', width=200)
        self.results_tree.column('行号', width=60)
        self.results_tree.column('匹配类型', width=100)
        self.results_tree.column('匹配内容', width=200)
        self.results_tree.column('上下文', width=400)
        
        # 添加滚动条
        scrollbar_v = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        scrollbar_h = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # 绑定双击事件
        self.results_tree.bind('<Double-1>', self.on_result_double_click)
        
        # 状态栏
        self.status_label = ttk.Label(main_frame, text="📊 准备就绪")
        self.status_label.grid(row=6, column=0, columnspan=3, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 停止标志
        self.stop_flag = False

    def browse_directory(self):
        """浏览选择目录"""
        directory = filedialog.askdirectory(title="选择JS文件目录")
        if directory:
            self.directory_var.set(directory)
            self.current_directory = directory

    def start_analysis(self):
        """开始分析"""
        directory = self.directory_var.get().strip()
        if not directory or not os.path.exists(directory):
            messagebox.showerror("错误", "请选择有效的JS文件目录")
            return
        
        self.stop_flag = False
        self.analyze_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        
        # 清空之前的结果
        self.clear_results()
        
        def analyze():
            try:
                self.status_label.config(text="🔍 正在分析JS文件...")
                
                # 获取所有JS文件
                js_files = []
                for root, dirs, files in os.walk(directory):
                    for file in files:
                        if file.endswith('.js'):
                            js_files.append(os.path.join(root, file))
                
                self.status_label.config(text=f"📁 找到 {len(js_files)} 个JS文件，开始分析...")
                
                # 分析每个文件
                for i, js_file in enumerate(js_files):
                    if self.stop_flag:
                        break
                        
                    self.analyze_file(js_file)
                    
                    # 更新进度
                    progress_text = f"🔍 分析进度: {i+1}/{len(js_files)} - {os.path.basename(js_file)}"
                    self.status_label.config(text=progress_text)
                
                if not self.stop_flag:
                    self.status_label.config(text=f"✅ 分析完成！找到 {len(self.analysis_results)} 个匹配项")
                    messagebox.showinfo("完成", f"分析完成！\n找到 {len(self.analysis_results)} 个相关代码片段")
                else:
                    self.status_label.config(text="⏹️ 分析已停止")
                    
            except Exception as e:
                self.status_label.config(text=f"❌ 分析出错: {e}")
                messagebox.showerror("错误", f"分析过程出错: {e}")
            finally:
                self.root.after(0, lambda: [
                    self.progress.stop(),
                    self.analyze_btn.config(state='normal'),
                    self.stop_btn.config(state='disabled')
                ])
                
        threading.Thread(target=analyze, daemon=True).start()

    def stop_analysis(self):
        """停止分析"""
        self.stop_flag = True

    def analyze_file(self, file_path):
        """分析单个JS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 构建搜索模式列表
            patterns_to_search = []
            
            if self.search_x_s_common.get():
                patterns_to_search.extend([(p, 'x-s-common') for p in self.search_patterns['x-s-common']])
            
            if self.search_signature.get():
                patterns_to_search.extend([(p, 'signature') for p in self.search_patterns['signature']])
            
            if self.search_encrypt.get():
                patterns_to_search.extend([(p, 'encrypt') for p in self.search_patterns['encrypt']])
            
            # 添加自定义模式
            if self.custom_pattern.get().strip():
                patterns_to_search.append((self.custom_pattern.get().strip(), 'custom'))
            
            # 搜索每一行
            for line_num, line in enumerate(lines, 1):
                if self.stop_flag:
                    break
                    
                for pattern, match_type in patterns_to_search:
                    try:
                        flags = 0 if self.case_sensitive.get() else re.IGNORECASE
                        matches = re.finditer(pattern, line, flags)
                        
                        for match in matches:
                            # 获取上下文
                            context = ""
                            if self.show_context.get():
                                start_line = max(0, line_num - 3)
                                end_line = min(len(lines), line_num + 2)
                                context_lines = lines[start_line:end_line]
                                context = "\\n".join(context_lines)
                            
                            result = {
                                'file': os.path.basename(file_path),
                                'full_path': file_path,
                                'line_number': line_num,
                                'match_type': match_type,
                                'matched_text': match.group(),
                                'full_line': line.strip(),
                                'context': context,
                                'pattern': pattern
                            }
                            
                            self.analysis_results.append(result)
                            
                            # 添加到树形视图
                            self.root.after(0, lambda r=result: self.add_result_to_tree(r))
                            
                    except re.error:
                        # 忽略正则表达式错误
                        pass
                        
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")

    def add_result_to_tree(self, result):
        """添加结果到树形视图"""
        self.results_tree.insert('', 'end', values=(
            result['file'],
            result['line_number'],
            result['match_type'],
            result['matched_text'][:50] + ('...' if len(result['matched_text']) > 50 else ''),
            result['full_line'][:100] + ('...' if len(result['full_line']) > 100 else '')
        ))

    def on_result_double_click(self, event):
        """双击结果项时显示详细信息"""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            values = item['values']
            
            # 找到对应的详细结果
            for result in self.analysis_results:
                if (result['file'] == values[0] and 
                    result['line_number'] == values[1] and 
                    result['match_type'] == values[2]):
                    
                    self.show_detail_window(result)
                    break

    def show_detail_window(self, result):
        """显示详细信息窗口"""
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"详细信息 - {result['file']}")
        detail_window.geometry("800x600")
        
        # 创建文本框显示详细信息
        text_frame = ttk.Frame(detail_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        detail_text = scrolledtext.ScrolledText(text_frame, height=30, width=100)
        detail_text.pack(fill=tk.BOTH, expand=True)
        
        # 填充详细信息
        detail_info = f"""文件: {result['full_path']}
行号: {result['line_number']}
匹配类型: {result['match_type']}
匹配模式: {result['pattern']}
匹配文本: {result['matched_text']}

完整行内容:
{result['full_line']}

上下文:
{result['context']}
"""
        
        detail_text.insert(tk.END, detail_info)
        detail_text.config(state='disabled')

    def clear_results(self):
        """清空结果"""
        self.analysis_results.clear()
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        self.status_label.config(text="📊 结果已清空")

    def export_results(self):
        """导出分析结果"""
        if not self.analysis_results:
            messagebox.showwarning("警告", "没有可导出的结果")
            return
        
        try:
            filename = filedialog.asksaveasfilename(
                title="保存分析结果",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )
            
            if filename:
                if filename.endswith('.json'):
                    # 导出为JSON格式
                    export_data = {
                        'analysis_time': datetime.now().isoformat(),
                        'directory': self.current_directory,
                        'total_matches': len(self.analysis_results),
                        'results': self.analysis_results
                    }
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, indent=2, ensure_ascii=False)
                else:
                    # 导出为文本格式
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"JavaScript代码分析结果\n")
                        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"分析目录: {self.current_directory}\n")
                        f.write(f"总匹配数: {len(self.analysis_results)}\n")
                        f.write("="*80 + "\n\n")
                        
                        for i, result in enumerate(self.analysis_results, 1):
                            f.write(f"{i}. {result['file']} (行 {result['line_number']})\n")
                            f.write(f"   类型: {result['match_type']}\n")
                            f.write(f"   匹配: {result['matched_text']}\n")
                            f.write(f"   内容: {result['full_line']}\n")
                            f.write("\n")
                
                self.status_label.config(text=f"✅ 结果已导出到: {filename}")
                messagebox.showinfo("成功", f"结果已导出到:\n{filename}")
                
        except Exception as e:
            messagebox.showerror("错误", f"导出失败: {e}")

    def extract_key_code(self):
        """提取关键代码片段"""
        if not self.analysis_results:
            messagebox.showwarning("警告", "没有可提取的结果")
            return
        
        # 创建新窗口显示关键代码
        extract_window = tk.Toplevel(self.root)
        extract_window.title("🔑 关键代码提取")
        extract_window.geometry("1000x700")
        
        # 创建文本框
        text_frame = ttk.Frame(extract_window, padding="10")
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        extract_text = scrolledtext.ScrolledText(text_frame, height=40, width=120)
        extract_text.pack(fill=tk.BOTH, expand=True)
        
        # 按类型分组显示关键代码
        x_s_common_results = [r for r in self.analysis_results if r['match_type'] == 'x-s-common']
        signature_results = [r for r in self.analysis_results if r['match_type'] == 'signature']
        encrypt_results = [r for r in self.analysis_results if r['match_type'] == 'encrypt']
        
        extract_text.insert(tk.END, "🔑 关键代码提取结果\n")
        extract_text.insert(tk.END, "="*80 + "\n\n")
        
        if x_s_common_results:
            extract_text.insert(tk.END, "📍 X-S-COMMON 相关代码:\n")
            extract_text.insert(tk.END, "-"*50 + "\n")
            for result in x_s_common_results[:10]:  # 限制显示数量
                extract_text.insert(tk.END, f"文件: {result['file']} (行 {result['line_number']})\n")
                extract_text.insert(tk.END, f"代码: {result['full_line']}\n\n")
        
        if signature_results:
            extract_text.insert(tk.END, "\n📍 签名相关代码:\n")
            extract_text.insert(tk.END, "-"*50 + "\n")
            for result in signature_results[:10]:
                extract_text.insert(tk.END, f"文件: {result['file']} (行 {result['line_number']})\n")
                extract_text.insert(tk.END, f"代码: {result['full_line']}\n\n")
        
        if encrypt_results:
            extract_text.insert(tk.END, "\n📍 加密相关代码:\n")
            extract_text.insert(tk.END, "-"*50 + "\n")
            for result in encrypt_results[:10]:
                extract_text.insert(tk.END, f"文件: {result['file']} (行 {result['line_number']})\n")
                extract_text.insert(tk.END, f"代码: {result['full_line']}\n\n")


def main():
    """主函数"""
    root = tk.Tk()
    app = JSAnalyzerGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
