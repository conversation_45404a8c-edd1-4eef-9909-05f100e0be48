#!/usr/bin/env python3
"""
小红书 x-s-common 逆向分析启动器
"""

import os
import sys
import subprocess
import time
import requests

def print_banner():
    """打印横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                小红书 x-s-common 逆向分析工具                 ║
    ║                                                              ║
    ║  🔍 浏览器调试  🎣 Hook分析  🔧 签名生成  🧪 自动测试        ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_dependencies():
    """检查依赖"""
    print("🔍 检查依赖...")
    
    # 检查 Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js: {result.stdout.strip()}")
        else:
            print("❌ Node.js 未安装")
            return False
    except FileNotFoundError:
        print("❌ Node.js 未安装")
        return False
    
    # 检查 Python
    print(f"✅ Python: {sys.version.split()[0]}")
    
    # 检查 Python 包
    required_packages = ['flask', 'requests', 'playwright']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    
    # 安装 Node.js 依赖
    if os.path.exists('package.json'):
        print("📦 安装 Node.js 依赖...")
        subprocess.run(['npm', 'install'], cwd=os.path.dirname(__file__))
    
    # 安装 Python 依赖
    if os.path.exists('requirements.txt'):
        print("📦 安装 Python 依赖...")
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
    
    # 安装 Playwright 浏览器
    print("📦 安装 Playwright 浏览器...")
    subprocess.run([sys.executable, '-m', 'playwright', 'install', 'chromium'])

def start_signature_server():
    """启动签名服务器"""
    print("🚀 启动签名服务器...")

    # 优先使用基于 _webmsxyw 的高级签名服务
    webmsxyw_server = os.path.join(os.path.dirname(__file__), 'services', 'webmsxyw_signature_service.py')
    legacy_server = os.path.join(os.path.dirname(__file__), 'tools', 'signature_server.py')

    server_script = None
    port = None

    if os.path.exists(webmsxyw_server):
        print("🎯 使用基于 _webmsxyw 的高级签名服务")
        server_script = webmsxyw_server
        port = 5107
    elif os.path.exists(legacy_server):
        print("🟨 使用传统签名服务")
        server_script = legacy_server
        port = 5108
    else:
        print("❌ 签名服务器脚本不存在")
        return None

    process = subprocess.Popen([sys.executable, server_script])

    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    for i in range(15):  # 增加等待时间，因为 _webmsxyw 服务需要初始化浏览器
        try:
            response = requests.get(f'http://localhost:{port}/health', timeout=2)
            if response.status_code == 200:
                print("✅ 签名服务器启动成功")
                return process
        except:
            time.sleep(1)

    print("⚠️  服务器启动可能失败")
    return process

def run_browser_debug():
    """运行浏览器调试"""
    print("🔍 启动浏览器调试...")

    # 优先使用 Python 版本
    debug_script_py = os.path.join(os.path.dirname(__file__), 'analysis', 'browser_debugger.py')
    debug_script_js = os.path.join(os.path.dirname(__file__), 'analysis', 'browser_debugger.js')

    if os.path.exists(debug_script_py):
        print("🐍 使用 Python 版本的浏览器调试器")
        subprocess.run([sys.executable, debug_script_py])
    elif os.path.exists(debug_script_js):
        print("🟨 使用 JavaScript 版本的浏览器调试器")
        subprocess.run(['node', debug_script_js])
    else:
        print("❌ 浏览器调试脚本不存在")

def run_hook_analysis():
    """运行 Hook 分析"""
    print("🎣 启动 Hook 分析...")

    # 优先使用 Python 版本
    hook_script_py = os.path.join(os.path.dirname(__file__), 'analysis', 'hook_analysis.py')
    hook_script_js = os.path.join(os.path.dirname(__file__), 'analysis', 'hook_analysis.js')

    if os.path.exists(hook_script_py):
        print("🐍 使用 Python 版本的 Hook 分析器")
        subprocess.run([sys.executable, hook_script_py])
    elif os.path.exists(hook_script_js):
        print("🟨 使用 JavaScript 版本的 Hook 分析器")
        subprocess.run(['node', hook_script_js])
    else:
        print("❌ Hook 分析脚本不存在")

def run_tests():
    """运行测试"""
    print("🧪 运行测试套件...")
    
    test_script = os.path.join(os.path.dirname(__file__), 'tools', 'test_suite.py')
    
    if os.path.exists(test_script):
        subprocess.run([sys.executable, test_script])
    else:
        print("❌ 测试脚本不存在")

def test_js_generator():
    """测试 JS 生成器"""
    print("🧪 测试 JS 生成器...")

    js_script = os.path.join(os.path.dirname(__file__), 'reverse', 'x_s_common_v1.js')

    if os.path.exists(js_script):
        subprocess.run(['node', js_script])
    else:
        print("❌ JS 生成器不存在")

def run_webmsxyw_analysis():
    """运行 _webmsxyw 函数专项分析"""
    print("🎯 启动 _webmsxyw 函数专项分析...")

    analyzer_script = os.path.join(os.path.dirname(__file__), 'analysis', 'webmsxyw_analyzer.py')

    if os.path.exists(analyzer_script):
        subprocess.run([sys.executable, analyzer_script])
    else:
        print("❌ _webmsxyw 分析器不存在")

def run_payload_analysis():
    """运行 Payload 结构分析"""
    print("🔍 启动 Payload 结构分析...")

    analyzer_script = os.path.join(os.path.dirname(__file__), 'analysis', 'payload_analyzer.py')

    if os.path.exists(analyzer_script):
        subprocess.run([sys.executable, analyzer_script])
    else:
        print("❌ Payload 分析器不存在")

def run_api_tests():
    """运行 API 测试"""
    print("🧪 启动 API 测试...")

    tester_script = os.path.join(os.path.dirname(__file__), 'testing', 'api_tester.py')

    if os.path.exists(tester_script):
        subprocess.run([sys.executable, tester_script])
    else:
        print("❌ API 测试器不存在")

def show_menu():
    """显示菜单"""
    menu = """
    📋 选择操作:

    1. 🔧 安装依赖
    2. 🚀 启动签名服务器 (高级 _webmsxyw 版本)
    3. 🔍 浏览器调试分析
    4. 🎣 Hook 深度分析
    5. 🎯 _webmsxyw 函数专项分析
    6. 🔍 Payload 结构分析
    7. 🧪 API 测试 (使用真实签名)
    8. 🧪 测试 JS 生成器
    9. 🧪 运行完整测试
    10. 📊 查看项目信息
    11. 🚪 退出

    """
    print(menu)

def show_project_info():
    """显示项目信息"""
    info = """
    📊 项目信息:
    
    📁 项目结构:
    ├── analysis/          # 分析工具
    │   ├── browser_debugger.py   # 浏览器调试 (Python)
    │   ├── hook_analysis.py      # Hook 分析 (Python)
    │   ├── webmsxyw_analyzer.py  # _webmsxyw 函数专项分析
    │   ├── payload_analyzer.py   # Payload 结构分析
    │   ├── signature_analyzer.py # 签名数据分析器
    │   └── simple_analyzer.py    # 简化分析器
    ├── services/          # 签名服务
    │   └── webmsxyw_signature_service.py  # 高级签名服务
    ├── testing/           # 测试工具
    │   └── api_tester.py         # API 测试器
    ├── reverse/           # 逆向实现
    │   └── x_s_common_v1.js      # 签名生成器
    ├── tools/             # 辅助工具
    │   ├── signature_server.py   # 传统签名服务器
    │   └── test_suite.py         # 测试套件
    └── output/            # 输出文件
    
    🌐 服务端口:
    - 高级签名服务: http://localhost:5107 (基于 _webmsxyw)
    - 传统签名服务: http://localhost:5108

    📝 主要功能:
    - 基于 _webmsxyw 的真实签名生成
    - Payload 结构深度分析
    - 真实 API 调用测试
    - 浏览器环境调试
    - Hook 函数分析
    - 自动化测试验证

    💡 使用建议:
    1. 运行 _webmsxyw 专项分析了解函数特性
    2. 使用 Payload 分析研究签名结构
    3. 启动高级签名服务进行 API 测试
    4. 通过真实 API 调用验证签名有效性
    """
    print(info)

def main():
    """主函数"""
    print_banner()
    
    # 切换到项目目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    while True:
        show_menu()
        
        try:
            choice = input("请选择操作 (1-11): ").strip()

            if choice == '1':
                if check_dependencies():
                    print("✅ 所有依赖都已安装")
                else:
                    install_dependencies()

            elif choice == '2':
                if check_dependencies():
                    server_process = start_signature_server()
                    if server_process:
                        input("按 Enter 键停止服务器...")
                        server_process.terminate()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '3':
                if check_dependencies():
                    run_browser_debug()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '4':
                if check_dependencies():
                    run_hook_analysis()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '5':
                if check_dependencies():
                    run_webmsxyw_analysis()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '6':
                if check_dependencies():
                    run_payload_analysis()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '7':
                if check_dependencies():
                    run_api_tests()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '8':
                test_js_generator()

            elif choice == '9':
                if check_dependencies():
                    # 先启动服务器
                    server_process = start_signature_server()
                    time.sleep(3)  # 等待服务器启动

                    # 运行测试
                    run_tests()

                    # 停止服务器
                    if server_process:
                        server_process.terminate()
                else:
                    print("❌ 请先安装依赖")

            elif choice == '10':
                show_project_info()

            elif choice == '11':
                print("👋 再见!")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
        
        input("\n按 Enter 键继续...")

if __name__ == '__main__':
    main()
