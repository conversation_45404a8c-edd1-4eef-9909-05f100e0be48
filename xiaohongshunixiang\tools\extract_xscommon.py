#!/usr/bin/env python3
"""
提取生成 x-s-common 请求头值的函数及其依赖的工具
"""

import os
import re
import json
from datetime import datetime


def extract_xs_common_header_functions(file_path):
    """从JS文件中提取生成 x-s-common 请求头的相关函数"""
    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()

        # 搜索与 x-s-common 相关的代码模式
        patterns = [
            # 直接设置 x-s-common 头
            r'["\']x-s-common["\'][\s]*:[\s]*[^,}]+',
            r'setRequestHeader\s*\(\s*["\']x-s-common["\'][\s]*,[\s]*[^)]+\)',
            r'headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+',

            # 可能的函数调用模式
            r'["\']x-s-common["\'][\s]*:[\s]*\w+\([^)]*\)',
            r'setRequestHeader\s*\(\s*["\']x-s-common["\'][\s]*,[\s]*\w+\([^)]*\)\s*\)',

            # 变量赋值模式
            r'\w+\s*=\s*[^;]*x-s-common[^;]*',
            r'x-s-common[^=]*=\s*[^;]+',
        ]

        results = []

        for pattern in patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                start = match.start()
                end = match.end()

                # 获取匹配的代码片段
                matched_code = match.group()

                # 尝试扩展上下文，找到完整的函数或对象
                context_start = max(0, start - 200)  # 向前200字符
                context_end = min(len(content), end + 200)  # 向后200字符
                context = content[context_start:context_end]

                # 获取行号
                lines_before = content[:start].count('\n')
                line_number = lines_before + 1

                results.append({
                    'line_number': line_number,
                    'matched_code': matched_code,
                    'context': context,
                    'pattern_used': pattern,
                    'start_pos': start,
                    'end_pos': end
                })

        # 搜索可能的函数名
        function_patterns = [
            r'function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)',
            r'function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)',
            r'function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)',
            r'(\w+)\s*[:=]\s*function[^{]*\{[^}]*x-s-common[^}]*\}',
        ]

        for pattern in function_patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                start = match.start()

                # 尝试找到完整的函数体
                func_start = content.find('{', start)
                if func_start == -1:
                    continue

                # 计算括号匹配来找到函数结束
                brace_count = 1
                pos = func_start + 1

                while pos < len(content) and brace_count > 0:
                    if content[pos] == '{':
                        brace_count += 1
                    elif content[pos] == '}':
                        brace_count -= 1
                    pos += 1

                if brace_count == 0:
                    # 找到完整函数
                    full_function = content[start:pos]

                    # 获取行号
                    lines_before = content[:start].count('\n')
                    line_number = lines_before + 1

                    results.append({
                        'line_number': line_number,
                        'function_code': full_function,
                        'function_name': match.group(1) if match.groups() else 'unknown',
                        'pattern_used': pattern,
                        'type': 'function'
                    })

        return results
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {e}")
        return []


def search_related_functions(content, function_names):
    """搜索相关的函数定义"""
    results = {}
    
    for func_name in function_names:
        patterns = [
            rf'function\s+{func_name}\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',
            rf'{func_name}\s*[:=]\s*function\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',
            rf'var\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',
            rf'let\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',
            rf'const\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',
            rf'{func_name}\s*\([^)]*\)\s*\{{[^}}]*(?:\{{[^}}]*\}}[^}}]*)*\}}',  # 简单函数定义
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.DOTALL | re.IGNORECASE)
            for match in matches:
                if func_name not in results:
                    results[func_name] = []
                
                # 获取行号
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results[func_name].append({
                    'line_number': line_number,
                    'function_code': match.group(),
                    'pattern_used': pattern
                })
    
    return results


def search_variable_definitions(content, var_names):
    """搜索变量定义"""
    results = {}
    
    for var_name in var_names:
        patterns = [
            rf'var\s+{var_name}\s*=\s*[^;]+;',
            rf'let\s+{var_name}\s*=\s*[^;]+;',
            rf'const\s+{var_name}\s*=\s*[^;]+;',
            rf'{var_name}\s*=\s*[^;]+;',
        ]
        
        for pattern in patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                if var_name not in results:
                    results[var_name] = []
                
                # 获取行号
                lines_before = content[:match.start()].count('\n')
                line_number = lines_before + 1
                
                results[var_name].append({
                    'line_number': line_number,
                    'definition': match.group(),
                    'pattern_used': pattern
                })
    
    return results


def beautify_js_code(code):
    """简单的JS代码美化"""
    # 基本的格式化
    code = re.sub(r';\s*', ';\n', code)
    code = re.sub(r'\{\s*', '{\n    ', code)
    code = re.sub(r'\}\s*', '\n}\n', code)
    code = re.sub(r',\s*', ',\n    ', code)
    
    return code


def main():
    """主函数"""
    print("🔍 提取生成 x-s-common 请求头的函数及其依赖")
    print("="*60)

    # 搜索目录
    js_dir = os.path.join(os.getcwd(), "data", "js")
    output_dir = os.path.join(os.getcwd(), "analysis")

    if not os.path.exists(js_dir):
        print(f"❌ JS文件目录不存在: {js_dir}")
        return

    os.makedirs(output_dir, exist_ok=True)

    # 需要查找的相关函数（可能与签名生成相关）
    related_functions = [
        'sign',
        'signature',
        'encrypt',
        'hash',
        'md5',
        'sha',
        'hmac',
        'encode',
        'getSign',
        'createSign',
        'generateSign',
        'calcSign',
        'buildSign',
        'makeSign',
        'getHeader',
        'setHeader',
        'addHeader',
        'createHeader',
        'buildHeader',
        'getCommon',
        'common',
        'xs',
        'xsign'
    ]

    # 需要查找的变量（可能包含签名相关的配置）
    related_variables = [
        'headers',
        'header',
        'sign',
        'signature',
        'common',
        'xs',
        'key',
        'secret',
        'salt',
        'token'
    ]

    all_results = {
        'xs_common_patterns': [],
        'related_functions': {},
        'related_variables': {},
        'analysis_time': datetime.now().isoformat()
    }
    
    # 搜索所有JS文件
    js_files = []
    for root, _, files in os.walk(js_dir):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))

    print(f"📁 搜索 {len(js_files)} 个JS文件...")

    for js_file in js_files:
        print(f"🔍 分析文件: {os.path.basename(js_file)}")

        # 读取文件内容
        try:
            with open(js_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")
            continue

        # 提取 x-s-common 相关代码
        xs_common_results = extract_xs_common_header_functions(js_file)
        if xs_common_results:
            print(f"✅ 找到 {len(xs_common_results)} 个 x-s-common 相关代码片段")
            for result in xs_common_results:
                result['file'] = os.path.basename(js_file)
                result['full_path'] = js_file
                all_results['xs_common_patterns'].append(result)
        
        # 搜索相关函数
        func_results = search_related_functions(content, related_functions)
        for func_name, matches in func_results.items():
            if func_name not in all_results['related_functions']:
                all_results['related_functions'][func_name] = []
            
            for match in matches:
                match['file'] = os.path.basename(js_file)
                match['full_path'] = js_file
                all_results['related_functions'][func_name].append(match)
        
        # 搜索相关变量
        var_results = search_variable_definitions(content, related_variables)
        for var_name, matches in var_results.items():
            if var_name not in all_results['related_variables']:
                all_results['related_variables'][var_name] = []
            
            for match in matches:
                match['file'] = os.path.basename(js_file)
                match['full_path'] = js_file
                all_results['related_variables'][var_name].append(match)
    
    # 输出结果
    print("\n📊 分析结果:")
    print(f"  x-s-common 相关代码: {len(all_results['xs_common_patterns'])} 个")
    print(f"  相关函数: {sum(len(v) for v in all_results['related_functions'].values())} 个")
    print(f"  相关变量: {sum(len(v) for v in all_results['related_variables'].values())} 个")

    # 保存详细结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # 保存JSON格式
    json_file = os.path.join(output_dir, f"xs_common_header_extraction_{timestamp}.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)

    # 保存可读格式
    txt_file = os.path.join(output_dir, f"xs_common_header_analysis_{timestamp}.txt")
    with open(txt_file, 'w', encoding='utf-8') as f:
        f.write("X-S-Common 请求头生成函数提取分析报告\n")
        f.write("="*60 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # x-s-common 相关代码
        f.write("🔍 x-s-common 相关代码片段:\n")
        f.write("-"*40 + "\n")
        for i, pattern in enumerate(all_results['xs_common_patterns'], 1):
            f.write(f"\n{i}. 文件: {pattern['file']} (行 {pattern['line_number']})\n")
            f.write(f"匹配模式: {pattern['pattern_used']}\n")
            if 'matched_code' in pattern:
                f.write(f"匹配代码: {pattern['matched_code']}\n")
            if 'function_code' in pattern:
                f.write("函数代码:\n")
                f.write(beautify_js_code(pattern['function_code']))
            if 'context' in pattern:
                f.write("上下文:\n")
                f.write(pattern['context'][:500] + "..." if len(pattern['context']) > 500 else pattern['context'])
            f.write("\n" + "="*80 + "\n")
        
        # 相关函数
        f.write("\n🔧 相关函数:\n")
        f.write("-"*30 + "\n")
        for func_name, matches in all_results['related_functions'].items():
            if matches:
                f.write(f"\n📍 {func_name}:\n")
                for match in matches:
                    f.write(f"  文件: {match['file']} (行 {match['line_number']})\n")
                    f.write(f"  代码: {match['function_code'][:100]}...\n\n")
        
        # 相关变量
        f.write("\n📊 相关变量:\n")
        f.write("-"*30 + "\n")
        for var_name, matches in all_results['related_variables'].items():
            if matches:
                f.write(f"\n📍 {var_name}:\n")
                for match in matches:
                    f.write(f"  文件: {match['file']} (行 {match['line_number']})\n")
                    f.write(f"  定义: {match['definition']}\n\n")
    
    # 保存美化的JS代码
    js_output_file = os.path.join(output_dir, f"xs_common_header_extracted_{timestamp}.js")
    with open(js_output_file, 'w', encoding='utf-8') as f:
        f.write("// X-S-Common 请求头生成函数及其依赖提取\n")
        f.write(f"// 提取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 写入 x-s-common 相关代码
        for i, pattern in enumerate(all_results['xs_common_patterns'], 1):
            f.write(f"// ========== x-s-common 相关代码 #{i} ==========\n")
            f.write(f"// 来源: {pattern['file']} 行 {pattern['line_number']}\n")
            f.write(f"// 匹配模式: {pattern['pattern_used']}\n")

            if 'function_code' in pattern:
                f.write("// 完整函数:\n")
                f.write(beautify_js_code(pattern['function_code']))
            elif 'matched_code' in pattern:
                f.write("// 匹配代码:\n")
                f.write(pattern['matched_code'])

            if 'context' in pattern:
                f.write("\n// 上下文:\n")
                f.write("/*\n")
                f.write(pattern['context'][:1000] + "..." if len(pattern['context']) > 1000 else pattern['context'])
                f.write("\n*/\n")

            f.write("\n\n")

        # 写入相关函数
        for func_name, matches in all_results['related_functions'].items():
            if matches:
                f.write(f"// ========== {func_name} ==========\n")
                for match in matches:
                    f.write(f"// 来源: {match['file']} 行 {match['line_number']}\n")
                    f.write(beautify_js_code(match['function_code']))
                    f.write("\n\n")

    print(f"\n✅ 分析完成！结果已保存到:")
    print(f"  📄 详细数据: {json_file}")
    print(f"  📝 分析报告: {txt_file}")
    print(f"  💻 提取代码: {js_output_file}")

    # 显示关键发现
    if all_results['xs_common_patterns']:
        print(f"\n🎯 关键发现:")
        for pattern in all_results['xs_common_patterns']:
            if 'function_name' in pattern:
                print(f"  📍 在 {pattern['file']} 第 {pattern['line_number']} 行找到函数: {pattern['function_name']}")
            else:
                print(f"  📍 在 {pattern['file']} 第 {pattern['line_number']} 行找到 x-s-common 相关代码")

    # 给出下一步建议
    print(f"\n💡 下一步建议:")
    print(f"  1. 查看分析报告，重点关注包含 'x-s-common' 的代码片段")
    print(f"  2. 分析这些代码片段的上下文，找到生成签名的完整函数")
    print(f"  3. 查找函数中调用的其他辅助函数")
    print(f"  4. 尝试在 Node.js 环境中补环境运行这些函数")


if __name__ == '__main__':
    main()
