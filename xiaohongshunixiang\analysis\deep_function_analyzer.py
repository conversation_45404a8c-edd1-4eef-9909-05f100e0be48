#!/usr/bin/env python3
"""
深度函数分析器 - 专门分析关键函数的实现
"""

import re
import json

def extract_function_implementations(js_content):
    """提取函数实现"""
    
    # 要查找的关键函数
    target_functions = [
        'getPlatformCode',
        'O',  # 用于生成x9的函数
        'genRandomString',
        'encrypt_crc32',
        'generateLocalId',
        'xsCommon',
        'xhsSign',
        'signAdaptor'
    ]
    
    results = {}
    
    for func_name in target_functions:
        print(f"🔍 搜索函数: {func_name}")
        
        # 多种函数定义模式
        patterns = [
            # function funcName() {}
            rf'function\s+{func_name}\s*\([^)]*\)\s*\{{[^}}]*\}}',
            # var funcName = function() {}
            rf'var\s+{func_name}\s*=\s*function[^{{]*\{{[^}}]*\}}',
            # let funcName = function() {}
            rf'let\s+{func_name}\s*=\s*function[^{{]*\{{[^}}]*\}}',
            # const funcName = function() {}
            rf'const\s+{func_name}\s*=\s*function[^{{]*\{{[^}}]*\}}',
            # funcName: function() {}
            rf'{func_name}\s*:\s*function[^{{]*\{{[^}}]*\}}',
            # funcName() {}
            rf'{func_name}\s*\([^)]*\)\s*\{{[^}}]*\}}',
            # 更宽泛的搜索
            rf'[a-zA-Z_$][a-zA-Z0-9_$]*\s*[=:]\s*function[^{{]*\{{[^}}]*{func_name}[^}}]*\}}'
        ]
        
        found_implementations = []
        
        for pattern in patterns:
            matches = re.finditer(pattern, js_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                impl = match.group(0)
                if len(impl) < 2000:  # 避免匹配到过长的代码
                    found_implementations.append({
                        'pattern': pattern,
                        'implementation': impl,
                        'position': match.start()
                    })
        
        if found_implementations:
            results[func_name] = found_implementations
            print(f"   ✅ 找到 {len(found_implementations)} 个实现")
        else:
            print(f"   ❌ 未找到实现")
    
    return results

def search_variable_assignments(js_content):
    """搜索变量赋值"""
    
    # 搜索可能的变量赋值
    variables_to_find = ['x1', 'x8', 'x10', 'C', 'p', 'd']
    
    results = {}
    
    for var_name in variables_to_find:
        print(f"🔍 搜索变量: {var_name}")
        
        # 搜索赋值模式
        patterns = [
            rf'{var_name}\s*=\s*[^;,\n]+',
            rf'var\s+{var_name}\s*=\s*[^;,\n]+',
            rf'let\s+{var_name}\s*=\s*[^;,\n]+',
            rf'const\s+{var_name}\s*=\s*[^;,\n]+',
        ]
        
        assignments = []
        
        for pattern in patterns:
            matches = re.finditer(pattern, js_content, re.IGNORECASE)
            for match in matches:
                assignment = match.group(0)
                assignments.append({
                    'assignment': assignment,
                    'position': match.start(),
                    'context': js_content[max(0, match.start()-50):match.end()+50]
                })
        
        if assignments:
            results[var_name] = assignments
            print(f"   ✅ 找到 {len(assignments)} 个赋值")
        else:
            print(f"   ❌ 未找到赋值")
    
    return results

def analyze_xs_common_generation_context(js_content):
    """分析 X-S-Common 生成的上下文"""
    
    print("🔍 分析 X-S-Common 生成上下文...")
    
    # 搜索包含 X-S-Common 的代码块
    pattern = r'.{200}X-S-Common.{200}'
    matches = re.finditer(pattern, js_content, re.IGNORECASE | re.DOTALL)
    
    contexts = []
    for match in matches:
        context = match.group(0)
        contexts.append({
            'context': context,
            'position': match.start()
        })
    
    return contexts

def extract_platform_related_code(js_content):
    """提取平台相关的代码"""
    
    print("🔍 提取平台相关代码...")
    
    # 搜索平台相关的模式
    patterns = [
        r'["\']PC["\']',
        r'["\']iOS["\']',
        r'["\']Android["\']',
        r'["\']web["\']',
        r'["\']ios["\']',
        r'["\']android["\']',
        r'platform[^=]*=[^;,\n]+',
        r'Platform[^=]*=[^;,\n]+',
    ]
    
    platform_code = []
    
    for pattern in patterns:
        matches = re.finditer(pattern, js_content, re.IGNORECASE)
        for match in matches:
            code = match.group(0)
            context = js_content[max(0, match.start()-100):match.end()+100]
            platform_code.append({
                'code': code,
                'context': context,
                'position': match.start()
            })
    
    return platform_code

def main():
    """主函数"""
    input_file = "xiaohongshunixiang/data/vendor-dynamic.f0f5c43a.js"
    
    print("🔍 深度分析关键函数实现...")
    print("=" * 60)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
            
        print(f"📄 文件大小: {len(js_content)} 字符")
        
        # 1. 提取函数实现
        print(f"\n1️⃣ 提取函数实现:")
        function_implementations = extract_function_implementations(js_content)
        
        # 2. 搜索变量赋值
        print(f"\n2️⃣ 搜索变量赋值:")
        variable_assignments = search_variable_assignments(js_content)
        
        # 3. 分析 X-S-Common 生成上下文
        print(f"\n3️⃣ 分析 X-S-Common 生成上下文:")
        xs_common_contexts = analyze_xs_common_generation_context(js_content)
        
        # 4. 提取平台相关代码
        print(f"\n4️⃣ 提取平台相关代码:")
        platform_code = extract_platform_related_code(js_content)
        
        # 保存结果
        results = {
            'function_implementations': function_implementations,
            'variable_assignments': variable_assignments,
            'xs_common_contexts': xs_common_contexts,
            'platform_code': platform_code
        }
        
        output_file = "xiaohongshunixiang/output/deep_function_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 详细分析结果已保存到: {output_file}")
        
        # 显示摘要
        print(f"\n📊 分析摘要:")
        print(f"   函数实现: {len(function_implementations)} 个")
        print(f"   变量赋值: {len(variable_assignments)} 个")
        print(f"   X-S-Common 上下文: {len(xs_common_contexts)} 个")
        print(f"   平台相关代码: {len(platform_code)} 个")
        
        # 显示部分关键发现
        if function_implementations:
            print(f"\n🔧 找到的关键函数:")
            for func_name, implementations in function_implementations.items():
                print(f"   {func_name}: {len(implementations)} 个实现")
                if implementations:
                    # 显示第一个实现的片段
                    impl = implementations[0]['implementation']
                    preview = impl[:100].replace('\n', ' ')
                    print(f"      预览: {preview}...")
                    
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == '__main__':
    main()
