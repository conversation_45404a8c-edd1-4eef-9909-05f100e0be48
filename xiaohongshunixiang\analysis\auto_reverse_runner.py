#!/usr/bin/env python3
"""
自动化逆向分析执行器
整合所有分析工具，一键执行完整的逆向分析流程
"""

import asyncio
import json
import time
import os
import logging
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AutoReverseRunner:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
        self.results = {}
        
    async def init(self):
        """初始化浏览器环境"""
        logger.info("🚀 初始化自动化逆向分析环境...")

        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器便于观察
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # 创建页面
            self.page = await self.browser.new_page()
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                             '(KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36'
            })
            
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            
            logger.info("✅ 浏览器环境初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 初始化失败: {e}")
            return False
    
    async def load_xhs_environment(self):
        """加载 xhs.js 环境"""
        logger.info("📁 加载 xhs.js 环境...")
        
        try:
            # 读取 xhs.js 文件
            xhs_js_path = "../xhs0828补环境/xhs.js"
            if os.path.exists(xhs_js_path):
                with open(xhs_js_path, 'r', encoding='utf-8') as f:
                    xhs_js_content = f.read()
                
                # 在页面中执行 xhs.js 代码
                await self.page.evaluate(xhs_js_content)
                
                # 验证环境
                verification = await self.page.evaluate("""
                    (() => {
                        return {
                            window_exists: typeof window !== 'undefined',
                            o_function_exists: typeof o === 'function',
                            getXs_function_exists: typeof getXs === 'function',
                            webmsxyw_exists: typeof window._webmsxyw === 'function'
                        };
                    })()
                """)
                
                logger.info(f"🔍 环境验证: {verification}")
                self.results['environment_verification'] = verification
                
                return True
            else:
                logger.error(f"❌ xhs.js 文件不存在: {xhs_js_path}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 加载 xhs.js 环境失败: {e}")
            return False
    
    async def run_js_analysis(self):
        """运行 JavaScript 分析"""
        logger.info("🔍 运行 JavaScript 逆向分析...")
        
        try:
            # 读取 JS 分析脚本
            js_analyzer_path = "analysis/js_reverse_analyzer.js"
            if os.path.exists(js_analyzer_path):
                with open(js_analyzer_path, 'r', encoding='utf-8') as f:
                    js_analyzer_content = f.read()
                
                # 执行分析脚本
                await self.page.evaluate(js_analyzer_content)
                
                # 等待分析完成
                await asyncio.sleep(3)
                
                # 获取分析结果
                analysis_results = await self.page.evaluate("""
                    (() => {
                        return window.reverseAnalysisResults || null;
                    })()
                """)
                
                if analysis_results:
                    logger.info("✅ JavaScript 分析完成")
                    self.results['js_analysis'] = analysis_results
                    return True
                else:
                    logger.error("❌ 未获取到 JavaScript 分析结果")
                    return False
            else:
                logger.error(f"❌ JS 分析脚本不存在: {js_analyzer_path}")
                return False
                
        except Exception as e:
            logger.error(f"❌ JavaScript 分析失败: {e}")
            return False
    
    async def test_signature_generation(self):
        """测试签名生成"""
        logger.info("🧪 测试签名生成功能...")
        
        test_cases = [
            {
                'name': 'user_info',
                'url': 'https://edith.xiaohongshu.com/api/sns/web/v2/user/me',
                'data': '{}',
                'a1': '1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399'
            },
            {
                'name': 'homefeed',
                'url': 'https://edith.xiaohongshu.com/api/sns/web/v1/homefeed',
                'data': '{"cursor_score":"","num":31,"refresh_type":1}',
                'a1': '1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399'
            },
            {
                'name': 'search_notes',
                'url': 'https://edith.xiaohongshu.com/api/sns/web/v1/search/notes',
                'data': '{"keyword":"美食","page":1,"page_size":20}',
                'a1': '1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399'
            }
        ]
        
        test_results = []
        
        for test_case in test_cases:
            try:
                logger.info(f"🔬 测试 {test_case['name']}: {test_case['url']}")
                
                # 测试 getXs 函数
                getxs_result = await self.page.evaluate("""
                    (params) => {
                        try {
                            if (typeof getXs === 'function') {
                                const result = getXs(params.url, params.data, params.a1);
                                return {
                                    success: true,
                                    result: result,
                                    type: typeof result
                                };
                            } else {
                                return {
                                    success: false,
                                    error: 'getXs function not found'
                                };
                            }
                        } catch (e) {
                            return {
                                success: false,
                                error: e.message
                            };
                        }
                    }
                """, test_case)
                
                # 测试 _webmsxyw 函数
                webmsxyw_result = await self.page.evaluate("""
                    (params) => {
                        try {
                            if (typeof window._webmsxyw === 'function') {
                                const result = window._webmsxyw(params.url, params.data);
                                return {
                                    success: true,
                                    result: result,
                                    type: typeof result
                                };
                            } else {
                                return {
                                    success: false,
                                    error: '_webmsxyw function not found'
                                };
                            }
                        } catch (e) {
                            return {
                                success: false,
                                error: e.message
                            };
                        }
                    }
                """, test_case)
                
                test_result = {
                    'test_case': test_case,
                    'getxs_result': getxs_result,
                    'webmsxyw_result': webmsxyw_result,
                    'overall_success': getxs_result['success'] or webmsxyw_result['success']
                }
                
                test_results.append(test_result)
                
                if test_result['overall_success']:
                    logger.info(f"✅ {test_case['name']} 测试成功")
                else:
                    logger.warning(f"⚠️ {test_case['name']} 测试失败")
                
            except Exception as e:
                logger.error(f"❌ 测试 {test_case['name']} 异常: {e}")
                test_results.append({
                    'test_case': test_case,
                    'error': str(e),
                    'overall_success': False
                })
        
        self.results['signature_tests'] = test_results
        
        # 统计成功率
        success_count = sum(1 for result in test_results if result.get('overall_success', False))
        success_rate = success_count / len(test_results) if test_results else 0
        
        logger.info(f"📊 签名测试完成: {success_count}/{len(test_results)} 成功 ({success_rate:.1%})")
        
        return success_rate > 0
    
    async def analyze_algorithm_complexity(self):
        """分析算法复杂度"""
        logger.info("🔬 分析算法复杂度...")
        
        try:
            complexity_analysis = await self.page.evaluate("""
                (() => {
                    const analysis = {
                        o_function: null,
                        global_functions: [],
                        complexity_metrics: {}
                    };
                    
                    // 分析 o() 函数
                    if (typeof o === 'function') {
                        const source = o.toString();
                        analysis.o_function = {
                            length: source.length,
                            lines: source.split('\\n').length,
                            functions: (source.match(/function/g) || []).length,
                            loops: (source.match(/for\\s*\\(|while\\s*\\(/g) || []).length,
                            conditions: (source.match(/if\\s*\\(/g) || []).length,
                            variables: (source.match(/\\b[a-zA-Z_$][a-zA-Z0-9_$]*\\b/g) || []).length
                        };
                    }
                    
                    // 分析全局函数
                    for (let key in window) {
                        if (typeof window[key] === 'function' && key.length < 20) {
                            try {
                                const source = window[key].toString();
                                if (!source.includes('[native code]')) {
                                    analysis.global_functions.push({
                                        name: key,
                                        length: source.length,
                                        complexity: source.split('\\n').length + 
                                                   (source.match(/function/g) || []).length * 10
                                    });
                                }
                            } catch (e) {
                                // 忽略错误
                            }
                        }
                    }
                    
                    // 排序并取前10个
                    analysis.global_functions.sort((a, b) => b.complexity - a.complexity);
                    analysis.global_functions = analysis.global_functions.slice(0, 10);
                    
                    return analysis;
                })()
            """)
            
            self.results['complexity_analysis'] = complexity_analysis
            logger.info("✅ 算法复杂度分析完成")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 算法复杂度分析失败: {e}")
            return False
    
    async def generate_comprehensive_report(self):
        """生成综合报告"""
        logger.info("📊 生成综合分析报告...")
        
        # 计算总体评分
        score = 0
        max_score = 100
        
        # 环境验证评分 (20分)
        env_verification = self.results.get('environment_verification', {})
        if env_verification.get('o_function_exists'):
            score += 10
        if env_verification.get('getXs_function_exists'):
            score += 5
        if env_verification.get('webmsxyw_exists'):
            score += 5
        
        # JavaScript 分析评分 (30分)
        js_analysis = self.results.get('js_analysis', {})
        if js_analysis:
            if js_analysis.get('functions', {}).get('o', {}).get('exists'):
                score += 15
            if js_analysis.get('functions', {}).get('getXs', {}).get('exists'):
                score += 10
            if js_analysis.get('algorithms', {}).get('patterns'):
                score += 5
        
        # 签名测试评分 (30分)
        signature_tests = self.results.get('signature_tests', [])
        if signature_tests:
            success_count = sum(1 for test in signature_tests if test.get('overall_success', False))
            success_rate = success_count / len(signature_tests)
            score += int(success_rate * 30)
        
        # 复杂度分析评分 (20分)
        complexity_analysis = self.results.get('complexity_analysis', {})
        if complexity_analysis.get('o_function'):
            score += 10
        if complexity_analysis.get('global_functions'):
            score += 10
        
        # 生成建议
        recommendations = []
        
        if score >= 80:
            recommendations.append("🎉 逆向分析非常成功！可以开始实际应用开发")
        elif score >= 60:
            recommendations.append("✅ 逆向分析基本成功，需要进一步优化")
        elif score >= 40:
            recommendations.append("⚠️ 逆向分析部分成功，需要重点解决关键问题")
        else:
            recommendations.append("❌ 逆向分析需要重新开始，检查环境和方法")
        
        if not env_verification.get('o_function_exists'):
            recommendations.append("🔧 o() 函数缺失，需要检查 xhs.js 文件完整性")
        
        if signature_tests:
            success_rate = sum(1 for test in signature_tests if test.get('overall_success', False)) / len(signature_tests)
            if success_rate < 0.5:
                recommendations.append("🔧 签名生成成功率低，需要调试算法实现")
        
        recommendations.append("📚 建议使用浏览器开发者工具进行进一步调试")
        recommendations.append("🔍 建议对比真实网站的签名生成过程")
        
        # 生成最终报告
        final_report = {
            'timestamp': time.time(),
            'score': score,
            'max_score': max_score,
            'success_rate': score / max_score,
            'summary': {
                'environment_ok': bool(env_verification),
                'js_analysis_ok': bool(js_analysis),
                'signature_tests_ok': bool(signature_tests),
                'complexity_analysis_ok': bool(complexity_analysis)
            },
            'recommendations': recommendations,
            'detailed_results': self.results
        }
        
        # 保存报告
        report_path = f"output/comprehensive_reverse_report_{int(time.time())}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(final_report, f, indent=2, ensure_ascii=False)
        
        logger.info(f"📋 综合报告已保存到: {report_path}")
        logger.info(f"🏆 总体评分: {score}/{max_score} ({score/max_score:.1%})")
        
        return final_report
    
    async def run_complete_analysis(self):
        """运行完整的逆向分析流程"""
        logger.info("🎯 开始完整的逆向分析流程...")
        
        try:
            # 1. 初始化环境
            if not await self.init():
                return None
            
            # 2. 加载 xhs.js 环境
            if not await self.load_xhs_environment():
                return None
            
            # 3. 运行 JavaScript 分析
            await self.run_js_analysis()
            
            # 4. 测试签名生成
            await self.test_signature_generation()
            
            # 5. 分析算法复杂度
            await self.analyze_algorithm_complexity()
            
            # 6. 生成综合报告
            final_report = await self.generate_comprehensive_report()
            
            logger.info("🎉 完整逆向分析流程执行完成！")
            
            return final_report
            
        except Exception as e:
            logger.error(f"❌ 逆向分析流程执行失败: {e}")
            return None
        
        finally:
            await self.cleanup()
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理资源...")
        
        if self.page:
            await self.page.close()
        
        if self.browser:
            await self.browser.close()
        
        if self.playwright:
            await self.playwright.stop()
        
        logger.info("✅ 资源清理完成")

async def main():
    """主函数"""
    runner = AutoReverseRunner()
    report = await runner.run_complete_analysis()
    
    if report:
        print("\n" + "="*60)
        print("🎯 小红书签名逆向分析完成！")
        print("="*60)
        print(f"📊 总体评分: {report['score']}/{report['max_score']} ({report['success_rate']:.1%})")
        print("\n💡 优化建议:")
        for i, rec in enumerate(report['recommendations'], 1):
            print(f"   {i}. {rec}")
        print("="*60)
    else:
        print("❌ 逆向分析失败，请检查日志信息")

if __name__ == "__main__":
    asyncio.run(main())
