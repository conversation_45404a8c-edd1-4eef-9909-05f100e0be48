# 🔍 JavaScript文件爬取工具

一个功能强大的GUI工具，用于爬取指定网站的所有JavaScript文件。

## ✨ 功能特性

- **🎯 智能识别**: 自动识别页面中的所有JS文件引用
- **🔄 多层爬取**: 支持设置爬取深度，递归分析子页面
- **💾 本地保存**: 可选择将JS文件下载到本地
- **📊 实时统计**: 显示爬取进度和统计信息
- **🌐 网络配置**: 支持代理设置和SSL配置
- **📝 详细日志**: 实时显示爬取过程和错误信息
- **💾 结果导出**: 支持导出爬取结果为JSON或文本格式

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行工具

```bash
# 直接运行GUI工具
python tools/js_crawler_gui.py

# 或者运行示例
python examples/js_crawler_usage.py
```

### 3. 使用步骤

1. **输入目标URL**: 在URL输入框中输入要爬取的网站地址
2. **配置选项**: 
   - 选择是否保存JS文件到本地
   - 设置爬取深度（1-3层）
   - 配置网络选项（代理、SSL等）
3. **开始爬取**: 点击"🚀 开始爬取"按钮
4. **查看结果**: 在日志区域查看爬取进度和结果
5. **导出结果**: 可选择导出爬取结果到文件

## 🔧 配置选项

### 爬取选项
- **保存JS文件到本地**: 是否将找到的JS文件下载到本地
- **跟随重定向**: 是否跟随HTTP重定向
- **爬取深度**: 设置递归爬取的深度（1-3层）

### 网络配置
- **SSL证书验证**: 是否启用SSL证书验证
- **代理设置**: 可配置HTTP/HTTPS代理

## 📁 文件识别方式

工具会通过以下方式识别JavaScript文件：

1. **HTML标签**: `<script src="...">` 标签
2. **内联脚本**: 分析内联JavaScript中的动态加载
3. **CSS文件**: 检查CSS文件中可能引用的JS文件
4. **动态加载**: 识别 `import()`, `require()` 等动态加载

## 📊 输出格式

### 统计信息
- 找到的JS文件总数
- 成功下载的文件数
- 下载失败的文件数

### 文件信息
每个JS文件包含以下信息：
- 文件URL
- 文件名
- 文件大小
- 内容类型
- 下载状态
- 本地保存路径（如果已下载）

## 💾 导出格式

### JSON格式
```json
{
  "crawl_time": "2024-01-01T12:00:00",
  "target_url": "https://example.com",
  "statistics": {
    "total_found": 10,
    "downloaded": 8,
    "failed": 2
  },
  "js_files": [
    {
      "url": "https://example.com/script.js",
      "filename": "script.js",
      "size": "1024",
      "content_type": "application/javascript",
      "status": "Downloaded",
      "local_path": "/path/to/script.js"
    }
  ]
}
```

### 文本格式
```
JavaScript文件爬取结果
爬取时间: 2024-01-01 12:00:00
目标URL: https://example.com
统计信息: 找到 10 个文件
================================================================================

1. script.js
   URL: https://example.com/script.js
   大小: 1024
   状态: Downloaded
   本地路径: /path/to/script.js
```

## ⚠️ 注意事项

1. **合法使用**: 请确保您有权限爬取目标网站的内容
2. **速度控制**: 工具会自动控制请求频率，避免对目标服务器造成压力
3. **文件大小**: 大型JS文件可能需要较长时间下载
4. **网络环境**: 某些网站可能需要配置代理才能访问

## 🛠️ 故障排除

### 常见问题

1. **SSL证书错误**: 关闭"启用SSL证书验证"选项
2. **网络连接问题**: 检查代理设置或网络连接
3. **权限问题**: 确保有写入保存目录的权限
4. **内存不足**: 减少爬取深度或分批处理

### 日志级别

- ℹ️ **INFO**: 一般信息
- ✅ **SUCCESS**: 成功操作
- ⚠️ **WARNING**: 警告信息
- ❌ **ERROR**: 错误信息
- 🔍 **DEBUG**: 调试信息

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的JS文件爬取功能
- GUI界面和配置选项
- 结果导出功能

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具！

## 📄 许可证

本项目采用MIT许可证。
