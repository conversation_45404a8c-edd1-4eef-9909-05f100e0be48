#!/usr/bin/env node
/**
 * 小红书 x-s-common 请求头生成器
 * 基于逆向分析的完整补环境实现
 */

// ==================== 常量定义 ====================

// 版本和密钥常量
const RC4_SECRET_VERSION = "1";
const RC4_SECRET_VERSION_KEY = "b1b1";
const LOCAL_ID_KEY = "a1";
const MINI_BROSWER_INFO_KEY = "b1";
const SIGN_COUNT_KEY = "sc";
const version = "4.0.8";

// URL匹配数组 (变量S)
const NEED_XSCOMMON_URLS = [
    "fe_api/burdock/v2/user/keyInfo",
    "fe_api/burdock/v2/shield/profile",
    "fe_api/burdock/v2/shield/captcha",
    "fe_api/burdock/v2/shield/registerCanvas",
    "api/sec/v1/shield/webprofile",
    "api/sec/v1/shield/captcha",
    /fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/tags/,
    /fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/image_stickers/,
    /fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/other\/notes/,
    /fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/related/,
    "/fe_api/burdock/v2/note/post",
    "/api/sns/web",
    "/api/redcaptcha",
    "/api/store/jpd/main"
];

// 实时URL匹配数组 (变量k)
const NEED_REAL_TIME_XSCOMMON_URLS = [];

// 被阻止的主机列表 (变量g)
const BLOCKED_HOSTS = [
    "/t.xiaohongshu.com",
    "/c.xiaohongshu.com",
    "spltest.xiaohongshu.com",
    "t2.xiaohongshu.com",
    "t2-test.xiaohongshu.com",
    "lng.xiaohongshu.com",
    "apm-track.xiaohongshu.com",
    "apm-track-test.xiaohongshu.com",
    "fse.xiaohongshu.com",
    "fse.devops.xiaohongshu.com",
    "fesentry.xiaohongshu.com",
    "spider-tracker.xiaohongshu.com"
];

// PlatformCode枚举
const PlatformCode = {
    Android: 1,
    iOS: 2,
    MacOs: 3,
    Linux: 4,
    other: 5
};

// Base64查找表 (基于code字符串生成)
const code = "ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5";
const lookup = [];

// 初始化Base64查找表
for (let i = 0; i < code.length; i++) {
    lookup[i] = code.charAt(i);
}

// ==================== 模拟浏览器环境 ====================

// 模拟localStorage
const localStorage = {
    storage: {},
    getItem(key) {
        return this.storage[key] || null;
    },
    setItem(key, value) {
        this.storage[key] = String(value);
    },
    removeItem(key) {
        delete this.storage[key];
    }
};

// 模拟sessionStorage
const sessionStorage = {
    storage: {},
    getItem(key) {
        return this.storage[key] || null;
    },
    setItem(key, value) {
        this.storage[key] = String(value);
    },
    removeItem(key) {
        delete this.storage[key];
    }
};

// 模拟cookie对象
const js_cookie = {
    A: {
        get(key) {
            // 这里应该从实际cookie中获取，暂时返回空字符串
            return "";
        }
    }
};

// 模拟l.Z.get方法
const l = {
    Z: {
        get(key) {
            if (key === "a1") {
                return js_cookie.A.get(key);
            }
            return "";
        }
    }
};

// ==================== 核心算法函数 ====================

/**
 * 获取平台代码
 */
function getPlatformCode(platform) {
    switch (platform) {
        case "Android":
            return PlatformCode.Android;
        case "iOS":
            return PlatformCode.iOS;
        case "Mac OS":
            return PlatformCode.MacOs;
        case "Linux":
            return PlatformCode.Linux;
        default:
            return PlatformCode.other;
    }
}

/**
 * 获取签名计数
 */
function getSigCount(hasSign) {
    let count = Number(sessionStorage.getItem(SIGN_COUNT_KEY)) || 0;
    if (hasSign) {
        count++;
        sessionStorage.setItem(SIGN_COUNT_KEY, count.toString());
    }
    return count;
}

/**
 * 判断是否需要签名
 */
function utils_shouldSign(url) {
    let shouldSign = true;
    
    // 检查是否是本站URL
    if (url.indexOf("xiaohongshu.com") > -1) {
        shouldSign = true;
    } else {
        // 检查是否在阻止列表中
        for (let host of BLOCKED_HOSTS) {
            if (url.indexOf(host) > -1) {
                shouldSign = false;
                break;
            }
        }
    }
    
    return shouldSign;
}

/**
 * UTF-8编码函数
 */
function encodeUtf8(str) {
    const encoded = encodeURIComponent(str);
    const bytes = [];
    
    for (let i = 0; i < encoded.length; i++) {
        const char = encoded.charAt(i);
        if (char === '%') {
            const hex = encoded.charAt(i + 1) + encoded.charAt(i + 2);
            const byte = parseInt(hex, 16);
            bytes.push(byte);
            i += 2;
        } else {
            bytes.push(char.charCodeAt(0));
        }
    }
    
    return bytes;
}

/**
 * Base64编码块函数
 */
function encodeChunk(bytes, start, end) {
    const result = [];
    
    for (let i = start; i < end; i += 3) {
        const a = bytes[i] || 0;
        const b = bytes[i + 1] || 0;
        const c = bytes[i + 2] || 0;
        
        const triplet = (a << 16) | (b << 8) | c;
        
        result.push(
            lookup[(triplet >> 18) & 63] +
            lookup[(triplet >> 12) & 63] +
            lookup[(triplet >> 6) & 63] +
            lookup[triplet & 63]
        );
    }
    
    return result.join('');
}

/**
 * 自定义Base64编码函数
 */
function b64Encode(bytes) {
    const len = bytes.length;
    const remainder = len % 3;
    const chunks = [];
    const chunkSize = 16383;
    
    // 处理完整的3字节块
    for (let i = 0; i < len - remainder; i += chunkSize) {
        const end = Math.min(i + chunkSize, len - remainder);
        chunks.push(encodeChunk(bytes, i, end));
    }
    
    // 处理剩余字节
    if (remainder === 1) {
        const lastByte = bytes[len - 1];
        chunks.push(
            lookup[lastByte >> 2] +
            lookup[(lastByte << 4) & 63] +
            "=="
        );
    } else if (remainder === 2) {
        const secondLast = bytes[len - 2];
        const last = bytes[len - 1];
        const combined = (secondLast << 8) + last;
        chunks.push(
            lookup[combined >> 10] +
            lookup[(combined >> 4) & 63] +
            lookup[(combined << 2) & 63] +
            "="
        );
    }
    
    return chunks.join('');
}

/**
 * CRC32算法 - mcr/O函数的实现
 */
function mcr(inputStr) {
    // CRC32查找表
    const crcTable = [
        0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685, 2657392035,
        249268274, 2044508324, 3772115230, 2547177864, 162941995, 2125561021, 3887607047, 2428444049,
        498536548, 1789927666, 4089016648, 2227061214, 450548861, 1843258603, 4107580753, 2211677639,
        325883990, 1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755, 2366115317,
        997073096, 1281953886, 3579855332, 2724688242, 1006888145, 1258607687, 3524101629, 2768942443,
        901097722, 1119000684, 3686517206, 2898065728, 853044451, 1172266101, 3705015759, 2882616665,
        651767980, 1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705, 3099436303,
        671266974, 1594198024, 3322730930, 2970347812, 795835527, 1483230225, 3244367275, 3060149565,
        1994146192, 31158534, 2563907772, 4023717930, 1907459465, 112637215, 2680153253, 3904427059,
        2013776290, 251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719, 3865271297,
        1802195444, 476864866, 2238001368, 4066508878, 1812370925, 453092731, 2181625025, 4111451223,
        1706088902, 314042704, 2344532202, 4240017532, 1658658271, 366619977, 2362670323, 4224994405,
        1303535960, 984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733, 3554079995,
        1131014506, 879679996, 2909243462, 3663771856, 1141124467, 855842277, 2852801631, 3708648649,
        1342533948, 654459306, 3188396048, 3373015174, 1466479909, 544179635, 3110523913, 3462522015,
        1591671054, 702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443, 3233442989,
        3988292384
    ];

    function rightWithoutSign(num, bit = 0) {
        return (num >>> bit);
    }

    let crc = -1;
    const maxLen = Math.min(57, inputStr.length);

    for (let i = 0; i < maxLen; i++) {
        const byte = inputStr.charCodeAt(i);
        const tableIndex = (crc & 255) ^ byte;
        crc = crcTable[tableIndex] ^ rightWithoutSign(crc, 8);
    }

    return (crc ^ -1 ^ 3988292384) >>> 0;
}

/**
 * 生成设备指纹 (b1值)
 */
function generateB1Value() {
    const timestamp = Date.now().toString();
    const deviceInfo = "Windows-Chrome-120.0.0.0";
    const combined = `${deviceInfo}-${timestamp}`;

    // 简单的哈希函数模拟
    let hash = 0;
    for (let i = 0; i < combined.length; i++) {
        const char = combined.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }

    return Math.abs(hash).toString(16).substring(0, 16);
}

/**
 * 核心函数：生成 x-s-common 请求头
 */
function xsCommon(config, request) {
    try {
        const platform = config.platform || "PC";
        const url = request.url;

        // 检查URL是否需要签名
        const urlPatterns = NEED_XSCOMMON_URLS.map(pattern => {
            if (typeof pattern === 'string') {
                return new RegExp(pattern);
            }
            return pattern;
        });

        const needsSign = urlPatterns.some(pattern => pattern.test(url));
        if (!needsSign || !utils_shouldSign(url)) {
            return request;
        }

        // 获取现有的签名头
        const xSign = request.headers["X-Sign"] || "";
        const xT = request.headers["X-t"] || "";
        const xS = request.headers["X-s"] || "";

        // 获取签名计数
        const sigCount = getSigCount(xSign || xT || xS);

        // 获取存储的值
        const b1Value = localStorage.getItem(MINI_BROSWER_INFO_KEY) || generateB1Value();
        const b1b1Value = localStorage.getItem(RC4_SECRET_VERSION_KEY) || RC4_SECRET_VERSION;
        const a1Value = l.Z.get(LOCAL_ID_KEY) || "";

        // 存储b1值
        localStorage.setItem(MINI_BROSWER_INFO_KEY, b1Value);

        // 构建x-s-common数据结构
        const commonData = {
            s0: getPlatformCode(platform),
            s1: "",
            x0: b1b1Value,
            x1: version, // 变量C
            x2: platform || "PC",
            x3: "xhs-pc-web",
            x4: "4.68.0",
            x5: a1Value,
            x6: xT,
            x7: xS,
            x8: b1Value,
            x9: mcr(`${xT}${xS}${b1Value}`), // O函数的调用
            x10: sigCount,
            x11: "normal"
        };

        // 检查是否需要实时更新
        const realTimePatterns = NEED_REAL_TIME_XSCOMMON_URLS.map(pattern => {
            if (typeof pattern === 'string') {
                return new RegExp(pattern);
            }
            return pattern;
        });

        const needsRealTime = realTimePatterns.some(pattern => pattern.test(url));

        // 如果需要实时更新且有指纹API，则异步更新
        if (needsRealTime && typeof window !== 'undefined' && window.xhsFingerprintV3) {
            window.xhsFingerprintV3.getCurMiniUa((newB1) => {
                commonData.x8 = newB1;
                commonData.x9 = mcr(`${xT}${xS}${newB1}`);
                request.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(commonData)));
            });
        } else {
            // 直接设置X-S-Common头
            request.headers["X-S-Common"] = b64Encode(encodeUtf8(JSON.stringify(commonData)));
        }

        return request;

    } catch (error) {
        console.error("生成x-s-common失败:", error);
        return request;
    }
}

// ==================== 导出和测试函数 ====================

/**
 * 简化的生成函数，直接返回x-s-common值
 */
function generateXSCommon(url, options = {}) {
    const config = {
        platform: options.platform || "PC"
    };

    const request = {
        url: url,
        headers: {
            "X-Sign": options.xSign || "",
            "X-t": options.xT || "",
            "X-s": options.xS || ""
        }
    };

    const result = xsCommon(config, request);
    return result.headers["X-S-Common"] || null;
}

/**
 * 设置用户数据
 */
function setUserData(userData) {
    if (userData.a1) {
        js_cookie.A.get = function(key) {
            if (key === "a1") return userData.a1;
            return "";
        };
    }

    if (userData.b1) {
        localStorage.setItem(MINI_BROSWER_INFO_KEY, userData.b1);
    }

    if (userData.b1b1) {
        localStorage.setItem(RC4_SECRET_VERSION_KEY, userData.b1b1);
    }
}

/**
 * 解析cookie字符串
 */
function parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
            cookies[name] = decodeURIComponent(value);
        }
    });
    return cookies;
}

/**
 * 测试函数
 */
function test() {
    console.log("🧪 测试 x-s-common 生成器");
    console.log("==================================================");

    // 使用真实的cookie数据
    const realCookie = "abRequestId=41b545ec-e397-57bd-9d91-3569da43d3d0; xsecappid=xhs-pc-web; a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; webId=00ec9cba945a033e639b90fd1084ed06; gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf; x-hng=lang=zh-CN&domain=edith.xiaohongshu.com; web_session=040069b2f9b2fb2f11df169d7f3a4bba9874fc; webBuild=4.68.0; acw_tc=0a4add3517500006000197379e2d9dab37f158557fee88b1e97b2160e31b06; loadts=1750000601796; websectiga=cf46039d1971c7b9a650d87269f31ac8fe3bf71d61ebf9d9a0a87efb414b816c; sec_poison_id=accf80f2-1df7-43fd-8187-351ca062b405; unread={%22ub%22:%22684cec01000000000f03ba8d%22%2C%22ue%22:%22684e4f2c00000000230012dd%22%2C%22uc%22:15}";

    const cookies = parseCookie(realCookie);
    console.log("📋 解析的Cookie数据:");
    console.log("  a1:", cookies.a1);
    console.log("  webId:", cookies.webId);
    console.log("  gid:", cookies.gid);
    console.log("  webBuild:", cookies.webBuild);

    // 设置真实数据
    setUserData({
        a1: cookies.a1,
        b1: cookies.webId, // 使用webId作为设备指纹
        b1b1: "1"
    });

    // 测试URL
    const testUrls = [
        "https://www.xiaohongshu.com/api/sns/web/v1/feed",
        "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post",
        "https://www.xiaohongshu.com/api/sec/v1/shield/webprofile"
    ];

    testUrls.forEach((url, index) => {
        console.log(`\n📝 测试 ${index + 1}: ${url}`);

        const xsCommon = generateXSCommon(url, {
            platform: "PC",
            xT: Date.now().toString(),
            xS: "test_xs_value"
        });

        if (xsCommon) {
            console.log(`✅ 生成成功: ${xsCommon.substring(0, 50)}...`);

            // 尝试解码验证
            try {
                const decoded = Buffer.from(xsCommon, 'base64').toString('utf-8');
                const parsed = JSON.parse(decoded);
                console.log(`📊 解码验证:`, {
                    s0: parsed.s0,
                    x1: parsed.x1,
                    x2: parsed.x2,
                    x3: parsed.x3,
                    x4: parsed.x4,
                    x10: parsed.x10,
                    x11: parsed.x11
                });
            } catch (e) {
                console.log(`❌ 解码失败: ${e.message}`);
            }
        } else {
            console.log(`❌ 生成失败`);
        }
    });

    console.log("\n🎯 测试完成！");
}

// ==================== 模块导出 ====================

if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        generateXSCommon,
        xsCommon,
        setUserData,
        mcr,
        b64Encode,
        encodeUtf8,
        test,

        // 常量
        NEED_XSCOMMON_URLS,
        NEED_REAL_TIME_XSCOMMON_URLS,
        BLOCKED_HOSTS,
        PlatformCode,
        version
    };
} else if (typeof window !== 'undefined') {
    // 浏览器环境
    window.XSCommonGenerator = {
        generateXSCommon,
        xsCommon,
        setUserData,
        mcr,
        b64Encode,
        encodeUtf8,
        test
    };
}

// ==================== 命令行测试 ====================

if (require.main === module) {
    // 如果直接运行此文件，执行测试
    test();

    // 命令行参数处理
    const args = process.argv.slice(2);
    if (args.length > 0) {
        const url = args[0];
        const platform = args[1] || "PC";

        console.log(`\n🚀 生成 x-s-common for: ${url}`);
        const result = generateXSCommon(url, { platform });

        if (result) {
            console.log(`✅ 结果: ${result}`);
        } else {
            console.log(`❌ 生成失败`);
        }
    }
}
