#!/usr/bin/env python3
"""
简单搜索工具
"""

import os
import re

def search_in_files(directory, pattern):
    """在文件中搜索模式"""
    results = []
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.js'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if re.search(pattern, content, re.IGNORECASE):
                            results.append(file_path)
                            print(f"✅ 找到匹配: {file}")
                except Exception as e:
                    print(f"❌ 读取文件出错 {file}: {e}")
    
    return results

def main():
    js_dir = os.path.join(os.getcwd(), "data", "js")
    
    print("🔍 搜索包含 xsCommon 的文件...")
    files_with_xscommon = search_in_files(js_dir, r'xsCommon')
    
    print(f"\n找到 {len(files_with_xscommon)} 个包含 xsCommon 的文件:")
    for file_path in files_with_xscommon:
        print(f"  📄 {file_path}")
    
    print("\n🔍 搜索包含 x-s-common 的文件...")
    files_with_header = search_in_files(js_dir, r'x-s-common')
    
    print(f"\n找到 {len(files_with_header)} 个包含 x-s-common 的文件:")
    for file_path in files_with_header:
        print(f"  📄 {file_path}")

if __name__ == '__main__':
    main()
