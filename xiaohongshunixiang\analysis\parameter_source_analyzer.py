#!/usr/bin/env python3
"""
参数来源分析器 - 专门分析 x1, x8, x10 参数的来源
"""

import re
import json

def analyze_getcurminiua_function(js_content):
    """分析 getCurMiniUa 函数"""
    print("🔍 分析 getCurMiniUa 函数...")
    
    patterns = [
        r'getCurMiniUa[^{]*\{[^}]*\}',
        r'function\s+getCurMiniUa[^{]*\{[^}]*\}',
        r'getCurMiniUa\s*[:=]\s*function[^{]*\{[^}]*\}',
        r'[a-zA-Z_$][a-zA-Z0-9_$]*\.getCurMiniUa[^{]*\{[^}]*\}',
    ]
    
    results = []
    for pattern in patterns:
        matches = re.finditer(pattern, js_content, re.IGNORECASE | re.DOTALL)
        for match in matches:
            impl = match.group(0)
            if len(impl) < 1000:  # 避免匹配过长的代码
                results.append({
                    'implementation': impl,
                    'position': match.start(),
                    'context': js_content[max(0, match.start()-200):match.end()+200]
                })
    
    return results

def analyze_variable_context(js_content, var_name, context_size=300):
    """分析变量的上下文"""
    print(f"🔍 分析变量 {var_name} 的上下文...")
    
    # 搜索变量赋值的上下文
    pattern = rf'{var_name}\s*[=:]\s*[^;,\n]+'
    matches = re.finditer(pattern, js_content, re.IGNORECASE)
    
    contexts = []
    for match in matches:
        start = max(0, match.start() - context_size)
        end = min(len(js_content), match.end() + context_size)
        context = js_content[start:end]
        
        contexts.append({
            'assignment': match.group(0),
            'position': match.start(),
            'context': context
        })
    
    return contexts

def find_function_calls_with_variables(js_content):
    """查找包含特定变量的函数调用"""
    print("🔍 查找相关函数调用...")
    
    # 要查找的函数和变量模式
    patterns = [
        r'getCurMiniUa\s*\([^)]*\)',
        r'getABInfo\s*\([^)]*\)',
        r'getUserToken\s*\([^)]*\)',
        r'getDeviceId\s*\([^)]*\)',
        r'getFingerprint\s*\([^)]*\)',
        r'generateId\s*\([^)]*\)',
        r'localStorage\.getItem\s*\([^)]*\)',
        r'sessionStorage\.getItem\s*\([^)]*\)',
        r'document\.cookie',
        r'navigator\.[a-zA-Z]+',
        r'window\.[a-zA-Z_$][a-zA-Z0-9_$]*',
    ]
    
    results = {}
    for pattern in patterns:
        matches = []
        for match in re.finditer(pattern, js_content, re.IGNORECASE):
            context_start = max(0, match.start() - 150)
            context_end = min(len(js_content), match.end() + 150)
            context = js_content[context_start:context_end]
            
            matches.append({
                'call': match.group(0),
                'position': match.start(),
                'context': context
            })
        
        if matches:
            results[pattern] = matches
    
    return results

def analyze_xs_common_generation_flow(js_content):
    """分析 X-S-Common 生成流程中的变量赋值"""
    print("🔍 分析 X-S-Common 生成流程...")
    
    # 查找包含 X-S-Common 生成的完整代码块
    pattern = r'.{500}X-S-Common.{500}'
    matches = re.finditer(pattern, js_content, re.IGNORECASE | re.DOTALL)
    
    generation_flows = []
    for match in matches:
        flow = match.group(0)
        
        # 在这个流程中查找变量赋值
        variables = ['x1', 'x8', 'x10', 'C', 'p', 'd', 'f']
        found_vars = {}
        
        for var in variables:
            var_pattern = rf'{var}\s*[=:]\s*[^;,\n]+'
            var_matches = re.findall(var_pattern, flow, re.IGNORECASE)
            if var_matches:
                found_vars[var] = var_matches
        
        if found_vars:
            generation_flows.append({
                'flow': flow,
                'position': match.start(),
                'variables': found_vars
            })
    
    return generation_flows

def search_device_fingerprint_functions(js_content):
    """搜索设备指纹相关函数"""
    print("🔍 搜索设备指纹相关函数...")
    
    fingerprint_keywords = [
        'fingerprint', 'deviceId', 'clientId', 'sessionId', 'userId',
        'browser', 'platform', 'userAgent', 'screen', 'timezone',
        'language', 'plugins', 'canvas', 'webgl', 'audio'
    ]
    
    results = {}
    for keyword in fingerprint_keywords:
        pattern = rf'[a-zA-Z_$][a-zA-Z0-9_$]*[^a-zA-Z0-9_$]*{keyword}[^a-zA-Z0-9_$]*[^{{]*\{{[^}}]*\}}'
        matches = re.finditer(pattern, js_content, re.IGNORECASE | re.DOTALL)
        
        found_functions = []
        for match in matches:
            func = match.group(0)
            if len(func) < 500:  # 避免匹配过长的代码
                found_functions.append({
                    'function': func,
                    'position': match.start(),
                    'keyword': keyword
                })
        
        if found_functions:
            results[keyword] = found_functions
    
    return results

def main():
    """主函数"""
    input_file = "xiaohongshunixiang/data/vendor-dynamic.f0f5c43a.js"
    
    print("🔍 深度分析 x1, x8, x10 参数来源...")
    print("=" * 60)
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            js_content = f.read()
            
        print(f"📄 文件大小: {len(js_content)} 字符")
        
        # 1. 分析 getCurMiniUa 函数
        print(f"\n1️⃣ 分析 getCurMiniUa 函数:")
        getcurminiua_results = analyze_getcurminiua_function(js_content)
        
        # 2. 分析变量上下文
        print(f"\n2️⃣ 分析关键变量上下文:")
        variable_contexts = {}
        for var in ['x1', 'x8', 'x10', 'C', 'p', 'd', 'f']:
            variable_contexts[var] = analyze_variable_context(js_content, var)
        
        # 3. 查找相关函数调用
        print(f"\n3️⃣ 查找相关函数调用:")
        function_calls = find_function_calls_with_variables(js_content)
        
        # 4. 分析 X-S-Common 生成流程
        print(f"\n4️⃣ 分析 X-S-Common 生成流程:")
        generation_flows = analyze_xs_common_generation_flow(js_content)
        
        # 5. 搜索设备指纹函数
        print(f"\n5️⃣ 搜索设备指纹相关函数:")
        fingerprint_functions = search_device_fingerprint_functions(js_content)
        
        # 保存结果
        results = {
            'getcurminiua_functions': getcurminiua_results,
            'variable_contexts': variable_contexts,
            'function_calls': function_calls,
            'generation_flows': generation_flows,
            'fingerprint_functions': fingerprint_functions
        }
        
        output_file = "xiaohongshunixiang/output/parameter_source_analysis.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
            
        print(f"\n💾 详细分析结果已保存到: {output_file}")
        
        # 显示摘要
        print(f"\n📊 分析摘要:")
        print(f"   getCurMiniUa 函数: {len(getcurminiua_results)} 个")
        print(f"   变量上下文: {sum(len(contexts) for contexts in variable_contexts.values())} 个")
        print(f"   函数调用: {sum(len(calls) for calls in function_calls.values())} 个")
        print(f"   生成流程: {len(generation_flows)} 个")
        print(f"   指纹函数: {sum(len(funcs) for funcs in fingerprint_functions.values())} 个")
        
        # 显示关键发现
        if getcurminiua_results:
            print(f"\n🔧 getCurMiniUa 函数发现:")
            for i, result in enumerate(getcurminiua_results[:3]):
                impl = result['implementation'][:100].replace('\n', ' ')
                print(f"   {i+1}. {impl}...")
                
        if generation_flows:
            print(f"\n📋 X-S-Common 生成流程中的变量:")
            for i, flow in enumerate(generation_flows[:2]):
                print(f"   流程 {i+1}:")
                for var, assignments in flow['variables'].items():
                    print(f"      {var}: {assignments[:2]}")  # 只显示前2个赋值
                    
    except Exception as e:
        print(f"❌ 分析失败: {e}")

if __name__ == '__main__':
    main()
