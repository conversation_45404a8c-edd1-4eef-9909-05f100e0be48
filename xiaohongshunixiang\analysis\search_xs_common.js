// 🔍 专门搜索 X-S-Common 相关代码

(() => {
    console.log('🚀 开始搜索 X-S-Common 相关代码...');
    
    const results = {
        functions: [],
        variables: [],
        strings: [],
        objects: [],
        scripts: [],
        timestamp: Date.now()
    };
    
    // 所有可能的 X-S-Common 变体
    const xsCommonVariants = [
        'X-S-Common',
        'x-s-common', 
        'X-s-common',
        'x-S-Common',
        'X-S-COMMON',
        'x-s-COMMON',
        'XSCommon',
        'xsCommon',
        'xs_common',
        'XS_COMMON',
        'X_S_Common',
        'x_s_common'
    ];
    
    // 1. 搜索全局对象中的函数和变量
    const searchGlobalScope = () => {
        console.log('🔍 搜索全局作用域...');
        
        for (let key in window) {
            try {
                const obj = window[key];
                
                // 检查变量名是否包含相关关键词
                const keyLower = key.toLowerCase();
                if (keyLower.includes('sign') || 
                    keyLower.includes('common') || 
                    keyLower.includes('header') ||
                    keyLower.includes('xs') ||
                    key.length <= 3) { // 短变量名可能是混淆后的
                    
                    if (typeof obj === 'function') {
                        const funcStr = obj.toString();
                        
                        // 检查函数内容是否包含 X-S-Common 变体
                        const hasXSCommon = xsCommonVariants.some(variant => 
                            funcStr.includes(variant)
                        );
                        
                        if (hasXSCommon) {
                            results.functions.push({
                                name: key,
                                source: funcStr,
                                type: 'contains_xs_common'
                            });
                            console.log(`✅ 找到包含 X-S-Common 的函数: ${key}`);
                        }
                    } else if (typeof obj === 'object' && obj !== null) {
                        // 检查对象属性
                        try {
                            const objStr = JSON.stringify(obj);
                            const hasXSCommon = xsCommonVariants.some(variant => 
                                objStr.includes(variant)
                            );
                            
                            if (hasXSCommon) {
                                results.objects.push({
                                    name: key,
                                    content: obj,
                                    type: 'contains_xs_common'
                                });
                                console.log(`✅ 找到包含 X-S-Common 的对象: ${key}`);
                            }
                        } catch (e) {}
                    } else if (typeof obj === 'string') {
                        const hasXSCommon = xsCommonVariants.some(variant => 
                            obj.includes(variant)
                        );
                        
                        if (hasXSCommon) {
                            results.strings.push({
                                name: key,
                                value: obj,
                                type: 'contains_xs_common'
                            });
                            console.log(`✅ 找到包含 X-S-Common 的字符串: ${key}`);
                        }
                    }
                }
            } catch (e) {}
        }
    };
    
    // 2. 搜索所有脚本内容
    const searchScripts = () => {
        console.log('🔍 搜索脚本内容...');
        
        const scripts = document.querySelectorAll('script');
        
        scripts.forEach((script, index) => {
            if (script.textContent) {
                const content = script.textContent;
                
                // 检查是否包含 X-S-Common 变体
                xsCommonVariants.forEach(variant => {
                    if (content.includes(variant)) {
                        console.log(`✅ 脚本 ${index} 包含: ${variant}`);
                        
                        // 提取包含该变体的代码行
                        const lines = content.split('\n');
                        const relevantLines = [];
                        
                        lines.forEach((line, lineIndex) => {
                            if (line.includes(variant)) {
                                // 获取上下文（前后5行）
                                const start = Math.max(0, lineIndex - 5);
                                const end = Math.min(lines.length, lineIndex + 6);
                                
                                relevantLines.push({
                                    lineNumber: lineIndex + 1,
                                    context: lines.slice(start, end).join('\n'),
                                    matchingLine: line.trim()
                                });
                            }
                        });
                        
                        results.scripts.push({
                            scriptIndex: index,
                            variant: variant,
                            relevantLines: relevantLines,
                            scriptSrc: script.src || 'inline'
                        });
                    }
                });
            }
        });
    };
    
    // 3. 搜索网络请求中的 headers
    const hookNetworkRequests = () => {
        console.log('🔍 Hook 网络请求...');
        
        // Hook fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            if (options.headers) {
                for (let [key, value] of Object.entries(options.headers)) {
                    if (xsCommonVariants.some(variant => 
                        key.toLowerCase().includes(variant.toLowerCase())
                    )) {
                        console.log(`🔑 网络请求中发现 ${key}: ${value}`);
                        
                        results.networkHeaders = results.networkHeaders || [];
                        results.networkHeaders.push({
                            url: url,
                            headerName: key,
                            headerValue: value,
                            timestamp: Date.now()
                        });
                        
                        // 尝试获取调用栈
                        try {
                            throw new Error('Stack trace');
                        } catch (e) {
                            console.log('📍 调用栈:', e.stack);
                        }
                    }
                }
            }
            
            return originalFetch.apply(this, arguments);
        };
        
        // Hook XMLHttpRequest
        const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
        XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
            if (xsCommonVariants.some(variant => 
                name.toLowerCase().includes(variant.toLowerCase())
            )) {
                console.log(`🔑 XMLHttpRequest 中发现 ${name}: ${value}`);
                
                results.networkHeaders = results.networkHeaders || [];
                results.networkHeaders.push({
                    type: 'XMLHttpRequest',
                    headerName: name,
                    headerValue: value,
                    timestamp: Date.now()
                });
            }
            
            return originalSetRequestHeader.call(this, name, value);
        };
    };
    
    // 4. 执行搜索
    searchGlobalScope();
    searchScripts();
    hookNetworkRequests();
    
    // 5. 输出结果
    console.log('📊 搜索结果统计:');
    console.log(`- 函数: ${results.functions.length}`);
    console.log(`- 对象: ${results.objects.length}`);
    console.log(`- 字符串: ${results.strings.length}`);
    console.log(`- 脚本: ${results.scripts.length}`);
    
    // 保存到全局变量
    window.xsCommonSearchResults = results;
    
    console.log('💾 搜索结果已保存到 window.xsCommonSearchResults');
    console.log('📋 使用 copy(window.xsCommonSearchResults) 复制结果');
    
    // 如果找到了相关内容，立即显示
    if (results.functions.length > 0) {
        console.log('🎯 找到的相关函数:');
        results.functions.forEach((func, index) => {
            console.log(`${index + 1}. ${func.name}:`);
            console.log(func.source.substring(0, 500) + '...');
        });
    }
    
    if (results.scripts.length > 0) {
        console.log('🎯 找到的相关脚本代码:');
        results.scripts.forEach((script, index) => {
            console.log(`${index + 1}. 脚本 ${script.scriptIndex} 包含 ${script.variant}:`);
            script.relevantLines.forEach(line => {
                console.log(`   行 ${line.lineNumber}: ${line.matchingLine}`);
            });
        });
    }
    
    return {
        message: 'X-S-Common 搜索完成',
        functionsFound: results.functions.length,
        scriptsFound: results.scripts.length,
        objectsFound: results.objects.length,
        stringsFound: results.strings.length
    };
})();
