X-S-Common 请求头生成函数提取分析报告
============================================================
分析时间: 2025-06-15 23:02:52

🔍 x-s-common 相关代码片段:
----------------------------------------

1. 文件: index.788b3226.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function clickHeader(){
    b.value=!1
}

================================================================================

2. 文件: index.788b3226.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function getHeaderPageWithInstanceId(e){
    return{
    type:"Page",
    value:{
    pageInstance:{
    type:"PageInstance",
    value:"web_header_page"
}
,
    instanceId:e
}

}

}

================================================================================

3. 文件: index.788b3226.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function headerPoint(e,
    t,
    n){
    return _headerPoint.apply(this,
    arguments)
}

================================================================================

4. 文件: index.788b3226.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function _headerPoint(){
    return(_headerPoint=(0,
    r._)(function(e,
    t,
    n){
    var r;
return(0,
    o.Jh)(this,
    function(o){
    switch(o.label){
    case 0:return[4,
    getTrackData(e,
    t)];
case 1:return r=o.sent(),
    n&&n.length>0?r.page=getHeaderPageWithInstanceId(n):r.page=p,
    a.Q.push(r),
    [2]
}

}
)
}
)).apply(this,
    arguments)
}

================================================================================

5. 文件: index.788b3226_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function clickHeader(){
    b.value=!1
}

================================================================================

6. 文件: index.788b3226_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function getHeaderPageWithInstanceId(e){
    return{
    type:"Page",
    value:{
    pageInstance:{
    type:"PageInstance",
    value:"web_header_page"
}
,
    instanceId:e
}

}

}

================================================================================

7. 文件: index.788b3226_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function headerPoint(e,
    t,
    n){
    return _headerPoint.apply(this,
    arguments)
}

================================================================================

8. 文件: index.788b3226_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function _headerPoint(){
    return(_headerPoint=(0,
    r._)(function(e,
    t,
    n){
    var r;
return(0,
    o.Jh)(this,
    function(o){
    switch(o.label){
    case 0:return[4,
    getTrackData(e,
    t)];
case 1:return r=o.sent(),
    n&&n.length>0?r.page=getHeaderPageWithInstanceId(n):r.page=p,
    a.Q.push(r),
    [2]
}

}
)
}
)).apply(this,
    arguments)
}

================================================================================

9. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=merge(e[r],
    t):e[r]=t
}

================================================================================

10. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge(e[r],
    t):(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge({
    
}
,
    t):e[r]=t
}

================================================================================

11. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=o(t,
    r):e[n]=t
}

================================================================================

12. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    isPlainObject(e[r])&&isPlainObject(t)?e[r]=merge(e[r],
    t):isPlainObject(t)?e[r]=merge({
    
}
,
    t):isArray(t)?e[r]=t.slice():e[r]=t
}

================================================================================

13. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=a(t,
    r):e[n]=t
}

================================================================================

14. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function setRequestHeader(e,
    t){
    void 0===l&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,
    e)
}

================================================================================

15. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function cleanHeaderConfig(t){
    delete e.headers[t]
}

================================================================================

16. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function normalizeHeaderName(e,
    t){
    n.forEach(e,
    function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}
)
}

================================================================================

17. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}

================================================================================

18. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.substr(0,
    i)).toLowerCase(),
    r=n.trim(e.substr(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}

================================================================================

19. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function setRequestHeader(e,
    t){
    void 0===v&&"content-type"===t.toLowerCase()?delete g[t]:E.setRequestHeader(t,
    e)
}

================================================================================

20. 文件: library-axios.435de88b.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.slice(0,
    i)).toLowerCase(),
    r=n.trim(e.slice(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}

================================================================================

21. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=merge(e[r],
    t):e[r]=t
}

================================================================================

22. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge(e[r],
    t):(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge({
    
}
,
    t):e[r]=t
}

================================================================================

23. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=o(t,
    r):e[n]=t
}

================================================================================

24. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    r){
    isPlainObject(e[r])&&isPlainObject(t)?e[r]=merge(e[r],
    t):isPlainObject(t)?e[r]=merge({
    
}
,
    t):isArray(t)?e[r]=t.slice():e[r]=t
}

================================================================================

25. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=a(t,
    r):e[n]=t
}

================================================================================

26. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function setRequestHeader(e,
    t){
    void 0===l&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,
    e)
}

================================================================================

27. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function cleanHeaderConfig(t){
    delete e.headers[t]
}

================================================================================

28. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function normalizeHeaderName(e,
    t){
    n.forEach(e,
    function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}
)
}

================================================================================

29. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}

================================================================================

30. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.substr(0,
    i)).toLowerCase(),
    r=n.trim(e.substr(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}

================================================================================

31. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function setRequestHeader(e,
    t){
    void 0===v&&"content-type"===t.toLowerCase()?delete g[t]:E.setRequestHeader(t,
    e)
}

================================================================================

32. 文件: library-axios.435de88b_1.js (行 1)
匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
函数代码:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.slice(0,
    i)).toLowerCase(),
    r=n.trim(e.slice(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}

================================================================================

33. 文件: library-polyfill.5f7e25b2.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}

================================================================================

34. 文件: library-polyfill.5f7e25b2.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}
return e
}

================================================================================

35. 文件: library-polyfill.5f7e25b2_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}

================================================================================

36. 文件: library-polyfill.5f7e25b2_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}
return e
}

================================================================================

37. 文件: library-vue.a552caa8.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonEncode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}

================================================================================

38. 文件: library-vue.a552caa8_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonEncode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}

================================================================================

39. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
匹配代码: headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var...
================================================================================

40. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
匹配代码: null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(e...
================================================================================

41. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: x-s-common[^=]*=\s*[^;]+
匹配代码: X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDevic...
================================================================================

42. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(e,
    r){
    "object"===(0,
    d._)(a[r])&&(void 0===e?"undefined":(0,
    d._)(e))==="object"?a[r]=merge(a[r],
    e):a[r]=e
}

================================================================================

43. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function encrypt_sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}
return r
}
,
    a="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    s="iamspam",
    u=new Date().getTime(),
    c="undefined"==typeof window?i.g:window;
return void 0!==c&&c&&c.navigator&&c.navigator.userAgent&&c.alert&&(s="test"),
    {
    "X-s":function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}
(N([u,
    s,
    e,
    "[object Object]"===Object.prototype.toString.call(r)||"[object Array]"===Object.prototype.toString.call(r)?JSON.stringify(r):""].join(""))),
    "X-t":u
}

}

================================================================================

44. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function utils_shouldSign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}
),
    r)
}

================================================================================

45. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function SignReload(){
    this.count=1,
    this.time=+new Date
}

================================================================================

46. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shouldSignReload(){
    try{
    var e=+new Date,
    r=JSON.stringify(localStorage.getItem(T)||{
    
}
),
    i=!!(r&&r.count),
    a=r&&r.time&&e-r.time<36e5;
if(!(i&&a)){
    var s=new utils_SignReload;
return localStorage.setItem(T,
    JSON.stringify(s)),
    !0
}
if(r.count>3)return!1;
return r.count=r.count+1,
    localStorage.setItem(T,
    JSON.stringify(r)),
    !0
}
catch(e){
    return!1
}

}

================================================================================

47. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function updateSign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(M,
    {
    callFrom:a,
    callback:u
}
,
    2,
    60)];
case 2:return c=(d=v.sent()).secPoisonId,
    x(d.data),
    delete window[u],
    updateTokenTs(),
    [3,
    4];
case 3:return v.sent(),
    [3,
    4];
case 4:return setTimeout(function(){
    return updateSign(e)
}
,
    5*r),
    [3,
    6];
case 5:setTimeout(function(){
    return updateSign(e)
}
,
    r/12),
    v.label=6;
case 6:return[2]
}

}
)
}
)
}

================================================================================

48. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signAdaptor(e,
    r){
    var i;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    function(){
    var a,
    s;
return signAdaptor_generator(this,
    function(u){
    return a=Date.now(),
    "function"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,
    r):xhsSign(e,
    r),
    "function"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,
    r):xsCommon(e,
    r),
    "function"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,
    r):xhsToken(e,
    r),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-a,
    type:(null==window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href
}

}
),
    [2,
    r]
}
)
}
)
}

================================================================================

49. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function xhsSign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}
)&&utils_shouldSign(i)))return r;
d&&signLackReload();
try{
    var p=getRealUrl(i,
    a,
    s),
    f=encrypt_sign;
c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);
var v=f(p,
    u)||{
    
}
;
r.headers["X-t"]=v["X-t"],
    r.headers["X-s"]=v["X-s"]
}
catch(e){
    
}
if(!0!==e.disableMns)try{
    if(window.mns){
    var p=getRealUrl(i,
    a,
    s),
    h="[object Object]"===Object.prototype.toString.call(u)||"[object Array]"===Object.prototype.toString.call(u),
    g=N([p,
    h?JSON.stringify(u):""].join(""));
r.headers["X-Mns"]=window.mns.getMnsToken(p,
    u,
    g)||""
}
else r.headers["X-Mns"]="unload"
}
catch(e){
    r.headers["X-Mns"]="error"
}
return r
}

================================================================================

50. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signLackReload(e){
    if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    Error("网络连接不可用，请刷新重试。")
}

================================================================================

51. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function isIgnoreErrors(e){
    var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],
    i="".concat(null==e?void 0:e.errorType,
    ": ").concat(null==e?void 0:e.errorMessage);
return r.includes(i)
}

================================================================================

52. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonConvert(e,
    r){
    return isConstWithNumber(e)?e:e.replace(u,
    (e,
    i,
    a,
    s)=>{
    let u=r(a);
return`${
    i
}
${
    u
}
${
    s
}
`
}
)
}

================================================================================

53. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}
)
}
catch(e){
    
}

}

================================================================================

54. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushRealTimeXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}
)
}
catch(e){
    
}

}

================================================================================

55. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xsCommon(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
),
    !utils_shouldSign(u))return r;
var c=r.headers["X-Sign"]||"",
    d=getSigCount(c),
    p=localStorage.getItem("b1"),
    f=localStorage.getItem("b1b1")||"1",
    v={
    s0:getPlatformCode(s),
    s1:"",
    x0:f,
    x1:C,
    x2:s||"PC",
    x3:"xhs-pc-web",
    x4:"4.68.0",
    x5:l.Z.get("a1"),
    x6:"",
    x7:"",
    x8:p,
    x9:O("".concat("").concat("").concat(p)),
    x10:d,
    x11:"normal"
}
,
    h=k.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
);
(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){
    v.x8=e,
    v.x9=O("".concat("").concat("").concat(e)),
    r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
catch(e){
    
}
return r
}

================================================================================

56. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xCommonParams(e,
    r){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    function(){
    var i,
    a,
    s,
    u,
    l,
    d,
    p,
    f,
    v,
    h,
    g,
    m,
    _,
    y,
    w,
    E,
    T,
    S;
return xCommonParams_generator(this,
    function(S){
    switch(S.label){
    case 0:if(i=e.platform,
    a=e.includes,
    s=e.carryDeviceInfo,
    u=a.some(function(e){
    return r.url.includes(e)
}
),
    !(c.YF.isXHS&&s&&u))return[2,
    r];
l=Date.now(),
    p=(d=(0,
    c.Vk)()).major,
    f=d.minor,
    v=d.patch,
    h=getDeviceInfo(),
    (g=new URLSearchParams).append("platform",
    i),
    g.append("versionName",
    "".concat(p,
    ".").concat(f,
    ".").concat(v)),
    S.label=1;
case 1:if(S.trys.push([1,
    4,
    ,
    5]),
    g.has("deviceId"))return[3,
    3];
return[4,
    h];
case 2:_=(m=S.sent()).deviceId,
    y=m.uniqueId,
    w=m.deviceFingerprint,
    E=m.deviceFingerprint1,
    T=m.fid,
    g.append("deviceId",
    _||y),
    g.append("device_fingerprint",
    w),
    g.append("device_fingerprint1",
    E),
    g.append("fid",
    T),
    S.label=3;
case 3:return[3,
    5];
case 4:return S.sent(),
    [3,
    5];
case 5:return r.headers["xy-common-params"]=g.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-l,
    type:"xCommonParams",
    source:null==r?void 0:r.url
}

}
),
    [2,
    r]
}

}
)
}
)
}

================================================================================

57. 文件: vendor-dynamic.f0f5c43a.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonChecks(e){
    var r,
    i,
    u=e.navigator,
    c=e.location,
    p=u.userAgent,
    f=os_getOS(p),
    v=browser_getBrowser(p),
    h=getMiniprogramType(p,
    e),
    g=iphoneXCheck(f),
    m=query_extractFromQuery(c.search),
    _="iOS"===f,
    y="Android"===f,
    w="Harmony"===f,
    E="HarmonyArk"===f,
    T=!!window.xhsbridge||!!window.XHSBridge,
    S=!!(null===(i=window.webkit)||void 0===i?void 0:null===(r=i.messageHandlers)||void 0===r?void 0:r.getDeviceInfo),
    b=!!window.XHSBridge,
    k=T||S||"discover"===v||m.isXHS||b,
    C="redtop"===v,
    P=(p||"").toLowerCase().indexOf("mobile")>-1,
    A=browser_getBuildNumber(p),
    R=!k&&!y&&!_&&!P&&!w&&!E,
    I=!1;
if(k||C){
    var O=browser_getBrowserVersion(p);
I=(O.major>6||6===O.major&&O.minor>=7)&&m.isFullscreen||_&&m.isNaviHidden||C&&m.isFullscreen
}
return(0,
    s._)((0,
    a._)({
    
}
,
    d),
    {
    isIOS:_,
    isAndroid:y,
    isHarmony:w,
    isHarmonyArk:E,
    isXHS:k,
    isFullscreen:I,
    isWeixin:"micromessenger"===v,
    isAlipay:"alipay"===v,
    isWeibo:"weibo"===v,
    isQQ:"qq"===v,
    isQQBrowser:"mqqbrowser"===v,
    isMiniprogram:h===l.weixin||m.isMiniprogram,
    isBaiduMiniprogram:h===l.baidu,
    isQQMiniprogram:h===l.qq,
    isAlipayMiniprogram:h===l.alipay,
    isToutiaoMiniprogram:h===l.toutiao,
    isIphone14Pro:iphone14ProCheck(f),
    isIphoneX:g.isIphoneX,
    iphoneXType:g.iphoneXType,
    isTop:C,
    isUniik:"uniik"===v,
    isSpark:"spark"===v,
    isXhsMerchant:"merchant"===v,
    isSnowPeak:"snowpeak"===v,
    isInternation:"internation"===v,
    isCatalog:"catalog"===v,
    isOdyssey:"odyssey"===v,
    isPC:R,
    isMobile:P,
    buildNumber:A
}
)
}

================================================================================

58. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
匹配代码: headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var...
================================================================================

59. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
匹配代码: null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(e...
================================================================================

60. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: x-s-common[^=]*=\s*[^;]+
匹配代码: X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
上下文:
=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDevic...
================================================================================

61. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(e,
    r){
    "object"===(0,
    d._)(a[r])&&(void 0===e?"undefined":(0,
    d._)(e))==="object"?a[r]=merge(a[r],
    e):a[r]=e
}

================================================================================

62. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function encrypt_sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}
return r
}
,
    a="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    s="iamspam",
    u=new Date().getTime(),
    c="undefined"==typeof window?i.g:window;
return void 0!==c&&c&&c.navigator&&c.navigator.userAgent&&c.alert&&(s="test"),
    {
    "X-s":function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}
(N([u,
    s,
    e,
    "[object Object]"===Object.prototype.toString.call(r)||"[object Array]"===Object.prototype.toString.call(r)?JSON.stringify(r):""].join(""))),
    "X-t":u
}

}

================================================================================

63. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function utils_shouldSign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}
),
    r)
}

================================================================================

64. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function SignReload(){
    this.count=1,
    this.time=+new Date
}

================================================================================

65. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shouldSignReload(){
    try{
    var e=+new Date,
    r=JSON.stringify(localStorage.getItem(T)||{
    
}
),
    i=!!(r&&r.count),
    a=r&&r.time&&e-r.time<36e5;
if(!(i&&a)){
    var s=new utils_SignReload;
return localStorage.setItem(T,
    JSON.stringify(s)),
    !0
}
if(r.count>3)return!1;
return r.count=r.count+1,
    localStorage.setItem(T,
    JSON.stringify(r)),
    !0
}
catch(e){
    return!1
}

}

================================================================================

66. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function updateSign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(M,
    {
    callFrom:a,
    callback:u
}
,
    2,
    60)];
case 2:return c=(d=v.sent()).secPoisonId,
    x(d.data),
    delete window[u],
    updateTokenTs(),
    [3,
    4];
case 3:return v.sent(),
    [3,
    4];
case 4:return setTimeout(function(){
    return updateSign(e)
}
,
    5*r),
    [3,
    6];
case 5:setTimeout(function(){
    return updateSign(e)
}
,
    r/12),
    v.label=6;
case 6:return[2]
}

}
)
}
)
}

================================================================================

67. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signAdaptor(e,
    r){
    var i;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    function(){
    var a,
    s;
return signAdaptor_generator(this,
    function(u){
    return a=Date.now(),
    "function"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,
    r):xhsSign(e,
    r),
    "function"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,
    r):xsCommon(e,
    r),
    "function"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,
    r):xhsToken(e,
    r),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-a,
    type:(null==window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href
}

}
),
    [2,
    r]
}
)
}
)
}

================================================================================

68. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function xhsSign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}
)&&utils_shouldSign(i)))return r;
d&&signLackReload();
try{
    var p=getRealUrl(i,
    a,
    s),
    f=encrypt_sign;
c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);
var v=f(p,
    u)||{
    
}
;
r.headers["X-t"]=v["X-t"],
    r.headers["X-s"]=v["X-s"]
}
catch(e){
    
}
if(!0!==e.disableMns)try{
    if(window.mns){
    var p=getRealUrl(i,
    a,
    s),
    h="[object Object]"===Object.prototype.toString.call(u)||"[object Array]"===Object.prototype.toString.call(u),
    g=N([p,
    h?JSON.stringify(u):""].join(""));
r.headers["X-Mns"]=window.mns.getMnsToken(p,
    u,
    g)||""
}
else r.headers["X-Mns"]="unload"
}
catch(e){
    r.headers["X-Mns"]="error"
}
return r
}

================================================================================

69. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signLackReload(e){
    if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    Error("网络连接不可用，请刷新重试。")
}

================================================================================

70. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function isIgnoreErrors(e){
    var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],
    i="".concat(null==e?void 0:e.errorType,
    ": ").concat(null==e?void 0:e.errorMessage);
return r.includes(i)
}

================================================================================

71. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonConvert(e,
    r){
    return isConstWithNumber(e)?e:e.replace(u,
    (e,
    i,
    a,
    s)=>{
    let u=r(a);
return`${
    i
}
${
    u
}
${
    s
}
`
}
)
}

================================================================================

72. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}
)
}
catch(e){
    
}

}

================================================================================

73. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushRealTimeXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}
)
}
catch(e){
    
}

}

================================================================================

74. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xsCommon(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
),
    !utils_shouldSign(u))return r;
var c=r.headers["X-Sign"]||"",
    d=getSigCount(c),
    p=localStorage.getItem("b1"),
    f=localStorage.getItem("b1b1")||"1",
    v={
    s0:getPlatformCode(s),
    s1:"",
    x0:f,
    x1:C,
    x2:s||"PC",
    x3:"xhs-pc-web",
    x4:"4.68.0",
    x5:l.Z.get("a1"),
    x6:"",
    x7:"",
    x8:p,
    x9:O("".concat("").concat("").concat(p)),
    x10:d,
    x11:"normal"
}
,
    h=k.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
);
(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){
    v.x8=e,
    v.x9=O("".concat("").concat("").concat(e)),
    r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
catch(e){
    
}
return r
}

================================================================================

75. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xCommonParams(e,
    r){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    function(){
    var i,
    a,
    s,
    u,
    l,
    d,
    p,
    f,
    v,
    h,
    g,
    m,
    _,
    y,
    w,
    E,
    T,
    S;
return xCommonParams_generator(this,
    function(S){
    switch(S.label){
    case 0:if(i=e.platform,
    a=e.includes,
    s=e.carryDeviceInfo,
    u=a.some(function(e){
    return r.url.includes(e)
}
),
    !(c.YF.isXHS&&s&&u))return[2,
    r];
l=Date.now(),
    p=(d=(0,
    c.Vk)()).major,
    f=d.minor,
    v=d.patch,
    h=getDeviceInfo(),
    (g=new URLSearchParams).append("platform",
    i),
    g.append("versionName",
    "".concat(p,
    ".").concat(f,
    ".").concat(v)),
    S.label=1;
case 1:if(S.trys.push([1,
    4,
    ,
    5]),
    g.has("deviceId"))return[3,
    3];
return[4,
    h];
case 2:_=(m=S.sent()).deviceId,
    y=m.uniqueId,
    w=m.deviceFingerprint,
    E=m.deviceFingerprint1,
    T=m.fid,
    g.append("deviceId",
    _||y),
    g.append("device_fingerprint",
    w),
    g.append("device_fingerprint1",
    E),
    g.append("fid",
    T),
    S.label=3;
case 3:return[3,
    5];
case 4:return S.sent(),
    [3,
    5];
case 5:return r.headers["xy-common-params"]=g.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-l,
    type:"xCommonParams",
    source:null==r?void 0:r.url
}

}
),
    [2,
    r]
}

}
)
}
)
}

================================================================================

76. 文件: vendor-dynamic.f0f5c43a_1.js (行 1)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function commonChecks(e){
    var r,
    i,
    u=e.navigator,
    c=e.location,
    p=u.userAgent,
    f=os_getOS(p),
    v=browser_getBrowser(p),
    h=getMiniprogramType(p,
    e),
    g=iphoneXCheck(f),
    m=query_extractFromQuery(c.search),
    _="iOS"===f,
    y="Android"===f,
    w="Harmony"===f,
    E="HarmonyArk"===f,
    T=!!window.xhsbridge||!!window.XHSBridge,
    S=!!(null===(i=window.webkit)||void 0===i?void 0:null===(r=i.messageHandlers)||void 0===r?void 0:r.getDeviceInfo),
    b=!!window.XHSBridge,
    k=T||S||"discover"===v||m.isXHS||b,
    C="redtop"===v,
    P=(p||"").toLowerCase().indexOf("mobile")>-1,
    A=browser_getBuildNumber(p),
    R=!k&&!y&&!_&&!P&&!w&&!E,
    I=!1;
if(k||C){
    var O=browser_getBrowserVersion(p);
I=(O.major>6||6===O.major&&O.minor>=7)&&m.isFullscreen||_&&m.isNaviHidden||C&&m.isFullscreen
}
return(0,
    s._)((0,
    a._)({
    
}
,
    d),
    {
    isIOS:_,
    isAndroid:y,
    isHarmony:w,
    isHarmonyArk:E,
    isXHS:k,
    isFullscreen:I,
    isWeixin:"micromessenger"===v,
    isAlipay:"alipay"===v,
    isWeibo:"weibo"===v,
    isQQ:"qq"===v,
    isQQBrowser:"mqqbrowser"===v,
    isMiniprogram:h===l.weixin||m.isMiniprogram,
    isBaiduMiniprogram:h===l.baidu,
    isQQMiniprogram:h===l.qq,
    isAlipayMiniprogram:h===l.alipay,
    isToutiaoMiniprogram:h===l.toutiao,
    isIphone14Pro:iphone14ProCheck(f),
    isIphoneX:g.isIphoneX,
    iphoneXType:g.iphoneXType,
    isTop:C,
    isUniik:"uniik"===v,
    isSpark:"spark"===v,
    isXhsMerchant:"merchant"===v,
    isSnowPeak:"snowpeak"===v,
    isInternation:"internation"===v,
    isCatalog:"catalog"===v,
    isOdyssey:"odyssey"===v,
    isPC:R,
    isMobile:P,
    buildNumber:A
}
)
}

================================================================================

77. 文件: vendor-main.e645eae.js (行 2)
匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
匹配代码: headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
id 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toStr...
================================================================================

78. 文件: vendor-main.e645eae.js (行 2)
匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
匹配代码: x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
call(g,(function(t){return t.test(u)}));(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorag...
================================================================================

79. 文件: vendor-main.e645eae.js (行 2)
匹配模式: x-s-common[^=]*=\s*[^;]+
匹配代码: X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
tCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e...
================================================================================

80. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function encrypt_sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}
return e
}
(t);
l<t.length;
)i=(e=t.charCodeAt(l++))>>2,
    a=(3&e)<<4|(r=t.charCodeAt(l++))>>4,
    u=(15&r)<<2|(o=t.charCodeAt(l++))>>6,
    c=63&o,
    isNaN(r)?u=c=64:isNaN(o)&&(c=64),
    s=s+n.charAt(i)+n.charAt(a)+n.charAt(u)+n.charAt(c);
return s
}
(MD5([o,
    r,
    t,
    a?stringify_default()(e):""].join(""))),
    "X-t":o
}

}

================================================================================

81. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function utils_shouldSign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}
)),
    e
}

================================================================================

82. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shouldSignReload(){
    try{
    var t=+new Date,
    e=stringify_default()(localStorage.getItem(signLackInfo)||{
    
}
),
    n=!(!e||!e.count),
    r=e&&e.time&&t-e.time<timeGap;
if(!(n&&r)){
    var o=new SignReload;
return localStorage.setItem(signLackInfo,
    stringify_default()(o)),
    !0
}
return!(e.count>maxReloadTime)&&(e.count=e.count+1,
    localStorage.setItem(signLackInfo,
    stringify_default()(e)),
    !0)
}
catch(i){
    return!1
}

}

================================================================================

83. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function updateSign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(wraperScript,
    {
    callFrom:r,
    callback:i
}
,
    2,
    60)];
case 2:return u=c.sent(),
    a=u.secPoisonId,
    wrapperEval(u.data),
    delete window[i],
    updateTokenTs(),
    [3,
    4];
case 3:return c.sent(),
    [3,
    4];
case 4:return set_timeout_default()((function(){
    return updateSign(t)
}
),
    5*e),
    [3,
    6];
case 5:set_timeout_default()((function(){
    return updateSign(t)
}
),
    e/12),
    c.label=6;
case 6:return[2]
}

}
))
}
))
}

================================================================================

84. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signAdaptor(t,
    e){
    var n;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    (function(){
    var r;
return signAdaptor_generator(this,
    (function(o){
    return r=now_default()(),
    "function"==typeof t.shouldSign?t.shouldSign(e)&&xhsSign(t,
    e):xhsSign(t,
    e),
    "function"==typeof t.shouldFp?t.shouldFp(e)&&xsCommon(t,
    e):xsCommon(t,
    e),
    "function"==typeof t.shouldToken?t.shouldToken(e)&&xhsToken(t,
    e):xhsToken(t,
    e),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-r,
    type:(null===window||void 0===window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(n=null==e?void 0:e.url)&&void 0!==n?n:window.location.href
}

}
),
    [2,
    e]
}
))
}
))
}

================================================================================

85. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function xhsSign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}
))&&utils_shouldSign(n)))return e;
c&&signLackReload();
try{
    var s=getRealUrl(n,
    r,
    o),
    l=encrypt_sign;
a&&void 0!==window._webmsxyw&&(l=window._webmsxyw);
var f=l(s,
    i)||{
    
}
;
e.headers["X-t"]=f["X-t"],
    e.headers["X-s"]=f["X-s"]
}
catch(v){
    
}
if(!0!==t.disableMns)try{
    if(window.mns){
    var p=i,
    h=(s=getRealUrl(n,
    r,
    o),
    "[object Object]"===Object.prototype.toString.call(p)||"[object Array]"===Object.prototype.toString.call(p)),
    d=MD5([s,
    h?stringify_default()(p):""].join(""));
e.headers["X-Mns"]=window.mns.getMnsToken(s,
    p,
    d)||""
}
else e.headers["X-Mns"]="unload"
}
catch(v){
    e.headers["X-Mns"]="error"
}
return e
}

================================================================================

86. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signLackReload(t){
    if(t&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    new Error("网络连接不可用，请刷新重试。")
}

================================================================================

87. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}

================================================================================

88. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushRealTimeXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}

================================================================================

89. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xsCommon(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
));
if(!some_default()(c).call(c,
    (function(t){
    return t.test(u)
}
)))return e;
var s=e.headers["X-t"]||"",
    l=e.headers["X-s"]||"",
    f=e.headers["X-Sign"]||"",
    p=getSigCount(s&&l||f),
    h=localStorage.getItem(MINI_BROSWER_INFO_KEY),
    d=localStorage.getItem(RC4_SECRET_VERSION_KEY)||RC4_SECRET_VERSION,
    v={
    s0:getPlatformCode(a),
    s1:"",
    x0:d,
    x1:version,
    x2:a||"PC",
    x3:"login",
    x4:"0.10.14",
    x5:js_cookie.A.get(LOCAL_ID_KEY),
    x6:s,
    x7:l,
    x8:h,
    x9:mcr(concat_default()(o=concat_default()(i="".concat(s)).call(i,
    l)).call(o,
    h)),
    x10:p
}
,
    g=map_default()(NEED_REAL_TIME_XSCOMMON_URLS).call(NEED_REAL_TIME_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
)),
    y=some_default()(g).call(g,
    (function(t){
    return t.test(u)
}
));
(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){
    var n,
    r;
v.x8=t,
    v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,
    l)).call(n,
    t)),
    e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
)):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
catch(m){
    
}
return e
}

================================================================================

90. 文件: vendor-main.e645eae.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xCommonParams(t,
    e){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    (function(){
    var n,
    r,
    o,
    i,
    a,
    u,
    c,
    s,
    l,
    f,
    p,
    h,
    d,
    v,
    g,
    y,
    m;
return xCommonParams_generator(this,
    (function(w){
    var b,
    _;
switch(w.label){
    case 0:if(n=t.platform,
    r=includes_default()(t),
    o=t.carryDeviceInfo,
    i=some_default()(r).call(r,
    (function(t){
    var n;
return includes_default()(n=e.url).call(n,
    t)
}
)),
    !(ozone_detector.RI.isXHS&&o&&i))return[2,
    e];
a=now_default()(),
    u=(0,
    ozone_detector.JF)(),
    c=u.major,
    s=u.minor,
    l=u.patch,
    f=(0,
    index_web.NW)(),
    (p=new(url_search_params_default())).append("platform",
    n),
    p.append("versionName",
    concat_default()(b=concat_default()(_="".concat(c,
    ".")).call(_,
    s,
    ".")).call(b,
    l)),
    w.label=1;
case 1:return w.trys.push([1,
    4,
    ,
    5]),
    p.has("deviceId")?[3,
    3]:[4,
    f];
case 2:h=w.sent(),
    d=h.deviceId,
    v=h.uniqueId,
    g=h.deviceFingerprint,
    y=h.deviceFingerprint1,
    m=h.fid,
    p.append("deviceId",
    d||v),
    p.append("device_fingerprint",
    g),
    p.append("device_fingerprint1",
    y),
    p.append("fid",
    m),
    w.label=3;
case 3:return[3,
    5];
case 4:return w.sent(),
    [3,
    5];
case 5:return e.headers["xy-common-params"]=p.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-a,
    type:"xCommonParams",
    source:null==e?void 0:e.url
}

}
),
    [2,
    e]
}

}
))
}
))
}

================================================================================

91. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
匹配代码: headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
id 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toStr...
================================================================================

92. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
匹配代码: x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
call(g,(function(t){return t.test(u)}));(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorag...
================================================================================

93. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: x-s-common[^=]*=\s*[^;]+
匹配代码: X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
上下文:
tCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e...
================================================================================

94. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function encrypt_sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}
return e
}
(t);
l<t.length;
)i=(e=t.charCodeAt(l++))>>2,
    a=(3&e)<<4|(r=t.charCodeAt(l++))>>4,
    u=(15&r)<<2|(o=t.charCodeAt(l++))>>6,
    c=63&o,
    isNaN(r)?u=c=64:isNaN(o)&&(c=64),
    s=s+n.charAt(i)+n.charAt(a)+n.charAt(u)+n.charAt(c);
return s
}
(MD5([o,
    r,
    t,
    a?stringify_default()(e):""].join(""))),
    "X-t":o
}

}

================================================================================

95. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function utils_shouldSign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}
)),
    e
}

================================================================================

96. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shouldSignReload(){
    try{
    var t=+new Date,
    e=stringify_default()(localStorage.getItem(signLackInfo)||{
    
}
),
    n=!(!e||!e.count),
    r=e&&e.time&&t-e.time<timeGap;
if(!(n&&r)){
    var o=new SignReload;
return localStorage.setItem(signLackInfo,
    stringify_default()(o)),
    !0
}
return!(e.count>maxReloadTime)&&(e.count=e.count+1,
    localStorage.setItem(signLackInfo,
    stringify_default()(e)),
    !0)
}
catch(i){
    return!1
}

}

================================================================================

97. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function updateSign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(wraperScript,
    {
    callFrom:r,
    callback:i
}
,
    2,
    60)];
case 2:return u=c.sent(),
    a=u.secPoisonId,
    wrapperEval(u.data),
    delete window[i],
    updateTokenTs(),
    [3,
    4];
case 3:return c.sent(),
    [3,
    4];
case 4:return set_timeout_default()((function(){
    return updateSign(t)
}
),
    5*e),
    [3,
    6];
case 5:set_timeout_default()((function(){
    return updateSign(t)
}
),
    e/12),
    c.label=6;
case 6:return[2]
}

}
))
}
))
}

================================================================================

98. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signAdaptor(t,
    e){
    var n;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    (function(){
    var r;
return signAdaptor_generator(this,
    (function(o){
    return r=now_default()(),
    "function"==typeof t.shouldSign?t.shouldSign(e)&&xhsSign(t,
    e):xhsSign(t,
    e),
    "function"==typeof t.shouldFp?t.shouldFp(e)&&xsCommon(t,
    e):xsCommon(t,
    e),
    "function"==typeof t.shouldToken?t.shouldToken(e)&&xhsToken(t,
    e):xhsToken(t,
    e),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-r,
    type:(null===window||void 0===window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(n=null==e?void 0:e.url)&&void 0!==n?n:window.location.href
}

}
),
    [2,
    e]
}
))
}
))
}

================================================================================

99. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function xhsSign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}
))&&utils_shouldSign(n)))return e;
c&&signLackReload();
try{
    var s=getRealUrl(n,
    r,
    o),
    l=encrypt_sign;
a&&void 0!==window._webmsxyw&&(l=window._webmsxyw);
var f=l(s,
    i)||{
    
}
;
e.headers["X-t"]=f["X-t"],
    e.headers["X-s"]=f["X-s"]
}
catch(v){
    
}
if(!0!==t.disableMns)try{
    if(window.mns){
    var p=i,
    h=(s=getRealUrl(n,
    r,
    o),
    "[object Object]"===Object.prototype.toString.call(p)||"[object Array]"===Object.prototype.toString.call(p)),
    d=MD5([s,
    h?stringify_default()(p):""].join(""));
e.headers["X-Mns"]=window.mns.getMnsToken(s,
    p,
    d)||""
}
else e.headers["X-Mns"]="unload"
}
catch(v){
    e.headers["X-Mns"]="error"
}
return e
}

================================================================================

100. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function signLackReload(t){
    if(t&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    new Error("网络连接不可用，请刷新重试。")
}

================================================================================

101. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}

================================================================================

102. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function pushRealTimeXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}

================================================================================

103. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xsCommon(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
));
if(!some_default()(c).call(c,
    (function(t){
    return t.test(u)
}
)))return e;
var s=e.headers["X-t"]||"",
    l=e.headers["X-s"]||"",
    f=e.headers["X-Sign"]||"",
    p=getSigCount(s&&l||f),
    h=localStorage.getItem(MINI_BROSWER_INFO_KEY),
    d=localStorage.getItem(RC4_SECRET_VERSION_KEY)||RC4_SECRET_VERSION,
    v={
    s0:getPlatformCode(a),
    s1:"",
    x0:d,
    x1:version,
    x2:a||"PC",
    x3:"login",
    x4:"0.10.14",
    x5:js_cookie.A.get(LOCAL_ID_KEY),
    x6:s,
    x7:l,
    x8:h,
    x9:mcr(concat_default()(o=concat_default()(i="".concat(s)).call(i,
    l)).call(o,
    h)),
    x10:p
}
,
    g=map_default()(NEED_REAL_TIME_XSCOMMON_URLS).call(NEED_REAL_TIME_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
)),
    y=some_default()(g).call(g,
    (function(t){
    return t.test(u)
}
));
(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){
    var n,
    r;
v.x8=t,
    v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,
    l)).call(n,
    t)),
    e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
)):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
catch(m){
    
}
return e
}

================================================================================

104. 文件: vendor-main.e645eae_1.js (行 2)
匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
函数代码:
function xCommonParams(t,
    e){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    (function(){
    var n,
    r,
    o,
    i,
    a,
    u,
    c,
    s,
    l,
    f,
    p,
    h,
    d,
    v,
    g,
    y,
    m;
return xCommonParams_generator(this,
    (function(w){
    var b,
    _;
switch(w.label){
    case 0:if(n=t.platform,
    r=includes_default()(t),
    o=t.carryDeviceInfo,
    i=some_default()(r).call(r,
    (function(t){
    var n;
return includes_default()(n=e.url).call(n,
    t)
}
)),
    !(ozone_detector.RI.isXHS&&o&&i))return[2,
    e];
a=now_default()(),
    u=(0,
    ozone_detector.JF)(),
    c=u.major,
    s=u.minor,
    l=u.patch,
    f=(0,
    index_web.NW)(),
    (p=new(url_search_params_default())).append("platform",
    n),
    p.append("versionName",
    concat_default()(b=concat_default()(_="".concat(c,
    ".")).call(_,
    s,
    ".")).call(b,
    l)),
    w.label=1;
case 1:return w.trys.push([1,
    4,
    ,
    5]),
    p.has("deviceId")?[3,
    3]:[4,
    f];
case 2:h=w.sent(),
    d=h.deviceId,
    v=h.uniqueId,
    g=h.deviceFingerprint,
    y=h.deviceFingerprint1,
    m=h.fid,
    p.append("deviceId",
    d||v),
    p.append("device_fingerprint",
    g),
    p.append("device_fingerprint1",
    y),
    p.append("fid",
    m),
    w.label=3;
case 3:return[3,
    5];
case 4:return w.sent(),
    [3,
    5];
case 5:return e.headers["xy-common-params"]=p.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-a,
    type:"xCommonParams",
    source:null==e?void 0:e.url
}

}
),
    [2,
    e]
}

}
))
}
))
}

================================================================================

105. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(ek.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&(A[B]=N)
}

================================================================================

106. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function baseAssign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}

================================================================================

107. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignMergeValue(A,
    B,
    N){
    (void 0!==N&&!eq(A[B],
    N)||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}

================================================================================

108. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(eV.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}

================================================================================

109. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function baseAssignValue(A,
    B,
    N){
    "__proto__"==B&&e6?e6(A,
    B,
    {
    configurable:!0,
    enumerable:!0,
    value:N,
    writable:!0
}
):A[B]=N
}

================================================================================

110. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function createAssigner(A){
    return baseRest(function(B,
    N){
    var U=-1,
    H=N.length,
    W=H>1?N[H-1]:void 0,
    j=H>2?N[2]:void 0;
for(W=A.length>3&&"function"==typeof W?(H--,
    W):void 0,
    j&&isIterateeCall(N[0],
    N[1],
    j)&&(W=H<3?void 0:W,
    H=1),
    B=Object(B);
++U<H;
){
    var V=N[U];
V&&A(B,
    V,
    U,
    W)
}
return B
}
)
}

================================================================================

111. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getHighBitsUnsigned(){
    return this.high>>>0
}

================================================================================

112. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getLowBitsUnsigned(){
    return this.low>>>0
}

================================================================================

113. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}

================================================================================

114. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}

================================================================================

115. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}

================================================================================

116. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getHighBitsUnsigned(){
    return this.high>>>0
}

================================================================================

117. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getLowBitsUnsigned(){
    return this.low>>>0
}

================================================================================

118. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}

================================================================================

119. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}

================================================================================

120. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}

================================================================================

121. 文件: vendor.621a7319.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}
return A
}

================================================================================

122. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(ek.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&(A[B]=N)
}

================================================================================

123. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function baseAssign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}

================================================================================

124. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignMergeValue(A,
    B,
    N){
    (void 0!==N&&!eq(A[B],
    N)||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}

================================================================================

125. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(eV.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}

================================================================================

126. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function baseAssignValue(A,
    B,
    N){
    "__proto__"==B&&e6?e6(A,
    B,
    {
    configurable:!0,
    enumerable:!0,
    value:N,
    writable:!0
}
):A[B]=N
}

================================================================================

127. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function createAssigner(A){
    return baseRest(function(B,
    N){
    var U=-1,
    H=N.length,
    W=H>1?N[H-1]:void 0,
    j=H>2?N[2]:void 0;
for(W=A.length>3&&"function"==typeof W?(H--,
    W):void 0,
    j&&isIterateeCall(N[0],
    N[1],
    j)&&(W=H<3?void 0:W,
    H=1),
    B=Object(B);
++U<H;
){
    var V=N[U];
V&&A(B,
    V,
    U,
    W)
}
return B
}
)
}

================================================================================

128. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getHighBitsUnsigned(){
    return this.high>>>0
}

================================================================================

129. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getLowBitsUnsigned(){
    return this.low>>>0
}

================================================================================

130. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}

================================================================================

131. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}

================================================================================

132. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}

================================================================================

133. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getHighBitsUnsigned(){
    return this.high>>>0
}

================================================================================

134. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function getLowBitsUnsigned(){
    return this.low>>>0
}

================================================================================

135. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}

================================================================================

136. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}

================================================================================

137. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}

================================================================================

138. 文件: vendor.621a7319_1.js (行 1)
匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
函数代码:
function assign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}
return A
}

================================================================================

🔧 相关函数:
------------------------------

📍 encode:
  文件: library-axios.435de88b.js (行 1)
  代码: function encode(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%...

  文件: library-axios.435de88b.js (行 1)
  代码: function encode(e){var t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"}...

  文件: library-axios.435de88b.js (行 1)
  代码: function encode(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2...

  文件: library-axios.435de88b.js (行 1)
  代码: encode(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$")...

  文件: library-axios.435de88b.js (行 1)
  代码: encode(e){var t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"}...

  文件: library-axios.435de88b.js (行 1)
  代码: encode(t){return e.call(this,t,encode)}...

  文件: library-axios.435de88b.js (行 1)
  代码: encode(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")...

  文件: library-axios.435de88b_1.js (行 1)
  代码: function encode(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%...

  文件: library-axios.435de88b_1.js (行 1)
  代码: function encode(e){var t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"}...

  文件: library-axios.435de88b_1.js (行 1)
  代码: function encode(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2...

  文件: library-axios.435de88b_1.js (行 1)
  代码: encode(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$")...

  文件: library-axios.435de88b_1.js (行 1)
  代码: encode(e){var t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"}...

  文件: library-axios.435de88b_1.js (行 1)
  代码: encode(t){return e.call(this,t,encode)}...

  文件: library-axios.435de88b_1.js (行 1)
  代码: encode(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")...

  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: encode=function(t){var r,e,n=[],o=(t=ucs2decode(t)).length,i=128,u=0,s=72;for(r=0;r<t.length;r++)(e=...

  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: Encode=function(t,r){var e=g(t,0);return e>32&&e<127&&!h(r,t)?t:encodeURIComponent(t)}...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: encode=function(t){var r,e,n=[],o=(t=ucs2decode(t)).length,i=128,u=0,s=72;for(r=0;r<t.length;r++)(e=...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: Encode=function(t,r){var e=g(t,0);return e>32&&e<127&&!h(r,t)?t:encodeURIComponent(t)}...

  文件: library-vue.a552caa8.js (行 1)
  代码: Encode(e){return encodeURI(""+e).replace(L,"|").replace(A,"[").replace(x,"]")}...

  文件: library-vue.a552caa8_1.js (行 1)
  代码: Encode(e){return encodeURI(""+e).replace(L,"|").replace(A,"[").replace(x,"]")}...

  文件: main.7e49175.js (行 2)
  代码: encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");var t=new Uint8Array(...

  文件: main.7e49175_1.js (行 2)
  代码: encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");var t=new Uint8Array(...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: function encode(e){var r,i,s,u,c,l,d,p="",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: encode(e){e=e.replace(/\r\n/g,"\n");for(var r="",i=0;i<e.length;i++){var a=e.charCodeAt(i);a<128?r+=...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: encode(e){var r,i,s,u,c,l,d,p="",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),i=e.charC...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: function encode(e){var r,i,s,u,c,l,d,p="",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: encode(e){e=e.replace(/\r\n/g,"\n");for(var r="",i=0;i<e.length;i++){var a=e.charCodeAt(i);a<128?r+=...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: encode(e){var r,i,s,u,c,l,d,p="",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),i=e.charC...

  文件: vendor-main.e645eae.js (行 2)
  代码: Encode(t){for(var e,n=t.length,r=n%3,o=[],i=16383,a=0,u=n-r;a<u;a+=i)o.push(encodeChunk(t,a,a+i>u?u:...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Encode(t){for(var e,n=t.length,r=n%3,o=[],i=16383,a=0,u=n-r;a<u;a+=i)o.push(encodeChunk(t,a,a+i>u?u:...

  文件: vendor.621a7319.js (行 1)
  代码: function encode(A){if(!this.genPoly)throw Error("Encoder not initialized");let B=new Uint8Array(A.le...

  文件: vendor.621a7319.js (行 1)
  代码: Encode=function(A){return encodeURIComponent(String(A))}...

  文件: vendor.621a7319.js (行 1)
  代码: encode(A){if(!this.genPoly)throw Error("Encoder not initialized");let B=new Uint8Array(A.length+this...

  文件: vendor.621a7319_1.js (行 1)
  代码: function encode(A){if(!this.genPoly)throw Error("Encoder not initialized");let B=new Uint8Array(A.le...

  文件: vendor.621a7319_1.js (行 1)
  代码: Encode=function(A){return encodeURIComponent(String(A))}...

  文件: vendor.621a7319_1.js (行 1)
  代码: encode(A){if(!this.genPoly)throw Error("Encoder not initialized");let B=new Uint8Array(A.length+this...


📍 sign:
  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: function sign(t){var r=+t;return 0===r||r!=r?r:r<0?-1:1}...

  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: sign(t){var r=+t;return 0===r||r!=r?r:r<0?-1:1}...

  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: sign(t,r){for(var e=f(t),o=arguments.length,a=1,p=c.f,h=s.f;o>a;){for(var d,y=l(arguments[a++]),g=p?...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: function sign(t){var r=+t;return 0===r||r!=r?r:r<0?-1:1}...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: sign(t){var r=+t;return 0===r||r!=r?r:r<0?-1:1}...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: sign(t,r){for(var e=f(t),o=arguments.length,a=1,p=c.f,h=s.f;o>a;){for(var d,y=l(arguments[a++]),g=p?...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: sign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;i<a;i++)...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\r\n/g,"\n");for(var r="",i=0;i<e.l...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Sign(e){var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Sign(e){return token_awaiter(this,void 0,void 0,function(){var r,i,a,s,u,c,d,v;return token_generato...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Sign(e,r){var i=r.url,a=r.params,s=r.paramsSerializer,u=r.data,c=e.configInit,l=e.xsIgnore,d=e.autoR...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: sign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;i<a;i++)...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\r\n/g,"\n");for(var r="",i=0;i<e.l...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Sign(e){var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Sign(e){return token_awaiter(this,void 0,void 0,function(){var r,i,a,s,u,c,d,v;return token_generato...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Sign(e,r){var i=r.url,a=r.params,s=r.paramsSerializer,u=r.data,c=e.configInit,l=e.xsIgnore,d=e.autoR...

  文件: vendor-main.e645eae.js (行 2)
  代码: sign=function(){return __assign=assign_default()||function(t){for(var e,n=1,r=arguments.length;n<r;n...

  文件: vendor-main.e645eae.js (行 2)
  代码: sign(t,e){var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",r="iamspam",o=(n...

  文件: vendor-main.e645eae.js (行 2)
  代码: Sign(t){var e=!0;return index_of_default()(t).call(t,window.location.host)>-1||index_of_default()(t)...

  文件: vendor-main.e645eae.js (行 2)
  代码: Sign(t){return token_awaiter(this,void 0,void 0,(function(){var e,n,r,o,i,a,u;return token_generator...

  文件: vendor-main.e645eae.js (行 2)
  代码: Sign(t,e){var n=e.url,r=e.params,o=e.paramsSerializer,i=e.data,a=t.configInit,u=t.xsIgnore,c=t.autoR...

  文件: vendor-main.e645eae.js (行 2)
  代码: sign({visitor:function(t,e,n,r){return Ze.isNode&&Te.isBuffer(t)?(this.append(e,t.toString("base64")...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: sign=function(){return __assign=assign_default()||function(t){for(var e,n=1,r=arguments.length;n<r;n...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: sign(t,e){var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",r="iamspam",o=(n...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Sign(t){var e=!0;return index_of_default()(t).call(t,window.location.host)>-1||index_of_default()(t)...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Sign(t){return token_awaiter(this,void 0,void 0,(function(){var e,n,r,o,i,a,u;return token_generator...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Sign(t,e){var n=e.url,r=e.params,o=e.paramsSerializer,i=e.data,a=t.configInit,u=t.xsIgnore,c=t.autoR...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: sign({visitor:function(t,e,n,r){return Ze.isNode&&Te.isBuffer(t)?(this.append(e,t.toString("base64")...

  文件: vendor.621a7319.js (行 1)
  代码: sign(A,B){return A&&copyObject(B,keys(B),A)}...

  文件: vendor.621a7319.js (行 1)
  代码: sign(A){for(var B=1;B<arguments.length;B++){var N=arguments[B];for(var U in N)A[U]=N[U]}...

  文件: vendor.621a7319_1.js (行 1)
  代码: sign(A,B){return A&&copyObject(B,keys(B),A)}...

  文件: vendor.621a7319_1.js (行 1)
  代码: sign(A){for(var B=1;B<arguments.length;B++){var N=arguments[B];for(var U in N)A[U]=N[U]}...


📍 hash:
  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: Hash:function(){var t=this.fragment;return t?"#"+t:""}...

  文件: library-polyfill.5f7e25b2.js (行 1)
  代码: Hash:function(t){if(""===(t=m(t))){this.fragment=null;return}...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: Hash:function(){var t=this.fragment;return t?"#"+t:""}...

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  代码: Hash:function(t){if(""===(t=m(t))){this.fragment=null;return}...

  文件: library-vue.a552caa8.js (行 1)
  代码: Hash(e){return commonEncode(e).replace(I,"{").replace(M,"}...

  文件: library-vue.a552caa8_1.js (行 1)
  代码: Hash(e){return commonEncode(e).replace(I,"{").replace(M,"}...

  文件: main.7e49175.js (行 2)
  代码: hash=function(){var e,t,n,r,i,o,a=this.blocks;this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-27...

  文件: main.7e49175.js (行 2)
  代码: Hash:function(){var e=this.fragment;return e?"#"+e:""}...

  文件: main.7e49175.js (行 2)
  代码: Hash:function(e){""!==(e=x(e))?("#"===P(e,0)&&(e=q(e,1)),this.fragment="",this.parse(e,Fe)):this.fra...

  文件: main.7e49175_1.js (行 2)
  代码: hash=function(){var e,t,n,r,i,o,a=this.blocks;this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-27...

  文件: main.7e49175_1.js (行 2)
  代码: Hash:function(){var e=this.fragment;return e?"#"+e:""}...

  文件: main.7e49175_1.js (行 2)
  代码: Hash:function(e){""!==(e=x(e))?("#"===P(e,0)&&(e=q(e,1)),this.fragment="",this.parse(e,Fe)):this.fra...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Hash(){this.entryHash={}...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Hash(){this.entryHash={}...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Hash(){this.entryHash={}...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Hash(){this.entryHash={}...

  文件: vendor.621a7319.js (行 1)
  代码: function Hash(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...

  文件: vendor.621a7319.js (行 1)
  代码: function Hash(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1...

  文件: vendor.621a7319.js (行 1)
  代码: Hash(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...

  文件: vendor.621a7319.js (行 1)
  代码: Hash(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...

  文件: vendor.621a7319_1.js (行 1)
  代码: function Hash(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...

  文件: vendor.621a7319_1.js (行 1)
  代码: function Hash(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1...

  文件: vendor.621a7319_1.js (行 1)
  代码: Hash(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...

  文件: vendor.621a7319_1.js (行 1)
  代码: Hash(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}...


📍 encrypt:
  文件: main.7e49175.js (行 2)
  代码: encrypt:function(t,n){return e(t,n,"encrypt")}...

  文件: main.7e49175.js (行 2)
  代码: encrypt:function(e){var t=n(g.toBytesNone("1p6ki2u2cknvza4j"),!0),o=g.toBytes(e);if((o=n(o)).length%...

  文件: main.7e49175_1.js (行 2)
  代码: encrypt:function(t,n){return e(t,n,"encrypt")}...

  文件: main.7e49175_1.js (行 2)
  代码: encrypt:function(e){var t=n(g.toBytesNone("1p6ki2u2cknvza4j"),!0),o=g.toBytes(e);if((o=n(o)).length%...


📍 md5:
  文件: main.7e49175.js (行 2)
  代码: Md5(e,(function(e,r){if(e)return n(s.error(e));var i='"'+r+'"';v[t]=i,b({loaded:x+=o,total:g}...

  文件: main.7e49175.js (行 2)
  代码: Md5(s,e.Body,(function(s){s&&(c&&c.setParams({md5EndTime:(new Date).getTime()}...

  文件: main.7e49175.js (行 2)
  代码: Md5(a,e.Body,(function(r){r&&(e.Headers["x-cos-meta-md5"]=r),a&&i&&i.setParams({md5EndTime:(new Date...

  文件: main.7e49175.js (行 2)
  代码: Md5(i,e.Body,(function(a){a&&(e.Headers["Content-MD5"]=o.b64(a)),i&&r&&r.setParams({md5EndTime:(new ...

  文件: main.7e49175.js (行 2)
  代码: Md5(t,(function(e,t){n(t)}...

  文件: main.7e49175_1.js (行 2)
  代码: Md5(e,(function(e,r){if(e)return n(s.error(e));var i='"'+r+'"';v[t]=i,b({loaded:x+=o,total:g}...

  文件: main.7e49175_1.js (行 2)
  代码: Md5(s,e.Body,(function(s){s&&(c&&c.setParams({md5EndTime:(new Date).getTime()}...

  文件: main.7e49175_1.js (行 2)
  代码: Md5(a,e.Body,(function(r){r&&(e.Headers["x-cos-meta-md5"]=r),a&&i&&i.setParams({md5EndTime:(new Date...

  文件: main.7e49175_1.js (行 2)
  代码: Md5(i,e.Body,(function(a){a&&(e.Headers["Content-MD5"]=o.b64(a)),i&&r&&r.setParams({md5EndTime:(new ...

  文件: main.7e49175_1.js (行 2)
  代码: Md5(t,(function(e,t){n(t)}...

  文件: vendor-main.e645eae.js (行 2)
  代码: MD5=function(t){function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: MD5=function(t){function e(r){if(n[r])return n[r].exports;var o=n[r]={i:r,l:!1,exports:{}...

  文件: vendor.621a7319.js (行 1)
  代码: md5=function(A,N){A.constructor==String?A=N&&"binary"===N.encoding?W.stringToBytes(A):U.stringToByte...

  文件: vendor.621a7319_1.js (行 1)
  代码: md5=function(A,N){A.constructor==String?A=N&&"binary"===N.encoding?W.stringToBytes(A):U.stringToByte...


📍 xs:
  文件: main.7e49175.js (行 2)
  代码: function xs(e,t,n,r){e.addEventListener(t,n,r)}...

  文件: main.7e49175.js (行 2)
  代码: Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputText...

  文件: main.7e49175.js (行 2)
  代码: var Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="Input...

  文件: main.7e49175.js (行 2)
  代码: xs(e,t,n,r){e.addEventListener(t,n,r)}...

  文件: main.7e49175.js (行 2)
  代码: xs(e,i?"change":"input",(function(t){if(!t.target.composing){var n=e.value;o&&(n=xe(n).call(n)),s&&(...

  文件: main.7e49175.js (行 2)
  代码: xs(e,"change",(function(){var t;e.value=xe(t=e.value).call(t)}...

  文件: main.7e49175_1.js (行 2)
  代码: function xs(e,t,n,r){e.addEventListener(t,n,r)}...

  文件: main.7e49175_1.js (行 2)
  代码: Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputText...

  文件: main.7e49175_1.js (行 2)
  代码: var Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="Input...

  文件: main.7e49175_1.js (行 2)
  代码: xs(e,t,n,r){e.addEventListener(t,n,r)}...

  文件: main.7e49175_1.js (行 2)
  代码: xs(e,i?"change":"input",(function(t){if(!t.target.composing){var n=e.value;o&&(n=xe(n).call(n)),s&&(...

  文件: main.7e49175_1.js (行 2)
  代码: xs(e,"change",(function(){var t;e.value=xe(t=e.value).call(t)}...


📍 common:
  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Common(e){try{(e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Common(e){try{(e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){k.push(e)}...

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  代码: Common(e,r){var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Common(e){try{(e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Common(e){try{(e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){k.push(e)}...

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  代码: Common(e,r){var i,a;try{var s=e.platform,u=r.url;if(S.map(function(e){return new RegExp(e)}...

  文件: vendor-main.e645eae.js (行 2)
  代码: Common(t){try{var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];for_each...

  文件: vendor-main.e645eae.js (行 2)
  代码: Common(t){try{var e=t&&is_array_default()(t)&&t.length>0?t:[];for_each_default()(e).call(e,(function...

  文件: vendor-main.e645eae.js (行 2)
  代码: Common(t,e){var n,r;try{var o,i,a=t.platform,u=e.url,c=map_default()(NEED_XSCOMMON_URLS).call(NEED_X...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Common(t){try{var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];for_each...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Common(t){try{var e=t&&is_array_default()(t)&&t.length>0?t:[];for_each_default()(e).call(e,(function...

  文件: vendor-main.e645eae_1.js (行 2)
  代码: Common(t,e){var n,r;try{var o,i,a=t.platform,u=e.url,c=map_default()(NEED_XSCOMMON_URLS).call(NEED_X...


📊 相关变量:
------------------------------

📍 key:
  文件: index.788b3226.js (行 1)
  定义: key===e.activeKey,"vertical-channel-bg":n.key===e.verticalChannelKey}]),onClick:function(){handleChannelClick(n)}},[(0,r.WI)(t.$slots,"icon",{channelKey:n.key}),(0,r.Uk)(" "+(0,r.zw)(n.label),1)],10,i)}),128))]}),_:3},8,["class"])}}}),l=(0,n(83222).default)(u,[["__scopeId","data-v-c69fb658"]])},23815:function(e,t,n){n.d(t,{Z:function(){return l}});

  文件: index.788b3226.js (行 1)
  定义: key===n?"refresh":"goto_channel_tab"})];

  文件: index.788b3226.js (行 1)
  定义: Key="")},query:s,resetQuery:resetQuery,unreadInfo:d,updateQuery:function(t){s.needNum=l.minRenderNotes-2*l.columns,s.noteIndex=e.value.length,s.num=t,"homefeed_recommend"===s.category&&(s.searchKey=i.value)},updateUnreadQuery:function(){if("homefeed_recommend"===s.category&&""===i.value){var e,t=null===(e=window)||void 0===e?void 0:e.localStorage.getItem(S.Z.UNREAD_NOTE_INFO);

  文件: index.788b3226.js (行 1)
  定义: key===g.w8.Fav?en.value:et.value}),eo=(0,p.iH)(N.map(function(){return{num:30,cursor:"",userId:Y,hasMore:!0,page:1}})),ea=(0,p.iH)((0,i._)(N.map(function(){return[]}))),ei=(0,p.iH)(N.map(function(){return 0})),eu=(0,p.iH)(N.map(function(){return!1})),el="";

  文件: index.788b3226.js (行 1)
  定义: key===g.rs.Board&&er.value.index&&(eo.value[er.value.index].page=1,eo.value[er.value.index].hasMore=!0,eu.value[er.value.index]=!1),P.userBoardList=[],ev(),[2]})}),function refreshUserBoard(){return I.apply(this,arguments)});

  文件: index.788b3226.js (行 1)
  定义: Key===e.itemKey});

  文件: index.788b3226_1.js (行 1)
  定义: key===e.activeKey,"vertical-channel-bg":n.key===e.verticalChannelKey}]),onClick:function(){handleChannelClick(n)}},[(0,r.WI)(t.$slots,"icon",{channelKey:n.key}),(0,r.Uk)(" "+(0,r.zw)(n.label),1)],10,i)}),128))]}),_:3},8,["class"])}}}),l=(0,n(83222).default)(u,[["__scopeId","data-v-c69fb658"]])},23815:function(e,t,n){n.d(t,{Z:function(){return l}});

  文件: index.788b3226_1.js (行 1)
  定义: key===n?"refresh":"goto_channel_tab"})];

  文件: index.788b3226_1.js (行 1)
  定义: Key="")},query:s,resetQuery:resetQuery,unreadInfo:d,updateQuery:function(t){s.needNum=l.minRenderNotes-2*l.columns,s.noteIndex=e.value.length,s.num=t,"homefeed_recommend"===s.category&&(s.searchKey=i.value)},updateUnreadQuery:function(){if("homefeed_recommend"===s.category&&""===i.value){var e,t=null===(e=window)||void 0===e?void 0:e.localStorage.getItem(S.Z.UNREAD_NOTE_INFO);

  文件: index.788b3226_1.js (行 1)
  定义: key===g.w8.Fav?en.value:et.value}),eo=(0,p.iH)(N.map(function(){return{num:30,cursor:"",userId:Y,hasMore:!0,page:1}})),ea=(0,p.iH)((0,i._)(N.map(function(){return[]}))),ei=(0,p.iH)(N.map(function(){return 0})),eu=(0,p.iH)(N.map(function(){return!1})),el="";

  文件: index.788b3226_1.js (行 1)
  定义: key===g.rs.Board&&er.value.index&&(eo.value[er.value.index].page=1,eo.value[er.value.index].hasMore=!0,eu.value[er.value.index]=!1),P.userBoardList=[],ev(),[2]})}),function refreshUserBoard(){return I.apply(this,arguments)});

  文件: index.788b3226_1.js (行 1)
  定义: Key===e.itemKey});

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===r)return e};

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===o&&(void 0===a||s.value===a)){if(Z(n,u,1),void 0!==a)break}else u++}!c&&(this.size=n.length),r.updateURL()},get:function get(t){var r=U(this).entries;

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===e)return r[n].value;

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===e&&J(n,r[o].value);

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===n&&(void 0===i||u.value===i))return!0}return!1},set:function set(t,r){var e,n=U(this);

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===a&&(i?Z(o,s--,1):(i=!0,e.value=u));

  文件: library-polyfill.5f7e25b2.js (行 1)
  定义: key===u?(d=!0,f(this,r.key)):v++;

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===r)return e};

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===o&&(void 0===a||s.value===a)){if(Z(n,u,1),void 0!==a)break}else u++}!c&&(this.size=n.length),r.updateURL()},get:function get(t){var r=U(this).entries;

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===e)return r[n].value;

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===e&&J(n,r[o].value);

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===n&&(void 0===i||u.value===i))return!0}return!1},set:function set(t,r){var e,n=U(this);

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===a&&(i?Z(o,s--,1):(i=!0,e.value=u));

  文件: library-polyfill.5f7e25b2_1.js (行 1)
  定义: key===u?(d=!0,f(this,r.key)):v++;

  文件: library-vue.a552caa8.js (行 1)
  定义: Key=e=>shared_esm_bundler_isString(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,g=shared_esm_bundler_makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cacheStringFunction=e=>{let t=Object.create(null);

  文件: library-vue.a552caa8.js (行 1)
  定义: key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){let e=this._object[this._key];

  文件: library-vue.a552caa8.js (行 1)
  定义: key===o.key){resetShapeFlag(o);

  文件: library-vue.a552caa8.js (行 1)
  定义: key=r.key),t}:r.fn)}return e}function renderSlot(e,t,n={},r,o){if(ev.isCE||ev.parent&&isAsyncWrapper(ev.parent)&&ev.parent.isCE)return"default"!==t&&(n.name=t),e4("slot",n,r&&r());

  文件: library-vue.a552caa8.js (行 1)
  定义: Key=e=>"_"===e[0]||"$stable"===e,normalizeSlotValue=e=>m(e)?e.map(normalizeVNode):[normalizeVNode(e)],normalizeSlot=(e,t,n)=>{if(t._n)return t;

  文件: library-vue.a552caa8.js (行 1)
  定义: key===t.key}let e3="__vInternal",normalizeKey=({key:e})=>null!=e?e:null,normalizeRef=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?shared_esm_bundler_isString(e)||reactivity_esm_bundler_isRef(e)||shared_esm_bundler_isFunction(e)?{i:ev,r:e,k:t,f:!!n}:e:null);

  文件: library-vue.a552caa8_1.js (行 1)
  定义: Key=e=>shared_esm_bundler_isString(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,g=shared_esm_bundler_makeMap(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),cacheStringFunction=e=>{let t=Object.create(null);

  文件: library-vue.a552caa8_1.js (行 1)
  定义: key=t,this._defaultValue=n,this.__v_isRef=!0}get value(){let e=this._object[this._key];

  文件: library-vue.a552caa8_1.js (行 1)
  定义: key===o.key){resetShapeFlag(o);

  文件: library-vue.a552caa8_1.js (行 1)
  定义: key=r.key),t}:r.fn)}return e}function renderSlot(e,t,n={},r,o){if(ev.isCE||ev.parent&&isAsyncWrapper(ev.parent)&&ev.parent.isCE)return"default"!==t&&(n.name=t),e4("slot",n,r&&r());

  文件: library-vue.a552caa8_1.js (行 1)
  定义: Key=e=>"_"===e[0]||"$stable"===e,normalizeSlotValue=e=>m(e)?e.map(normalizeVNode):[normalizeVNode(e)],normalizeSlot=(e,t,n)=>{if(t._n)return t;

  文件: library-vue.a552caa8_1.js (行 1)
  定义: key===t.key}let e3="__vInternal",normalizeKey=({key:e})=>null!=e?e:null,normalizeRef=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?shared_esm_bundler_isString(e)||reactivity_esm_bundler_isRef(e)||shared_esm_bundler_isFunction(e)?{i:ev,r:e,k:t,f:!!n}:e:null);

  文件: main.7e49175.js (行 2)
  定义: key=void 0,this.sc=0}return ue(e,[{key:"track",value:function(e){if(Ut&&$t&&Ut!==this.computed){var t=this.activeLink;

  文件: main.7e49175.js (行 2)
  定义: key=n),i.track()}}function dn(e,t,n,r,i,o){var a=sn.get(e);

  文件: main.7e49175.js (行 2)
  定义: key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}return ue(e,[{key:"value",get:function(){var e=this._object[this._key];

  文件: main.7e49175.js (行 2)
  定义: key===t.key}var ia=function(e){var t=e.key;

  文件: main.7e49175.js (行 2)
  定义: key=n.key),e}:n.fn)},r=0;

  文件: main.7e49175.js (行 2)
  定义: Key=n.clone(),a=this._iKey=n.clone(),s=o.words,c=a.words,u=0;

  文件: main.7e49175.js (行 2)
  定义: Key===u&&(!l||e.StorageClass.toUpperCase()===l.toUpperCase())}))).call(c)).call(a,(function(e){return e.UploadId||e.UploadID}));

  文件: main.7e49175.js (行 2)
  定义: Key=e.Key.substr(1)),o+=f,n.options.EnableReporter){var v,g=n.options.UseAccelerate||"string"==typeof n.options.Domain&&M(v=n.options.Domain).call(v,"accelerate."),m=f>i?"sliceUploadFile":"putObject";

  文件: main.7e49175.js (行 2)
  定义: Key===m){p=t;

  文件: main.7e49175.js (行 2)
  定义: Key=m,c._StsCache.push(p),y())}));

  文件: main.7e49175.js (行 2)
  定义: Key=m,p.TmpSecretId||(p.TmpSecretId=p.SecretId),p.TmpSecretKey||(p.TmpSecretKey=p.SecretKey);

  文件: main.7e49175.js (行 2)
  定义: Key=n.Key.substr(1))}}(),h=M(o=["getAuth","getObjectUrl"]).call(o,e);

  文件: main.7e49175.js (行 2)
  定义: key===t)return n};

  文件: main.7e49175.js (行 2)
  定义: key===n)return t[r].value;

  文件: main.7e49175.js (行 2)
  定义: key===n&&X(r,t[i].value);

  文件: main.7e49175.js (行 2)
  定义: key===r&&(void 0===o||s.value===o))return!0}return!1},set:function(e,t){var n=F(this);

  文件: main.7e49175.js (行 2)
  定义: key===a&&(o?J(i,u--,1):(o=!0,r.value=s));

  文件: main.7e49175_1.js (行 2)
  定义: key=void 0,this.sc=0}return ue(e,[{key:"track",value:function(e){if(Ut&&$t&&Ut!==this.computed){var t=this.activeLink;

  文件: main.7e49175_1.js (行 2)
  定义: key=n),i.track()}}function dn(e,t,n,r,i,o){var a=sn.get(e);

  文件: main.7e49175_1.js (行 2)
  定义: key=n,this._defaultValue=r,this.__v_isRef=!0,this._value=void 0}return ue(e,[{key:"value",get:function(){var e=this._object[this._key];

  文件: main.7e49175_1.js (行 2)
  定义: key===t.key}var ia=function(e){var t=e.key;

  文件: main.7e49175_1.js (行 2)
  定义: key=n.key),e}:n.fn)},r=0;

  文件: main.7e49175_1.js (行 2)
  定义: Key=n.clone(),a=this._iKey=n.clone(),s=o.words,c=a.words,u=0;

  文件: main.7e49175_1.js (行 2)
  定义: Key===u&&(!l||e.StorageClass.toUpperCase()===l.toUpperCase())}))).call(c)).call(a,(function(e){return e.UploadId||e.UploadID}));

  文件: main.7e49175_1.js (行 2)
  定义: Key=e.Key.substr(1)),o+=f,n.options.EnableReporter){var v,g=n.options.UseAccelerate||"string"==typeof n.options.Domain&&M(v=n.options.Domain).call(v,"accelerate."),m=f>i?"sliceUploadFile":"putObject";

  文件: main.7e49175_1.js (行 2)
  定义: Key===m){p=t;

  文件: main.7e49175_1.js (行 2)
  定义: Key=m,c._StsCache.push(p),y())}));

  文件: main.7e49175_1.js (行 2)
  定义: Key=m,p.TmpSecretId||(p.TmpSecretId=p.SecretId),p.TmpSecretKey||(p.TmpSecretKey=p.SecretKey);

  文件: main.7e49175_1.js (行 2)
  定义: Key=n.Key.substr(1))}}(),h=M(o=["getAuth","getObjectUrl"]).call(o,e);

  文件: main.7e49175_1.js (行 2)
  定义: key===t)return n};

  文件: main.7e49175_1.js (行 2)
  定义: key===n)return t[r].value;

  文件: main.7e49175_1.js (行 2)
  定义: key===n&&X(r,t[i].value);

  文件: main.7e49175_1.js (行 2)
  定义: key===r&&(void 0===o||s.value===o))return!0}return!1},set:function(e,t){var n=F(this);

  文件: main.7e49175_1.js (行 2)
  定义: key===a&&(o?J(i,u--,1):(o=!0,r.value=s));

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: KEY="red_abTest_config",r.USER_ID_KEY="x-user-id",r.WEB_ID_KEY=u,r.getCookie=getCookie,r.getDate=function(){var e=new Date,r=e.getFullYear().toString(),i=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getDate().toString().padStart(2,"0");

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: key===a});

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: key=E),_.label=1;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: key=L),_.label=9;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: Key="ProfileRefreshKey",E.PurchaseRefresh="purchase_refresh",E.RealNameVerifyCompleted="realNameVerifyCompleted",E.RedCAPTCHAVerifySuccess="red_captcha_verify_success",E.RelieveFreezeAccountSuccess="relieve_freeze_account_success",E.ReportFinishEvent="report_finish_event",E.ReportNoteRemoveEvent="report_note_remove_event",E.RnDevtools="rn-devtools",E.RnDevtoolsEmitter="rn-devtools-emitter",E.RnNativeNoteLike="rn-native-note-like",E.RnPostOrder="rn_post_order",E.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",E.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",E.SearchRnCard="search_rn_card",E.SellerFeedbackAction="seller-feedback-action",E.SetProfileEntries="setProfileEntries",E.ShareLiveTrailerCard="share_live_trailer_card",E.ShowTopicReadTask="show_topic_read_task",E.SizeBeenEntered="size-been-entered",E.SqaLike="sqa-like",E.SyncScrollviewRef="syncScrollviewRef",E.Test="test",E.TranslateY="translateY",E.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",E.UpdateTravelInfo="updateTravelInfo",E.VisitedChanged="visitedChanged",(T=eo||(eo={})).BusinessExecutionEnd="businessExecutionEnd",T.BusinessExecutionStart="businessExecutionStart",T.CoreHTTPRequestEnd="coreHttpRequestEnd",T.CoreHTTPRequestStart="coreHttpRequestStart",T.FirstMeaningfulPaint="firstMeaningfulPaint",T.FrameExecutionEnd="frameExecutionEnd",T.FrameExecutionStart="frameExecutionStart",T.RouterStart="routerStart",T.ViewRenderEnd="viewRenderEnd",(S=ea||(ea={})).More="more",S.Share="share",(b=es||(es={})).Center="center",b.Event="event",b.General="general",b.GoodsDetail="goodsDetail",b.Topic="topic",b.XiuxiuInvite="xiuxiuInvite",(k=eu||(eu={})).Image="image",k.Link="link",k.MiniProgram="miniProgram",k.Text="text",(C=ec||(ec={})).Image="image",C.Link="link",(P=el||(el={})).Image="image",P.Link="link",P.Text="text",(A=ed||(ed={})).Emoji="emoji",A.Image="image",A.Link="link",A.MiniProgram="miniProgram",A.Text="text",(R=ep||(ep={})).Image="image",R.Link="link",R.Text="text",(I=ef||(ef={})).Image="image",I.Link="link",I.Text="text",(O=ev||(ev={})).Goods="goods",O.Universal="universal",(N=eh||(eh={})).PageComplete="page_complete",N.PageLoad="page_load",(L=eg||(eg={})).Pause="pause",L.Resume="resume"},92014:function(e,r,i){"use strict";

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: Key="ProfileRefreshKey",S.PurchaseRefresh="purchase_refresh",S.RealNameVerifyCompleted="realNameVerifyCompleted",S.RedCAPTCHAVerifySuccess="red_captcha_verify_success",S.RelieveFreezeAccountSuccess="relieve_freeze_account_success",S.ReportFinishEvent="report_finish_event",S.ReportNoteRemoveEvent="report_note_remove_event",S.RnDevtools="rn-devtools",S.RnDevtoolsEmitter="rn-devtools-emitter",S.RnNativeNoteLike="rn-native-note-like",S.RnPostOrder="rn_post_order",S.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",S.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",S.SearchRnCard="search_rn_card",S.SellerFeedbackAction="seller-feedback-action",S.SetProfileEntries="setProfileEntries",S.ShareLiveTrailerCard="share_live_trailer_card",S.ShowTopicReadTask="show_topic_read_task",S.SizeBeenEntered="size-been-entered",S.SqaLike="sqa-like",S.SyncScrollviewRef="syncScrollviewRef",S.Test="test",S.TranslateY="translateY",S.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",S.UpdateTravelInfo="updateTravelInfo",S.VisitedChanged="visitedChanged",S);

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: key="".concat("RED_IM_STORE","_").concat(s),Store.sdk=e,u&&(Store.storage=u),[4,Store.storage.getItem(Store.key)];

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: KEY="red_abTest_config",r.USER_ID_KEY="x-user-id",r.WEB_ID_KEY=u,r.getCookie=getCookie,r.getDate=function(){var e=new Date,r=e.getFullYear().toString(),i=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getDate().toString().padStart(2,"0");

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: key===a});

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: key=E),_.label=1;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: key=L),_.label=9;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: Key="ProfileRefreshKey",E.PurchaseRefresh="purchase_refresh",E.RealNameVerifyCompleted="realNameVerifyCompleted",E.RedCAPTCHAVerifySuccess="red_captcha_verify_success",E.RelieveFreezeAccountSuccess="relieve_freeze_account_success",E.ReportFinishEvent="report_finish_event",E.ReportNoteRemoveEvent="report_note_remove_event",E.RnDevtools="rn-devtools",E.RnDevtoolsEmitter="rn-devtools-emitter",E.RnNativeNoteLike="rn-native-note-like",E.RnPostOrder="rn_post_order",E.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",E.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",E.SearchRnCard="search_rn_card",E.SellerFeedbackAction="seller-feedback-action",E.SetProfileEntries="setProfileEntries",E.ShareLiveTrailerCard="share_live_trailer_card",E.ShowTopicReadTask="show_topic_read_task",E.SizeBeenEntered="size-been-entered",E.SqaLike="sqa-like",E.SyncScrollviewRef="syncScrollviewRef",E.Test="test",E.TranslateY="translateY",E.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",E.UpdateTravelInfo="updateTravelInfo",E.VisitedChanged="visitedChanged",(T=eo||(eo={})).BusinessExecutionEnd="businessExecutionEnd",T.BusinessExecutionStart="businessExecutionStart",T.CoreHTTPRequestEnd="coreHttpRequestEnd",T.CoreHTTPRequestStart="coreHttpRequestStart",T.FirstMeaningfulPaint="firstMeaningfulPaint",T.FrameExecutionEnd="frameExecutionEnd",T.FrameExecutionStart="frameExecutionStart",T.RouterStart="routerStart",T.ViewRenderEnd="viewRenderEnd",(S=ea||(ea={})).More="more",S.Share="share",(b=es||(es={})).Center="center",b.Event="event",b.General="general",b.GoodsDetail="goodsDetail",b.Topic="topic",b.XiuxiuInvite="xiuxiuInvite",(k=eu||(eu={})).Image="image",k.Link="link",k.MiniProgram="miniProgram",k.Text="text",(C=ec||(ec={})).Image="image",C.Link="link",(P=el||(el={})).Image="image",P.Link="link",P.Text="text",(A=ed||(ed={})).Emoji="emoji",A.Image="image",A.Link="link",A.MiniProgram="miniProgram",A.Text="text",(R=ep||(ep={})).Image="image",R.Link="link",R.Text="text",(I=ef||(ef={})).Image="image",I.Link="link",I.Text="text",(O=ev||(ev={})).Goods="goods",O.Universal="universal",(N=eh||(eh={})).PageComplete="page_complete",N.PageLoad="page_load",(L=eg||(eg={})).Pause="pause",L.Resume="resume"},92014:function(e,r,i){"use strict";

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: Key="ProfileRefreshKey",S.PurchaseRefresh="purchase_refresh",S.RealNameVerifyCompleted="realNameVerifyCompleted",S.RedCAPTCHAVerifySuccess="red_captcha_verify_success",S.RelieveFreezeAccountSuccess="relieve_freeze_account_success",S.ReportFinishEvent="report_finish_event",S.ReportNoteRemoveEvent="report_note_remove_event",S.RnDevtools="rn-devtools",S.RnDevtoolsEmitter="rn-devtools-emitter",S.RnNativeNoteLike="rn-native-note-like",S.RnPostOrder="rn_post_order",S.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",S.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",S.SearchRnCard="search_rn_card",S.SellerFeedbackAction="seller-feedback-action",S.SetProfileEntries="setProfileEntries",S.ShareLiveTrailerCard="share_live_trailer_card",S.ShowTopicReadTask="show_topic_read_task",S.SizeBeenEntered="size-been-entered",S.SqaLike="sqa-like",S.SyncScrollviewRef="syncScrollviewRef",S.Test="test",S.TranslateY="translateY",S.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",S.UpdateTravelInfo="updateTravelInfo",S.VisitedChanged="visitedChanged",S);

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: key="".concat("RED_IM_STORE","_").concat(s),Store.sdk=e,u&&(Store.storage=u),[4,Store.storage.getItem(Store.key)];

  文件: vendor-main.e645eae.js (行 2)
  定义: key=t,this.value=e,this.a=void 0}function R(t){if(8192>=t.length)return String.fromCharCode.apply(null,t);

  文件: vendor-main.e645eae.js (行 2)
  定义: key===e.key}var pr="__vInternal",hr=function(t){var e=t.key;

  文件: vendor-main.e645eae.js (行 2)
  定义: KEY="b1b1",LOCAL_ID_KEY="a1",WEB_ID_KEY="webId",GID="gid",MINI_BROSWER_INFO_KEY="b1",PROFILE_COUNT_KEY="p1",PROFILE_TRIGGER_TIME_KEY="ptt",PROFILE_SERVER_TIME_KEY="pst",SIGN_COUNT_KEY="sc",XHS_SIGN="websectiga",XHS_POISON_ID="sec_poison_id",APP_ID_NAME="xsecappid",PLATFORM_CODE_MAP={"Mac PC":0},BLOCKED_HOSTS=["/t.xiaohongshu.com","/c.xiaohongshu.com","spltest.xiaohongshu.com","t2.xiaohongshu.com","t2-test.xiaohongshu.com","lng.xiaohongshu.com","apm-track.xiaohongshu.com","apm-track-test.xiaohongshu.com","fse.xiaohongshu.com","fse.devops.xiaohongshu.com","fesentry.xiaohongshu.com","spider-tracker.xiaohongshu.com"],PROFILE_BLOCKED_PATHS=["/privacy","/privacy/teenager"],scrintingUrl="/api/sec/v1/scripting",sdtSourceUrl="/api/sec/v1/sbtsource",redConfig="/api/redcaptcha/v2/getconfig",scriptingEval="scriptingEval",sdtSourceStorageKey="sdt_source_storage_key",sdtSourceInitKey="sdt_source_init",lastTokenUpdate="last_tiga_update_time",signLackInfo="sign_lack_info",NEED_XSCOMMON_URLS=["fe_api/burdock/v2/user/keyInfo","fe_api/burdock/v2/shield/profile","fe_api/burdock/v2/shield/captcha","fe_api/burdock/v2/shield/registerCanvas","api/sec/v1/shield/webprofile","api/sec/v1/shield/captcha",/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/tags/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/image_stickers/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/other\/notes/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/related/,"/fe_api/burdock/v2/note/post","/api/sns/web","/api/redcaptcha","/api/store/jpd/main"],RISK_ERROR_CODE_MAP={300011:"检测到帐号异常，请稍后重试",300012:"网络连接异常，请检查网络设置后重试",300013:"访问频次异常，请勿频繁操作",300015:"浏览器异常，请尝试更换浏览器后重试"},logName="infra_sec_web_api_walify",verifyLogName="infra_sec_verify_walify",spamLogName="infra_sec_spam_walify",NEED_REAL_TIME_XSCOMMON_URLS=[],version="4.0.8",slice=__webpack_require__(45978),slice_default=__webpack_require__.n(slice),define_property=__webpack_require__(53303),define_property_default=__webpack_require__.n(define_property),parse_int=__webpack_require__(85533),parse_int_default=__webpack_require__.n(parse_int),lookup=[],code="ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5",i=0,len=code.length;

  文件: vendor-main.e645eae.js (行 2)
  定义: key=d.eventKey,delete d.eventKey),v||null!==(o=d)&&void 0!==o&&o.event_key||null!==(i=d)&&void 0!==i&&i.event_info){t.next=12;

  文件: vendor-main.e645eae.js (行 2)
  定义: key=d.moduleKey,delete d.moduleKey),null!==(l=d)&&void 0!==l&&l.event_info||(null!==(w=d)&&void 0!==w&&w.attributes?(g&&(d.attributes=V(g,d.attributes)),d.attributes=fr(d.attributes)):g&&(d.attributes=fr(g))),null!==(f=d)&&void 0!==f&&f.event_info||null!==(p=d)&&void 0!==p&&p.context||(d={event_info:d}),null!==(h=d)&&void 0!==h&&h.event_info&&(this.event_seq+=1,d.event_info.event_seq=this.event_seq,d.event_info.event_time=Bt()(),d.event_info.event_id=(0,qt.A)(),v&&(d.event_info.page_key=v),m&&(d.event_info.route_matched_path=null===(b=m)||void 0===b?void 0:b.path),y&&(d.event_info.url=y));

  文件: vendor-main.e645eae.js (行 2)
  定义: Key="ProfileRefreshKey",t.PurchaseRefresh="purchase_refresh",t.RealNameVerifyCompleted="realNameVerifyCompleted",t.RedCAPTCHAVerifySuccess="red_captcha_verify_success",t.RelieveFreezeAccountSuccess="relieve_freeze_account_success",t.ReportFinishEvent="report_finish_event",t.ReportNoteRemoveEvent="report_note_remove_event",t.RnDevtools="rn-devtools",t.RnDevtoolsEmitter="rn-devtools-emitter",t.RnNativeNoteLike="rn-native-note-like",t.RnPostOrder="rn_post_order",t.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",t.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",t.SearchRnCard="search_rn_card",t.SellerFeedbackAction="seller-feedback-action",t.SetProfileEntries="setProfileEntries",t.ShareLiveTrailerCard="share_live_trailer_card",t.ShowTopicReadTask="show_topic_read_task",t.SizeBeenEntered="size-been-entered",t.SqaLike="sqa-like",t.SyncScrollviewRef="syncScrollviewRef",t.Test="test",t.TranslateY="translateY",t.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",t.UpdateTravelInfo="updateTravelInfo",t.VisitedChanged="visitedChanged"}(mn||(mn={})),function(t){t.BusinessExecutionEnd="businessExecutionEnd",t.BusinessExecutionStart="businessExecutionStart",t.CoreHTTPRequestEnd="coreHttpRequestEnd",t.CoreHTTPRequestStart="coreHttpRequestStart",t.FirstMeaningfulPaint="firstMeaningfulPaint",t.FrameExecutionEnd="frameExecutionEnd",t.FrameExecutionStart="frameExecutionStart",t.RouterStart="routerStart",t.ViewRenderEnd="viewRenderEnd"}(wn||(wn={})),function(t){t.More="more",t.Share="share"}(bn||(bn={})),function(t){t.Center="center",t.Event="event",t.General="general",t.GoodsDetail="goodsDetail",t.Topic="topic",t.XiuxiuInvite="xiuxiuInvite"}(_n||(_n={})),function(t){t.Image="image",t.Link="link",t.MiniProgram="miniProgram",t.Text="text"}(xn||(xn={})),function(t){t.Image="image",t.Link="link"}(kn||(kn={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(En||(En={})),function(t){t.Emoji="emoji",t.Image="image",t.Link="link",t.MiniProgram="miniProgram",t.Text="text"}(Sn||(Sn={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(An||(An={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(Tn||(Tn={})),function(t){t.Goods="goods",t.Universal="universal"}(Rn||(Rn={})),function(t){t.PageComplete="page_complete",t.PageLoad="page_load"}(Ln||(Ln={})),function(t){t.Pause="pause",t.Resume="resume"}(Cn||(Cn={}))},98633:function(t,e,n){"use strict";

  文件: vendor-main.e645eae_1.js (行 2)
  定义: key=t,this.value=e,this.a=void 0}function R(t){if(8192>=t.length)return String.fromCharCode.apply(null,t);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: key===e.key}var pr="__vInternal",hr=function(t){var e=t.key;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: KEY="b1b1",LOCAL_ID_KEY="a1",WEB_ID_KEY="webId",GID="gid",MINI_BROSWER_INFO_KEY="b1",PROFILE_COUNT_KEY="p1",PROFILE_TRIGGER_TIME_KEY="ptt",PROFILE_SERVER_TIME_KEY="pst",SIGN_COUNT_KEY="sc",XHS_SIGN="websectiga",XHS_POISON_ID="sec_poison_id",APP_ID_NAME="xsecappid",PLATFORM_CODE_MAP={"Mac PC":0},BLOCKED_HOSTS=["/t.xiaohongshu.com","/c.xiaohongshu.com","spltest.xiaohongshu.com","t2.xiaohongshu.com","t2-test.xiaohongshu.com","lng.xiaohongshu.com","apm-track.xiaohongshu.com","apm-track-test.xiaohongshu.com","fse.xiaohongshu.com","fse.devops.xiaohongshu.com","fesentry.xiaohongshu.com","spider-tracker.xiaohongshu.com"],PROFILE_BLOCKED_PATHS=["/privacy","/privacy/teenager"],scrintingUrl="/api/sec/v1/scripting",sdtSourceUrl="/api/sec/v1/sbtsource",redConfig="/api/redcaptcha/v2/getconfig",scriptingEval="scriptingEval",sdtSourceStorageKey="sdt_source_storage_key",sdtSourceInitKey="sdt_source_init",lastTokenUpdate="last_tiga_update_time",signLackInfo="sign_lack_info",NEED_XSCOMMON_URLS=["fe_api/burdock/v2/user/keyInfo","fe_api/burdock/v2/shield/profile","fe_api/burdock/v2/shield/captcha","fe_api/burdock/v2/shield/registerCanvas","api/sec/v1/shield/webprofile","api/sec/v1/shield/captcha",/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/tags/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/image_stickers/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/other\/notes/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/related/,"/fe_api/burdock/v2/note/post","/api/sns/web","/api/redcaptcha","/api/store/jpd/main"],RISK_ERROR_CODE_MAP={300011:"检测到帐号异常，请稍后重试",300012:"网络连接异常，请检查网络设置后重试",300013:"访问频次异常，请勿频繁操作",300015:"浏览器异常，请尝试更换浏览器后重试"},logName="infra_sec_web_api_walify",verifyLogName="infra_sec_verify_walify",spamLogName="infra_sec_spam_walify",NEED_REAL_TIME_XSCOMMON_URLS=[],version="4.0.8",slice=__webpack_require__(45978),slice_default=__webpack_require__.n(slice),define_property=__webpack_require__(53303),define_property_default=__webpack_require__.n(define_property),parse_int=__webpack_require__(85533),parse_int_default=__webpack_require__.n(parse_int),lookup=[],code="ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5",i=0,len=code.length;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: key=d.eventKey,delete d.eventKey),v||null!==(o=d)&&void 0!==o&&o.event_key||null!==(i=d)&&void 0!==i&&i.event_info){t.next=12;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: key=d.moduleKey,delete d.moduleKey),null!==(l=d)&&void 0!==l&&l.event_info||(null!==(w=d)&&void 0!==w&&w.attributes?(g&&(d.attributes=V(g,d.attributes)),d.attributes=fr(d.attributes)):g&&(d.attributes=fr(g))),null!==(f=d)&&void 0!==f&&f.event_info||null!==(p=d)&&void 0!==p&&p.context||(d={event_info:d}),null!==(h=d)&&void 0!==h&&h.event_info&&(this.event_seq+=1,d.event_info.event_seq=this.event_seq,d.event_info.event_time=Bt()(),d.event_info.event_id=(0,qt.A)(),v&&(d.event_info.page_key=v),m&&(d.event_info.route_matched_path=null===(b=m)||void 0===b?void 0:b.path),y&&(d.event_info.url=y));

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Key="ProfileRefreshKey",t.PurchaseRefresh="purchase_refresh",t.RealNameVerifyCompleted="realNameVerifyCompleted",t.RedCAPTCHAVerifySuccess="red_captcha_verify_success",t.RelieveFreezeAccountSuccess="relieve_freeze_account_success",t.ReportFinishEvent="report_finish_event",t.ReportNoteRemoveEvent="report_note_remove_event",t.RnDevtools="rn-devtools",t.RnDevtoolsEmitter="rn-devtools-emitter",t.RnNativeNoteLike="rn-native-note-like",t.RnPostOrder="rn_post_order",t.SNSRNDiscoverCommentAction="SNSRNDiscoverCommentAction",t.SearchRNCardImpressionEvent="searchRNCardImpressionEvent",t.SearchRnCard="search_rn_card",t.SellerFeedbackAction="seller-feedback-action",t.SetProfileEntries="setProfileEntries",t.ShareLiveTrailerCard="share_live_trailer_card",t.ShowTopicReadTask="show_topic_read_task",t.SizeBeenEntered="size-been-entered",t.SqaLike="sqa-like",t.SyncScrollviewRef="syncScrollviewRef",t.Test="test",t.TranslateY="translateY",t.UpdateRnLiveNoticeListInfo="update_rn_live_notice_list_info",t.UpdateTravelInfo="updateTravelInfo",t.VisitedChanged="visitedChanged"}(mn||(mn={})),function(t){t.BusinessExecutionEnd="businessExecutionEnd",t.BusinessExecutionStart="businessExecutionStart",t.CoreHTTPRequestEnd="coreHttpRequestEnd",t.CoreHTTPRequestStart="coreHttpRequestStart",t.FirstMeaningfulPaint="firstMeaningfulPaint",t.FrameExecutionEnd="frameExecutionEnd",t.FrameExecutionStart="frameExecutionStart",t.RouterStart="routerStart",t.ViewRenderEnd="viewRenderEnd"}(wn||(wn={})),function(t){t.More="more",t.Share="share"}(bn||(bn={})),function(t){t.Center="center",t.Event="event",t.General="general",t.GoodsDetail="goodsDetail",t.Topic="topic",t.XiuxiuInvite="xiuxiuInvite"}(_n||(_n={})),function(t){t.Image="image",t.Link="link",t.MiniProgram="miniProgram",t.Text="text"}(xn||(xn={})),function(t){t.Image="image",t.Link="link"}(kn||(kn={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(En||(En={})),function(t){t.Emoji="emoji",t.Image="image",t.Link="link",t.MiniProgram="miniProgram",t.Text="text"}(Sn||(Sn={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(An||(An={})),function(t){t.Image="image",t.Link="link",t.Text="text"}(Tn||(Tn={})),function(t){t.Goods="goods",t.Universal="universal"}(Rn||(Rn={})),function(t){t.PageComplete="page_complete",t.PageLoad="page_load"}(Ln||(Ln={})),function(t){t.Pause="pause",t.Resume="resume"}(Cn||(Cn={}))},98633:function(t,e,n){"use strict";

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(A){for(var B in A)return B},goog.object.getAnyValue=function(A){for(var B in A)return A[B]},goog.object.contains=function(A,B){return goog.object.containsValue(A,B)},goog.object.getValues=function(A){var B,N=[],U=0;

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(A,B){return null!==A&&B in A},goog.object.containsValue=function(A,B){for(var N in A)if(A[N]==B)return!0;

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(A,B,N){for(var U in A)if(B.call(N,A[U],U,A))return U},goog.object.findValue=function(A,B,N){return(B=goog.object.findKey(A,B,N))&&A[B]},goog.object.isEmpty=function(A){for(var B in A)return!1;

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(A,B,N){var U=N||goog.array.defaultCompare;

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(A,B,N){goog.array.sortByKey(A,function(A){return A[B]},N)},goog.array.isSorted=function(A,B,N){B=B||goog.array.defaultCompare;

  文件: vendor.621a7319.js (行 1)
  定义: key=A,this.value=B,this.valueWrapper=void 0},jspb.ExtensionFieldInfo=function(A,B,N,U,H){this.fieldIndex=A,this.fieldName=B,this.ctor=N,this.toObjectFn=U,this.isRepeated=H},goog.exportSymbol("jspb.ExtensionFieldInfo",jspb.ExtensionFieldInfo),jspb.ExtensionFieldBinaryInfo=function(A,B,N,U,H,W){this.fieldInfo=A,this.binaryReaderFn=B,this.binaryWriterFn=N,this.binaryMessageSerializeFn=U,this.binaryMessageDeserializeFn=H,this.isPacked=W},goog.exportSymbol("jspb.ExtensionFieldBinaryInfo",jspb.ExtensionFieldBinaryInfo),jspb.ExtensionFieldInfo.prototype.isMessageType=function(){return!!this.ctor},goog.exportProperty(jspb.ExtensionFieldInfo.prototype,"isMessageType",jspb.ExtensionFieldInfo.prototype.isMessageType),jspb.Message=function(){},goog.exportSymbol("jspb.Message",jspb.Message),jspb.Message.GENERATE_TO_OBJECT=!0,goog.exportProperty(jspb.Message,"GENERATE_TO_OBJECT",jspb.Message.GENERATE_TO_OBJECT),jspb.Message.GENERATE_FROM_OBJECT=!goog.DISALLOW_TEST_ONLY_CODE,goog.exportProperty(jspb.Message,"GENERATE_FROM_OBJECT",jspb.Message.GENERATE_FROM_OBJECT),jspb.Message.GENERATE_TO_STRING=!0,jspb.Message.ASSUME_LOCAL_ARRAYS=!1,jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS=!0,jspb.Message.SUPPORTS_UINT8ARRAY_="function"==typeof Uint8Array,jspb.Message.prototype.getJsPbMessageId=function(){return this.messageId_},goog.exportProperty(jspb.Message.prototype,"getJsPbMessageId",jspb.Message.prototype.getJsPbMessageId),jspb.Message.getIndex_=function(A,B){return B+A.arrayIndexOffset_},jspb.Message.hiddenES6Property_=function(){},jspb.Message.getFieldNumber_=function(A,B){return B-A.arrayIndexOffset_},jspb.Message.initialize=function(A,B,N,U,H,W){if(A.wrappers_=null,B||(B=N?[N]:[]),A.messageId_=N?String(N):void 0,A.arrayIndexOffset_=0===N?-1:0,A.array=B,jspb.Message.initPivotAndExtensionObject_(A,U),A.convertedPrimitiveFields_={},jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS||(A.repeatedFields=H),H)for(B=0;

  文件: vendor.621a7319.js (行 1)
  定义: key===A){eE();

  文件: vendor.621a7319.js (行 1)
  定义: key=B.props[A],delete B.props[A])});

  文件: vendor.621a7319.js (行 1)
  定义: Key=function(B){var N;

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(A){for(var B in A)return B},goog.object.getAnyValue=function(A){for(var B in A)return A[B]},goog.object.contains=function(A,B){return goog.object.containsValue(A,B)},goog.object.getValues=function(A){var B,N=[],U=0;

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(A,B){return null!==A&&B in A},goog.object.containsValue=function(A,B){for(var N in A)if(A[N]==B)return!0;

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(A,B,N){for(var U in A)if(B.call(N,A[U],U,A))return U},goog.object.findValue=function(A,B,N){return(B=goog.object.findKey(A,B,N))&&A[B]},goog.object.isEmpty=function(A){for(var B in A)return!1;

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(A,B,N){var U=N||goog.array.defaultCompare;

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(A,B,N){goog.array.sortByKey(A,function(A){return A[B]},N)},goog.array.isSorted=function(A,B,N){B=B||goog.array.defaultCompare;

  文件: vendor.621a7319_1.js (行 1)
  定义: key=A,this.value=B,this.valueWrapper=void 0},jspb.ExtensionFieldInfo=function(A,B,N,U,H){this.fieldIndex=A,this.fieldName=B,this.ctor=N,this.toObjectFn=U,this.isRepeated=H},goog.exportSymbol("jspb.ExtensionFieldInfo",jspb.ExtensionFieldInfo),jspb.ExtensionFieldBinaryInfo=function(A,B,N,U,H,W){this.fieldInfo=A,this.binaryReaderFn=B,this.binaryWriterFn=N,this.binaryMessageSerializeFn=U,this.binaryMessageDeserializeFn=H,this.isPacked=W},goog.exportSymbol("jspb.ExtensionFieldBinaryInfo",jspb.ExtensionFieldBinaryInfo),jspb.ExtensionFieldInfo.prototype.isMessageType=function(){return!!this.ctor},goog.exportProperty(jspb.ExtensionFieldInfo.prototype,"isMessageType",jspb.ExtensionFieldInfo.prototype.isMessageType),jspb.Message=function(){},goog.exportSymbol("jspb.Message",jspb.Message),jspb.Message.GENERATE_TO_OBJECT=!0,goog.exportProperty(jspb.Message,"GENERATE_TO_OBJECT",jspb.Message.GENERATE_TO_OBJECT),jspb.Message.GENERATE_FROM_OBJECT=!goog.DISALLOW_TEST_ONLY_CODE,goog.exportProperty(jspb.Message,"GENERATE_FROM_OBJECT",jspb.Message.GENERATE_FROM_OBJECT),jspb.Message.GENERATE_TO_STRING=!0,jspb.Message.ASSUME_LOCAL_ARRAYS=!1,jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS=!0,jspb.Message.SUPPORTS_UINT8ARRAY_="function"==typeof Uint8Array,jspb.Message.prototype.getJsPbMessageId=function(){return this.messageId_},goog.exportProperty(jspb.Message.prototype,"getJsPbMessageId",jspb.Message.prototype.getJsPbMessageId),jspb.Message.getIndex_=function(A,B){return B+A.arrayIndexOffset_},jspb.Message.hiddenES6Property_=function(){},jspb.Message.getFieldNumber_=function(A,B){return B-A.arrayIndexOffset_},jspb.Message.initialize=function(A,B,N,U,H,W){if(A.wrappers_=null,B||(B=N?[N]:[]),A.messageId_=N?String(N):void 0,A.arrayIndexOffset_=0===N?-1:0,A.array=B,jspb.Message.initPivotAndExtensionObject_(A,U),A.convertedPrimitiveFields_={},jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS||(A.repeatedFields=H),H)for(B=0;

  文件: vendor.621a7319_1.js (行 1)
  定义: key===A){eE();

  文件: vendor.621a7319_1.js (行 1)
  定义: key=B.props[A],delete B.props[A])});

  文件: vendor.621a7319_1.js (行 1)
  定义: Key=function(B){var N;


📍 token:
  文件: index.788b3226.js (行 1)
  定义: token=").concat(e.note.xsecToken,"&xsec_source=").concat(O.value)}),ev=(0,u.Fl)(function(){var t,n,r,o,a=e.note;

  文件: index.788b3226_1.js (行 1)
  定义: token=").concat(e.note.xsecToken,"&xsec_source=").concat(O.value)}),ev=(0,u.Fl)(function(){var t,n,r,o,a=e.note;

  文件: library-axios.435de88b.js (行 1)
  定义: Token=r(11860),a.isCancel=r(43592),a.all=function all(e){return Promise.all(e)},a.spread=r(42471),e.exports=a,e.exports.default=a},20647:function(e,t,r){"use strict";

  文件: library-axios.435de88b_1.js (行 1)
  定义: Token=r(11860),a.isCancel=r(43592),a.all=function all(e){return Promise.all(e)},a.spread=r(42471),e.exports=a,e.exports.default=a},20647:function(e,t,r){"use strict";

  文件: main.7e49175.js (行 2)
  定义: token="+n.SecurityToken),n.ClientIP&&(s+="&clientIP="+n.ClientIP),n.ClientUA&&(s+="&clientUA="+n.ClientUA),n.Token&&(s+="&token="+n.Token),a&&(s+="&"+a),P((function(){t(null,{Url:s})}))}}));

  文件: main.7e49175.js (行 2)
  定义: token="+u.SecurityToken:""),a&&(s+="&"+a)):a&&(s+="?"+a),s}function ze(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},r=(e&&e.AccessControlList||{}).Grant;

  文件: main.7e49175.js (行 2)
  定义: Token=n.XCosSecurityToken,delete n.XCosSecurityToken),t&&t(e,n))},c=this,u=e.Bucket||"",l=e.Region||"",f=e.Key||"";

  文件: main.7e49175.js (行 2)
  定义: token=e.AuthData.Token),e.AuthData.ClientIP&&(g.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(g.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(g.headers[m]=e.AuthData.SecurityToken),g.headers&&(g.headers=o.clearKey(g.headers)),e.retry&&(g.headers["x-cos-sdk-retry"]=!0),g=o.clearKey(g),e.onProgress&&"function"==typeof e.onProgress){var y=p&&(p.size||p.length)||0;

  文件: main.7e49175.js (行 2)
  定义: Token=t.getToken,this.enableResume=t.enableResume}return ue(e,[{key:"cacheKey",get:function(){var e;

  文件: main.7e49175_1.js (行 2)
  定义: token="+n.SecurityToken),n.ClientIP&&(s+="&clientIP="+n.ClientIP),n.ClientUA&&(s+="&clientUA="+n.ClientUA),n.Token&&(s+="&token="+n.Token),a&&(s+="&"+a),P((function(){t(null,{Url:s})}))}}));

  文件: main.7e49175_1.js (行 2)
  定义: token="+u.SecurityToken:""),a&&(s+="&"+a)):a&&(s+="?"+a),s}function ze(e){var t={GrantFullControl:[],GrantWrite:[],GrantRead:[],GrantReadAcp:[],GrantWriteAcp:[],ACL:""},n={FULL_CONTROL:"GrantFullControl",WRITE:"GrantWrite",READ:"GrantRead",READ_ACP:"GrantReadAcp",WRITE_ACP:"GrantWriteAcp"},r=(e&&e.AccessControlList||{}).Grant;

  文件: main.7e49175_1.js (行 2)
  定义: Token=n.XCosSecurityToken,delete n.XCosSecurityToken),t&&t(e,n))},c=this,u=e.Bucket||"",l=e.Region||"",f=e.Key||"";

  文件: main.7e49175_1.js (行 2)
  定义: token=e.AuthData.Token),e.AuthData.ClientIP&&(g.headers.clientIP=e.AuthData.ClientIP),e.AuthData.ClientUA&&(g.headers.clientUA=e.AuthData.ClientUA),e.AuthData.SecurityToken&&(g.headers[m]=e.AuthData.SecurityToken),g.headers&&(g.headers=o.clearKey(g.headers)),e.retry&&(g.headers["x-cos-sdk-retry"]=!0),g=o.clearKey(g),e.onProgress&&"function"==typeof e.onProgress){var y=p&&(p.size||p.length)||0;

  文件: main.7e49175_1.js (行 2)
  定义: Token=t.getToken,this.enableResume=t.enableResume}return ue(e,[{key:"cacheKey",get:function(){var e;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: Token=i),a&&(Z.sessionId=a),s&&"string"==typeof s&&(Z.hashExp=s),Z};

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: token=e.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),e.response.session_id&&(r.response.session_id=e.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),e.response.images&&!e.response.image&&(r.response.image=e.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),checks_checkRes(r,s.resT),i.extractValue){if(0===r.result)return r.response;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: Token=i),a&&(B.sessionId=a),s&&"string"==typeof s&&(B.hashExp=s),u&&(B.flags=u),B})}function getABInfo(){return getABInfoByBridge().catch(()=>B)}function getFlagValue(...e){return getABInfo().then(r=>{let{flags:i}=r,a=[];

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: token=i.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),i.response.session_id&&(s.response.session_id=i.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),i.response.images&&!i.response.image&&(s.response.image=i.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),webp2png(s),checkRes(s,a.resT),e.extractValue){if(0===s.result)return addMeasure({platformType:"H5",bridgeName:"getUserInfoOld",timing:Date.now()-r,logType:V.Timing}),s.response;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: token=i.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),i.response.session_id&&(s.response.session_id=i.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),i.response.images&&!i.response.image&&(s.response.image=i.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),webp2png(s),checkRes(s,a.resT),e.extractValue){if(0===s.result)return(0,k.b)({platformType:"H5",bridgeName:"getUserInfoOld",timing:Date.now()-r,logType:l.tM.Timing}),s.response;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: Token=i),a&&(Z.sessionId=a),s&&"string"==typeof s&&(Z.hashExp=s),Z};

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: token=e.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),e.response.session_id&&(r.response.session_id=e.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),e.response.images&&!e.response.image&&(r.response.image=e.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),checks_checkRes(r,s.resT),i.extractValue){if(0===r.result)return r.response;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: Token=i),a&&(B.sessionId=a),s&&"string"==typeof s&&(B.hashExp=s),u&&(B.flags=u),B})}function getABInfo(){return getABInfoByBridge().catch(()=>B)}function getFlagValue(...e){return getABInfo().then(r=>{let{flags:i}=r,a=[];

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: token=i.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),i.response.session_id&&(s.response.session_id=i.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),i.response.images&&!i.response.image&&(s.response.image=i.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),webp2png(s),checkRes(s,a.resT),e.extractValue){if(0===s.result)return addMeasure({platformType:"H5",bridgeName:"getUserInfoOld",timing:Date.now()-r,logType:V.Timing}),s.response;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: token=i.response.user_token,logDeprecated_warnDeprecated("res.response.user_token","res.response.userToken")),i.response.session_id&&(s.response.session_id=i.response.session_id,logDeprecated_warnDeprecated("res.response.session_id","res.response.sessionId")),i.response.images&&!i.response.image&&(s.response.image=i.response.images,logDeprecated_warnDeprecated("res.response.images","res.response.image"))),webp2png(s),checkRes(s,a.resT),e.extractValue){if(0===s.result)return(0,k.b)({platformType:"H5",bridgeName:"getUserInfoOld",timing:Date.now()-r,logType:l.tM.Timing}),s.response;

  文件: vendor-main.e645eae.js (行 2)
  定义: Token=n(3191),c.isCancel=n(93864),c.all=function(t){return Promise.all(t)},c.spread=n(17980),t.exports=c,t.exports.default=c},31928:function(t){"use strict";

  文件: vendor-main.e645eae.js (行 2)
  定义: Token=r),o&&(Pt.sessionId=o),i&&"string"==typeof i&&(Pt.hashExp=i),Pt})).catch((function(){return At(It).then((function(t){var e=t.data,n=e.user_token,r=W()(e);

  文件: vendor-main.e645eae.js (行 2)
  定义: Token=n),r&&(Pt.flags=r);

  文件: vendor-main.e645eae.js (行 2)
  定义: token=n.response.user_token,Ni("res.response.user_token","res.response.userToken")),n.response.session_id&&(o.response.session_id=n.response.session_id,Ni("res.response.session_id","res.response.sessionId")),n.response.images&&!n.response.image&&(o.response.image=n.response.images,Ni("res.response.images","res.response.image"))),ni(o),Li(o,r.resT),t.extractValue){if(0===o.result)return di({platformType:"H5",bridgeName:"getUserInfoOld",timing:T()()-e,logType:Wo.Timing}),o.response;

  文件: vendor-main.e645eae.js (行 2)
  定义: Token=er,or.isCancel=fn,or.VERSION=Xn,or.toFormData=Be,or.AxiosError=Oe,or.Cancel=or.CanceledError,or.all=function(t){return Promise.all(t)},or.spread=function(t){return function(e){return t.apply(null,e)}},or.isAxiosError=function(t){return Te.isObject(t)&&!0===t.isAxiosError},or.mergeConfig=En,or.AxiosHeaders=sn,or.formToJSON=t=>Qe(Te.isHTMLForm(t)?new FormData(t):t),or.getAdapter=Gn,or.HttpStatusCode=rr,or.default=or;

  文件: vendor-main.e645eae.js (行 2)
  定义: token=n.response.user_token,le("res.response.user_token","res.response.userToken")),n.response.session_id&&(o.response.session_id=n.response.session_id,le("res.response.session_id","res.response.sessionId")),n.response.images&&!n.response.image&&(o.response.image=n.response.images,le("res.response.images","res.response.image"))),vt(o),oe(o,r.resT),t.extractValue){if(0===o.result)return Tt({platformType:"H5",bridgeName:"getUserInfoOld",timing:O()()-e,logType:i.Timing}),o.response;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Token=n(3191),c.isCancel=n(93864),c.all=function(t){return Promise.all(t)},c.spread=n(17980),t.exports=c,t.exports.default=c},31928:function(t){"use strict";

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Token=r),o&&(Pt.sessionId=o),i&&"string"==typeof i&&(Pt.hashExp=i),Pt})).catch((function(){return At(It).then((function(t){var e=t.data,n=e.user_token,r=W()(e);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Token=n),r&&(Pt.flags=r);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: token=n.response.user_token,Ni("res.response.user_token","res.response.userToken")),n.response.session_id&&(o.response.session_id=n.response.session_id,Ni("res.response.session_id","res.response.sessionId")),n.response.images&&!n.response.image&&(o.response.image=n.response.images,Ni("res.response.images","res.response.image"))),ni(o),Li(o,r.resT),t.extractValue){if(0===o.result)return di({platformType:"H5",bridgeName:"getUserInfoOld",timing:T()()-e,logType:Wo.Timing}),o.response;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Token=er,or.isCancel=fn,or.VERSION=Xn,or.toFormData=Be,or.AxiosError=Oe,or.Cancel=or.CanceledError,or.all=function(t){return Promise.all(t)},or.spread=function(t){return function(e){return t.apply(null,e)}},or.isAxiosError=function(t){return Te.isObject(t)&&!0===t.isAxiosError},or.mergeConfig=En,or.AxiosHeaders=sn,or.formToJSON=t=>Qe(Te.isHTMLForm(t)?new FormData(t):t),or.getAdapter=Gn,or.HttpStatusCode=rr,or.default=or;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: token=n.response.user_token,le("res.response.user_token","res.response.userToken")),n.response.session_id&&(o.response.session_id=n.response.session_id,le("res.response.session_id","res.response.sessionId")),n.response.images&&!n.response.image&&(o.response.image=n.response.images,le("res.response.images","res.response.image"))),vt(o),oe(o,r.resT),t.extractValue){if(0===o.result)return Tt({platformType:"H5",bridgeName:"getUserInfoOld",timing:O()()-e,logType:i.Timing}),o.response;


📍 headers:
  文件: library-axios.435de88b.js (行 1)
  定义: headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],function cleanHeaderConfig(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function onAdapterResolution(t){return throwIfCancellationRequested(e),t.data=o(t.data,t.headers,e.transformResponse),t},function onAdapterRejection(t){return!i(t)&&(throwIfCancellationRequested(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},80536:function(e,t,r){"use strict";

  文件: library-axios.435de88b.js (行 1)
  定义: headers={common:{Accept:"application/json, text/plain, */*"}},o.forEach(["delete","get","head"],function forEachMethodNoData(e){a.headers[e]={}}),o.forEach(["post","put","patch"],function forEachMethodWithData(e){a.headers[e]=o.merge(s)}),e.exports=a},73685:function(e){"use strict";

  文件: library-axios.435de88b_1.js (行 1)
  定义: headers=e.headers||{},e.data=o(e.data,e.headers,e.transformRequest),e.headers=n.merge(e.headers.common||{},e.headers[e.method]||{},e.headers),n.forEach(["delete","get","head","post","put","patch","common"],function cleanHeaderConfig(t){delete e.headers[t]}),(e.adapter||s.adapter)(e).then(function onAdapterResolution(t){return throwIfCancellationRequested(e),t.data=o(t.data,t.headers,e.transformResponse),t},function onAdapterRejection(t){return!i(t)&&(throwIfCancellationRequested(e),t&&t.response&&(t.response.data=o(t.response.data,t.response.headers,e.transformResponse))),Promise.reject(t)})}},80536:function(e,t,r){"use strict";

  文件: library-axios.435de88b_1.js (行 1)
  定义: headers={common:{Accept:"application/json, text/plain, */*"}},o.forEach(["delete","get","head"],function forEachMethodNoData(e){a.headers[e]={}}),o.forEach(["post","put","patch"],function forEachMethodWithData(e){a.headers[e]=o.merge(s)}),e.exports=a},73685:function(e){"use strict";

  文件: main.7e49175.js (行 2)
  定义: Headers={}),s.each(e.Headers,(function(t,n){"content-length"===n.toLowerCase()&&delete e.Headers[n]})),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,i=0;

  文件: main.7e49175.js (行 2)
  定义: Headers=o,h.multipartInit(i,(function(e,r){if(h._isRunningTask(n)){if(e)return C.emit("error",e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else{var i=[];

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else!n.ReplicationConfiguration&&(n.ReplicationConfiguration={}),n.ReplicationConfiguration.Rule&&(n.ReplicationConfiguration.Rules=o.makeArray(n.ReplicationConfiguration.Rule),delete n.ReplicationConfiguration.Rule),t(e,n)}))}function U(e,t){Qe.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})}))}function F(e,t){if(e.WebsiteConfiguration){var n=o.clone(e.WebsiteConfiguration||{}),r=n.RoutingRules||n.RoutingRule||[];

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175.js (行 2)
  定义: headers=e.headers),t(null,r)}else{var i=[];

  文件: main.7e49175.js (行 2)
  定义: headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=o.clearKey(e.qs),e.headers&&(e.headers=o.clearKey(e.headers)),e.qs&&(e.qs=o.clearKey(e.qs));

  文件: main.7e49175.js (行 2)
  定义: headers=o.clearKey(g.headers)),e.retry&&(g.headers["x-cos-sdk-retry"]=!0),g=o.clearKey(g),e.onProgress&&"function"==typeof e.onProgress){var y=p&&(p.size||p.length)||0;

  文件: main.7e49175.js (行 2)
  定义: headers=l.headers),i)g.url&&(c.url=g.url),g.method&&(c.method=g.method),i=o.extend(i||{},c),t(i,null);

  文件: main.7e49175.js (行 2)
  定义: Headers=j(n)}}return t},ae=function(e,t){return function(n,r){var i,o,a,s=this;

  文件: main.7e49175_1.js (行 2)
  定义: Headers={}),s.each(e.Headers,(function(t,n){"content-length"===n.toLowerCase()&&delete e.Headers[n]})),function(){for(var t=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,5120],r=1048576,i=0;

  文件: main.7e49175_1.js (行 2)
  定义: Headers=o,h.multipartInit(i,(function(e,r){if(h._isRunningTask(n)){if(e)return C.emit("error",e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else{var i=[];

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else!n.ReplicationConfiguration&&(n.ReplicationConfiguration={}),n.ReplicationConfiguration.Rule&&(n.ReplicationConfiguration.Rules=o.makeArray(n.ReplicationConfiguration.Rule),delete n.ReplicationConfiguration.Rule),t(e,n)}))}function U(e,t){Qe.call(this,{Action:"name/cos:DeleteBucketReplication",method:"DELETE",Bucket:e.Bucket,Region:e.Region,headers:e.Headers,action:"replication",tracker:e.tracker},(function(e,n){return e&&204===e.statusCode?t(null,{statusCode:e.statusCode}):e?t(e):void t(null,{statusCode:n.statusCode,headers:n.headers})}))}function F(e,t){if(e.WebsiteConfiguration){var n=o.clone(e.WebsiteConfiguration||{}),r=n.RoutingRules||n.RoutingRule||[];

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else t(e);

  文件: main.7e49175_1.js (行 2)
  定义: headers=e.headers),t(null,r)}else{var i=[];

  文件: main.7e49175_1.js (行 2)
  定义: headers={}),!e.qs&&(e.qs={}),e.VersionId&&(e.qs.versionId=e.VersionId),e.qs=o.clearKey(e.qs),e.headers&&(e.headers=o.clearKey(e.headers)),e.qs&&(e.qs=o.clearKey(e.qs));

  文件: main.7e49175_1.js (行 2)
  定义: headers=o.clearKey(g.headers)),e.retry&&(g.headers["x-cos-sdk-retry"]=!0),g=o.clearKey(g),e.onProgress&&"function"==typeof e.onProgress){var y=p&&(p.size||p.length)||0;

  文件: main.7e49175_1.js (行 2)
  定义: headers=l.headers),i)g.url&&(c.url=g.url),g.method&&(c.method=g.method),i=o.extend(i||{},c),t(i,null);

  文件: main.7e49175_1.js (行 2)
  定义: Headers=j(n)}}return t},ae=function(e,t){return function(n,r){var i,o,a,s=this;

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: headers={}),!p.headers[b]){if(0===p.url.indexOf("/")&&0!==p.url.indexOf("//"))p.headers[b]=generateTraceId();

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: headers=(0,a._)({},e.headers,h,g)}return e}),r.http.interceptors.spam.use(function(e){var r=(null==e?void 0:e.headers)&&e.headers["x-kong-sign"];

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: headers={}),!p.headers[b]){if(0===p.url.indexOf("/")&&0!==p.url.indexOf("//"))p.headers[b]=generateTraceId();

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: headers=(0,a._)({},e.headers,h,g)}return e}),r.http.interceptors.spam.use(function(e){var r=(null==e?void 0:e.headers)&&e.headers["x-kong-sign"];

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=t.headers||{},t.data=o(t.data,t.headers,t.transformRequest),t.headers=r.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||a.adapter)(t).then((function(e){return u(t),e.data=o(e.data,e.headers,t.transformResponse),e}),(function(e){return i(e)||(u(t),e&&e.response&&(e.response.data=o(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},5449:function(t){"use strict";

  文件: vendor-main.e645eae.js (行 2)
  定义: headers={}),!u.headers[et])if(0===g()(c=u.url).call(c,"/")&&0!==g()(s=u.url).call(s,"//"))u.headers[et]=tt();

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=u=sn.from(u),e.url=Ve(xn(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&u.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),Te.isFormData(r))if(Ze.hasStandardBrowserEnv||Ze.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=sn.from(t.headers),t.data=ln.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=sn.from(e.headers),e}),(function(e){return fn(e)||(Wn(t),e&&e.response&&(e.response.data=ln.call(t,t.transformResponse,e.response),e.response.headers=sn.from(e.response.headers))),Promise.reject(e)}))}const Xn="1.8.4",Kn={};

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=sn.concat(i,o);

  文件: vendor-main.e645eae.js (行 2)
  定义: Headers=sn,or.formToJSON=t=>Qe(Te.isHTMLForm(t)?new FormData(t):t),or.getAdapter=Gn,or.HttpStatusCode=rr,or.default=or;

  文件: vendor-main.e645eae.js (行 2)
  定义: headers=mt(mt(mt({},t.headers),h),d)}return t})),e.http.interceptors.spam.use((function(t){var e=(null==t?void 0:t.headers)&&t.headers["x-kong-sign"];

  文件: vendor-main.e645eae.js (行 2)
  定义: headers={}),!c.headers[me])if(0===f()(l=c.url).call(l,"/")&&0!==f()(p=c.url).call(p,"//"))c.headers[me]=ye();

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=t.headers||{},t.data=o(t.data,t.headers,t.transformRequest),t.headers=r.merge(t.headers.common||{},t.headers[t.method]||{},t.headers),r.forEach(["delete","get","head","post","put","patch","common"],(function(e){delete t.headers[e]})),(t.adapter||a.adapter)(t).then((function(e){return u(t),e.data=o(e.data,e.headers,t.transformResponse),e}),(function(e){return i(e)||(u(t),e&&e.response&&(e.response.data=o(e.response.data,e.response.headers,t.transformResponse))),Promise.reject(e)}))}},5449:function(t){"use strict";

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers={}),!u.headers[et])if(0===g()(c=u.url).call(c,"/")&&0!==g()(s=u.url).call(s,"//"))u.headers[et]=tt();

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=u=sn.from(u),e.url=Ve(xn(e.baseURL,e.url,e.allowAbsoluteUrls),t.params,t.paramsSerializer),c&&u.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),Te.isFormData(r))if(Ze.hasStandardBrowserEnv||Ze.hasStandardBrowserWebWorkerEnv)u.setContentType(void 0);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=sn.from(t.headers),t.data=ln.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=sn.from(e.headers),e}),(function(e){return fn(e)||(Wn(t),e&&e.response&&(e.response.data=ln.call(t,t.transformResponse,e.response),e.response.headers=sn.from(e.response.headers))),Promise.reject(e)}))}const Xn="1.8.4",Kn={};

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=sn.concat(i,o);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Headers=sn,or.formToJSON=t=>Qe(Te.isHTMLForm(t)?new FormData(t):t),or.getAdapter=Gn,or.HttpStatusCode=rr,or.default=or;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers=mt(mt(mt({},t.headers),h),d)}return t})),e.http.interceptors.spam.use((function(t){var e=(null==t?void 0:t.headers)&&t.headers["x-kong-sign"];

  文件: vendor-main.e645eae_1.js (行 2)
  定义: headers={}),!c.headers[me])if(0===f()(l=c.url).call(l,"/")&&0!==f()(p=c.url).call(p,"//"))c.headers[me]=ye();


📍 sign:
  文件: main.7e49175.js (行 2)
  定义: sign="+encodeURIComponent(n.Authorization)),n.SecurityToken&&(s+="&x-cos-security-token="+n.SecurityToken),n.ClientIP&&(s+="&clientIP="+n.ClientIP),n.ClientUA&&(s+="&clientUA="+n.ClientUA),n.Token&&(s+="&token="+n.Token),a&&(s+="&"+a),P((function(){t(null,{Url:s})}))}}));

  文件: main.7e49175_1.js (行 2)
  定义: sign="+encodeURIComponent(n.Authorization)),n.SecurityToken&&(s+="&x-cos-security-token="+n.SecurityToken),n.ClientIP&&(s+="&clientIP="+n.ClientIP),n.ClientUA&&(s+="&clientUA="+n.ClientUA),n.Token&&(s+="&token="+n.Token),a&&(s+="&"+a),P((function(){t(null,{Url:s})}))}}));

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: sign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: sign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n);

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n),!t.composing){if(document.activeElement===t&&"range"!==t.type){var c;

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n),It(t,"change",(function(){var e=t._modelValue,n=fe(t),r=t.checked,o=t._assign;

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n),ue(t,e,n)}};

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n),It(t,"change",(function(){t._assign(fe(t))}))},beforeUpdate:function(t,e,n){var r=e.value,o=e.oldValue;

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n),r!==o&&(t.checked=ot(r,n.props.value))}},se={deep:!0,created:function(t,e,n){var r=e.value,o=e.modifiers.number,i=st(r);

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=ne(n)},mounted:function(t,e){le(t,e.value)},beforeUpdate:function(t,e,n){t._assign=ne(n)},updated:function(t,e){le(t,e.value)}};

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=__webpack_require__(77915),assign_default=__webpack_require__.n(object_assign),ozone_detector=__webpack_require__(63854),js_cookie=__webpack_require__(80670),concat=__webpack_require__(93966),concat_default=__webpack_require__.n(concat),stringify=__webpack_require__(44848),stringify_default=__webpack_require__.n(stringify),for_each=__webpack_require__(83137),for_each_default=__webpack_require__.n(for_each),is_array=__webpack_require__(3014),is_array_default=__webpack_require__.n(is_array),set_timeout=__webpack_require__(81356),set_timeout_default=__webpack_require__.n(set_timeout),some=__webpack_require__(29754),some_default=__webpack_require__.n(some),core_js_url=__webpack_require__(20595),url_default=__webpack_require__.n(core_js_url),iterator=__webpack_require__(38657),iterator_default=__webpack_require__.n(iterator),symbol=__webpack_require__(49152),symbol_default=__webpack_require__.n(symbol),promise=__webpack_require__(74703),promise_default=__webpack_require__.n(promise),fill=__webpack_require__(70119),fill_default=__webpack_require__.n(fill),map=__webpack_require__(79346),map_default=__webpack_require__.n(map),index_of=__webpack_require__(49360),index_of_default=__webpack_require__.n(index_of),PULL_BLOCK_STATUS=461,NONE_FINGERPRINT_STATUS=462,RISK_LOGIN_STATUS=465,RISK_SPAM_STATUS=471,const_ORGANIZATION="eR46sBuqF0fdw7KWFLYa",RC4_SECRET_VERSION="1",LOCAL_ID_SECRET_VERSION="0",RC4_SECRET_VERSION_KEY="b1b1",LOCAL_ID_KEY="a1",WEB_ID_KEY="webId",GID="gid",MINI_BROSWER_INFO_KEY="b1",PROFILE_COUNT_KEY="p1",PROFILE_TRIGGER_TIME_KEY="ptt",PROFILE_SERVER_TIME_KEY="pst",SIGN_COUNT_KEY="sc",XHS_SIGN="websectiga",XHS_POISON_ID="sec_poison_id",APP_ID_NAME="xsecappid",PLATFORM_CODE_MAP={"Mac PC":0},BLOCKED_HOSTS=["/t.xiaohongshu.com","/c.xiaohongshu.com","spltest.xiaohongshu.com","t2.xiaohongshu.com","t2-test.xiaohongshu.com","lng.xiaohongshu.com","apm-track.xiaohongshu.com","apm-track-test.xiaohongshu.com","fse.xiaohongshu.com","fse.devops.xiaohongshu.com","fesentry.xiaohongshu.com","spider-tracker.xiaohongshu.com"],PROFILE_BLOCKED_PATHS=["/privacy","/privacy/teenager"],scrintingUrl="/api/sec/v1/scripting",sdtSourceUrl="/api/sec/v1/sbtsource",redConfig="/api/redcaptcha/v2/getconfig",scriptingEval="scriptingEval",sdtSourceStorageKey="sdt_source_storage_key",sdtSourceInitKey="sdt_source_init",lastTokenUpdate="last_tiga_update_time",signLackInfo="sign_lack_info",NEED_XSCOMMON_URLS=["fe_api/burdock/v2/user/keyInfo","fe_api/burdock/v2/shield/profile","fe_api/burdock/v2/shield/captcha","fe_api/burdock/v2/shield/registerCanvas","api/sec/v1/shield/webprofile","api/sec/v1/shield/captcha",/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/tags/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/image_stickers/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/other\/notes/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/related/,"/fe_api/burdock/v2/note/post","/api/sns/web","/api/redcaptcha","/api/store/jpd/main"],RISK_ERROR_CODE_MAP={300011:"检测到帐号异常，请稍后重试",300012:"网络连接异常，请检查网络设置后重试",300013:"访问频次异常，请勿频繁操作",300015:"浏览器异常，请尝试更换浏览器后重试"},logName="infra_sec_web_api_walify",verifyLogName="infra_sec_verify_walify",spamLogName="infra_sec_spam_walify",NEED_REAL_TIME_XSCOMMON_URLS=[],version="4.0.8",slice=__webpack_require__(45978),slice_default=__webpack_require__.n(slice),define_property=__webpack_require__(53303),define_property_default=__webpack_require__.n(define_property),parse_int=__webpack_require__(85533),parse_int_default=__webpack_require__.n(parse_int),lookup=[],code="ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5",i=0,len=code.length;

  文件: vendor-main.e645eae.js (行 2)
  定义: sign=function(){return __assign=assign_default()||function(t){for(var e,n=1,r=arguments.length;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n),!t.composing){if(document.activeElement===t&&"range"!==t.type){var c;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n),It(t,"change",(function(){var e=t._modelValue,n=fe(t),r=t.checked,o=t._assign;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n),ue(t,e,n)}};

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n),It(t,"change",(function(){t._assign(fe(t))}))},beforeUpdate:function(t,e,n){var r=e.value,o=e.oldValue;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n),r!==o&&(t.checked=ot(r,n.props.value))}},se={deep:!0,created:function(t,e,n){var r=e.value,o=e.modifiers.number,i=st(r);

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=ne(n)},mounted:function(t,e){le(t,e.value)},beforeUpdate:function(t,e,n){t._assign=ne(n)},updated:function(t,e){le(t,e.value)}};

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=__webpack_require__(77915),assign_default=__webpack_require__.n(object_assign),ozone_detector=__webpack_require__(63854),js_cookie=__webpack_require__(80670),concat=__webpack_require__(93966),concat_default=__webpack_require__.n(concat),stringify=__webpack_require__(44848),stringify_default=__webpack_require__.n(stringify),for_each=__webpack_require__(83137),for_each_default=__webpack_require__.n(for_each),is_array=__webpack_require__(3014),is_array_default=__webpack_require__.n(is_array),set_timeout=__webpack_require__(81356),set_timeout_default=__webpack_require__.n(set_timeout),some=__webpack_require__(29754),some_default=__webpack_require__.n(some),core_js_url=__webpack_require__(20595),url_default=__webpack_require__.n(core_js_url),iterator=__webpack_require__(38657),iterator_default=__webpack_require__.n(iterator),symbol=__webpack_require__(49152),symbol_default=__webpack_require__.n(symbol),promise=__webpack_require__(74703),promise_default=__webpack_require__.n(promise),fill=__webpack_require__(70119),fill_default=__webpack_require__.n(fill),map=__webpack_require__(79346),map_default=__webpack_require__.n(map),index_of=__webpack_require__(49360),index_of_default=__webpack_require__.n(index_of),PULL_BLOCK_STATUS=461,NONE_FINGERPRINT_STATUS=462,RISK_LOGIN_STATUS=465,RISK_SPAM_STATUS=471,const_ORGANIZATION="eR46sBuqF0fdw7KWFLYa",RC4_SECRET_VERSION="1",LOCAL_ID_SECRET_VERSION="0",RC4_SECRET_VERSION_KEY="b1b1",LOCAL_ID_KEY="a1",WEB_ID_KEY="webId",GID="gid",MINI_BROSWER_INFO_KEY="b1",PROFILE_COUNT_KEY="p1",PROFILE_TRIGGER_TIME_KEY="ptt",PROFILE_SERVER_TIME_KEY="pst",SIGN_COUNT_KEY="sc",XHS_SIGN="websectiga",XHS_POISON_ID="sec_poison_id",APP_ID_NAME="xsecappid",PLATFORM_CODE_MAP={"Mac PC":0},BLOCKED_HOSTS=["/t.xiaohongshu.com","/c.xiaohongshu.com","spltest.xiaohongshu.com","t2.xiaohongshu.com","t2-test.xiaohongshu.com","lng.xiaohongshu.com","apm-track.xiaohongshu.com","apm-track-test.xiaohongshu.com","fse.xiaohongshu.com","fse.devops.xiaohongshu.com","fesentry.xiaohongshu.com","spider-tracker.xiaohongshu.com"],PROFILE_BLOCKED_PATHS=["/privacy","/privacy/teenager"],scrintingUrl="/api/sec/v1/scripting",sdtSourceUrl="/api/sec/v1/sbtsource",redConfig="/api/redcaptcha/v2/getconfig",scriptingEval="scriptingEval",sdtSourceStorageKey="sdt_source_storage_key",sdtSourceInitKey="sdt_source_init",lastTokenUpdate="last_tiga_update_time",signLackInfo="sign_lack_info",NEED_XSCOMMON_URLS=["fe_api/burdock/v2/user/keyInfo","fe_api/burdock/v2/shield/profile","fe_api/burdock/v2/shield/captcha","fe_api/burdock/v2/shield/registerCanvas","api/sec/v1/shield/webprofile","api/sec/v1/shield/captcha",/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/tags/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/image_stickers/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/other\/notes/,/fe_api\/burdock\/v2\/note\/[0-9a-zA-Z]+\/related/,"/fe_api/burdock/v2/note/post","/api/sns/web","/api/redcaptcha","/api/store/jpd/main"],RISK_ERROR_CODE_MAP={300011:"检测到帐号异常，请稍后重试",300012:"网络连接异常，请检查网络设置后重试",300013:"访问频次异常，请勿频繁操作",300015:"浏览器异常，请尝试更换浏览器后重试"},logName="infra_sec_web_api_walify",verifyLogName="infra_sec_verify_walify",spamLogName="infra_sec_spam_walify",NEED_REAL_TIME_XSCOMMON_URLS=[],version="4.0.8",slice=__webpack_require__(45978),slice_default=__webpack_require__.n(slice),define_property=__webpack_require__(53303),define_property_default=__webpack_require__.n(define_property),parse_int=__webpack_require__(85533),parse_int_default=__webpack_require__.n(parse_int),lookup=[],code="ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5",i=0,len=code.length;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: sign=function(){return __assign=assign_default()||function(t){for(var e,n=1,r=arguments.length;


📍 signature:
  文件: main.7e49175.js (行 2)
  定义: signature="+c.HmacSHA1(C,b).toString()].join("&")},I=function(e,t,n){var r,i=t/8,o=_e(e).call(e,n,n+i);

  文件: main.7e49175_1.js (行 2)
  定义: signature="+c.HmacSHA1(C,b).toString()].join("&")},I=function(e,t,n){var r,i=t/8,o=_e(e).call(e,n,n+i);


📍 xs:
  文件: main.7e49175.js (行 2)
  定义: var Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputTextarea",e.Upload="Upload",e}(Xs||{});

  文件: main.7e49175.js (行 2)
  定义: Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputTextarea",e.Upload="Upload",e}(Xs||{});

  文件: main.7e49175_1.js (行 2)
  定义: var Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputTextarea",e.Upload="Upload",e}(Xs||{});

  文件: main.7e49175_1.js (行 2)
  定义: Xs=function(e){return e.Text="Text",e.TagCheck="TagCheck",e.Input="Input",e.InputTextarea="InputTextarea",e.Upload="Upload",e}(Xs||{});


📍 header:
  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: Header=function(){var e=arguments[0],r=arguments[1];

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: header=(0,c.merge)(u.header,a),u.body=s||{},u}},13398:function(e,r,i){"use strict";

  文件: vendor-dynamic.f0f5c43a.js (行 1)
  定义: header=p._(d._({},s.header),{authorization:null===(a=this.bindCtx)||void 0===a?void 0:a.sid}));

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: Header=function(){var e=arguments[0],r=arguments[1];

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: header=(0,c.merge)(u.header,a),u.body=s||{},u}},13398:function(e,r,i){"use strict";

  文件: vendor-dynamic.f0f5c43a_1.js (行 1)
  定义: header=p._(d._({},s.header),{authorization:null===(a=this.bindCtx)||void 0===a?void 0:a.sid}));

  文件: vendor-main.e645eae.js (行 2)
  定义: Header=st.prototype.Oa,st.prototype.Lc=function(){var t=this.c;

  文件: vendor-main.e645eae.js (行 2)
  定义: Header=function(){var t=arguments[0],r=arguments[1];

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Header=st.prototype.Oa,st.prototype.Lc=function(){var t=this.c;

  文件: vendor-main.e645eae_1.js (行 2)
  定义: Header=function(){var t=arguments[0],r=arguments[1];

  文件: vendor.621a7319.js (行 1)
  定义: HEADER=new goog.dom.TagName("HEADER"),goog.dom.TagName.HGROUP=new goog.dom.TagName("HGROUP"),goog.dom.TagName.HR=new goog.dom.TagName("HR"),goog.dom.TagName.HTML=new goog.dom.TagName("HTML"),goog.dom.TagName.I=new goog.dom.TagName("I"),goog.dom.TagName.IFRAME=new goog.dom.TagName("IFRAME"),goog.dom.TagName.IMG=new goog.dom.TagName("IMG"),goog.dom.TagName.INPUT=new goog.dom.TagName("INPUT"),goog.dom.TagName.INS=new goog.dom.TagName("INS"),goog.dom.TagName.ISINDEX=new goog.dom.TagName("ISINDEX"),goog.dom.TagName.KBD=new goog.dom.TagName("KBD"),goog.dom.TagName.KEYGEN=new goog.dom.TagName("KEYGEN"),goog.dom.TagName.LABEL=new goog.dom.TagName("LABEL"),goog.dom.TagName.LEGEND=new goog.dom.TagName("LEGEND"),goog.dom.TagName.LI=new goog.dom.TagName("LI"),goog.dom.TagName.LINK=new goog.dom.TagName("LINK"),goog.dom.TagName.MAIN=new goog.dom.TagName("MAIN"),goog.dom.TagName.MAP=new goog.dom.TagName("MAP"),goog.dom.TagName.MARK=new goog.dom.TagName("MARK"),goog.dom.TagName.MATH=new goog.dom.TagName("MATH"),goog.dom.TagName.MENU=new goog.dom.TagName("MENU"),goog.dom.TagName.MENUITEM=new goog.dom.TagName("MENUITEM"),goog.dom.TagName.META=new goog.dom.TagName("META"),goog.dom.TagName.METER=new goog.dom.TagName("METER"),goog.dom.TagName.NAV=new goog.dom.TagName("NAV"),goog.dom.TagName.NOFRAMES=new goog.dom.TagName("NOFRAMES"),goog.dom.TagName.NOSCRIPT=new goog.dom.TagName("NOSCRIPT"),goog.dom.TagName.OBJECT=new goog.dom.TagName("OBJECT"),goog.dom.TagName.OL=new goog.dom.TagName("OL"),goog.dom.TagName.OPTGROUP=new goog.dom.TagName("OPTGROUP"),goog.dom.TagName.OPTION=new goog.dom.TagName("OPTION"),goog.dom.TagName.OUTPUT=new goog.dom.TagName("OUTPUT"),goog.dom.TagName.P=new goog.dom.TagName("P"),goog.dom.TagName.PARAM=new goog.dom.TagName("PARAM"),goog.dom.TagName.PICTURE=new goog.dom.TagName("PICTURE"),goog.dom.TagName.PRE=new goog.dom.TagName("PRE"),goog.dom.TagName.PROGRESS=new goog.dom.TagName("PROGRESS"),goog.dom.TagName.Q=new goog.dom.TagName("Q"),goog.dom.TagName.RP=new goog.dom.TagName("RP"),goog.dom.TagName.RT=new goog.dom.TagName("RT"),goog.dom.TagName.RTC=new goog.dom.TagName("RTC"),goog.dom.TagName.RUBY=new goog.dom.TagName("RUBY"),goog.dom.TagName.S=new goog.dom.TagName("S"),goog.dom.TagName.SAMP=new goog.dom.TagName("SAMP"),goog.dom.TagName.SCRIPT=new goog.dom.TagName("SCRIPT"),goog.dom.TagName.SECTION=new goog.dom.TagName("SECTION"),goog.dom.TagName.SELECT=new goog.dom.TagName("SELECT"),goog.dom.TagName.SMALL=new goog.dom.TagName("SMALL"),goog.dom.TagName.SOURCE=new goog.dom.TagName("SOURCE"),goog.dom.TagName.SPAN=new goog.dom.TagName("SPAN"),goog.dom.TagName.STRIKE=new goog.dom.TagName("STRIKE"),goog.dom.TagName.STRONG=new goog.dom.TagName("STRONG"),goog.dom.TagName.STYLE=new goog.dom.TagName("STYLE"),goog.dom.TagName.SUB=new goog.dom.TagName("SUB"),goog.dom.TagName.SUMMARY=new goog.dom.TagName("SUMMARY"),goog.dom.TagName.SUP=new goog.dom.TagName("SUP"),goog.dom.TagName.SVG=new goog.dom.TagName("SVG"),goog.dom.TagName.TABLE=new goog.dom.TagName("TABLE"),goog.dom.TagName.TBODY=new goog.dom.TagName("TBODY"),goog.dom.TagName.TD=new goog.dom.TagName("TD"),goog.dom.TagName.TEMPLATE=new goog.dom.TagName("TEMPLATE"),goog.dom.TagName.TEXTAREA=new goog.dom.TagName("TEXTAREA"),goog.dom.TagName.TFOOT=new goog.dom.TagName("TFOOT"),goog.dom.TagName.TH=new goog.dom.TagName("TH"),goog.dom.TagName.THEAD=new goog.dom.TagName("THEAD"),goog.dom.TagName.TIME=new goog.dom.TagName("TIME"),goog.dom.TagName.TITLE=new goog.dom.TagName("TITLE"),goog.dom.TagName.TR=new goog.dom.TagName("TR"),goog.dom.TagName.TRACK=new goog.dom.TagName("TRACK"),goog.dom.TagName.TT=new goog.dom.TagName("TT"),goog.dom.TagName.U=new goog.dom.TagName("U"),goog.dom.TagName.UL=new goog.dom.TagName("UL"),goog.dom.TagName.VAR=new goog.dom.TagName("VAR"),goog.dom.TagName.VIDEO=new goog.dom.TagName("VIDEO"),goog.dom.TagName.WBR=new goog.dom.TagName("WBR"),goog.dom.tags={},goog.dom.tags.VOID_TAGS_={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},goog.dom.tags.isVoidTag=function(A){return!0===goog.dom.tags.VOID_TAGS_[A]},goog.html={},goog.html.trustedtypes={},goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#html"):null,goog.string={},goog.string.TypedString=function(){},goog.string.Const=function(A,B){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=A===goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_&&B||"",this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=goog.string.Const.TYPE_MARKER_},goog.string.Const.prototype.implementsGoogStringTypedString=!0,goog.string.Const.prototype.getTypedStringValue=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_},goog.DEBUG&&(goog.string.Const.prototype.toString=function(){return"Const{"+this.stringConstValueWithSecurityContract__googStringSecurityPrivate_+"}"}),goog.string.Const.unwrap=function(A){return A instanceof goog.string.Const&&A.constructor===goog.string.Const&&A.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===goog.string.Const.TYPE_MARKER_?A.stringConstValueWithSecurityContract__googStringSecurityPrivate_:(goog.asserts.fail("expected object of type Const, got '"+A+"'"),"type_error:Const")},goog.string.Const.from=function(A){return new goog.string.Const(goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_,A)},goog.string.Const.TYPE_MARKER_={},goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.Const.EMPTY=goog.string.Const.from(""),goog.html.SafeScript=function(){this.privateDoNotAccessOrElseSafeScriptWrappedValue_="",this.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeScript.prototype.implementsGoogStringTypedString=!0,goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeScript.fromConstant=function(A){return 0===(A=goog.string.Const.unwrap(A)).length?goog.html.SafeScript.EMPTY:goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeScript.fromConstantAndArgs=function(A,B){for(var N=[],U=1;

  文件: vendor.621a7319.js (行 1)
  定义: Header=function(){this.decoder_.unskipVarint(this.nextField_<<3|this.nextWireType_)},jspb.BinaryReader.prototype.skipMatchingFields=function(){var A=this.nextField_;

  文件: vendor.621a7319_1.js (行 1)
  定义: HEADER=new goog.dom.TagName("HEADER"),goog.dom.TagName.HGROUP=new goog.dom.TagName("HGROUP"),goog.dom.TagName.HR=new goog.dom.TagName("HR"),goog.dom.TagName.HTML=new goog.dom.TagName("HTML"),goog.dom.TagName.I=new goog.dom.TagName("I"),goog.dom.TagName.IFRAME=new goog.dom.TagName("IFRAME"),goog.dom.TagName.IMG=new goog.dom.TagName("IMG"),goog.dom.TagName.INPUT=new goog.dom.TagName("INPUT"),goog.dom.TagName.INS=new goog.dom.TagName("INS"),goog.dom.TagName.ISINDEX=new goog.dom.TagName("ISINDEX"),goog.dom.TagName.KBD=new goog.dom.TagName("KBD"),goog.dom.TagName.KEYGEN=new goog.dom.TagName("KEYGEN"),goog.dom.TagName.LABEL=new goog.dom.TagName("LABEL"),goog.dom.TagName.LEGEND=new goog.dom.TagName("LEGEND"),goog.dom.TagName.LI=new goog.dom.TagName("LI"),goog.dom.TagName.LINK=new goog.dom.TagName("LINK"),goog.dom.TagName.MAIN=new goog.dom.TagName("MAIN"),goog.dom.TagName.MAP=new goog.dom.TagName("MAP"),goog.dom.TagName.MARK=new goog.dom.TagName("MARK"),goog.dom.TagName.MATH=new goog.dom.TagName("MATH"),goog.dom.TagName.MENU=new goog.dom.TagName("MENU"),goog.dom.TagName.MENUITEM=new goog.dom.TagName("MENUITEM"),goog.dom.TagName.META=new goog.dom.TagName("META"),goog.dom.TagName.METER=new goog.dom.TagName("METER"),goog.dom.TagName.NAV=new goog.dom.TagName("NAV"),goog.dom.TagName.NOFRAMES=new goog.dom.TagName("NOFRAMES"),goog.dom.TagName.NOSCRIPT=new goog.dom.TagName("NOSCRIPT"),goog.dom.TagName.OBJECT=new goog.dom.TagName("OBJECT"),goog.dom.TagName.OL=new goog.dom.TagName("OL"),goog.dom.TagName.OPTGROUP=new goog.dom.TagName("OPTGROUP"),goog.dom.TagName.OPTION=new goog.dom.TagName("OPTION"),goog.dom.TagName.OUTPUT=new goog.dom.TagName("OUTPUT"),goog.dom.TagName.P=new goog.dom.TagName("P"),goog.dom.TagName.PARAM=new goog.dom.TagName("PARAM"),goog.dom.TagName.PICTURE=new goog.dom.TagName("PICTURE"),goog.dom.TagName.PRE=new goog.dom.TagName("PRE"),goog.dom.TagName.PROGRESS=new goog.dom.TagName("PROGRESS"),goog.dom.TagName.Q=new goog.dom.TagName("Q"),goog.dom.TagName.RP=new goog.dom.TagName("RP"),goog.dom.TagName.RT=new goog.dom.TagName("RT"),goog.dom.TagName.RTC=new goog.dom.TagName("RTC"),goog.dom.TagName.RUBY=new goog.dom.TagName("RUBY"),goog.dom.TagName.S=new goog.dom.TagName("S"),goog.dom.TagName.SAMP=new goog.dom.TagName("SAMP"),goog.dom.TagName.SCRIPT=new goog.dom.TagName("SCRIPT"),goog.dom.TagName.SECTION=new goog.dom.TagName("SECTION"),goog.dom.TagName.SELECT=new goog.dom.TagName("SELECT"),goog.dom.TagName.SMALL=new goog.dom.TagName("SMALL"),goog.dom.TagName.SOURCE=new goog.dom.TagName("SOURCE"),goog.dom.TagName.SPAN=new goog.dom.TagName("SPAN"),goog.dom.TagName.STRIKE=new goog.dom.TagName("STRIKE"),goog.dom.TagName.STRONG=new goog.dom.TagName("STRONG"),goog.dom.TagName.STYLE=new goog.dom.TagName("STYLE"),goog.dom.TagName.SUB=new goog.dom.TagName("SUB"),goog.dom.TagName.SUMMARY=new goog.dom.TagName("SUMMARY"),goog.dom.TagName.SUP=new goog.dom.TagName("SUP"),goog.dom.TagName.SVG=new goog.dom.TagName("SVG"),goog.dom.TagName.TABLE=new goog.dom.TagName("TABLE"),goog.dom.TagName.TBODY=new goog.dom.TagName("TBODY"),goog.dom.TagName.TD=new goog.dom.TagName("TD"),goog.dom.TagName.TEMPLATE=new goog.dom.TagName("TEMPLATE"),goog.dom.TagName.TEXTAREA=new goog.dom.TagName("TEXTAREA"),goog.dom.TagName.TFOOT=new goog.dom.TagName("TFOOT"),goog.dom.TagName.TH=new goog.dom.TagName("TH"),goog.dom.TagName.THEAD=new goog.dom.TagName("THEAD"),goog.dom.TagName.TIME=new goog.dom.TagName("TIME"),goog.dom.TagName.TITLE=new goog.dom.TagName("TITLE"),goog.dom.TagName.TR=new goog.dom.TagName("TR"),goog.dom.TagName.TRACK=new goog.dom.TagName("TRACK"),goog.dom.TagName.TT=new goog.dom.TagName("TT"),goog.dom.TagName.U=new goog.dom.TagName("U"),goog.dom.TagName.UL=new goog.dom.TagName("UL"),goog.dom.TagName.VAR=new goog.dom.TagName("VAR"),goog.dom.TagName.VIDEO=new goog.dom.TagName("VIDEO"),goog.dom.TagName.WBR=new goog.dom.TagName("WBR"),goog.dom.tags={},goog.dom.tags.VOID_TAGS_={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},goog.dom.tags.isVoidTag=function(A){return!0===goog.dom.tags.VOID_TAGS_[A]},goog.html={},goog.html.trustedtypes={},goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#html"):null,goog.string={},goog.string.TypedString=function(){},goog.string.Const=function(A,B){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=A===goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_&&B||"",this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=goog.string.Const.TYPE_MARKER_},goog.string.Const.prototype.implementsGoogStringTypedString=!0,goog.string.Const.prototype.getTypedStringValue=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_},goog.DEBUG&&(goog.string.Const.prototype.toString=function(){return"Const{"+this.stringConstValueWithSecurityContract__googStringSecurityPrivate_+"}"}),goog.string.Const.unwrap=function(A){return A instanceof goog.string.Const&&A.constructor===goog.string.Const&&A.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===goog.string.Const.TYPE_MARKER_?A.stringConstValueWithSecurityContract__googStringSecurityPrivate_:(goog.asserts.fail("expected object of type Const, got '"+A+"'"),"type_error:Const")},goog.string.Const.from=function(A){return new goog.string.Const(goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_,A)},goog.string.Const.TYPE_MARKER_={},goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.Const.EMPTY=goog.string.Const.from(""),goog.html.SafeScript=function(){this.privateDoNotAccessOrElseSafeScriptWrappedValue_="",this.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeScript.prototype.implementsGoogStringTypedString=!0,goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeScript.fromConstant=function(A){return 0===(A=goog.string.Const.unwrap(A)).length?goog.html.SafeScript.EMPTY:goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeScript.fromConstantAndArgs=function(A,B){for(var N=[],U=1;

  文件: vendor.621a7319_1.js (行 1)
  定义: Header=function(){this.decoder_.unskipVarint(this.nextField_<<3|this.nextWireType_)},jspb.BinaryReader.prototype.skipMatchingFields=function(){var A=this.nextField_;

