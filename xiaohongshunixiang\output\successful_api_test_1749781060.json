[{"endpoint": {"url": "/api/sns/web/v2/user/me", "method": "GET", "description": "获取用户信息", "data": {}}, "success": true, "response": {"msg": "成功", "data": {"red_id": "18944963553", "user_id": "6843fccc000000001b022b38", "nickname": "小红薯6845435D", "desc": "还没有简介", "gender": 2, "images": "https://sns-avatar-qc.xhscdn.com/avatar/645b7f3c86578b8c6ab3b068.jpg?imageView2/2/w/360/format/webp", "imageb": "https://sns-avatar-qc.xhscdn.com/avatar/645b7f3c86578b8c6ab3b068.jpg?imageView2/2/w/540/format/webp", "guest": false}, "code": 0, "success": true}}, {"endpoint": {"url": "/api/sns/web/v1/system/config", "method": "GET", "description": "获取系统配置", "data": {}}, "success": true, "response": {"code": 0, "success": true, "msg": "成功", "data": {"downgrade_user_register": false}}}, {"endpoint": {"url": "/api/sns/web/v1/homefeed", "method": "GET", "description": "获取首页推荐", "data": {"cursor_score": "", "num": 31, "refresh_type": 1}}, "success": false, "response": "接口不存在"}, {"endpoint": {"url": "/api/sns/web/v1/search/notes", "method": "GET", "description": "搜索笔记", "data": {"keyword": "美食", "page": 1, "page_size": 20}}, "success": false, "response": "接口不存在"}, {"endpoint": {"url": "/api/sns/web/v1/note/detail", "method": "GET", "description": "获取笔记详情", "data": {"note_id": "6566c7a8000000001203e8c7"}}, "success": false, "response": "接口不存在"}, {"endpoint": {"url": "/api/sns/web/v1/user/otherinfo", "method": "GET", "description": "获取其他用户信息", "data": {"target_user_id": "5ff0e6410000000001008400"}}, "success": false, "response": "HTTP 406"}, {"endpoint": {"url": "/api/sns/web/v1/comment/page", "method": "GET", "description": "获取评论列表", "data": {"note_id": "6566c7a8000000001203e8c7", "cursor": "", "top_comment_id": ""}}, "success": false, "response": "接口不存在"}, {"endpoint": {"url": "/api/sns/web/v1/login/activate", "method": "POST", "description": "激活登录状态", "data": {}}, "success": false, "response": "HTTP 406"}]