// 🔍 深度调试脚本 - 追踪 x-s-common 真正的生成算法

(() => {
    console.log('🚀 开始深度调试 x-s-common 生成算法...');
    
    // 1. 保存原始函数引用
    const original_webmsxyw = window._webmsxyw;
    
    // 2. Hook _webmsxyw 函数来追踪内部调用
    if (typeof window._webmsxyw === 'function') {
        console.log('✅ 找到 _webmsxyw 函数，开始 Hook...');
        
        window._webmsxyw = function(...args) {
            console.log('🔍 _webmsxyw 被调用，参数:', args);
            
            // 在调用前设置断点追踪
            debugger; // 这里会暂停，可以单步调试
            
            const result = original_webmsxyw.apply(this, args);
            
            console.log('📤 _webmsxyw 返回结果:', result);
            
            // 如果返回结果包含 x-s-common，进一步分析
            if (result && result['x-s-common']) {
                console.log('🔑 生成的 x-s-common:', result['x-s-common']);
                
                // 尝试解码分析
                try {
                    const decoded = atob(result['x-s-common']);
                    console.log('🔓 x-s-common 解码结果:', decoded);
                } catch (e) {
                    console.log('❌ x-s-common 解码失败，可能使用自定义编码');
                }
            }
            
            return result;
        };
    }
    
    // 3. Hook 所有可能的编码函数
    const hookEncodingFunctions = () => {
        // Hook atob/btoa
        const originalAtob = window.atob;
        const originalBtoa = window.btoa;
        
        window.atob = function(str) {
            console.log('🔓 atob 被调用:', str.substring(0, 50) + '...');
            return originalAtob.call(this, str);
        };
        
        window.btoa = function(str) {
            const result = originalBtoa.call(this, str);
            console.log('🔐 btoa 被调用:', str.substring(0, 50) + '... -> ' + result.substring(0, 50) + '...');
            return result;
        };
        
        // Hook JSON.stringify
        const originalStringify = JSON.stringify;
        JSON.stringify = function(...args) {
            const result = originalStringify.apply(this, args);
            if (args[0] && typeof args[0] === 'object') {
                const obj = args[0];
                if (obj.hasOwnProperty('s0') || obj.hasOwnProperty('x0') || 
                    obj.hasOwnProperty('x-s-common') || obj.hasOwnProperty('signature')) {
                    console.log('📝 JSON.stringify 处理签名相关对象:', obj);
                    console.log('📤 JSON.stringify 结果:', result);
                }
            }
            return result;
        };
    };
    
    hookEncodingFunctions();
    
    // 4. 搜索所有可能包含签名算法的函数
    const findSignatureAlgorithms = () => {
        const algorithms = [];
        
        // 搜索全局对象
        for (let key in window) {
            try {
                const obj = window[key];
                if (typeof obj === 'function') {
                    const funcStr = obj.toString();
                    
                    // 查找包含特定模式的函数 - 包含所有大小写组合
                    if (funcStr.includes('x-s-common') ||
                        funcStr.includes('X-S-Common') ||
                        funcStr.includes('X-s-common') ||
                        funcStr.includes('x-S-Common') ||
                        funcStr.includes('X-S-COMMON') ||
                        funcStr.includes('x-s-COMMON') ||
                        funcStr.includes('base64') ||
                        funcStr.includes('Base64') ||
                        funcStr.includes('BASE64') ||
                        funcStr.includes('encode') ||
                        funcStr.includes('Encode') ||
                        funcStr.includes('ENCODE') ||
                        funcStr.includes('encrypt') ||
                        funcStr.includes('Encrypt') ||
                        funcStr.includes('ENCRYPT') ||
                        funcStr.includes('signature') ||
                        funcStr.includes('Signature') ||
                        funcStr.includes('SIGNATURE') ||
                        funcStr.includes('crc') ||
                        funcStr.includes('CRC') ||
                        funcStr.includes('md5') ||
                        funcStr.includes('MD5') ||
                        funcStr.includes('sha') ||
                        funcStr.includes('SHA') ||
                        funcStr.match(/[a-zA-Z]{50,}/) || // 长字符串常量
                        funcStr.match(/\[.*,.*,.*\]/) || // 数组常量
                        funcStr.includes('0x')) { // 十六进制常量
                        
                        algorithms.push({
                            name: key,
                            source: funcStr,
                            type: 'potential_algorithm'
                        });
                    }
                }
            } catch (e) {}
        }
        
        return algorithms;
    };
    
    // 5. 深度搜索脚本内容
    const searchScriptContents = () => {
        const scripts = document.querySelectorAll('script');
        const relevantCode = [];
        
        scripts.forEach((script, index) => {
            if (script.textContent) {
                const content = script.textContent;
                
                // 查找包含算法特征的代码段 - 包含所有大小写组合
                const patterns = [
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[xX]-[sS]-[cC]ommon[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[eE]ncode[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[eE]ncrypt[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[sS]ignature[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[bB]ase64[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[cC][rR][cC][^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[mM][dD]5[^}]*\}/gi,
                    /function\s+\w*\s*\([^)]*\)\s*\{[^}]*[sS][hH][aA][^}]*\}/gi,
                    /[xX]-[sS]-[cC]ommon/gi, // 直接搜索 X-S-Common
                    /[xX]-[sS]/gi, // 搜索 X-S
                    /[xX]-[tT]/gi, // 搜索 X-T
                    /\[[0-9,\s]{100,}\]/g, // 长数组（可能是查找表）
                    /"[A-Za-z0-9+/]{50,}"/g, // 长字符串（可能是密钥）
                ];
                
                patterns.forEach(pattern => {
                    const matches = content.match(pattern);
                    if (matches) {
                        matches.forEach(match => {
                            relevantCode.push({
                                scriptIndex: index,
                                pattern: pattern.toString(),
                                code: match
                            });
                        });
                    }
                });
            }
        });
        
        return relevantCode;
    };
    
    // 6. 执行深度搜索
    console.log('🔍 搜索签名算法...');
    const algorithms = findSignatureAlgorithms();
    console.log('📊 找到的潜在算法函数:', algorithms.length);
    algorithms.forEach((alg, index) => {
        console.log(`🔧 算法 ${index + 1}: ${alg.name}`);
        if (alg.source.length < 1000) {
            console.log('📝 源码:', alg.source);
        } else {
            console.log('📝 源码 (截断):', alg.source.substring(0, 500) + '...');
        }
    });
    
    console.log('🔍 搜索脚本内容...');
    const scriptCode = searchScriptContents();
    console.log('📊 找到的相关代码段:', scriptCode.length);
    scriptCode.forEach((code, index) => {
        console.log(`📜 代码段 ${index + 1}:`, code);
    });
    
    // 7. 保存结果到全局变量
    window.deepDebugResults = {
        algorithms: algorithms,
        scriptCode: scriptCode,
        timestamp: Date.now()
    };
    
    console.log('✅ 深度调试完成！');
    console.log('💾 结果已保存到 window.deepDebugResults');
    console.log('📋 使用 copy(window.deepDebugResults) 复制结果');
    
    return {
        message: '深度调试脚本已注入',
        algorithmsFound: algorithms.length,
        codeSegmentsFound: scriptCode.length
    };
})();
