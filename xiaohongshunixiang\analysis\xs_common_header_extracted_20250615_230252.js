// X-S-Common 请求头生成函数及其依赖提取
// 提取时间: 2025-06-15 23:02:52

// ========== x-s-common 相关代码 #1 ==========
// 来源: index.788b3226.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function clickHeader(){
    b.value=!1
}


// ========== x-s-common 相关代码 #2 ==========
// 来源: index.788b3226.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function getHeaderPageWithInstanceId(e){
    return{
    type:"Page",
    value:{
    pageInstance:{
    type:"PageInstance",
    value:"web_header_page"
}
,
    instanceId:e
}

}

}


// ========== x-s-common 相关代码 #3 ==========
// 来源: index.788b3226.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function headerPoint(e,
    t,
    n){
    return _headerPoint.apply(this,
    arguments)
}


// ========== x-s-common 相关代码 #4 ==========
// 来源: index.788b3226.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function _headerPoint(){
    return(_headerPoint=(0,
    r._)(function(e,
    t,
    n){
    var r;
return(0,
    o.Jh)(this,
    function(o){
    switch(o.label){
    case 0:return[4,
    getTrackData(e,
    t)];
case 1:return r=o.sent(),
    n&&n.length>0?r.page=getHeaderPageWithInstanceId(n):r.page=p,
    a.Q.push(r),
    [2]
}

}
)
}
)).apply(this,
    arguments)
}


// ========== x-s-common 相关代码 #5 ==========
// 来源: index.788b3226_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function clickHeader(){
    b.value=!1
}


// ========== x-s-common 相关代码 #6 ==========
// 来源: index.788b3226_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function getHeaderPageWithInstanceId(e){
    return{
    type:"Page",
    value:{
    pageInstance:{
    type:"PageInstance",
    value:"web_header_page"
}
,
    instanceId:e
}

}

}


// ========== x-s-common 相关代码 #7 ==========
// 来源: index.788b3226_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function headerPoint(e,
    t,
    n){
    return _headerPoint.apply(this,
    arguments)
}


// ========== x-s-common 相关代码 #8 ==========
// 来源: index.788b3226_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function _headerPoint(){
    return(_headerPoint=(0,
    r._)(function(e,
    t,
    n){
    var r;
return(0,
    o.Jh)(this,
    function(o){
    switch(o.label){
    case 0:return[4,
    getTrackData(e,
    t)];
case 1:return r=o.sent(),
    n&&n.length>0?r.page=getHeaderPageWithInstanceId(n):r.page=p,
    a.Q.push(r),
    [2]
}

}
)
}
)).apply(this,
    arguments)
}


// ========== x-s-common 相关代码 #9 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=merge(e[r],
    t):e[r]=t
}


// ========== x-s-common 相关代码 #10 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge(e[r],
    t):(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge({
    
}
,
    t):e[r]=t
}


// ========== x-s-common 相关代码 #11 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=o(t,
    r):e[n]=t
}


// ========== x-s-common 相关代码 #12 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    isPlainObject(e[r])&&isPlainObject(t)?e[r]=merge(e[r],
    t):isPlainObject(t)?e[r]=merge({
    
}
,
    t):isArray(t)?e[r]=t.slice():e[r]=t
}


// ========== x-s-common 相关代码 #13 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=a(t,
    r):e[n]=t
}


// ========== x-s-common 相关代码 #14 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function setRequestHeader(e,
    t){
    void 0===l&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,
    e)
}


// ========== x-s-common 相关代码 #15 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function cleanHeaderConfig(t){
    delete e.headers[t]
}


// ========== x-s-common 相关代码 #16 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function normalizeHeaderName(e,
    t){
    n.forEach(e,
    function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}
)
}


// ========== x-s-common 相关代码 #17 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}


// ========== x-s-common 相关代码 #18 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.substr(0,
    i)).toLowerCase(),
    r=n.trim(e.substr(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}


// ========== x-s-common 相关代码 #19 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function setRequestHeader(e,
    t){
    void 0===v&&"content-type"===t.toLowerCase()?delete g[t]:E.setRequestHeader(t,
    e)
}


// ========== x-s-common 相关代码 #20 ==========
// 来源: library-axios.435de88b.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.slice(0,
    i)).toLowerCase(),
    r=n.trim(e.slice(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}


// ========== x-s-common 相关代码 #21 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=merge(e[r],
    t):e[r]=t
}


// ========== x-s-common 相关代码 #22 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    "object"===n._(e[r])&&(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge(e[r],
    t):(void 0===t?"undefined":n._(t))==="object"?e[r]=deepMerge({
    
}
,
    t):e[r]=t
}


// ========== x-s-common 相关代码 #23 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=o(t,
    r):e[n]=t
}


// ========== x-s-common 相关代码 #24 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    r){
    isPlainObject(e[r])&&isPlainObject(t)?e[r]=merge(e[r],
    t):isPlainObject(t)?e[r]=merge({
    
}
,
    t):isArray(t)?e[r]=t.slice():e[r]=t
}


// ========== x-s-common 相关代码 #25 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(t,
    n){
    r&&"function"==typeof t?e[n]=a(t,
    r):e[n]=t
}


// ========== x-s-common 相关代码 #26 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function setRequestHeader(e,
    t){
    void 0===l&&"content-type"===t.toLowerCase()?delete d[t]:p.setRequestHeader(t,
    e)
}


// ========== x-s-common 相关代码 #27 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function cleanHeaderConfig(t){
    delete e.headers[t]
}


// ========== x-s-common 相关代码 #28 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function normalizeHeaderName(e,
    t){
    n.forEach(e,
    function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}
)
}


// ========== x-s-common 相关代码 #29 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function processHeader(r,
    n){
    n!==t&&n.toUpperCase()===t.toUpperCase()&&(e[t]=r,
    delete e[n])
}


// ========== x-s-common 相关代码 #30 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.substr(0,
    i)).toLowerCase(),
    r=n.trim(e.substr(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}


// ========== x-s-common 相关代码 #31 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function setRequestHeader(e,
    t){
    void 0===v&&"content-type"===t.toLowerCase()?delete g[t]:E.setRequestHeader(t,
    e)
}


// ========== x-s-common 相关代码 #32 ==========
// 来源: library-axios.435de88b_1.js 行 1
// 匹配模式: function\s+(\w*[Hh]eader\w*)\s*\([^)]*\)
// 完整函数:
function parseHeaders(e){
    var t,
    r,
    i,
    s={
    
}
;
return e?(n.forEach(e.split("\n"),
    function parser(e){
    if(i=e.indexOf(":"),
    t=n.trim(e.slice(0,
    i)).toLowerCase(),
    r=n.trim(e.slice(i+1)),
    t){
    if(!(s[t]&&o.indexOf(t)>=0))"set-cookie"===t?s[t]=(s[t]?s[t]:[]).concat([r]):s[t]=s[t]?s[t]+",
    "+r:r
}

}
),
    s):s
}


// ========== x-s-common 相关代码 #33 ==========
// 来源: library-polyfill.5f7e25b2.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// ========== x-s-common 相关代码 #34 ==========
// 来源: library-polyfill.5f7e25b2.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}
return e
}


// ========== x-s-common 相关代码 #35 ==========
// 来源: library-polyfill.5f7e25b2_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// ========== x-s-common 相关代码 #36 ==========
// 来源: library-polyfill.5f7e25b2_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}
return e
}


// ========== x-s-common 相关代码 #37 ==========
// 来源: library-vue.a552caa8.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonEncode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}


// ========== x-s-common 相关代码 #38 ==========
// 来源: library-vue.a552caa8_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonEncode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}


// ========== x-s-common 相关代码 #39 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
// 匹配代码:
headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #40 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
// 匹配代码:
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #41 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: x-s-common[^=]*=\s*[^;]+
// 匹配代码:
X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #42 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(e,
    r){
    "object"===(0,
    d._)(a[r])&&(void 0===e?"undefined":(0,
    d._)(e))==="object"?a[r]=merge(a[r],
    e):a[r]=e
}


// ========== x-s-common 相关代码 #43 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function encrypt_sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}
return r
}
,
    a="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    s="iamspam",
    u=new Date().getTime(),
    c="undefined"==typeof window?i.g:window;
return void 0!==c&&c&&c.navigator&&c.navigator.userAgent&&c.alert&&(s="test"),
    {
    "X-s":function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}
(N([u,
    s,
    e,
    "[object Object]"===Object.prototype.toString.call(r)||"[object Array]"===Object.prototype.toString.call(r)?JSON.stringify(r):""].join(""))),
    "X-t":u
}

}


// ========== x-s-common 相关代码 #44 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function utils_shouldSign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}
),
    r)
}


// ========== x-s-common 相关代码 #45 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function SignReload(){
    this.count=1,
    this.time=+new Date
}


// ========== x-s-common 相关代码 #46 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shouldSignReload(){
    try{
    var e=+new Date,
    r=JSON.stringify(localStorage.getItem(T)||{
    
}
),
    i=!!(r&&r.count),
    a=r&&r.time&&e-r.time<36e5;
if(!(i&&a)){
    var s=new utils_SignReload;
return localStorage.setItem(T,
    JSON.stringify(s)),
    !0
}
if(r.count>3)return!1;
return r.count=r.count+1,
    localStorage.setItem(T,
    JSON.stringify(r)),
    !0
}
catch(e){
    return!1
}

}


// ========== x-s-common 相关代码 #47 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function updateSign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(M,
    {
    callFrom:a,
    callback:u
}
,
    2,
    60)];
case 2:return c=(d=v.sent()).secPoisonId,
    x(d.data),
    delete window[u],
    updateTokenTs(),
    [3,
    4];
case 3:return v.sent(),
    [3,
    4];
case 4:return setTimeout(function(){
    return updateSign(e)
}
,
    5*r),
    [3,
    6];
case 5:setTimeout(function(){
    return updateSign(e)
}
,
    r/12),
    v.label=6;
case 6:return[2]
}

}
)
}
)
}


// ========== x-s-common 相关代码 #48 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signAdaptor(e,
    r){
    var i;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    function(){
    var a,
    s;
return signAdaptor_generator(this,
    function(u){
    return a=Date.now(),
    "function"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,
    r):xhsSign(e,
    r),
    "function"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,
    r):xsCommon(e,
    r),
    "function"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,
    r):xhsToken(e,
    r),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-a,
    type:(null==window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href
}

}
),
    [2,
    r]
}
)
}
)
}


// ========== x-s-common 相关代码 #49 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function xhsSign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}
)&&utils_shouldSign(i)))return r;
d&&signLackReload();
try{
    var p=getRealUrl(i,
    a,
    s),
    f=encrypt_sign;
c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);
var v=f(p,
    u)||{
    
}
;
r.headers["X-t"]=v["X-t"],
    r.headers["X-s"]=v["X-s"]
}
catch(e){
    
}
if(!0!==e.disableMns)try{
    if(window.mns){
    var p=getRealUrl(i,
    a,
    s),
    h="[object Object]"===Object.prototype.toString.call(u)||"[object Array]"===Object.prototype.toString.call(u),
    g=N([p,
    h?JSON.stringify(u):""].join(""));
r.headers["X-Mns"]=window.mns.getMnsToken(p,
    u,
    g)||""
}
else r.headers["X-Mns"]="unload"
}
catch(e){
    r.headers["X-Mns"]="error"
}
return r
}


// ========== x-s-common 相关代码 #50 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signLackReload(e){
    if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    Error("网络连接不可用，请刷新重试。")
}


// ========== x-s-common 相关代码 #51 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function isIgnoreErrors(e){
    var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],
    i="".concat(null==e?void 0:e.errorType,
    ": ").concat(null==e?void 0:e.errorMessage);
return r.includes(i)
}


// ========== x-s-common 相关代码 #52 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonConvert(e,
    r){
    return isConstWithNumber(e)?e:e.replace(u,
    (e,
    i,
    a,
    s)=>{
    let u=r(a);
return`${
    i
}
${
    u
}
${
    s
}
`
}
)
}


// ========== x-s-common 相关代码 #53 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}
)
}
catch(e){
    
}

}


// ========== x-s-common 相关代码 #54 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushRealTimeXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}
)
}
catch(e){
    
}

}


// ========== x-s-common 相关代码 #55 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xsCommon(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
),
    !utils_shouldSign(u))return r;
var c=r.headers["X-Sign"]||"",
    d=getSigCount(c),
    p=localStorage.getItem("b1"),
    f=localStorage.getItem("b1b1")||"1",
    v={
    s0:getPlatformCode(s),
    s1:"",
    x0:f,
    x1:C,
    x2:s||"PC",
    x3:"xhs-pc-web",
    x4:"4.68.0",
    x5:l.Z.get("a1"),
    x6:"",
    x7:"",
    x8:p,
    x9:O("".concat("").concat("").concat(p)),
    x10:d,
    x11:"normal"
}
,
    h=k.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
);
(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){
    v.x8=e,
    v.x9=O("".concat("").concat("").concat(e)),
    r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
catch(e){
    
}
return r
}


// ========== x-s-common 相关代码 #56 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xCommonParams(e,
    r){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    function(){
    var i,
    a,
    s,
    u,
    l,
    d,
    p,
    f,
    v,
    h,
    g,
    m,
    _,
    y,
    w,
    E,
    T,
    S;
return xCommonParams_generator(this,
    function(S){
    switch(S.label){
    case 0:if(i=e.platform,
    a=e.includes,
    s=e.carryDeviceInfo,
    u=a.some(function(e){
    return r.url.includes(e)
}
),
    !(c.YF.isXHS&&s&&u))return[2,
    r];
l=Date.now(),
    p=(d=(0,
    c.Vk)()).major,
    f=d.minor,
    v=d.patch,
    h=getDeviceInfo(),
    (g=new URLSearchParams).append("platform",
    i),
    g.append("versionName",
    "".concat(p,
    ".").concat(f,
    ".").concat(v)),
    S.label=1;
case 1:if(S.trys.push([1,
    4,
    ,
    5]),
    g.has("deviceId"))return[3,
    3];
return[4,
    h];
case 2:_=(m=S.sent()).deviceId,
    y=m.uniqueId,
    w=m.deviceFingerprint,
    E=m.deviceFingerprint1,
    T=m.fid,
    g.append("deviceId",
    _||y),
    g.append("device_fingerprint",
    w),
    g.append("device_fingerprint1",
    E),
    g.append("fid",
    T),
    S.label=3;
case 3:return[3,
    5];
case 4:return S.sent(),
    [3,
    5];
case 5:return r.headers["xy-common-params"]=g.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-l,
    type:"xCommonParams",
    source:null==r?void 0:r.url
}

}
),
    [2,
    r]
}

}
)
}
)
}


// ========== x-s-common 相关代码 #57 ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonChecks(e){
    var r,
    i,
    u=e.navigator,
    c=e.location,
    p=u.userAgent,
    f=os_getOS(p),
    v=browser_getBrowser(p),
    h=getMiniprogramType(p,
    e),
    g=iphoneXCheck(f),
    m=query_extractFromQuery(c.search),
    _="iOS"===f,
    y="Android"===f,
    w="Harmony"===f,
    E="HarmonyArk"===f,
    T=!!window.xhsbridge||!!window.XHSBridge,
    S=!!(null===(i=window.webkit)||void 0===i?void 0:null===(r=i.messageHandlers)||void 0===r?void 0:r.getDeviceInfo),
    b=!!window.XHSBridge,
    k=T||S||"discover"===v||m.isXHS||b,
    C="redtop"===v,
    P=(p||"").toLowerCase().indexOf("mobile")>-1,
    A=browser_getBuildNumber(p),
    R=!k&&!y&&!_&&!P&&!w&&!E,
    I=!1;
if(k||C){
    var O=browser_getBrowserVersion(p);
I=(O.major>6||6===O.major&&O.minor>=7)&&m.isFullscreen||_&&m.isNaviHidden||C&&m.isFullscreen
}
return(0,
    s._)((0,
    a._)({
    
}
,
    d),
    {
    isIOS:_,
    isAndroid:y,
    isHarmony:w,
    isHarmonyArk:E,
    isXHS:k,
    isFullscreen:I,
    isWeixin:"micromessenger"===v,
    isAlipay:"alipay"===v,
    isWeibo:"weibo"===v,
    isQQ:"qq"===v,
    isQQBrowser:"mqqbrowser"===v,
    isMiniprogram:h===l.weixin||m.isMiniprogram,
    isBaiduMiniprogram:h===l.baidu,
    isQQMiniprogram:h===l.qq,
    isAlipayMiniprogram:h===l.alipay,
    isToutiaoMiniprogram:h===l.toutiao,
    isIphone14Pro:iphone14ProCheck(f),
    isIphoneX:g.isIphoneX,
    iphoneXType:g.iphoneXType,
    isTop:C,
    isUniik:"uniik"===v,
    isSpark:"spark"===v,
    isXhsMerchant:"merchant"===v,
    isSnowPeak:"snowpeak"===v,
    isInternation:"internation"===v,
    isCatalog:"catalog"===v,
    isOdyssey:"odyssey"===v,
    isPC:R,
    isMobile:P,
    buildNumber:A
}
)
}


// ========== x-s-common 相关代码 #58 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
// 匹配代码:
headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #59 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
// 匹配代码:
null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
"xhs-pc-web",x4:"4.68.0",x5:l.Z.get("a1"),x6:"",x7:"",x8:p,x9:O("".concat("").concat("").concat(p)),x10:d,x11:"normal"},h=k.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)});(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #60 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: x-s-common[^=]*=\s*[^;]+
// 匹配代码:
X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0
// 上下文:
/*
=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O("".concat("").concat("").concat(e)),r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getItem("sc"))||0;return e&&(r++,sessionStorage.setItem("sc",r.toString())),r}i(42876),i(33933);var genDeviceFingerprint_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})}ret
*/


// ========== x-s-common 相关代码 #61 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(e,
    r){
    "object"===(0,
    d._)(a[r])&&(void 0===e?"undefined":(0,
    d._)(e))==="object"?a[r]=merge(a[r],
    e):a[r]=e
}


// ========== x-s-common 相关代码 #62 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function encrypt_sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}
return r
}
,
    a="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    s="iamspam",
    u=new Date().getTime(),
    c="undefined"==typeof window?i.g:window;
return void 0!==c&&c&&c.navigator&&c.navigator.userAgent&&c.alert&&(s="test"),
    {
    "X-s":function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}
(N([u,
    s,
    e,
    "[object Object]"===Object.prototype.toString.call(r)||"[object Array]"===Object.prototype.toString.call(r)?JSON.stringify(r):""].join(""))),
    "X-t":u
}

}


// ========== x-s-common 相关代码 #63 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function utils_shouldSign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}
),
    r)
}


// ========== x-s-common 相关代码 #64 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function SignReload(){
    this.count=1,
    this.time=+new Date
}


// ========== x-s-common 相关代码 #65 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shouldSignReload(){
    try{
    var e=+new Date,
    r=JSON.stringify(localStorage.getItem(T)||{
    
}
),
    i=!!(r&&r.count),
    a=r&&r.time&&e-r.time<36e5;
if(!(i&&a)){
    var s=new utils_SignReload;
return localStorage.setItem(T,
    JSON.stringify(s)),
    !0
}
if(r.count>3)return!1;
return r.count=r.count+1,
    localStorage.setItem(T,
    JSON.stringify(r)),
    !0
}
catch(e){
    return!1
}

}


// ========== x-s-common 相关代码 #66 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function updateSign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    l.Z.set(f,
    c,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(M,
    {
    callFrom:a,
    callback:u
}
,
    2,
    60)];
case 2:return c=(d=v.sent()).secPoisonId,
    x(d.data),
    delete window[u],
    updateTokenTs(),
    [3,
    4];
case 3:return v.sent(),
    [3,
    4];
case 4:return setTimeout(function(){
    return updateSign(e)
}
,
    5*r),
    [3,
    6];
case 5:setTimeout(function(){
    return updateSign(e)
}
,
    r/12),
    v.label=6;
case 6:return[2]
}

}
)
}
)
}


// ========== x-s-common 相关代码 #67 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signAdaptor(e,
    r){
    var i;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    function(){
    var a,
    s;
return signAdaptor_generator(this,
    function(u){
    return a=Date.now(),
    "function"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,
    r):xhsSign(e,
    r),
    "function"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,
    r):xsCommon(e,
    r),
    "function"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,
    r):xhsToken(e,
    r),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-a,
    type:(null==window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href
}

}
),
    [2,
    r]
}
)
}
)
}


// ========== x-s-common 相关代码 #68 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function xhsSign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}
)&&utils_shouldSign(i)))return r;
d&&signLackReload();
try{
    var p=getRealUrl(i,
    a,
    s),
    f=encrypt_sign;
c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);
var v=f(p,
    u)||{
    
}
;
r.headers["X-t"]=v["X-t"],
    r.headers["X-s"]=v["X-s"]
}
catch(e){
    
}
if(!0!==e.disableMns)try{
    if(window.mns){
    var p=getRealUrl(i,
    a,
    s),
    h="[object Object]"===Object.prototype.toString.call(u)||"[object Array]"===Object.prototype.toString.call(u),
    g=N([p,
    h?JSON.stringify(u):""].join(""));
r.headers["X-Mns"]=window.mns.getMnsToken(p,
    u,
    g)||""
}
else r.headers["X-Mns"]="unload"
}
catch(e){
    r.headers["X-Mns"]="error"
}
return r
}


// ========== x-s-common 相关代码 #69 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signLackReload(e){
    if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    Error("网络连接不可用，请刷新重试。")
}


// ========== x-s-common 相关代码 #70 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function isIgnoreErrors(e){
    var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],
    i="".concat(null==e?void 0:e.errorType,
    ": ").concat(null==e?void 0:e.errorMessage);
return r.includes(i)
}


// ========== x-s-common 相关代码 #71 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonConvert(e,
    r){
    return isConstWithNumber(e)?e:e.replace(u,
    (e,
    i,
    a,
    s)=>{
    let u=r(a);
return`${
    i
}
${
    u
}
${
    s
}
`
}
)
}


// ========== x-s-common 相关代码 #72 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}
)
}
catch(e){
    
}

}


// ========== x-s-common 相关代码 #73 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushRealTimeXsCommon(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}
)
}
catch(e){
    
}

}


// ========== x-s-common 相关代码 #74 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xsCommon(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
),
    !utils_shouldSign(u))return r;
var c=r.headers["X-Sign"]||"",
    d=getSigCount(c),
    p=localStorage.getItem("b1"),
    f=localStorage.getItem("b1b1")||"1",
    v={
    s0:getPlatformCode(s),
    s1:"",
    x0:f,
    x1:C,
    x2:s||"PC",
    x3:"xhs-pc-web",
    x4:"4.68.0",
    x5:l.Z.get("a1"),
    x6:"",
    x7:"",
    x8:p,
    x9:O("".concat("").concat("").concat(p)),
    x10:d,
    x11:"normal"
}
,
    h=k.map(function(e){
    return new RegExp(e)
}
).some(function(e){
    return e.test(u)
}
);
(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function(e){
    v.x8=e,
    v.x9=O("".concat("").concat("").concat(e)),
    r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
catch(e){
    
}
return r
}


// ========== x-s-common 相关代码 #75 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xCommonParams(e,
    r){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    function(){
    var i,
    a,
    s,
    u,
    l,
    d,
    p,
    f,
    v,
    h,
    g,
    m,
    _,
    y,
    w,
    E,
    T,
    S;
return xCommonParams_generator(this,
    function(S){
    switch(S.label){
    case 0:if(i=e.platform,
    a=e.includes,
    s=e.carryDeviceInfo,
    u=a.some(function(e){
    return r.url.includes(e)
}
),
    !(c.YF.isXHS&&s&&u))return[2,
    r];
l=Date.now(),
    p=(d=(0,
    c.Vk)()).major,
    f=d.minor,
    v=d.patch,
    h=getDeviceInfo(),
    (g=new URLSearchParams).append("platform",
    i),
    g.append("versionName",
    "".concat(p,
    ".").concat(f,
    ".").concat(v)),
    S.label=1;
case 1:if(S.trys.push([1,
    4,
    ,
    5]),
    g.has("deviceId"))return[3,
    3];
return[4,
    h];
case 2:_=(m=S.sent()).deviceId,
    y=m.uniqueId,
    w=m.deviceFingerprint,
    E=m.deviceFingerprint1,
    T=m.fid,
    g.append("deviceId",
    _||y),
    g.append("device_fingerprint",
    w),
    g.append("device_fingerprint1",
    E),
    g.append("fid",
    T),
    S.label=3;
case 3:return[3,
    5];
case 4:return S.sent(),
    [3,
    5];
case 5:return r.headers["xy-common-params"]=g.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:Date.now()-l,
    type:"xCommonParams",
    source:null==r?void 0:r.url
}

}
),
    [2,
    r]
}

}
)
}
)
}


// ========== x-s-common 相关代码 #76 ==========
// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function commonChecks(e){
    var r,
    i,
    u=e.navigator,
    c=e.location,
    p=u.userAgent,
    f=os_getOS(p),
    v=browser_getBrowser(p),
    h=getMiniprogramType(p,
    e),
    g=iphoneXCheck(f),
    m=query_extractFromQuery(c.search),
    _="iOS"===f,
    y="Android"===f,
    w="Harmony"===f,
    E="HarmonyArk"===f,
    T=!!window.xhsbridge||!!window.XHSBridge,
    S=!!(null===(i=window.webkit)||void 0===i?void 0:null===(r=i.messageHandlers)||void 0===r?void 0:r.getDeviceInfo),
    b=!!window.XHSBridge,
    k=T||S||"discover"===v||m.isXHS||b,
    C="redtop"===v,
    P=(p||"").toLowerCase().indexOf("mobile")>-1,
    A=browser_getBuildNumber(p),
    R=!k&&!y&&!_&&!P&&!w&&!E,
    I=!1;
if(k||C){
    var O=browser_getBrowserVersion(p);
I=(O.major>6||6===O.major&&O.minor>=7)&&m.isFullscreen||_&&m.isNaviHidden||C&&m.isFullscreen
}
return(0,
    s._)((0,
    a._)({
    
}
,
    d),
    {
    isIOS:_,
    isAndroid:y,
    isHarmony:w,
    isHarmonyArk:E,
    isXHS:k,
    isFullscreen:I,
    isWeixin:"micromessenger"===v,
    isAlipay:"alipay"===v,
    isWeibo:"weibo"===v,
    isQQ:"qq"===v,
    isQQBrowser:"mqqbrowser"===v,
    isMiniprogram:h===l.weixin||m.isMiniprogram,
    isBaiduMiniprogram:h===l.baidu,
    isQQMiniprogram:h===l.qq,
    isAlipayMiniprogram:h===l.alipay,
    isToutiaoMiniprogram:h===l.toutiao,
    isIphone14Pro:iphone14ProCheck(f),
    isIphoneX:g.isIphoneX,
    iphoneXType:g.iphoneXType,
    isTop:C,
    isUniik:"uniik"===v,
    isSpark:"spark"===v,
    isXhsMerchant:"merchant"===v,
    isSnowPeak:"snowpeak"===v,
    isInternation:"internation"===v,
    isCatalog:"catalog"===v,
    isOdyssey:"odyssey"===v,
    isPC:R,
    isMobile:P,
    buildNumber:A
}
)
}


// ========== x-s-common 相关代码 #77 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
// 匹配代码:
headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
id 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #78 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
// 匹配代码:
x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
call(g,(function(t){return t.test(u)}));(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #79 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: x-s-common[^=]*=\s*[^;]+
// 匹配代码:
X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
tCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #80 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function encrypt_sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}
return e
}
(t);
l<t.length;
)i=(e=t.charCodeAt(l++))>>2,
    a=(3&e)<<4|(r=t.charCodeAt(l++))>>4,
    u=(15&r)<<2|(o=t.charCodeAt(l++))>>6,
    c=63&o,
    isNaN(r)?u=c=64:isNaN(o)&&(c=64),
    s=s+n.charAt(i)+n.charAt(a)+n.charAt(u)+n.charAt(c);
return s
}
(MD5([o,
    r,
    t,
    a?stringify_default()(e):""].join(""))),
    "X-t":o
}

}


// ========== x-s-common 相关代码 #81 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function utils_shouldSign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}
)),
    e
}


// ========== x-s-common 相关代码 #82 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shouldSignReload(){
    try{
    var t=+new Date,
    e=stringify_default()(localStorage.getItem(signLackInfo)||{
    
}
),
    n=!(!e||!e.count),
    r=e&&e.time&&t-e.time<timeGap;
if(!(n&&r)){
    var o=new SignReload;
return localStorage.setItem(signLackInfo,
    stringify_default()(o)),
    !0
}
return!(e.count>maxReloadTime)&&(e.count=e.count+1,
    localStorage.setItem(signLackInfo,
    stringify_default()(e)),
    !0)
}
catch(i){
    return!1
}

}


// ========== x-s-common 相关代码 #83 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function updateSign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(wraperScript,
    {
    callFrom:r,
    callback:i
}
,
    2,
    60)];
case 2:return u=c.sent(),
    a=u.secPoisonId,
    wrapperEval(u.data),
    delete window[i],
    updateTokenTs(),
    [3,
    4];
case 3:return c.sent(),
    [3,
    4];
case 4:return set_timeout_default()((function(){
    return updateSign(t)
}
),
    5*e),
    [3,
    6];
case 5:set_timeout_default()((function(){
    return updateSign(t)
}
),
    e/12),
    c.label=6;
case 6:return[2]
}

}
))
}
))
}


// ========== x-s-common 相关代码 #84 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signAdaptor(t,
    e){
    var n;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    (function(){
    var r;
return signAdaptor_generator(this,
    (function(o){
    return r=now_default()(),
    "function"==typeof t.shouldSign?t.shouldSign(e)&&xhsSign(t,
    e):xhsSign(t,
    e),
    "function"==typeof t.shouldFp?t.shouldFp(e)&&xsCommon(t,
    e):xsCommon(t,
    e),
    "function"==typeof t.shouldToken?t.shouldToken(e)&&xhsToken(t,
    e):xhsToken(t,
    e),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-r,
    type:(null===window||void 0===window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(n=null==e?void 0:e.url)&&void 0!==n?n:window.location.href
}

}
),
    [2,
    e]
}
))
}
))
}


// ========== x-s-common 相关代码 #85 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function xhsSign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}
))&&utils_shouldSign(n)))return e;
c&&signLackReload();
try{
    var s=getRealUrl(n,
    r,
    o),
    l=encrypt_sign;
a&&void 0!==window._webmsxyw&&(l=window._webmsxyw);
var f=l(s,
    i)||{
    
}
;
e.headers["X-t"]=f["X-t"],
    e.headers["X-s"]=f["X-s"]
}
catch(v){
    
}
if(!0!==t.disableMns)try{
    if(window.mns){
    var p=i,
    h=(s=getRealUrl(n,
    r,
    o),
    "[object Object]"===Object.prototype.toString.call(p)||"[object Array]"===Object.prototype.toString.call(p)),
    d=MD5([s,
    h?stringify_default()(p):""].join(""));
e.headers["X-Mns"]=window.mns.getMnsToken(s,
    p,
    d)||""
}
else e.headers["X-Mns"]="unload"
}
catch(v){
    e.headers["X-Mns"]="error"
}
return e
}


// ========== x-s-common 相关代码 #86 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signLackReload(t){
    if(t&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    new Error("网络连接不可用，请刷新重试。")
}


// ========== x-s-common 相关代码 #87 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}


// ========== x-s-common 相关代码 #88 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushRealTimeXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}


// ========== x-s-common 相关代码 #89 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xsCommon(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
));
if(!some_default()(c).call(c,
    (function(t){
    return t.test(u)
}
)))return e;
var s=e.headers["X-t"]||"",
    l=e.headers["X-s"]||"",
    f=e.headers["X-Sign"]||"",
    p=getSigCount(s&&l||f),
    h=localStorage.getItem(MINI_BROSWER_INFO_KEY),
    d=localStorage.getItem(RC4_SECRET_VERSION_KEY)||RC4_SECRET_VERSION,
    v={
    s0:getPlatformCode(a),
    s1:"",
    x0:d,
    x1:version,
    x2:a||"PC",
    x3:"login",
    x4:"0.10.14",
    x5:js_cookie.A.get(LOCAL_ID_KEY),
    x6:s,
    x7:l,
    x8:h,
    x9:mcr(concat_default()(o=concat_default()(i="".concat(s)).call(i,
    l)).call(o,
    h)),
    x10:p
}
,
    g=map_default()(NEED_REAL_TIME_XSCOMMON_URLS).call(NEED_REAL_TIME_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
)),
    y=some_default()(g).call(g,
    (function(t){
    return t.test(u)
}
));
(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){
    var n,
    r;
v.x8=t,
    v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,
    l)).call(n,
    t)),
    e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
)):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
catch(m){
    
}
return e
}


// ========== x-s-common 相关代码 #90 ==========
// 来源: vendor-main.e645eae.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xCommonParams(t,
    e){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    (function(){
    var n,
    r,
    o,
    i,
    a,
    u,
    c,
    s,
    l,
    f,
    p,
    h,
    d,
    v,
    g,
    y,
    m;
return xCommonParams_generator(this,
    (function(w){
    var b,
    _;
switch(w.label){
    case 0:if(n=t.platform,
    r=includes_default()(t),
    o=t.carryDeviceInfo,
    i=some_default()(r).call(r,
    (function(t){
    var n;
return includes_default()(n=e.url).call(n,
    t)
}
)),
    !(ozone_detector.RI.isXHS&&o&&i))return[2,
    e];
a=now_default()(),
    u=(0,
    ozone_detector.JF)(),
    c=u.major,
    s=u.minor,
    l=u.patch,
    f=(0,
    index_web.NW)(),
    (p=new(url_search_params_default())).append("platform",
    n),
    p.append("versionName",
    concat_default()(b=concat_default()(_="".concat(c,
    ".")).call(_,
    s,
    ".")).call(b,
    l)),
    w.label=1;
case 1:return w.trys.push([1,
    4,
    ,
    5]),
    p.has("deviceId")?[3,
    3]:[4,
    f];
case 2:h=w.sent(),
    d=h.deviceId,
    v=h.uniqueId,
    g=h.deviceFingerprint,
    y=h.deviceFingerprint1,
    m=h.fid,
    p.append("deviceId",
    d||v),
    p.append("device_fingerprint",
    g),
    p.append("device_fingerprint1",
    y),
    p.append("fid",
    m),
    w.label=3;
case 3:return[3,
    5];
case 4:return w.sent(),
    [3,
    5];
case 5:return e.headers["xy-common-params"]=p.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-a,
    type:"xCommonParams",
    source:null==e?void 0:e.url
}

}
),
    [2,
    e]
}

}
))
}
))
}


// ========== x-s-common 相关代码 #91 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: headers\[["\']x-s-common["\']\][\s]*=[\s]*[^;]+
// 匹配代码:
headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
id 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #92 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: \w+\s*=\s*[^;]*x-s-common[^;]*
// 匹配代码:
x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
call(g,(function(t){return t.test(u)}));(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #93 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: x-s-common[^=]*=\s*[^;]+
// 匹配代码:
X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0
// 上下文:
/*
tCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){var n,r;v.x8=t,v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,l)).call(n,t)),e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))})):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))}catch(m){}return e}function getSigCount(t){var e=Number(sessionStorage.getItem(SIGN_COUNT_KEY))||0;return t&&(e++,sessionStorage.setItem(SIGN_COUNT_KEY,e.toString())),e}var instance_includes=__webpack_require__(38515),includes_default=__webpack_require__.n(instance_includes),genDeviceFingerprint_a
*/


// ========== x-s-common 相关代码 #94 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function encrypt_sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}
return e
}
(t);
l<t.length;
)i=(e=t.charCodeAt(l++))>>2,
    a=(3&e)<<4|(r=t.charCodeAt(l++))>>4,
    u=(15&r)<<2|(o=t.charCodeAt(l++))>>6,
    c=63&o,
    isNaN(r)?u=c=64:isNaN(o)&&(c=64),
    s=s+n.charAt(i)+n.charAt(a)+n.charAt(u)+n.charAt(c);
return s
}
(MD5([o,
    r,
    t,
    a?stringify_default()(e):""].join(""))),
    "X-t":o
}

}


// ========== x-s-common 相关代码 #95 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function utils_shouldSign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}
)),
    e
}


// ========== x-s-common 相关代码 #96 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shouldSignReload(){
    try{
    var t=+new Date,
    e=stringify_default()(localStorage.getItem(signLackInfo)||{
    
}
),
    n=!(!e||!e.count),
    r=e&&e.time&&t-e.time<timeGap;
if(!(n&&r)){
    var o=new SignReload;
return localStorage.setItem(signLackInfo,
    stringify_default()(o)),
    !0
}
return!(e.count>maxReloadTime)&&(e.count=e.count+1,
    localStorage.setItem(signLackInfo,
    stringify_default()(e)),
    !0)
}
catch(i){
    return!1
}

}


// ========== x-s-common 相关代码 #97 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function updateSign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.com",
    expires:.007
}
),
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.hk",
    expires:3
}
),
    js_cookie.A.set(XHS_POISON_ID,
    a,
    {
    domain:"xiaohongshu.hk",
    expires:.007
}
)
}
,
    [4,
    retry(wraperScript,
    {
    callFrom:r,
    callback:i
}
,
    2,
    60)];
case 2:return u=c.sent(),
    a=u.secPoisonId,
    wrapperEval(u.data),
    delete window[i],
    updateTokenTs(),
    [3,
    4];
case 3:return c.sent(),
    [3,
    4];
case 4:return set_timeout_default()((function(){
    return updateSign(t)
}
),
    5*e),
    [3,
    6];
case 5:set_timeout_default()((function(){
    return updateSign(t)
}
),
    e/12),
    c.label=6;
case 6:return[2]
}

}
))
}
))
}


// ========== x-s-common 相关代码 #98 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signAdaptor(t,
    e){
    var n;
return signAdaptor_awaiter(this,
    void 0,
    void 0,
    (function(){
    var r;
return signAdaptor_generator(this,
    (function(o){
    return r=now_default()(),
    "function"==typeof t.shouldSign?t.shouldSign(e)&&xhsSign(t,
    e):xhsSign(t,
    e),
    "function"==typeof t.shouldFp?t.shouldFp(e)&&xsCommon(t,
    e):xsCommon(t,
    e),
    "function"==typeof t.shouldToken?t.shouldToken(e)&&xhsToken(t,
    e):xhsToken(t,
    e),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-r,
    type:(null===window||void 0===window?void 0:window._webmsxyw)?"sign_new":"sign_old",
    source:null!==(n=null==e?void 0:e.url)&&void 0!==n?n:window.location.href
}

}
),
    [2,
    e]
}
))
}
))
}


// ========== x-s-common 相关代码 #99 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function xhsSign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}
))&&utils_shouldSign(n)))return e;
c&&signLackReload();
try{
    var s=getRealUrl(n,
    r,
    o),
    l=encrypt_sign;
a&&void 0!==window._webmsxyw&&(l=window._webmsxyw);
var f=l(s,
    i)||{
    
}
;
e.headers["X-t"]=f["X-t"],
    e.headers["X-s"]=f["X-s"]
}
catch(v){
    
}
if(!0!==t.disableMns)try{
    if(window.mns){
    var p=i,
    h=(s=getRealUrl(n,
    r,
    o),
    "[object Object]"===Object.prototype.toString.call(p)||"[object Array]"===Object.prototype.toString.call(p)),
    d=MD5([s,
    h?stringify_default()(p):""].join(""));
e.headers["X-Mns"]=window.mns.getMnsToken(s,
    p,
    d)||""
}
else e.headers["X-Mns"]="unload"
}
catch(v){
    e.headers["X-Mns"]="error"
}
return e
}


// ========== x-s-common 相关代码 #100 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function signLackReload(t){
    if(t&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),
    new Error("网络连接不可用，请刷新重试。")
}


// ========== x-s-common 相关代码 #101 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}


// ========== x-s-common 相关代码 #102 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function pushRealTimeXsCommon(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}
))
}
catch(n){
    
}

}


// ========== x-s-common 相关代码 #103 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xsCommon(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
));
if(!some_default()(c).call(c,
    (function(t){
    return t.test(u)
}
)))return e;
var s=e.headers["X-t"]||"",
    l=e.headers["X-s"]||"",
    f=e.headers["X-Sign"]||"",
    p=getSigCount(s&&l||f),
    h=localStorage.getItem(MINI_BROSWER_INFO_KEY),
    d=localStorage.getItem(RC4_SECRET_VERSION_KEY)||RC4_SECRET_VERSION,
    v={
    s0:getPlatformCode(a),
    s1:"",
    x0:d,
    x1:version,
    x2:a||"PC",
    x3:"login",
    x4:"0.10.14",
    x5:js_cookie.A.get(LOCAL_ID_KEY),
    x6:s,
    x7:l,
    x8:h,
    x9:mcr(concat_default()(o=concat_default()(i="".concat(s)).call(i,
    l)).call(o,
    h)),
    x10:p
}
,
    g=map_default()(NEED_REAL_TIME_XSCOMMON_URLS).call(NEED_REAL_TIME_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}
)),
    y=some_default()(g).call(g,
    (function(t){
    return t.test(u)
}
));
(null===(n=window.xhsFingerprintV3)||void 0===n?void 0:n.getCurMiniUa)&&y?null===(r=window.xhsFingerprintV3)||void 0===r||r.getCurMiniUa((function(t){
    var n,
    r;
v.x8=t,
    v.x9=mcr(concat_default()(n=concat_default()(r="".concat(s)).call(r,
    l)).call(n,
    t)),
    e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
)):e.headers["X-S-Common"]=b64Encode(encodeUtf8(stringify_default()(v)))
}
catch(m){
    
}
return e
}


// ========== x-s-common 相关代码 #104 ==========
// 来源: vendor-main.e645eae_1.js 行 2
// 匹配模式: function\s+(\w*[Cc]ommon\w*)\s*\([^)]*\)
// 完整函数:
function xCommonParams(t,
    e){
    return xCommonParams_awaiter(this,
    void 0,
    void 0,
    (function(){
    var n,
    r,
    o,
    i,
    a,
    u,
    c,
    s,
    l,
    f,
    p,
    h,
    d,
    v,
    g,
    y,
    m;
return xCommonParams_generator(this,
    (function(w){
    var b,
    _;
switch(w.label){
    case 0:if(n=t.platform,
    r=includes_default()(t),
    o=t.carryDeviceInfo,
    i=some_default()(r).call(r,
    (function(t){
    var n;
return includes_default()(n=e.url).call(n,
    t)
}
)),
    !(ozone_detector.RI.isXHS&&o&&i))return[2,
    e];
a=now_default()(),
    u=(0,
    ozone_detector.JF)(),
    c=u.major,
    s=u.minor,
    l=u.patch,
    f=(0,
    index_web.NW)(),
    (p=new(url_search_params_default())).append("platform",
    n),
    p.append("versionName",
    concat_default()(b=concat_default()(_="".concat(c,
    ".")).call(_,
    s,
    ".")).call(b,
    l)),
    w.label=1;
case 1:return w.trys.push([1,
    4,
    ,
    5]),
    p.has("deviceId")?[3,
    3]:[4,
    f];
case 2:h=w.sent(),
    d=h.deviceId,
    v=h.uniqueId,
    g=h.deviceFingerprint,
    y=h.deviceFingerprint1,
    m=h.fid,
    p.append("deviceId",
    d||v),
    p.append("device_fingerprint",
    g),
    p.append("device_fingerprint1",
    y),
    p.append("fid",
    m),
    w.label=3;
case 3:return[3,
    5];
case 4:return w.sent(),
    [3,
    5];
case 5:return e.headers["xy-common-params"]=p.toString(),
    logSec({
    name:"anti_spam_sign_cost",
    data:{
    cost:now_default()()-a,
    type:"xCommonParams",
    source:null==e?void 0:e.url
}

}
),
    [2,
    e]
}

}
))
}
))
}


// ========== x-s-common 相关代码 #105 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(ek.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&(A[B]=N)
}


// ========== x-s-common 相关代码 #106 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function baseAssign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}


// ========== x-s-common 相关代码 #107 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignMergeValue(A,
    B,
    N){
    (void 0!==N&&!eq(A[B],
    N)||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}


// ========== x-s-common 相关代码 #108 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(eV.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}


// ========== x-s-common 相关代码 #109 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function baseAssignValue(A,
    B,
    N){
    "__proto__"==B&&e6?e6(A,
    B,
    {
    configurable:!0,
    enumerable:!0,
    value:N,
    writable:!0
}
):A[B]=N
}


// ========== x-s-common 相关代码 #110 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function createAssigner(A){
    return baseRest(function(B,
    N){
    var U=-1,
    H=N.length,
    W=H>1?N[H-1]:void 0,
    j=H>2?N[2]:void 0;
for(W=A.length>3&&"function"==typeof W?(H--,
    W):void 0,
    j&&isIterateeCall(N[0],
    N[1],
    j)&&(W=H<3?void 0:W,
    H=1),
    B=Object(B);
++U<H;
){
    var V=N[U];
V&&A(B,
    V,
    U,
    W)
}
return B
}
)
}


// ========== x-s-common 相关代码 #111 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getHighBitsUnsigned(){
    return this.high>>>0
}


// ========== x-s-common 相关代码 #112 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getLowBitsUnsigned(){
    return this.low>>>0
}


// ========== x-s-common 相关代码 #113 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}


// ========== x-s-common 相关代码 #114 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}


// ========== x-s-common 相关代码 #115 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}


// ========== x-s-common 相关代码 #116 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getHighBitsUnsigned(){
    return this.high>>>0
}


// ========== x-s-common 相关代码 #117 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getLowBitsUnsigned(){
    return this.low>>>0
}


// ========== x-s-common 相关代码 #118 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}


// ========== x-s-common 相关代码 #119 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}


// ========== x-s-common 相关代码 #120 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}


// ========== x-s-common 相关代码 #121 ==========
// 来源: vendor.621a7319.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}
return A
}


// ========== x-s-common 相关代码 #122 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(ek.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&(A[B]=N)
}


// ========== x-s-common 相关代码 #123 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function baseAssign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}


// ========== x-s-common 相关代码 #124 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignMergeValue(A,
    B,
    N){
    (void 0!==N&&!eq(A[B],
    N)||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}


// ========== x-s-common 相关代码 #125 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assignValue(A,
    B,
    N){
    var U=A[B];
(!(eV.call(A,
    B)&&eq(U,
    N))||void 0===N&&!(B in A))&&baseAssignValue(A,
    B,
    N)
}


// ========== x-s-common 相关代码 #126 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function baseAssignValue(A,
    B,
    N){
    "__proto__"==B&&e6?e6(A,
    B,
    {
    configurable:!0,
    enumerable:!0,
    value:N,
    writable:!0
}
):A[B]=N
}


// ========== x-s-common 相关代码 #127 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function createAssigner(A){
    return baseRest(function(B,
    N){
    var U=-1,
    H=N.length,
    W=H>1?N[H-1]:void 0,
    j=H>2?N[2]:void 0;
for(W=A.length>3&&"function"==typeof W?(H--,
    W):void 0,
    j&&isIterateeCall(N[0],
    N[1],
    j)&&(W=H<3?void 0:W,
    H=1),
    B=Object(B);
++U<H;
){
    var V=N[U];
V&&A(B,
    V,
    U,
    W)
}
return B
}
)
}


// ========== x-s-common 相关代码 #128 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getHighBitsUnsigned(){
    return this.high>>>0
}


// ========== x-s-common 相关代码 #129 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getLowBitsUnsigned(){
    return this.low>>>0
}


// ========== x-s-common 相关代码 #130 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}


// ========== x-s-common 相关代码 #131 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}


// ========== x-s-common 相关代码 #132 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}


// ========== x-s-common 相关代码 #133 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getHighBitsUnsigned(){
    return this.high>>>0
}


// ========== x-s-common 相关代码 #134 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function getLowBitsUnsigned(){
    return this.low>>>0
}


// ========== x-s-common 相关代码 #135 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function shiftRightUnsigned(A){
    return(isLong(A)&&(A=A.toInt()),
    0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,
    this.high>>>A,
    this.unsigned):32===A?fromBits(this.high,
    0,
    this.unsigned):fromBits(this.high>>>A-32,
    0,
    this.unsigned)
}


// ========== x-s-common 相关代码 #136 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toSigned(){
    return this.unsigned?fromBits(this.low,
    this.high,
    !1):this
}


// ========== x-s-common 相关代码 #137 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function toUnsigned(){
    return this.unsigned?this:fromBits(this.low,
    this.high,
    !0)
}


// ========== x-s-common 相关代码 #138 ==========
// 来源: vendor.621a7319_1.js 行 1
// 匹配模式: function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)
// 完整函数:
function assign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}
return A
}


// ========== encode ==========
// 来源: library-axios.435de88b.js 行 1
function encode(e){
    return encodeURIComponent(e).replace(/%40/gi,
    "@").replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b.js 行 1
function encode(e){
    var t={
    "!":"%21",
    "'":"%27",
    "(":"%28",
    ")":"%29",
    "~":"%7E",
    "%20":"+",
    "%00":"\0"
}


// 来源: library-axios.435de88b.js 行 1
function encode(e){
    return encodeURIComponent(e).replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b.js 行 1
encode(e){
    return encodeURIComponent(e).replace(/%40/gi,
    "@").replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b.js 行 1
encode(e){
    var t={
    "!":"%21",
    "'":"%27",
    "(":"%28",
    ")":"%29",
    "~":"%7E",
    "%20":"+",
    "%00":"\0"
}


// 来源: library-axios.435de88b.js 行 1
encode(t){
    return e.call(this,
    t,
    encode)
}


// 来源: library-axios.435de88b.js 行 1
encode(e){
    return encodeURIComponent(e).replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b_1.js 行 1
function encode(e){
    return encodeURIComponent(e).replace(/%40/gi,
    "@").replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b_1.js 行 1
function encode(e){
    var t={
    "!":"%21",
    "'":"%27",
    "(":"%28",
    ")":"%29",
    "~":"%7E",
    "%20":"+",
    "%00":"\0"
}


// 来源: library-axios.435de88b_1.js 行 1
function encode(e){
    return encodeURIComponent(e).replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b_1.js 行 1
encode(e){
    return encodeURIComponent(e).replace(/%40/gi,
    "@").replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-axios.435de88b_1.js 行 1
encode(e){
    var t={
    "!":"%21",
    "'":"%27",
    "(":"%28",
    ")":"%29",
    "~":"%7E",
    "%20":"+",
    "%00":"\0"
}


// 来源: library-axios.435de88b_1.js 行 1
encode(t){
    return e.call(this,
    t,
    encode)
}


// 来源: library-axios.435de88b_1.js 行 1
encode(e){
    return encodeURIComponent(e).replace(/%3A/gi,
    ":").replace(/%24/g,
    "$").replace(/%2C/gi,
    ",
    ").replace(/%20/g,
    "+").replace(/%5B/gi,
    "[").replace(/%5D/gi,
    "]")
}


// 来源: library-polyfill.5f7e25b2.js 行 1
encode=function(t){
    var r,
    e,
    n=[],
    o=(t=ucs2decode(t)).length,
    i=128,
    u=0,
    s=72;
for(r=0;
r<t.length;
r++)(e=t[r])<128&&v(n,
    l(e));
var p=n.length,
    d=p;
for(p&&v(n,
    "-");
d<o;
){
    var y=0x7fffffff;
for(r=0;
r<t.length;
r++)(e=t[r])>=i&&e<y&&(y=e);
var g=d+1;
if(y-i>f((0x7fffffff-u)/g))throw new c(a);
for(u+=(y-i)*g,
    i=y,
    r=0;
r<t.length;
r++){
    if((e=t[r])<i&&++u>0x7fffffff)throw new c(a);
if(e===i){
    for(var b=u,
    m=36;
;
){
    var x=m<=s?1:m>=s+26?26:m-s;
if(b<x)break;
var w=b-x,
    A=36-x;
v(n,
    l(digitToBasic(x+w%A))),
    b=f(w/A),
    m+=36
}


// 来源: library-polyfill.5f7e25b2.js 行 1
Encode=function(t,
    r){
    var e=g(t,
    0);
return e>32&&e<127&&!h(r,
    t)?t:encodeURIComponent(t)
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
encode=function(t){
    var r,
    e,
    n=[],
    o=(t=ucs2decode(t)).length,
    i=128,
    u=0,
    s=72;
for(r=0;
r<t.length;
r++)(e=t[r])<128&&v(n,
    l(e));
var p=n.length,
    d=p;
for(p&&v(n,
    "-");
d<o;
){
    var y=0x7fffffff;
for(r=0;
r<t.length;
r++)(e=t[r])>=i&&e<y&&(y=e);
var g=d+1;
if(y-i>f((0x7fffffff-u)/g))throw new c(a);
for(u+=(y-i)*g,
    i=y,
    r=0;
r<t.length;
r++){
    if((e=t[r])<i&&++u>0x7fffffff)throw new c(a);
if(e===i){
    for(var b=u,
    m=36;
;
){
    var x=m<=s?1:m>=s+26?26:m-s;
if(b<x)break;
var w=b-x,
    A=36-x;
v(n,
    l(digitToBasic(x+w%A))),
    b=f(w/A),
    m+=36
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
Encode=function(t,
    r){
    var e=g(t,
    0);
return e>32&&e<127&&!h(r,
    t)?t:encodeURIComponent(t)
}


// 来源: library-vue.a552caa8.js 行 1
Encode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}


// 来源: library-vue.a552caa8_1.js 行 1
Encode(e){
    return encodeURI(""+e).replace(L,
    "|").replace(A,
    "[").replace(x,
    "]")
}


// 来源: main.7e49175.js 行 2
encode=function(e){
    if(!this.genPoly)throw new Error("Encoder not initialized");
var t=new Uint8Array(e.length+this.degree);
t.set(e);
var n=$u.mod(t,
    this.genPoly),
    r=this.degree-n.length;
if(r>0){
    var i=new Uint8Array(this.degree);
return i.set(n,
    r),
    i
}


// 来源: main.7e49175_1.js 行 2
encode=function(e){
    if(!this.genPoly)throw new Error("Encoder not initialized");
var t=new Uint8Array(e.length+this.degree);
t.set(e);
var n=$u.mod(t,
    this.genPoly),
    r=this.degree-n.length;
if(r>0){
    var i=new Uint8Array(this.degree);
return i.set(n,
    r),
    i
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Encode(e){
    for(var r,
    i=e.length,
    a=i%3,
    s=[],
    u=0,
    c=i-a;
u<c;
u+=16383)s.push(encodeChunk(e,
    u,
    u+16383>c?c:u+16383));
return 1===a?(r=e[i-1],
    s.push(P[r>>2]+P[r<<4&63]+"==")):2===a&&(r=(e[i-2]<<8)+e[i-1],
    s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+"=")),
    s.join("")
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
function encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Encode(e){
    for(var r,
    i=e.length,
    a=i%3,
    s=[],
    u=0,
    c=i-a;
u<c;
u+=16383)s.push(encodeChunk(e,
    u,
    u+16383>c?c:u+16383));
return 1===a?(r=e[i-1],
    s.push(P[r>>2]+P[r<<4&63]+"==")):2===a&&(r=(e[i-2]<<8)+e[i-1],
    s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+"=")),
    s.join("")
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
encode(e){
    var r,
    i,
    s,
    u,
    c,
    l,
    d,
    p="",
    f=0;
for(e=_utf8_encode(e);
f<e.length;
)r=e.charCodeAt(f++),
    i=e.charCodeAt(f++),
    s=e.charCodeAt(f++),
    u=r>>2,
    c=(3&r)<<4|i>>4,
    l=(15&i)<<2|s>>6,
    d=63&s,
    isNaN(i)?l=d=64:isNaN(s)&&(d=64),
    p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);
return p
}


// 来源: vendor-main.e645eae.js 行 2
Encode(t){
    for(var e,
    n=t.length,
    r=n%3,
    o=[],
    i=16383,
    a=0,
    u=n-r;
a<u;
a+=i)o.push(encodeChunk(t,
    a,
    a+i>u?u:a+i));
return 1===r?(e=t[n-1],
    o.push(lookup[e>>2]+lookup[e<<4&63]+"==")):2===r&&(e=(t[n-2]<<8)+t[n-1],
    o.push(lookup[e>>10]+lookup[e>>4&63]+lookup[e<<2&63]+"=")),
    o.join("")
}


// 来源: vendor-main.e645eae_1.js 行 2
Encode(t){
    for(var e,
    n=t.length,
    r=n%3,
    o=[],
    i=16383,
    a=0,
    u=n-r;
a<u;
a+=i)o.push(encodeChunk(t,
    a,
    a+i>u?u:a+i));
return 1===r?(e=t[n-1],
    o.push(lookup[e>>2]+lookup[e<<4&63]+"==")):2===r&&(e=(t[n-2]<<8)+t[n-1],
    o.push(lookup[e>>10]+lookup[e>>4&63]+lookup[e<<2&63]+"=")),
    o.join("")
}


// 来源: vendor.621a7319.js 行 1
function encode(A){
    if(!this.genPoly)throw Error("Encoder not initialized");
let B=new Uint8Array(A.length+this.degree);
B.set(A);
let N=U.mod(B,
    this.genPoly),
    H=this.degree-N.length;
if(H>0){
    let A=new Uint8Array(this.degree);
return A.set(N,
    H),
    A
}


// 来源: vendor.621a7319.js 行 1
Encode=function(A){
    return encodeURIComponent(String(A))
}


// 来源: vendor.621a7319.js 行 1
encode(A){
    if(!this.genPoly)throw Error("Encoder not initialized");
let B=new Uint8Array(A.length+this.degree);
B.set(A);
let N=U.mod(B,
    this.genPoly),
    H=this.degree-N.length;
if(H>0){
    let A=new Uint8Array(this.degree);
return A.set(N,
    H),
    A
}


// 来源: vendor.621a7319_1.js 行 1
function encode(A){
    if(!this.genPoly)throw Error("Encoder not initialized");
let B=new Uint8Array(A.length+this.degree);
B.set(A);
let N=U.mod(B,
    this.genPoly),
    H=this.degree-N.length;
if(H>0){
    let A=new Uint8Array(this.degree);
return A.set(N,
    H),
    A
}


// 来源: vendor.621a7319_1.js 行 1
Encode=function(A){
    return encodeURIComponent(String(A))
}


// 来源: vendor.621a7319_1.js 行 1
encode(A){
    if(!this.genPoly)throw Error("Encoder not initialized");
let B=new Uint8Array(A.length+this.degree);
B.set(A);
let N=U.mod(B,
    this.genPoly),
    H=this.degree-N.length;
if(H>0){
    let A=new Uint8Array(this.degree);
return A.set(N,
    H),
    A
}


// ========== sign ==========
// 来源: library-polyfill.5f7e25b2.js 行 1
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// 来源: library-polyfill.5f7e25b2.js 行 1
sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// 来源: library-polyfill.5f7e25b2.js 行 1
sign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
function sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
sign(t){
    var r=+t;
return 0===r||r!=r?r:r<0?-1:1
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
sign(t,
    r){
    for(var e=f(t),
    o=arguments.length,
    a=1,
    p=c.f,
    h=s.f;
o>a;
){
    for(var d,
    y=l(arguments[a++]),
    g=p?v(u(y),
    p(y)):u(y),
    b=g.length,
    m=0;
b>m;
)d=g[m++],
    (!n||i(h,
    y,
    d))&&(e[d]=y[d])
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
sign=function(){
    return(__assign=Object.assign||function(e){
    for(var r,
    i=1,
    a=arguments.length;
i<a;
i++)for(var s in r=arguments[i],
    r)Object.prototype.hasOwnProperty.call(r,
    s)&&(e[s]=r[s]);
return e
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Sign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Sign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Sign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
sign=function(){
    return(__assign=Object.assign||function(e){
    for(var r,
    i=1,
    a=arguments.length;
i<a;
i++)for(var s in r=arguments[i],
    r)Object.prototype.hasOwnProperty.call(r,
    s)&&(e[s]=r[s]);
return e
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
sign(e,
    r){
    var _utf8_encode=function _utf8_encode(e){
    e=e.replace(/\r\n/g,
    "\n");
for(var r="",
    i=0;
i<e.length;
i++){
    var a=e.charCodeAt(i);
a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),
    r+=String.fromCharCode(a>>6&63|128)),
    r+=String.fromCharCode(63&a|128))
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Sign(e){
    var r=!0;
return e.indexOf(window.location.host)>-1||e.indexOf("sit.xiaohongshu.com")>-1?r:(g.some(function(i){
    if(e.indexOf(i)>-1)return r=!1,
    !0
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Sign(e){
    return token_awaiter(this,
    void 0,
    void 0,
    function(){
    var r,
    i,
    a,
    s,
    u,
    c,
    d,
    v;
return token_generator(this,
    function(v){
    switch(v.label){
    case 0:if(r=6e4,
    i=e.isHidden,
    a=e.callFrom,
    s=token_shouldUpdate(),
    !("visible"===i&&s))return[3,
    5];
v.label=1;
case 1:return v.trys.push([1,
    3,
    ,
    4]),
    u="seccallback",
    c="",
    window[u]=function(e){
    l.Z.set(p,
    e,
    {
    domain:"xiaohongshu.com",
    expires:3
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Sign(e,
    r){
    var i=r.url,
    a=r.params,
    s=r.paramsSerializer,
    u=r.data,
    c=e.configInit,
    l=e.xsIgnore,
    d=e.autoReload;
if(!(!l.some(function(e){
    return i.indexOf(e)>=0
}


// 来源: vendor-main.e645eae.js 行 2
sign=function(){
    return __assign=assign_default()||function(t){
    for(var e,
    n=1,
    r=arguments.length;
n<r;
n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,
    o)&&(t[o]=e[o]);
return t
}


// 来源: vendor-main.e645eae.js 行 2
sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}


// 来源: vendor-main.e645eae.js 行 2
Sign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}


// 来源: vendor-main.e645eae.js 行 2
Sign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}


// 来源: vendor-main.e645eae.js 行 2
Sign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}


// 来源: vendor-main.e645eae.js 行 2
sign({
    visitor:function(t,
    e,
    n,
    r){
    return Ze.isNode&&Te.isBuffer(t)?(this.append(e,
    t.toString("base64")),
    !1):r.defaultVisitor.apply(this,
    arguments)
}


// 来源: vendor-main.e645eae_1.js 行 2
sign=function(){
    return __assign=assign_default()||function(t){
    for(var e,
    n=1,
    r=arguments.length;
n<r;
n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,
    o)&&(t[o]=e[o]);
return t
}


// 来源: vendor-main.e645eae_1.js 行 2
sign(t,
    e){
    var n="A4NjFqYu5wPHsO0XTdDgMa2r1ZQocVte9UJBvk6/7=yRnhISGKblCWi+LpfE8xzm3",
    r="iamspam",
    o=(new Date).getTime(),
    i="undefined"==typeof window?__webpack_require__.g:window;
void 0!==i&&i&&i.navigator&&i.navigator.userAgent&&i.alert&&(r="test");
var a="[object Object]"===Object.prototype.toString.call(e)||"[object Array]"===Object.prototype.toString.call(e);
return{
    "X-s":function(t){
    var e,
    r,
    o,
    i,
    a,
    u,
    c,
    s="",
    l=0;
for(t=function(t){
    t=t.replace(/\r\n/g,
    "\n");
for(var e="",
    n=0;
n<t.length;
n++){
    var r=t.charCodeAt(n);
r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),
    e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),
    e+=String.fromCharCode(r>>6&63|128),
    e+=String.fromCharCode(63&r|128))
}


// 来源: vendor-main.e645eae_1.js 行 2
Sign(t){
    var e=!0;
return index_of_default()(t).call(t,
    window.location.host)>-1||index_of_default()(t).call(t,
    "sit.xiaohongshu.com")>-1||some_default()(BLOCKED_HOSTS).call(BLOCKED_HOSTS,
    (function(n){
    if(index_of_default()(t).call(t,
    n)>-1)return e=!1,
    !0
}


// 来源: vendor-main.e645eae_1.js 行 2
Sign(t){
    return token_awaiter(this,
    void 0,
    void 0,
    (function(){
    var e,
    n,
    r,
    o,
    i,
    a,
    u;
return token_generator(this,
    (function(c){
    switch(c.label){
    case 0:if(e=6e4,
    n=t.isHidden,
    r=t.callFrom,
    o=shouldUpdate(),
    "visible"!==n||!o)return[3,
    5];
c.label=1;
case 1:return c.trys.push([1,
    3,
    ,
    4]),
    i="seccallback",
    a="",
    window[i]=function(t){
    js_cookie.A.set(XHS_SIGN,
    t,
    {
    domain:"xiaohongshu.com",
    expires:3
}


// 来源: vendor-main.e645eae_1.js 行 2
Sign(t,
    e){
    var n=e.url,
    r=e.params,
    o=e.paramsSerializer,
    i=e.data,
    a=t.configInit,
    u=t.xsIgnore,
    c=t.autoReload;
if(!(!some_default()(u).call(u,
    (function(t){
    return index_of_default()(n).call(n,
    t)>=0
}


// 来源: vendor-main.e645eae_1.js 行 2
sign({
    visitor:function(t,
    e,
    n,
    r){
    return Ze.isNode&&Te.isBuffer(t)?(this.append(e,
    t.toString("base64")),
    !1):r.defaultVisitor.apply(this,
    arguments)
}


// 来源: vendor.621a7319.js 行 1
sign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}


// 来源: vendor.621a7319.js 行 1
sign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}


// 来源: vendor.621a7319_1.js 行 1
sign(A,
    B){
    return A&&copyObject(B,
    keys(B),
    A)
}


// 来源: vendor.621a7319_1.js 行 1
sign(A){
    for(var B=1;
B<arguments.length;
B++){
    var N=arguments[B];
for(var U in N)A[U]=N[U]
}


// ========== hash ==========
// 来源: library-polyfill.5f7e25b2.js 行 1
Hash:function(){
    var t=this.fragment;
return t?"#"+t:""
}


// 来源: library-polyfill.5f7e25b2.js 行 1
Hash:function(t){
    if(""===(t=m(t))){
    this.fragment=null;
return
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
Hash:function(){
    var t=this.fragment;
return t?"#"+t:""
}


// 来源: library-polyfill.5f7e25b2_1.js 行 1
Hash:function(t){
    if(""===(t=m(t))){
    this.fragment=null;
return
}


// 来源: library-vue.a552caa8.js 行 1
Hash(e){
    return commonEncode(e).replace(I,
    "{
    ").replace(M,
    "
}


// 来源: library-vue.a552caa8_1.js 行 1
Hash(e){
    return commonEncode(e).replace(I,
    "{
    ").replace(M,
    "
}


// 来源: main.7e49175.js 行 2
hash=function(){
    var e,
    t,
    n,
    r,
    i,
    o,
    a=this.blocks;
this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-271733879|0)^(n=((n=(-271733879^(r=((r=(-1732584194^2004318071&e)+a[1]-117830708)<<12|r>>>20)+e|0)&(-271733879^e))+a[2]-1126478375)<<17|n>>>15)+r|0)&(r^e))+a[3]-1316259209)<<22|t>>>10)+n|0:(e=this.h0,
    t=this.h1,
    n=this.h2,
    t=((t+=((e=((e+=((r=this.h3)^t&(n^r))+a[0]-680876936)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[1]-389564586)<<12|r>>>20)+e|0)&(e^t))+a[2]+606105819)<<17|n>>>15)+r|0)&(r^e))+a[3]-1044525330)<<22|t>>>10)+n|0),
    t=((t+=((e=((e+=(r^t&(n^r))+a[4]-176418897)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[5]+1200080426)<<12|r>>>20)+e|0)&(e^t))+a[6]-1473231341)<<17|n>>>15)+r|0)&(r^e))+a[7]-45705983)<<22|t>>>10)+n|0,
    t=((t+=((e=((e+=(r^t&(n^r))+a[8]+1770035416)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[9]-1958414417)<<12|r>>>20)+e|0)&(e^t))+a[10]-42063)<<17|n>>>15)+r|0)&(r^e))+a[11]-1990404162)<<22|t>>>10)+n|0,
    t=((t+=((e=((e+=(r^t&(n^r))+a[12]+1804603682)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[13]-40341101)<<12|r>>>20)+e|0)&(e^t))+a[14]-1502002290)<<17|n>>>15)+r|0)&(r^e))+a[15]+1236535329)<<22|t>>>10)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[1]-165796510)<<5|e>>>27)+t|0)^t))+a[6]-1069501632)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[11]+643717713)<<14|n>>>18)+r|0)^r))+a[0]-373897302)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[5]-701558691)<<5|e>>>27)+t|0)^t))+a[10]+38016083)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[15]-660478335)<<14|n>>>18)+r|0)^r))+a[4]-405537848)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[9]+568446438)<<5|e>>>27)+t|0)^t))+a[14]-1019803690)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[3]-187363961)<<14|n>>>18)+r|0)^r))+a[8]+1163531501)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[13]-1444681467)<<5|e>>>27)+t|0)^t))+a[2]-51403784)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[7]+1735328473)<<14|n>>>18)+r|0)^r))+a[12]-1926607734)<<20|t>>>12)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[5]-378558)<<4|e>>>28)+t|0))+a[8]-2022574463)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[11]+1839030562)<<16|n>>>16)+r|0))+a[14]-35309556)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[1]-1530992060)<<4|e>>>28)+t|0))+a[4]+1272893353)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[7]-155497632)<<16|n>>>16)+r|0))+a[10]-1094730640)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[13]+681279174)<<4|e>>>28)+t|0))+a[0]-358537222)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[3]-722521979)<<16|n>>>16)+r|0))+a[6]+76029189)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[9]-640364487)<<4|e>>>28)+t|0))+a[12]-421815835)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[15]+530742520)<<16|n>>>16)+r|0))+a[2]-995338651)<<23|t>>>9)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[0]-198630844)<<6|e>>>26)+t|0)|~n))+a[7]+1126891415)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[14]-1416354905)<<15|n>>>17)+r|0)|~e))+a[5]-57434055)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[12]+1700485571)<<6|e>>>26)+t|0)|~n))+a[3]-1894986606)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[10]-1051523)<<15|n>>>17)+r|0)|~e))+a[1]-2054922799)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[8]+1873313359)<<6|e>>>26)+t|0)|~n))+a[15]-30611744)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[6]-1560198380)<<15|n>>>17)+r|0)|~e))+a[13]+1309151649)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[4]-145523070)<<6|e>>>26)+t|0)|~n))+a[11]-1120210379)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[2]+718787259)<<15|n>>>17)+r|0)|~e))+a[9]-343485551)<<21|t>>>11)+n|0,
    this.first?(this.h0=e+1732584193|0,
    this.h1=t-271733879|0,
    this.h2=n-1732584194|0,
    this.h3=r+271733878|0,
    this.first=!1):(this.h0=this.h0+e|0,
    this.h1=this.h1+t|0,
    this.h2=this.h2+n|0,
    this.h3=this.h3+r|0)
}


// 来源: main.7e49175.js 行 2
Hash:function(){
    var e=this.fragment;
return e?"#"+e:""
}


// 来源: main.7e49175.js 行 2
Hash:function(e){
    ""!==(e=x(e))?("#"===P(e,
    0)&&(e=q(e,
    1)),
    this.fragment="",
    this.parse(e,
    Fe)):this.fragment=null
}


// 来源: main.7e49175_1.js 行 2
hash=function(){
    var e,
    t,
    n,
    r,
    i,
    o,
    a=this.blocks;
this.first?t=((t=((e=((e=a[0]-680876937)<<7|e>>>25)-271733879|0)^(n=((n=(-271733879^(r=((r=(-1732584194^2004318071&e)+a[1]-117830708)<<12|r>>>20)+e|0)&(-271733879^e))+a[2]-1126478375)<<17|n>>>15)+r|0)&(r^e))+a[3]-1316259209)<<22|t>>>10)+n|0:(e=this.h0,
    t=this.h1,
    n=this.h2,
    t=((t+=((e=((e+=((r=this.h3)^t&(n^r))+a[0]-680876936)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[1]-389564586)<<12|r>>>20)+e|0)&(e^t))+a[2]+606105819)<<17|n>>>15)+r|0)&(r^e))+a[3]-1044525330)<<22|t>>>10)+n|0),
    t=((t+=((e=((e+=(r^t&(n^r))+a[4]-176418897)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[5]+1200080426)<<12|r>>>20)+e|0)&(e^t))+a[6]-1473231341)<<17|n>>>15)+r|0)&(r^e))+a[7]-45705983)<<22|t>>>10)+n|0,
    t=((t+=((e=((e+=(r^t&(n^r))+a[8]+1770035416)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[9]-1958414417)<<12|r>>>20)+e|0)&(e^t))+a[10]-42063)<<17|n>>>15)+r|0)&(r^e))+a[11]-1990404162)<<22|t>>>10)+n|0,
    t=((t+=((e=((e+=(r^t&(n^r))+a[12]+1804603682)<<7|e>>>25)+t|0)^(n=((n+=(t^(r=((r+=(n^e&(t^n))+a[13]-40341101)<<12|r>>>20)+e|0)&(e^t))+a[14]-1502002290)<<17|n>>>15)+r|0)&(r^e))+a[15]+1236535329)<<22|t>>>10)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[1]-165796510)<<5|e>>>27)+t|0)^t))+a[6]-1069501632)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[11]+643717713)<<14|n>>>18)+r|0)^r))+a[0]-373897302)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[5]-701558691)<<5|e>>>27)+t|0)^t))+a[10]+38016083)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[15]-660478335)<<14|n>>>18)+r|0)^r))+a[4]-405537848)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[9]+568446438)<<5|e>>>27)+t|0)^t))+a[14]-1019803690)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[3]-187363961)<<14|n>>>18)+r|0)^r))+a[8]+1163531501)<<20|t>>>12)+n|0,
    t=((t+=((r=((r+=(t^n&((e=((e+=(n^r&(t^n))+a[13]-1444681467)<<5|e>>>27)+t|0)^t))+a[2]-51403784)<<9|r>>>23)+e|0)^e&((n=((n+=(e^t&(r^e))+a[7]+1735328473)<<14|n>>>18)+r|0)^r))+a[12]-1926607734)<<20|t>>>12)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[5]-378558)<<4|e>>>28)+t|0))+a[8]-2022574463)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[11]+1839030562)<<16|n>>>16)+r|0))+a[14]-35309556)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[1]-1530992060)<<4|e>>>28)+t|0))+a[4]+1272893353)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[7]-155497632)<<16|n>>>16)+r|0))+a[10]-1094730640)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[13]+681279174)<<4|e>>>28)+t|0))+a[0]-358537222)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[3]-722521979)<<16|n>>>16)+r|0))+a[6]+76029189)<<23|t>>>9)+n|0,
    t=((t+=((o=(r=((r+=((i=t^n)^(e=((e+=(i^r)+a[9]-640364487)<<4|e>>>28)+t|0))+a[12]-421815835)<<11|r>>>21)+e|0)^e)^(n=((n+=(o^t)+a[15]+530742520)<<16|n>>>16)+r|0))+a[2]-995338651)<<23|t>>>9)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[0]-198630844)<<6|e>>>26)+t|0)|~n))+a[7]+1126891415)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[14]-1416354905)<<15|n>>>17)+r|0)|~e))+a[5]-57434055)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[12]+1700485571)<<6|e>>>26)+t|0)|~n))+a[3]-1894986606)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[10]-1051523)<<15|n>>>17)+r|0)|~e))+a[1]-2054922799)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[8]+1873313359)<<6|e>>>26)+t|0)|~n))+a[15]-30611744)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[6]-1560198380)<<15|n>>>17)+r|0)|~e))+a[13]+1309151649)<<21|t>>>11)+n|0,
    t=((t+=((r=((r+=(t^((e=((e+=(n^(t|~r))+a[4]-145523070)<<6|e>>>26)+t|0)|~n))+a[11]-1120210379)<<10|r>>>22)+e|0)^((n=((n+=(e^(r|~t))+a[2]+718787259)<<15|n>>>17)+r|0)|~e))+a[9]-343485551)<<21|t>>>11)+n|0,
    this.first?(this.h0=e+1732584193|0,
    this.h1=t-271733879|0,
    this.h2=n-1732584194|0,
    this.h3=r+271733878|0,
    this.first=!1):(this.h0=this.h0+e|0,
    this.h1=this.h1+t|0,
    this.h2=this.h2+n|0,
    this.h3=this.h3+r|0)
}


// 来源: main.7e49175_1.js 行 2
Hash:function(){
    var e=this.fragment;
return e?"#"+e:""
}


// 来源: main.7e49175_1.js 行 2
Hash:function(e){
    ""!==(e=x(e))?("#"===P(e,
    0)&&(e=q(e,
    1)),
    this.fragment="",
    this.parse(e,
    Fe)):this.fragment=null
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Hash(){
    this.entryHash={
    
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Hash(){
    this.entryHash={
    
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Hash(){
    this.entryHash={
    
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Hash(){
    this.entryHash={
    
}


// 来源: vendor.621a7319.js 行 1
function Hash(A){
    var B=-1,
    N=A?A.length:0;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319.js 行 1
function Hash(A){
    var B=-1,
    N=null==A?0:A.length;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319.js 行 1
Hash(A){
    var B=-1,
    N=A?A.length:0;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319.js 行 1
Hash(A){
    var B=-1,
    N=null==A?0:A.length;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319_1.js 行 1
function Hash(A){
    var B=-1,
    N=A?A.length:0;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319_1.js 行 1
function Hash(A){
    var B=-1,
    N=null==A?0:A.length;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319_1.js 行 1
Hash(A){
    var B=-1,
    N=A?A.length:0;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// 来源: vendor.621a7319_1.js 行 1
Hash(A){
    var B=-1,
    N=null==A?0:A.length;
for(this.clear();
++B<N;
){
    var U=A[B];
this.set(U[0],
    U[1])
}


// ========== encrypt ==========
// 来源: main.7e49175.js 行 2
encrypt:function(t,
    n){
    return e(t,
    n,
    "encrypt")
}


// 来源: main.7e49175.js 行 2
encrypt:function(e){
    var t=n(g.toBytesNone("1p6ki2u2cknvza4j"),
    !0),
    o=g.toBytes(e);
if((o=n(o)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");
for(var a=r(o.length),
    s=r(16),
    c=0;
c<o.length;
c+=16){
    i(o,
    s,
    0,
    c,
    c+16);
for(var u=0;
u<16;
u++)s[u]^=t[u];
i(t=m(s),
    a,
    c)
}


// 来源: main.7e49175_1.js 行 2
encrypt:function(t,
    n){
    return e(t,
    n,
    "encrypt")
}


// 来源: main.7e49175_1.js 行 2
encrypt:function(e){
    var t=n(g.toBytesNone("1p6ki2u2cknvza4j"),
    !0),
    o=g.toBytes(e);
if((o=n(o)).length%16!=0)throw new Error("invalid plaintext size (must be multiple of 16 bytes)");
for(var a=r(o.length),
    s=r(16),
    c=0;
c<o.length;
c+=16){
    i(o,
    s,
    0,
    c,
    c+16);
for(var u=0;
u<16;
u++)s[u]^=t[u];
i(t=m(s),
    a,
    c)
}


// ========== md5 ==========
// 来源: main.7e49175.js 行 2
Md5(e,
    (function(e,
    r){
    if(e)return n(s.error(e));
var i='"'+r+'"';
v[t]=i,
    b({
    loaded:x+=o,
    total:g
}


// 来源: main.7e49175.js 行 2
Md5(s,
    e.Body,
    (function(s){
    s&&(c&&c.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175.js 行 2
Md5(a,
    e.Body,
    (function(r){
    r&&(e.Headers["x-cos-meta-md5"]=r),
    a&&i&&i.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175.js 行 2
Md5(i,
    e.Body,
    (function(a){
    a&&(e.Headers["Content-MD5"]=o.b64(a)),
    i&&r&&r.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175.js 行 2
Md5(t,
    (function(e,
    t){
    n(t)
}


// 来源: main.7e49175_1.js 行 2
Md5(e,
    (function(e,
    r){
    if(e)return n(s.error(e));
var i='"'+r+'"';
v[t]=i,
    b({
    loaded:x+=o,
    total:g
}


// 来源: main.7e49175_1.js 行 2
Md5(s,
    e.Body,
    (function(s){
    s&&(c&&c.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175_1.js 行 2
Md5(a,
    e.Body,
    (function(r){
    r&&(e.Headers["x-cos-meta-md5"]=r),
    a&&i&&i.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175_1.js 行 2
Md5(i,
    e.Body,
    (function(a){
    a&&(e.Headers["Content-MD5"]=o.b64(a)),
    i&&r&&r.setParams({
    md5EndTime:(new Date).getTime()
}


// 来源: main.7e49175_1.js 行 2
Md5(t,
    (function(e,
    t){
    n(t)
}


// 来源: vendor-main.e645eae.js 行 2
MD5=function(t){
    function e(r){
    if(n[r])return n[r].exports;
var o=n[r]={
    i:r,
    l:!1,
    exports:{
    
}


// 来源: vendor-main.e645eae_1.js 行 2
MD5=function(t){
    function e(r){
    if(n[r])return n[r].exports;
var o=n[r]={
    i:r,
    l:!1,
    exports:{
    
}


// 来源: vendor.621a7319.js 行 1
md5=function(A,
    N){
    A.constructor==String?A=N&&"binary"===N.encoding?W.stringToBytes(A):U.stringToBytes(A):H(A)?A=Array.prototype.slice.call(A,
    0):!Array.isArray(A)&&A.constructor!==Uint8Array&&(A=A.toString());
for(var j=B.bytesToWords(A),
    V=8*A.length,
    K=0x67452301,
    X=-0x10325477,
    J=-0x67452302,
    ee=0x10325476,
    et=0;
et<j.length;
et++)j[et]=(j[et]<<8|j[et]>>>24)&0xff00ff|(j[et]<<24|j[et]>>>8)&0xff00ff00;
j[V>>>5]|=128<<V%32,
    j[(V+64>>>9<<4)+14]=V;
for(var er=md5._ff,
    en=md5._gg,
    ei=md5._hh,
    eo=md5._ii,
    et=0;
et<j.length;
et+=16){
    var ea=K,
    es=X,
    eu=J,
    el=ee;
K=er(K,
    X,
    J,
    ee,
    j[et+0],
    7,
    -0x28955b88),
    ee=er(ee,
    K,
    X,
    J,
    j[et+1],
    12,
    -0x173848aa),
    J=er(J,
    ee,
    K,
    X,
    j[et+2],
    17,
    0x242070db),
    X=er(X,
    J,
    ee,
    K,
    j[et+3],
    22,
    -0x3e423112),
    K=er(K,
    X,
    J,
    ee,
    j[et+4],
    7,
    -0xa83f051),
    ee=er(ee,
    K,
    X,
    J,
    j[et+5],
    12,
    0x4787c62a),
    J=er(J,
    ee,
    K,
    X,
    j[et+6],
    17,
    -0x57cfb9ed),
    X=er(X,
    J,
    ee,
    K,
    j[et+7],
    22,
    -0x2b96aff),
    K=er(K,
    X,
    J,
    ee,
    j[et+8],
    7,
    0x698098d8),
    ee=er(ee,
    K,
    X,
    J,
    j[et+9],
    12,
    -0x74bb0851),
    J=er(J,
    ee,
    K,
    X,
    j[et+10],
    17,
    -42063),
    X=er(X,
    J,
    ee,
    K,
    j[et+11],
    22,
    -0x76a32842),
    K=er(K,
    X,
    J,
    ee,
    j[et+12],
    7,
    0x6b901122),
    ee=er(ee,
    K,
    X,
    J,
    j[et+13],
    12,
    -0x2678e6d),
    J=er(J,
    ee,
    K,
    X,
    j[et+14],
    17,
    -0x5986bc72),
    X=er(X,
    J,
    ee,
    K,
    j[et+15],
    22,
    0x49b40821),
    K=en(K,
    X,
    J,
    ee,
    j[et+1],
    5,
    -0x9e1da9e),
    ee=en(ee,
    K,
    X,
    J,
    j[et+6],
    9,
    -0x3fbf4cc0),
    J=en(J,
    ee,
    K,
    X,
    j[et+11],
    14,
    0x265e5a51),
    X=en(X,
    J,
    ee,
    K,
    j[et+0],
    20,
    -0x16493856),
    K=en(K,
    X,
    J,
    ee,
    j[et+5],
    5,
    -0x29d0efa3),
    ee=en(ee,
    K,
    X,
    J,
    j[et+10],
    9,
    0x2441453),
    J=en(J,
    ee,
    K,
    X,
    j[et+15],
    14,
    -0x275e197f),
    X=en(X,
    J,
    ee,
    K,
    j[et+4],
    20,
    -0x182c0438),
    K=en(K,
    X,
    J,
    ee,
    j[et+9],
    5,
    0x21e1cde6),
    ee=en(ee,
    K,
    X,
    J,
    j[et+14],
    9,
    -0x3cc8f82a),
    J=en(J,
    ee,
    K,
    X,
    j[et+3],
    14,
    -0xb2af279),
    X=en(X,
    J,
    ee,
    K,
    j[et+8],
    20,
    0x455a14ed),
    K=en(K,
    X,
    J,
    ee,
    j[et+13],
    5,
    -0x561c16fb),
    ee=en(ee,
    K,
    X,
    J,
    j[et+2],
    9,
    -0x3105c08),
    J=en(J,
    ee,
    K,
    X,
    j[et+7],
    14,
    0x676f02d9),
    X=en(X,
    J,
    ee,
    K,
    j[et+12],
    20,
    -0x72d5b376),
    K=ei(K,
    X,
    J,
    ee,
    j[et+5],
    4,
    -378558),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+8],
    11,
    -0x788e097f),
    J=ei(J,
    ee,
    K,
    X,
    j[et+11],
    16,
    0x6d9d6122),
    X=ei(X,
    J,
    ee,
    K,
    j[et+14],
    23,
    -0x21ac7f4),
    K=ei(K,
    X,
    J,
    ee,
    j[et+1],
    4,
    -0x5b4115bc),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+4],
    11,
    0x4bdecfa9),
    J=ei(J,
    ee,
    K,
    X,
    j[et+7],
    16,
    -0x944b4a0),
    X=ei(X,
    J,
    ee,
    K,
    j[et+10],
    23,
    -0x41404390),
    K=ei(K,
    X,
    J,
    ee,
    j[et+13],
    4,
    0x289b7ec6),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+0],
    11,
    -0x155ed806),
    J=ei(J,
    ee,
    K,
    X,
    j[et+3],
    16,
    -0x2b10cf7b),
    X=ei(X,
    J,
    ee,
    K,
    j[et+6],
    23,
    0x4881d05),
    K=ei(K,
    X,
    J,
    ee,
    j[et+9],
    4,
    -0x262b2fc7),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+12],
    11,
    -0x1924661b),
    J=ei(J,
    ee,
    K,
    X,
    j[et+15],
    16,
    0x1fa27cf8),
    X=ei(X,
    J,
    ee,
    K,
    j[et+2],
    23,
    -0x3b53a99b),
    K=eo(K,
    X,
    J,
    ee,
    j[et+0],
    6,
    -0xbd6ddbc),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+7],
    10,
    0x432aff97),
    J=eo(J,
    ee,
    K,
    X,
    j[et+14],
    15,
    -0x546bdc59),
    X=eo(X,
    J,
    ee,
    K,
    j[et+5],
    21,
    -0x36c5fc7),
    K=eo(K,
    X,
    J,
    ee,
    j[et+12],
    6,
    0x655b59c3),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+3],
    10,
    -0x70f3336e),
    J=eo(J,
    ee,
    K,
    X,
    j[et+10],
    15,
    -1051523),
    X=eo(X,
    J,
    ee,
    K,
    j[et+1],
    21,
    -0x7a7ba22f),
    K=eo(K,
    X,
    J,
    ee,
    j[et+8],
    6,
    0x6fa87e4f),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+15],
    10,
    -0x1d31920),
    J=eo(J,
    ee,
    K,
    X,
    j[et+6],
    15,
    -0x5cfebcec),
    X=eo(X,
    J,
    ee,
    K,
    j[et+13],
    21,
    0x4e0811a1),
    K=eo(K,
    X,
    J,
    ee,
    j[et+4],
    6,
    -0x8ac817e),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+11],
    10,
    -0x42c50dcb),
    J=eo(J,
    ee,
    K,
    X,
    j[et+2],
    15,
    0x2ad7d2bb),
    X=eo(X,
    J,
    ee,
    K,
    j[et+9],
    21,
    -0x14792c6f),
    K=K+ea>>>0,
    X=X+es>>>0,
    J=J+eu>>>0,
    ee=ee+el>>>0
}


// 来源: vendor.621a7319_1.js 行 1
md5=function(A,
    N){
    A.constructor==String?A=N&&"binary"===N.encoding?W.stringToBytes(A):U.stringToBytes(A):H(A)?A=Array.prototype.slice.call(A,
    0):!Array.isArray(A)&&A.constructor!==Uint8Array&&(A=A.toString());
for(var j=B.bytesToWords(A),
    V=8*A.length,
    K=0x67452301,
    X=-0x10325477,
    J=-0x67452302,
    ee=0x10325476,
    et=0;
et<j.length;
et++)j[et]=(j[et]<<8|j[et]>>>24)&0xff00ff|(j[et]<<24|j[et]>>>8)&0xff00ff00;
j[V>>>5]|=128<<V%32,
    j[(V+64>>>9<<4)+14]=V;
for(var er=md5._ff,
    en=md5._gg,
    ei=md5._hh,
    eo=md5._ii,
    et=0;
et<j.length;
et+=16){
    var ea=K,
    es=X,
    eu=J,
    el=ee;
K=er(K,
    X,
    J,
    ee,
    j[et+0],
    7,
    -0x28955b88),
    ee=er(ee,
    K,
    X,
    J,
    j[et+1],
    12,
    -0x173848aa),
    J=er(J,
    ee,
    K,
    X,
    j[et+2],
    17,
    0x242070db),
    X=er(X,
    J,
    ee,
    K,
    j[et+3],
    22,
    -0x3e423112),
    K=er(K,
    X,
    J,
    ee,
    j[et+4],
    7,
    -0xa83f051),
    ee=er(ee,
    K,
    X,
    J,
    j[et+5],
    12,
    0x4787c62a),
    J=er(J,
    ee,
    K,
    X,
    j[et+6],
    17,
    -0x57cfb9ed),
    X=er(X,
    J,
    ee,
    K,
    j[et+7],
    22,
    -0x2b96aff),
    K=er(K,
    X,
    J,
    ee,
    j[et+8],
    7,
    0x698098d8),
    ee=er(ee,
    K,
    X,
    J,
    j[et+9],
    12,
    -0x74bb0851),
    J=er(J,
    ee,
    K,
    X,
    j[et+10],
    17,
    -42063),
    X=er(X,
    J,
    ee,
    K,
    j[et+11],
    22,
    -0x76a32842),
    K=er(K,
    X,
    J,
    ee,
    j[et+12],
    7,
    0x6b901122),
    ee=er(ee,
    K,
    X,
    J,
    j[et+13],
    12,
    -0x2678e6d),
    J=er(J,
    ee,
    K,
    X,
    j[et+14],
    17,
    -0x5986bc72),
    X=er(X,
    J,
    ee,
    K,
    j[et+15],
    22,
    0x49b40821),
    K=en(K,
    X,
    J,
    ee,
    j[et+1],
    5,
    -0x9e1da9e),
    ee=en(ee,
    K,
    X,
    J,
    j[et+6],
    9,
    -0x3fbf4cc0),
    J=en(J,
    ee,
    K,
    X,
    j[et+11],
    14,
    0x265e5a51),
    X=en(X,
    J,
    ee,
    K,
    j[et+0],
    20,
    -0x16493856),
    K=en(K,
    X,
    J,
    ee,
    j[et+5],
    5,
    -0x29d0efa3),
    ee=en(ee,
    K,
    X,
    J,
    j[et+10],
    9,
    0x2441453),
    J=en(J,
    ee,
    K,
    X,
    j[et+15],
    14,
    -0x275e197f),
    X=en(X,
    J,
    ee,
    K,
    j[et+4],
    20,
    -0x182c0438),
    K=en(K,
    X,
    J,
    ee,
    j[et+9],
    5,
    0x21e1cde6),
    ee=en(ee,
    K,
    X,
    J,
    j[et+14],
    9,
    -0x3cc8f82a),
    J=en(J,
    ee,
    K,
    X,
    j[et+3],
    14,
    -0xb2af279),
    X=en(X,
    J,
    ee,
    K,
    j[et+8],
    20,
    0x455a14ed),
    K=en(K,
    X,
    J,
    ee,
    j[et+13],
    5,
    -0x561c16fb),
    ee=en(ee,
    K,
    X,
    J,
    j[et+2],
    9,
    -0x3105c08),
    J=en(J,
    ee,
    K,
    X,
    j[et+7],
    14,
    0x676f02d9),
    X=en(X,
    J,
    ee,
    K,
    j[et+12],
    20,
    -0x72d5b376),
    K=ei(K,
    X,
    J,
    ee,
    j[et+5],
    4,
    -378558),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+8],
    11,
    -0x788e097f),
    J=ei(J,
    ee,
    K,
    X,
    j[et+11],
    16,
    0x6d9d6122),
    X=ei(X,
    J,
    ee,
    K,
    j[et+14],
    23,
    -0x21ac7f4),
    K=ei(K,
    X,
    J,
    ee,
    j[et+1],
    4,
    -0x5b4115bc),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+4],
    11,
    0x4bdecfa9),
    J=ei(J,
    ee,
    K,
    X,
    j[et+7],
    16,
    -0x944b4a0),
    X=ei(X,
    J,
    ee,
    K,
    j[et+10],
    23,
    -0x41404390),
    K=ei(K,
    X,
    J,
    ee,
    j[et+13],
    4,
    0x289b7ec6),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+0],
    11,
    -0x155ed806),
    J=ei(J,
    ee,
    K,
    X,
    j[et+3],
    16,
    -0x2b10cf7b),
    X=ei(X,
    J,
    ee,
    K,
    j[et+6],
    23,
    0x4881d05),
    K=ei(K,
    X,
    J,
    ee,
    j[et+9],
    4,
    -0x262b2fc7),
    ee=ei(ee,
    K,
    X,
    J,
    j[et+12],
    11,
    -0x1924661b),
    J=ei(J,
    ee,
    K,
    X,
    j[et+15],
    16,
    0x1fa27cf8),
    X=ei(X,
    J,
    ee,
    K,
    j[et+2],
    23,
    -0x3b53a99b),
    K=eo(K,
    X,
    J,
    ee,
    j[et+0],
    6,
    -0xbd6ddbc),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+7],
    10,
    0x432aff97),
    J=eo(J,
    ee,
    K,
    X,
    j[et+14],
    15,
    -0x546bdc59),
    X=eo(X,
    J,
    ee,
    K,
    j[et+5],
    21,
    -0x36c5fc7),
    K=eo(K,
    X,
    J,
    ee,
    j[et+12],
    6,
    0x655b59c3),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+3],
    10,
    -0x70f3336e),
    J=eo(J,
    ee,
    K,
    X,
    j[et+10],
    15,
    -1051523),
    X=eo(X,
    J,
    ee,
    K,
    j[et+1],
    21,
    -0x7a7ba22f),
    K=eo(K,
    X,
    J,
    ee,
    j[et+8],
    6,
    0x6fa87e4f),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+15],
    10,
    -0x1d31920),
    J=eo(J,
    ee,
    K,
    X,
    j[et+6],
    15,
    -0x5cfebcec),
    X=eo(X,
    J,
    ee,
    K,
    j[et+13],
    21,
    0x4e0811a1),
    K=eo(K,
    X,
    J,
    ee,
    j[et+4],
    6,
    -0x8ac817e),
    ee=eo(ee,
    K,
    X,
    J,
    j[et+11],
    10,
    -0x42c50dcb),
    J=eo(J,
    ee,
    K,
    X,
    j[et+2],
    15,
    0x2ad7d2bb),
    X=eo(X,
    J,
    ee,
    K,
    j[et+9],
    21,
    -0x14792c6f),
    K=K+ea>>>0,
    X=X+es>>>0,
    J=J+eu>>>0,
    ee=ee+el>>>0
}


// ========== xs ==========
// 来源: main.7e49175.js 行 2
function xs(e,
    t,
    n,
    r){
    e.addEventListener(t,
    n,
    r)
}


// 来源: main.7e49175.js 行 2
Xs=function(e){
    return e.Text="Text",
    e.TagCheck="TagCheck",
    e.Input="Input",
    e.InputTextarea="InputTextarea",
    e.Upload="Upload",
    e
}


// 来源: main.7e49175.js 行 2
var Xs=function(e){
    return e.Text="Text",
    e.TagCheck="TagCheck",
    e.Input="Input",
    e.InputTextarea="InputTextarea",
    e.Upload="Upload",
    e
}


// 来源: main.7e49175.js 行 2
xs(e,
    t,
    n,
    r){
    e.addEventListener(t,
    n,
    r)
}


// 来源: main.7e49175.js 行 2
xs(e,
    i?"change":"input",
    (function(t){
    if(!t.target.composing){
    var n=e.value;
o&&(n=xe(n).call(n)),
    s&&(n=xt(n)),
    e[Rs](n)
}


// 来源: main.7e49175.js 行 2
xs(e,
    "change",
    (function(){
    var t;
e.value=xe(t=e.value).call(t)
}


// 来源: main.7e49175_1.js 行 2
function xs(e,
    t,
    n,
    r){
    e.addEventListener(t,
    n,
    r)
}


// 来源: main.7e49175_1.js 行 2
Xs=function(e){
    return e.Text="Text",
    e.TagCheck="TagCheck",
    e.Input="Input",
    e.InputTextarea="InputTextarea",
    e.Upload="Upload",
    e
}


// 来源: main.7e49175_1.js 行 2
var Xs=function(e){
    return e.Text="Text",
    e.TagCheck="TagCheck",
    e.Input="Input",
    e.InputTextarea="InputTextarea",
    e.Upload="Upload",
    e
}


// 来源: main.7e49175_1.js 行 2
xs(e,
    t,
    n,
    r){
    e.addEventListener(t,
    n,
    r)
}


// 来源: main.7e49175_1.js 行 2
xs(e,
    i?"change":"input",
    (function(t){
    if(!t.target.composing){
    var n=e.value;
o&&(n=xe(n).call(n)),
    s&&(n=xt(n)),
    e[Rs](n)
}


// 来源: main.7e49175_1.js 行 2
xs(e,
    "change",
    (function(){
    var t;
e.value=xe(t=e.value).call(t)
}


// ========== common ==========
// 来源: vendor-dynamic.f0f5c43a.js 行 1
Common(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Common(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}


// 来源: vendor-dynamic.f0f5c43a.js 行 1
Common(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Common(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:["api/sec/v1/shield/webprofile"]).forEach(function(e){
    S.push(e)
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Common(e){
    try{
    (e&&Array.isArray(e)&&e.length>0?e:[]).forEach(function(e){
    k.push(e)
}


// 来源: vendor-dynamic.f0f5c43a_1.js 行 1
Common(e,
    r){
    var i,
    a;
try{
    var s=e.platform,
    u=r.url;
if(S.map(function(e){
    return new RegExp(e)
}


// 来源: vendor-main.e645eae.js 行 2
Common(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}


// 来源: vendor-main.e645eae.js 行 2
Common(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}


// 来源: vendor-main.e645eae.js 行 2
Common(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}


// 来源: vendor-main.e645eae_1.js 行 2
Common(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:["api/sec/v1/shield/webprofile"];
for_each_default()(e).call(e,
    (function(t){
    NEED_XSCOMMON_URLS.push(t)
}


// 来源: vendor-main.e645eae_1.js 行 2
Common(t){
    try{
    var e=t&&is_array_default()(t)&&t.length>0?t:[];
for_each_default()(e).call(e,
    (function(t){
    NEED_REAL_TIME_XSCOMMON_URLS.push(t)
}


// 来源: vendor-main.e645eae_1.js 行 2
Common(t,
    e){
    var n,
    r;
try{
    var o,
    i,
    a=t.platform,
    u=e.url,
    c=map_default()(NEED_XSCOMMON_URLS).call(NEED_XSCOMMON_URLS,
    (function(t){
    return new RegExp(t)
}


