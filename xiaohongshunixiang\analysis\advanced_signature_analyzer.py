#!/usr/bin/env python3
"""
高级小红书签名分析器
结合 xhs.js 环境补全文件进行深度逆向分析
"""

import asyncio
import json
import time
import os
import base64
import zlib
import logging
from playwright.async_api import async_playwright
import re

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedSignatureAnalyzer:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
        self.is_initialized = False
        self.xhs_js_path = "xhs0828补环境/xhs.js"
        
    async def init(self):
        """初始化分析器"""
        if self.is_initialized:
            return True
            
        logger.info("🚀 初始化高级签名分析器...")

        try:
            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器便于调试
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # 创建页面
            self.page = await self.browser.new_page()
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                             '(KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36'
            })
            
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            
            # 加载 xhs.js 环境补全文件
            await self._load_xhs_environment()
            
            self.is_initialized = True
            logger.info("✅ 高级签名分析器初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 分析器初始化失败: {e}")
            await self.cleanup()
            return False
    
    async def _load_xhs_environment(self):
        """加载 xhs.js 环境补全文件"""
        try:
            # 读取 xhs.js 文件
            if os.path.exists(self.xhs_js_path):
                with open(self.xhs_js_path, 'r', encoding='utf-8') as f:
                    xhs_js_content = f.read()
                
                logger.info(f"📁 加载 xhs.js 环境文件: {len(xhs_js_content)} 字符")
                
                # 在页面中执行 xhs.js 代码
                await self.page.evaluate(xhs_js_content)
                
                # 验证环境是否正确加载
                result = await self.page.evaluate("""
                    (() => {
                        return {
                            window_exists: typeof window !== 'undefined',
                            navigator_exists: typeof navigator !== 'undefined',
                            document_exists: typeof document !== 'undefined',
                            location_exists: typeof location !== 'undefined',
                            o_function_exists: typeof o === 'function',
                            getXs_function_exists: typeof getXs === 'function'
                        };
                    })()
                """)
                
                logger.info(f"🔍 环境验证结果: {result}")
                
                if result['o_function_exists']:
                    logger.info("✅ 发现 o() 函数 - 这可能是核心签名函数")
                
                if result['getXs_function_exists']:
                    logger.info("✅ 发现 getXs() 函数 - 这可能是签名入口函数")
                
            else:
                logger.warning(f"⚠️ xhs.js 文件不存在: {self.xhs_js_path}")
                
        except Exception as e:
            logger.error(f"❌ 加载 xhs.js 环境失败: {e}")
    
    async def analyze_o_function(self):
        """分析 o() 函数的实现"""
        try:
            logger.info("🔍 开始分析 o() 函数...")
            
            # 获取 o 函数的源码
            o_function_source = await self.page.evaluate("""
                (() => {
                    if (typeof o === 'function') {
                        return o.toString();
                    }
                    return null;
                })()
            """)
            
            if o_function_source:
                logger.info(f"📝 o() 函数源码长度: {len(o_function_source)} 字符")
                
                # 保存函数源码到文件
                output_path = "xiaohongshunixiang/output/o_function_source.js"
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(o_function_source)
                
                logger.info(f"💾 o() 函数源码已保存到: {output_path}")
                
                # 分析函数结构
                analysis = self._analyze_function_structure(o_function_source)
                return analysis
            else:
                logger.warning("⚠️ 未找到 o() 函数")
                return None
                
        except Exception as e:
            logger.error(f"❌ 分析 o() 函数失败: {e}")
            return None
    
    def _analyze_function_structure(self, function_source):
        """分析函数结构"""
        analysis = {
            'length': len(function_source),
            'has_obfuscation': False,
            'variable_patterns': [],
            'string_patterns': [],
            'function_calls': [],
            'complexity_score': 0
        }
        
        # 检测混淆
        if len(re.findall(r'[a-zA-Z]{1,2}\d+', function_source)) > 10:
            analysis['has_obfuscation'] = True
        
        # 提取变量模式
        analysis['variable_patterns'] = re.findall(r'\b[a-zA-Z_$][a-zA-Z0-9_$]*\b', function_source)[:20]
        
        # 提取字符串模式
        analysis['string_patterns'] = re.findall(r"'[^']*'|\"[^\"]*\"", function_source)[:10]
        
        # 提取函数调用
        analysis['function_calls'] = re.findall(r'\b[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(', function_source)[:15]
        
        # 计算复杂度分数
        analysis['complexity_score'] = len(function_source) // 100 + len(analysis['variable_patterns'])
        
        return analysis
    
    async def test_getXs_function(self):
        """测试 getXs 函数"""
        try:
            logger.info("🧪 开始测试 getXs() 函数...")
            
            # 测试参数
            test_url = "https://edith.xiaohongshu.com/api/sns/web/v2/user/me"
            test_data = "{}"
            test_a1 = "1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399"
            
            # 调用 getXs 函数
            result = await self.page.evaluate("""
                (params) => {
                    try {
                        if (typeof getXs === 'function') {
                            const signature = getXs(params.url, params.data, params.a1);
                            return {
                                success: true,
                                signature: signature,
                                type: typeof signature
                            };
                        } else {
                            return {
                                success: false,
                                error: 'getXs function not found'
                            };
                        }
                    } catch (e) {
                        return {
                            success: false,
                            error: e.message,
                            stack: e.stack
                        };
                    }
                }
            """, {
                'url': test_url,
                'data': test_data,
                'a1': test_a1
            })
            
            logger.info(f"🔍 getXs 测试结果: {result}")
            
            if result['success'] and result['signature']:
                # 分析签名结构
                signature_analysis = self._analyze_signature_structure(result['signature'])
                result['signature_analysis'] = signature_analysis
            
            return result
            
        except Exception as e:
            logger.error(f"❌ 测试 getXs() 函数失败: {e}")
            return None
    
    def _analyze_signature_structure(self, signature):
        """分析签名结构"""
        analysis = {
            'has_x_s': 'X-s' in signature if isinstance(signature, dict) else False,
            'has_x_t': 'X-t' in signature if isinstance(signature, dict) else False,
            'signature_type': type(signature).__name__,
            'signature_keys': list(signature.keys()) if isinstance(signature, dict) else None
        }
        
        if isinstance(signature, dict) and 'X-s' in signature:
            x_s = signature['X-s']
            analysis['x_s_length'] = len(x_s)
            analysis['x_s_starts_with_XYW'] = x_s.startswith('XYW_')
            
            # 尝试解析 X-s
            if x_s.startswith('XYW_'):
                try:
                    base64_data = x_s[4:]
                    decoded = base64.b64decode(base64_data).decode('utf-8')
                    parsed = json.loads(decoded)
                    analysis['x_s_parsed'] = parsed
                except Exception as e:
                    analysis['x_s_parse_error'] = str(e)
        
        return analysis
    
    async def extract_signature_algorithm(self):
        """提取签名算法"""
        try:
            logger.info("🔬 开始提取签名算法...")
            
            # 1. 分析 o() 函数
            o_analysis = await self.analyze_o_function()
            
            # 2. 测试 getXs 函数
            getxs_result = await self.test_getXs_function()
            
            # 3. 查找其他相关函数
            other_functions = await self._find_related_functions()
            
            # 4. 生成分析报告
            analysis_report = {
                'timestamp': time.time(),
                'o_function_analysis': o_analysis,
                'getxs_test_result': getxs_result,
                'other_functions': other_functions,
                'recommendations': self._generate_recommendations(o_analysis, getxs_result, other_functions)
            }
            
            # 保存分析报告
            report_path = f"xiaohongshunixiang/output/signature_algorithm_analysis_{int(time.time())}.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(analysis_report, f, indent=2, ensure_ascii=False)
            
            logger.info(f"📊 签名算法分析报告已保存到: {report_path}")
            
            return analysis_report
            
        except Exception as e:
            logger.error(f"❌ 提取签名算法失败: {e}")
            return None
    
    async def _find_related_functions(self):
        """查找相关函数"""
        try:
            # 获取全局函数列表
            global_functions = await self.page.evaluate("""
                (() => {
                    const functions = [];
                    for (let key in window) {
                        if (typeof window[key] === 'function' && key.length < 20) {
                            functions.push({
                                name: key,
                                source_length: window[key].toString().length
                            });
                        }
                    }
                    return functions.sort((a, b) => b.source_length - a.source_length);
                })()
            """)
            
            logger.info(f"🔍 发现 {len(global_functions)} 个全局函数")
            
            return global_functions[:10]  # 返回前10个最大的函数
            
        except Exception as e:
            logger.error(f"❌ 查找相关函数失败: {e}")
            return []
    
    def _generate_recommendations(self, o_analysis, getxs_result, other_functions):
        """生成优化建议"""
        recommendations = []
        
        if o_analysis and o_analysis.get('has_obfuscation'):
            recommendations.append("o() 函数存在混淆，建议使用 JS 美化工具进行反混淆")
        
        if getxs_result and getxs_result.get('success'):
            recommendations.append("getXs() 函数可以正常调用，建议深入分析其实现逻辑")
        
        if len(other_functions) > 5:
            recommendations.append("发现多个大型函数，建议逐一分析其功能")
        
        recommendations.append("建议使用浏览器开发者工具进行动态调试")
        recommendations.append("建议对比真实浏览器环境中的签名生成过程")
        
        return recommendations
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理分析器资源...")
        
        if self.page:
            await self.page.close()
        
        if self.browser:
            await self.browser.close()
        
        if self.playwright:
            await self.playwright.stop()
        
        self.is_initialized = False
        logger.info("✅ 分析器资源清理完成")

async def main():
    """主函数"""
    analyzer = AdvancedSignatureAnalyzer()
    
    try:
        # 初始化分析器
        if not await analyzer.init():
            logger.error("❌ 分析器初始化失败")
            return
        
        # 执行签名算法提取
        analysis_report = await analyzer.extract_signature_algorithm()
        
        if analysis_report:
            logger.info("🎉 签名算法分析完成！")
            logger.info(f"📊 分析报告: {json.dumps(analysis_report['recommendations'], indent=2, ensure_ascii=False)}")
        else:
            logger.error("❌ 签名算法分析失败")
    
    finally:
        await analyzer.cleanup()

if __name__ == "__main__":
    asyncio.run(main())
