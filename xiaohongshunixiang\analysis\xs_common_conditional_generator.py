#!/usr/bin/env python3
"""
条件性 X-S-Common 生成器
支持根据条件包含或排除某些参数
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from analysis.xs_common_generator import XSCommonGenerator
import json

class ConditionalXSCommonGenerator(XSCommonGenerator):
    """支持条件性参数的X-S-Common生成器"""
    
    def generate_xs_common_conditional(self, platform='PC', a1_cookie='', x8_value='', 
                                     x10_value=None, x1_value='', x0_value='1',
                                     include_x0=True, include_empty_params=True):
        """
        生成 X-S-Common 值 (支持条件性参数)
        
        Args:
            platform: 平台类型
            a1_cookie: 从cookie中获取的a1值
            x8_value: x8参数值
            x10_value: x10参数值
            x1_value: x1参数值
            x0_value: x0参数值
            include_x0: 是否包含x0参数
            include_empty_params: 是否包含空参数
            
        Returns:
            str: Base64编码的X-S-Common值
        """
        
        # 生成x9值
        x9_value = ''
        if x8_value:
            x9_hash = self.crc32_hash(x8_value)
            x9_value = str(x9_hash)
        
        # 处理x10值
        if x10_value is None:
            x10_final = self.generate_sig_count(increment=False, return_empty_for_first=True)
        else:
            x10_final = str(x10_value) if x10_value != "" else ""
        
        # 构建参数对象
        v = {
            's0': self.get_platform_code(platform),
            's1': '',
            'x1': x1_value,
            'x2': platform or 'PC',
            'x3': 'xhs-pc-web',
            'x4': '4.68.0',
            'x5': a1_cookie,
            'x6': '',
            'x7': '',
            'x8': x8_value,
            'x9': x9_value,
            'x10': x10_final,
            'x11': 'normal'
        }
        
        # 条件性添加x0参数
        if include_x0:
            v['x0'] = x0_value
        
        # 如果不包含空参数，则移除空值
        if not include_empty_params:
            v = {k: v for k, v in v.items() if v != ''}
        
        # 序列化为JSON
        json_str = json.dumps(v, separators=(',', ':'))
        
        # UTF-8编码
        utf8_data = self.encode_utf8(json_str)
        
        # Base64编码
        xs_common = self.b64_encode(utf8_data)
        
        return xs_common

def test_conditional_generation():
    """测试条件性生成"""
    print("🧪 测试条件性 X-S-Common 生成")
    print("=" * 60)
    
    generator = ConditionalXSCommonGenerator()
    test_cookie = "1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399"
    
    # 已知的X-S-Common值
    known_xs_common = "eyJzMCI6IndlYiIsInMxIjoiIiwieDEiOiIiLCJ4MiI6IlBDIiwieDMiOiJ4aHMtcGMtd2ViIiwieDQiOiI0LjY4LjAiLCJ4NSI6IjE5NzRhMDYyODUyYmprZjI4NGk1bHZtajZ5ajhwczdzcm8xaDlxeGRxNTAwMDA5NzgzOTkiLCJ4NiI6IiIsIng3IjoiIiwieDgiOiIiLCJ4OSI6IiIsIngxMCI6IiIsIngxMSI6Im5vcm1hbCJ9"
    
    print(f"🎯 目标值: {known_xs_common}")
    
    # 测试1: 包含x0参数
    print(f"\n1️⃣ 测试: 包含x0参数")
    xs_common1 = generator.generate_xs_common_conditional(
        platform='PC',
        a1_cookie=test_cookie,
        x8_value='',
        x10_value='',
        x1_value='',
        x0_value='1',
        include_x0=True
    )
    print(f"   结果: {xs_common1}")
    print(f"   匹配: {'✅' if xs_common1 == known_xs_common else '❌'}")
    
    # 测试2: 不包含x0参数
    print(f"\n2️⃣ 测试: 不包含x0参数")
    xs_common2 = generator.generate_xs_common_conditional(
        platform='PC',
        a1_cookie=test_cookie,
        x8_value='',
        x10_value='',
        x1_value='',
        x0_value='1',
        include_x0=False
    )
    print(f"   结果: {xs_common2}")
    print(f"   匹配: {'✅' if xs_common2 == known_xs_common else '❌'}")
    
    # 测试3: 不包含空参数
    print(f"\n3️⃣ 测试: 不包含空参数")
    xs_common3 = generator.generate_xs_common_conditional(
        platform='PC',
        a1_cookie=test_cookie,
        x8_value='',
        x10_value='',
        x1_value='',
        x0_value='1',
        include_x0=False,
        include_empty_params=False
    )
    print(f"   结果: {xs_common3}")
    print(f"   匹配: {'✅' if xs_common3 == known_xs_common else '❌'}")
    
    # 解码对比
    print(f"\n🔍 解码对比:")
    known_decoded = generator.decode_xs_common(known_xs_common)
    
    for i, xs_common in enumerate([xs_common1, xs_common2, xs_common3], 1):
        decoded = generator.decode_xs_common(xs_common)
        print(f"\n测试 {i} 解码:")
        if decoded:
            # 检查参数数量
            print(f"   参数数量: {len(decoded)} (目标: {len(known_decoded)})")
            
            # 检查是否有x0
            has_x0 = 'x0' in decoded
            should_have_x0 = 'x0' in known_decoded
            print(f"   x0参数: {'有' if has_x0 else '无'} (目标: {'有' if should_have_x0 else '无'})")
            
            # 检查关键差异
            if known_decoded:
                diff_count = 0
                for key in set(decoded.keys()) | set(known_decoded.keys()):
                    if decoded.get(key) != known_decoded.get(key):
                        diff_count += 1
                print(f"   差异数量: {diff_count}")

def analyze_known_value_structure():
    """分析已知值的结构"""
    print("\n📊 分析已知值结构:")
    
    generator = ConditionalXSCommonGenerator()
    known_xs_common = "eyJzMCI6IndlYiIsInMxIjoiIiwieDEiOiIiLCJ4MiI6IlBDIiwieDMiOiJ4aHMtcGMtd2ViIiwieDQiOiI0LjY4LjAiLCJ4NSI6IjE5NzRhMDYyODUyYmprZjI4NGk1bHZtajZ5ajhwczdzcm8xaDlxeGRxNTAwMDA5NzgzOTkiLCJ4NiI6IiIsIng3IjoiIiwieDgiOiIiLCJ4OSI6IiIsIngxMCI6IiIsIngxMSI6Im5vcm1hbCJ9"
    
    decoded = generator.decode_xs_common(known_xs_common)
    if decoded:
        print(f"参数总数: {len(decoded)}")
        print(f"包含的参数:")
        for key in sorted(decoded.keys()):
            value = decoded[key]
            print(f"   {key}: '{value}' ({'空' if value == '' else '非空'})")
        
        print(f"\n缺失的参数:")
        expected_params = ['s0', 's1', 'x0', 'x1', 'x2', 'x3', 'x4', 'x5', 'x6', 'x7', 'x8', 'x9', 'x10', 'x11']
        missing_params = [p for p in expected_params if p not in decoded]
        for param in missing_params:
            print(f"   {param}")

def main():
    """主函数"""
    print("🚀 条件性 X-S-Common 生成测试")
    print("=" * 80)
    
    # 分析已知值结构
    analyze_known_value_structure()
    
    # 测试条件性生成
    test_conditional_generation()
    
    print(f"\n💡 结论:")
    print(f"已知的X-S-Common值中确实缺少x0参数")
    print(f"这可能是因为:")
    print(f"1. 某些条件下x0参数不会被包含")
    print(f"2. localStorage.getItem('b1b1')返回null时不包含x0")
    print(f"3. 特定的URL或请求类型不包含x0")

if __name__ == '__main__':
    main()
