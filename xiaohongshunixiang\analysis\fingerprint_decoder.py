#!/usr/bin/env python3
"""
设备指纹解码器
分析和展示设备指纹的真实构成
"""

import base64
import json
import hashlib
import struct
import binascii

class FingerprintDecoder:
    def __init__(self):
        # 小红书的真实设备指纹
        self.xiaohongshu_fingerprint = "I38rHdgsjopgIvesdVwgIC+oIELmBZ5e3VwXLgFTIxS3bqwErFeexd0ekncAzMFYnqthIhJeSnMDKutRI3KsYorWHPtGrbV0P9WfIi/eWc6eYqtyQApPI37ekmR1QL+5Ii6sdneeSfqYHqwl2qt5B0DBIx+PGDi/sVtkIxdsxuwr4qtiIhuaIE3e3LV0I3VTIC7e0utl2ADmsLveDSKsSPw5IEvsiVtJOqw8BuwfPpdeTFWOIx4TIiu6ZPwrPut5IvlaLbgs3qtxIxes1VwHIkumIkIyejgsY/WTge7eSqte/D7sDcpipedeYrDtIC6eDVw2IENsSqtlnlSuNjVtIvoekqt3cZ7sVo4gIESyIhE2QfquIxhnqz8gIkIfoqwkICqWG73sdlOeVPw3IvAe0fgedfDQIi5s3MHM2utAIiKsidvekZNeTPt4nAOeWPwEIvT8zeveSVwAg9osfPwZI34rIxE5Luwwaqw+rekrPI5eDo/eVPwmIhJsSnAekmuvIiAsfI/sxBidIkve3PwlIhQk2VtqOqt1IxesTVtjIk0siqwdIh/sjut3wutnsPw5ICclI3l4wA4jwIAsWVw4IE4qIhOsSqtZBbTt/A0ejjp1IkGPGutPoqwhIvveVPtf+Dee3l5s1rELIE0s6edsiPtzcPwrICJefVwfIkgs60WrICKedo/eWVt3I37eVqwf8BYrIhQIIvKeVL3e60vejcge1qteIEqXICSEpPw8Ii+AIk6e1ImMJ7defVweIkPIgPwhOYNefW=="
        
    def analyze_fingerprint_format(self):
        """分析指纹格式"""
        print("🔍 设备指纹格式分析")
        print("=" * 60)
        
        fingerprint = self.xiaohongshu_fingerprint
        
        print(f"📊 基本信息:")
        print(f"   原始长度: {len(fingerprint)} 字符")
        print(f"   格式: Base64编码字符串")
        print(f"   开头: {fingerprint[:30]}...")
        print(f"   结尾: ...{fingerprint[-30:]}")
        
        # Base64解码
        try:
            decoded_bytes = base64.b64decode(fingerprint)
            print(f"\n📦 Base64解码结果:")
            print(f"   解码后长度: {len(decoded_bytes)} 字节")
            print(f"   数据类型: 二进制数据")
            
            # 显示十六进制
            hex_data = binascii.hexlify(decoded_bytes).decode()
            print(f"   十六进制: {hex_data[:60]}...")
            
            # 尝试解析为不同格式
            self._try_parse_formats(decoded_bytes)
            
        except Exception as e:
            print(f"   解码失败: {e}")
    
    def _try_parse_formats(self, data):
        """尝试解析为不同格式"""
        print(f"\n🔬 格式解析尝试:")
        
        # 1. 尝试UTF-8文本
        try:
            text = data.decode('utf-8')
            print(f"   ✅ UTF-8文本: {text[:50]}...")
        except:
            print(f"   ❌ 不是UTF-8文本")
        
        # 2. 尝试JSON
        try:
            text = data.decode('utf-8')
            json_data = json.loads(text)
            print(f"   ✅ JSON数据: {list(json_data.keys())}")
        except:
            print(f"   ❌ 不是JSON格式")
        
        # 3. 分析二进制结构
        print(f"   📊 二进制分析:")
        print(f"     前20字节: {data[:20]}")
        print(f"     后20字节: {data[-20:]}")
        
        # 4. 查找可能的文本片段
        text_fragments = []
        current_fragment = ""
        for byte in data:
            if 32 <= byte <= 126:  # 可打印ASCII字符
                current_fragment += chr(byte)
            else:
                if len(current_fragment) >= 3:
                    text_fragments.append(current_fragment)
                current_fragment = ""
        
        if text_fragments:
            print(f"   📝 发现文本片段: {text_fragments[:5]}")
        else:
            print(f"   ❌ 未发现明显的文本片段")
    
    def demonstrate_typical_fingerprints(self):
        """演示典型的设备指纹格式"""
        print(f"\n🎯 典型设备指纹格式对比")
        print("=" * 60)
        
        # 1. 简单哈希型
        print(f"1️⃣ 简单哈希型指纹:")
        simple_data = "Windows-Chrome-1920x1080-zh-CN"
        simple_hash = hashlib.md5(simple_data.encode()).hexdigest()
        print(f"   原始数据: {simple_data}")
        print(f"   MD5哈希: {simple_hash}")
        print(f"   特点: 固定长度，不可逆")
        
        # 2. JSON结构型
        print(f"\n2️⃣ JSON结构型指纹:")
        json_fingerprint = {
            "screen": {"width": 1920, "height": 1080},
            "browser": {"name": "Chrome", "version": "120.0.0.0"},
            "os": {"name": "Windows", "version": "10"},
            "timezone": "Asia/Shanghai",
            "language": "zh-CN",
            "plugins": ["PDF Viewer", "Chrome PDF Plugin"],
            "canvas": "a1b2c3d4e5f6...",
            "webgl": "renderer_info_hash"
        }
        json_str = json.dumps(json_fingerprint, separators=(',', ':'))
        json_b64 = base64.b64encode(json_str.encode()).decode()
        print(f"   JSON数据: {json_str[:80]}...")
        print(f"   Base64编码: {json_b64[:80]}...")
        print(f"   特点: 结构化，可解析，信息丰富")
        
        # 3. 二进制打包型
        print(f"\n3️⃣ 二进制打包型指纹:")
        # 模拟二进制打包
        binary_data = struct.pack('>IIHH', 1920, 1080, 120, 10)  # 屏幕宽高，浏览器版本，OS版本
        binary_data += b"Chrome\x00Windows\x00zh-CN\x00"  # 字符串数据
        binary_b64 = base64.b64encode(binary_data).decode()
        print(f"   二进制数据: {binascii.hexlify(binary_data).decode()}")
        print(f"   Base64编码: {binary_b64}")
        print(f"   特点: 紧凑，高效，需要特定解析器")
        
        # 4. 小红书类型分析
        print(f"\n4️⃣ 小红书指纹分析:")
        print(f"   长度: {len(self.xiaohongshu_fingerprint)} 字符")
        print(f"   编码: Base64")
        print(f"   解码后: 583字节二进制数据")
        print(f"   特点: 复杂的二进制结构，包含多种设备特征")
        print(f"   推测: 可能包含Canvas、WebGL、音频等多种指纹")
    
    def analyze_fingerprint_components(self):
        """分析指纹可能包含的组件"""
        print(f"\n🧩 设备指纹组件分析")
        print("=" * 60)
        
        print(f"📱 典型设备指纹包含的信息:")
        
        components = {
            "基础信息": [
                "User-Agent字符串",
                "屏幕分辨率 (1920x1080)",
                "色彩深度 (24bit)",
                "时区 (Asia/Shanghai)",
                "语言设置 (zh-CN)"
            ],
            "浏览器特征": [
                "浏览器版本和特性",
                "支持的MIME类型",
                "插件列表",
                "字体列表",
                "HTTP头信息"
            ],
            "硬件特征": [
                "CPU核心数",
                "内存大小",
                "GPU信息",
                "音频设备信息",
                "传感器数据"
            ],
            "高级指纹": [
                "Canvas指纹 (图形渲染特征)",
                "WebGL指纹 (3D渲染特征)",
                "音频指纹 (音频处理特征)",
                "字体渲染指纹",
                "WebRTC指纹"
            ],
            "行为特征": [
                "鼠标移动模式",
                "键盘输入特征",
                "滚动行为",
                "点击模式",
                "页面交互方式"
            ]
        }
        
        for category, items in components.items():
            print(f"\n🔸 {category}:")
            for item in items:
                print(f"   • {item}")
    
    def demonstrate_fingerprint_generation(self):
        """演示指纹生成过程"""
        print(f"\n⚙️ 设备指纹生成过程演示")
        print("=" * 60)
        
        print(f"🔄 典型的生成流程:")
        
        # 步骤1: 收集原始数据
        print(f"\n1️⃣ 收集原始设备信息:")
        raw_data = {
            "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "screen": {"width": 1920, "height": 1080, "colorDepth": 24},
            "timezone": -480,  # 分钟偏移
            "language": "zh-CN",
            "platform": "Win32",
            "cookieEnabled": True,
            "doNotTrack": None,
            "plugins": ["Chrome PDF Plugin", "Chrome PDF Viewer"],
            "canvas": "canvas_hash_a1b2c3d4",
            "webgl": "webgl_hash_e5f6g7h8"
        }
        
        for key, value in raw_data.items():
            print(f"   {key}: {value}")
        
        # 步骤2: 数据处理
        print(f"\n2️⃣ 数据处理和标准化:")
        processed_data = []
        for key, value in raw_data.items():
            if isinstance(value, dict):
                processed_data.append(f"{key}:{json.dumps(value, separators=(',', ':'))}")
            else:
                processed_data.append(f"{key}:{value}")
        
        combined_string = "|".join(processed_data)
        print(f"   合并字符串: {combined_string[:100]}...")
        
        # 步骤3: 生成指纹
        print(f"\n3️⃣ 生成最终指纹:")
        
        # 方法A: 简单哈希
        hash_fingerprint = hashlib.sha256(combined_string.encode()).hexdigest()
        print(f"   哈希方法: {hash_fingerprint}")
        
        # 方法B: Base64编码
        b64_fingerprint = base64.b64encode(combined_string.encode()).decode()
        print(f"   Base64方法: {b64_fingerprint[:60]}...")
        
        # 方法C: 自定义编码（类似小红书）
        print(f"   自定义编码: 复杂的二进制打包 + 自定义Base64")
        print(f"   结果长度: 780字符（如小红书）")

def main():
    """主函数"""
    print("🎯 设备指纹深度解析")
    print("=" * 80)
    
    decoder = FingerprintDecoder()
    
    # 1. 分析指纹格式
    decoder.analyze_fingerprint_format()
    
    # 2. 演示典型指纹格式
    decoder.demonstrate_typical_fingerprints()
    
    # 3. 分析指纹组件
    decoder.analyze_fingerprint_components()
    
    # 4. 演示生成过程
    decoder.demonstrate_fingerprint_generation()
    
    print(f"\n🎉 总结:")
    print(f"✅ 设备指纹不是简单的加密数据")
    print(f"✅ 而是设备特征信息的编码组合")
    print(f"✅ 小红书使用复杂的二进制格式")
    print(f"✅ 包含多维度的设备和行为特征")

if __name__ == '__main__':
    main()
