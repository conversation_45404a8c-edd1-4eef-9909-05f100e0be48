#!/usr/bin/env python3
"""
设备指纹生命周期分析
展示指纹的生成、存储和使用过程
"""

import json
import time

class FingerprintLifecycleAnalyzer:
    def __init__(self):
        pass
    
    def demonstrate_generation_process(self):
        """演示指纹生成过程"""
        print("🛠️ 设备指纹生成过程")
        print("=" * 60)
        
        print("📍 生成位置: 浏览器端 (JavaScript)")
        print("⏰ 生成时机: 页面加载时 / 首次访问时")
        
        print(f"\n🔧 JavaScript生成代码示例:")
        js_code = '''
// 1. 收集基础信息
function collectBasicInfo() {
    return {
        userAgent: navigator.userAgent,
        language: navigator.language,
        platform: navigator.platform,
        cookieEnabled: navigator.cookieEnabled,
        doNotTrack: navigator.doNotTrack,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timezoneOffset: new Date().getTimezoneOffset()
    };
}

// 2. 收集屏幕信息
function collectScreenInfo() {
    return {
        width: screen.width,
        height: screen.height,
        colorDepth: screen.colorDepth,
        pixelDepth: screen.pixelDepth,
        availWidth: screen.availWidth,
        availHeight: screen.availHeight
    };
}

// 3. 生成Canvas指纹
function generateCanvasFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 绘制复杂图形
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Canvas fingerprint test 🎨', 2, 2);
    
    // 绘制几何图形
    ctx.fillStyle = 'rgba(255,0,255,0.5)';
    ctx.fillRect(50, 50, 100, 100);
    
    // 返回图像数据哈希
    return canvas.toDataURL();
}

// 4. 生成WebGL指纹
function generateWebGLFingerprint() {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) return null;
    
    return {
        vendor: gl.getParameter(gl.VENDOR),
        renderer: gl.getParameter(gl.RENDERER),
        version: gl.getParameter(gl.VERSION),
        shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
    };
}

// 5. 生成音频指纹
function generateAudioFingerprint() {
    return new Promise((resolve) => {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const analyser = audioContext.createAnalyser();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(analyser);
        analyser.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 1000;
        oscillator.start();
        
        setTimeout(() => {
            const frequencyData = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(frequencyData);
            oscillator.stop();
            
            // 计算音频特征哈希
            const hash = Array.from(frequencyData).reduce((a, b) => a + b, 0);
            resolve(hash.toString());
        }, 100);
    });
}

// 6. 主指纹生成函数
async function generateDeviceFingerprint() {
    const fingerprint = {
        basic: collectBasicInfo(),
        screen: collectScreenInfo(),
        canvas: generateCanvasFingerprint(),
        webgl: generateWebGLFingerprint(),
        audio: await generateAudioFingerprint(),
        timestamp: Date.now()
    };
    
    // 序列化并编码
    const jsonString = JSON.stringify(fingerprint);
    const encoded = btoa(unescape(encodeURIComponent(jsonString)));
    
    return encoded;
}
'''
        
        print(js_code)
    
    def demonstrate_storage_locations(self):
        """演示指纹存储位置"""
        print(f"\n💾 设备指纹存储位置")
        print("=" * 60)
        
        storage_locations = {
            "1. localStorage": {
                "位置": "浏览器本地存储",
                "生命周期": "持久化，直到用户清除",
                "访问方式": "localStorage.getItem('deviceFingerprint')",
                "示例代码": '''
// 存储指纹
localStorage.setItem('b1', deviceFingerprint);
localStorage.setItem('b1b1', fingerprintHash);

// 读取指纹
const fingerprint = localStorage.getItem('b1');
const hash = localStorage.getItem('b1b1');
''',
                "小红书使用": "✅ 主要存储位置"
            },
            
            "2. sessionStorage": {
                "位置": "会话存储",
                "生命周期": "浏览器标签页关闭时清除",
                "访问方式": "sessionStorage.getItem('fingerprint')",
                "示例代码": '''
// 存储临时指纹数据
sessionStorage.setItem('tempFingerprint', data);
sessionStorage.setItem('sigCount', count);
''',
                "小红书使用": "✅ 存储签名计数等临时数据"
            },
            
            "3. Cookie": {
                "位置": "HTTP Cookie",
                "生命周期": "根据过期时间",
                "访问方式": "document.cookie",
                "示例代码": '''
// 存储指纹相关信息
document.cookie = "fp_id=abc123; path=/; max-age=31536000";
''',
                "小红书使用": "❌ 不直接存储指纹"
            },
            
            "4. IndexedDB": {
                "位置": "浏览器数据库",
                "生命周期": "持久化",
                "访问方式": "IndexedDB API",
                "示例代码": '''
// 复杂的指纹数据存储
const request = indexedDB.open('FingerprintDB', 1);
''',
                "小红书使用": "❓ 可能用于复杂数据"
            },
            
            "5. 内存变量": {
                "位置": "JavaScript变量",
                "生命周期": "页面刷新时清除",
                "访问方式": "全局变量",
                "示例代码": '''
// 临时存储在内存中
window.deviceFingerprint = generatedFingerprint;
''',
                "小红书使用": "✅ 运行时使用"
            }
        }
        
        for location, info in storage_locations.items():
            print(f"\n🔸 {location}:")
            for key, value in info.items():
                if key == "示例代码":
                    print(f"   {key}:")
                    print(f"   {value}")
                else:
                    print(f"   {key}: {value}")
    
    def demonstrate_usage_scenarios(self):
        """演示指纹使用场景"""
        print(f"\n🎯 设备指纹使用时机")
        print("=" * 60)
        
        usage_scenarios = [
            {
                "场景": "页面初始化",
                "时机": "页面加载完成后",
                "目的": "生成和存储指纹",
                "代码示例": '''
window.addEventListener('load', async () => {
    // 检查是否已有指纹
    let fingerprint = localStorage.getItem('b1');
    
    if (!fingerprint) {
        // 生成新指纹
        fingerprint = await generateDeviceFingerprint();
        localStorage.setItem('b1', fingerprint);
    }
    
    // 初始化完成
    console.log('Device fingerprint ready');
});'''
            },
            
            {
                "场景": "API请求签名",
                "时机": "每次API调用前",
                "目的": "生成x-s-common签名",
                "代码示例": '''
function makeAPIRequest(url, data) {
    // 获取存储的指纹
    const fingerprint = localStorage.getItem('b1');
    const a1Cookie = getCookie('a1');
    
    // 生成x-s-common
    const xsCommon = generateXSCommon({
        platform: 'PC',
        a1: a1Cookie,
        x8: fingerprint,  // 设备指纹
        x10: getSigCount()
    });
    
    // 发起请求
    fetch(url, {
        headers: {
            'x-s-common': xsCommon,
            'x-s': generateXS(url, data),
            'x-t': Date.now()
        },
        body: JSON.stringify(data)
    });
}'''
            },
            
            {
                "场景": "用户行为验证",
                "时机": "关键操作时",
                "目的": "验证用户身份",
                "代码示例": '''
function validateUserAction(action) {
    const currentFingerprint = generateQuickFingerprint();
    const storedFingerprint = localStorage.getItem('b1');
    
    // 指纹一致性检查
    if (fingerprintMatch(currentFingerprint, storedFingerprint)) {
        // 允许操作
        performAction(action);
    } else {
        // 可能的安全风险
        requestAdditionalVerification();
    }
}'''
            },
            
            {
                "场景": "异常检测",
                "时机": "检测到异常行为时",
                "目的": "安全防护",
                "代码示例": '''
function detectAnomalies() {
    const metrics = {
        mouseMovements: getMouseMetrics(),
        keyboardPattern: getKeyboardMetrics(),
        scrollBehavior: getScrollMetrics(),
        fingerprint: localStorage.getItem('b1')
    };
    
    // 发送到服务器进行分析
    sendSecurityMetrics(metrics);
}'''
            }
        ]
        
        for i, scenario in enumerate(usage_scenarios, 1):
            print(f"\n{i}️⃣ {scenario['场景']}:")
            print(f"   时机: {scenario['时机']}")
            print(f"   目的: {scenario['目的']}")
            print(f"   代码示例:")
            print(f"   {scenario['代码示例']}")
    
    def demonstrate_xiaohongshu_specific(self):
        """演示小红书特定的指纹使用"""
        print(f"\n🔴 小红书指纹使用分析")
        print("=" * 60)
        
        print(f"📍 基于我们的逆向分析:")
        
        xiaohongshu_flow = {
            "1. 指纹生成": {
                "位置": "小红书网站JavaScript",
                "时机": "页面加载时",
                "存储": "localStorage.setItem('b1', fingerprint)",
                "特点": "780字符的复杂Base64编码"
            },
            
            "2. 指纹存储": {
                "主要位置": "localStorage['b1']",
                "辅助存储": "localStorage['b1b1'] (可能是哈希)",
                "会话数据": "sessionStorage (签名计数等)",
                "生命周期": "长期持久化"
            },
            
            "3. 指纹使用": {
                "API签名": "作为x-s-common的x8参数",
                "CRC32计算": "计算指纹的CRC32作为x9参数",
                "请求验证": "每次API调用都需要",
                "安全检查": "服务器端验证指纹合法性"
            },
            
            "4. 关键发现": {
                "固定性": "指纹相对固定，不包含时间戳",
                "复杂性": "包含多种设备特征",
                "重要性": "API验证的核心组件",
                "验证": "我们已验证真实指纹可以成功调用API"
            }
        }
        
        for section, details in xiaohongshu_flow.items():
            print(f"\n🔸 {section}:")
            for key, value in details.items():
                print(f"   {key}: {value}")
        
        print(f"\n💡 关键代码流程:")
        key_flow = '''
// 1. 页面加载时生成/获取指纹
window.addEventListener('load', () => {
    let fingerprint = localStorage.getItem('b1');
    if (!fingerprint) {
        fingerprint = generateComplexFingerprint();
        localStorage.setItem('b1', fingerprint);
    }
});

// 2. API调用时使用指纹
function callXHSAPI(url, data) {
    const fingerprint = localStorage.getItem('b1');
    const a1 = getCookie('a1');
    
    // 生成x-s-common (包含指纹)
    const xsCommon = generateXSCommon({
        s0: 'web',
        x5: a1,
        x8: fingerprint,  // 关键：设备指纹
        x9: crc32(fingerprint),  // 指纹的CRC32
        x10: getSigCount()
    });
    
    // 发起请求
    return fetch(url, {
        headers: {
            'x-s-common': xsCommon,
            'x-s': generateXS(url, data),
            'x-t': Date.now().toString()
        }
    });
}
'''
        print(key_flow)

def main():
    """主函数"""
    print("🎯 设备指纹生命周期完整分析")
    print("=" * 80)
    
    analyzer = FingerprintLifecycleAnalyzer()
    
    # 1. 生成过程
    analyzer.demonstrate_generation_process()
    
    # 2. 存储位置
    analyzer.demonstrate_storage_locations()
    
    # 3. 使用场景
    analyzer.demonstrate_usage_scenarios()
    
    # 4. 小红书特定分析
    analyzer.demonstrate_xiaohongshu_specific()
    
    print(f"\n🎉 总结:")
    print(f"✅ 指纹由JavaScript在浏览器中生成")
    print(f"✅ 主要存储在localStorage中")
    print(f"✅ 每次API调用时都会使用")
    print(f"✅ 是小红书API验证的核心组件")

if __name__ == '__main__':
    main()
