{"name": "xiaohongshu-xs-common-reverse", "version": "1.0.0", "description": "小红书 x-s-common 逆向分析工具", "main": "reverse/x_s_common_v1.js", "scripts": {"start": "node reverse/x_s_common_v1.js", "test": "node reverse/x_s_common_v1.js && python tools/test_suite.py", "debug": "node analysis/browser_debugger.js", "hook": "node analysis/hook_analysis.js", "server": "python tools/signature_server.py", "analyze": "npm run debug && npm run hook"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "reverse", "signature", "x-s-common", "analysis"], "author": "Reverse Engineer", "license": "MIT", "dependencies": {"playwright": "^1.40.0", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.0"}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "local"}}