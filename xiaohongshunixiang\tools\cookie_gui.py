#!/usr/bin/env python3
"""
简化版小红书Cookie验证工具 - GUI版本
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import requests
import json
import threading
from datetime import datetime
import urllib3

# 禁用 SSL 警告（用于代理环境）
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class SimpleCookieValidatorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🍪 小红书Cookie验证工具 (简化版)")
        self.root.geometry("900x650")
        self.root.resizable(True, True)

        # SSL 和代理配置
        self.ssl_verify = tk.BooleanVar(value=False)  # 默认关闭 SSL 验证
        self.use_proxy = tk.BooleanVar(value=False)
        self.proxy_url = tk.StringVar(value="http://127.0.0.1:8080")

        self.setup_ui()

        # 默认cookie
        self.default_cookie = "a1=1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399;webId=77532cb79e595615769bcc5df41c0386;web_session=040069b937bf843c7b6e1952713a4befbda53e;"
        self.cookie_entry.insert(0, self.default_cookie)

    def get_request_config(self):
        """获取请求配置（SSL和代理）"""
        config = {
            'verify': self.ssl_verify.get()
        }

        if self.use_proxy.get() and self.proxy_url.get():
            proxy = self.proxy_url.get()
            config['proxies'] = {
                'http': proxy,
                'https': proxy
            }

        return config
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🍪 小红书Cookie验证工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Cookie输入区域
        ttk.Label(main_frame, text="Cookie:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.cookie_entry = ttk.Entry(main_frame, width=80)
        self.cookie_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))

        # 网络配置区域
        config_frame = ttk.LabelFrame(main_frame, text="🔧 网络配置", padding="5")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        # SSL 验证选项
        ssl_frame = ttk.Frame(config_frame)
        ssl_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Checkbutton(ssl_frame, text="启用SSL证书验证", variable=self.ssl_verify).pack(side=tk.LEFT)
        ttk.Label(ssl_frame, text="(关闭此选项可解决代理SSL问题)", foreground="gray").pack(side=tk.LEFT, padx=(10, 0))

        # 代理配置
        proxy_frame = ttk.Frame(config_frame)
        proxy_frame.pack(fill=tk.X)

        ttk.Checkbutton(proxy_frame, text="使用代理:", variable=self.use_proxy).pack(side=tk.LEFT)
        proxy_entry = ttk.Entry(proxy_frame, textvariable=self.proxy_url, width=30)
        proxy_entry.pack(side=tk.LEFT, padx=(5, 0))

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        self.validate_btn = ttk.Button(button_frame, text="🔍 验证Cookie", 
                                      command=self.start_validation)
        self.validate_btn.pack(side=tk.LEFT, padx=5)
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空日志", 
                                   command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        self.test_service_btn = ttk.Button(button_frame, text="🔧 测试签名服务", 
                                          command=self.test_sign_service)
        self.test_service_btn.pack(side=tk.LEFT, padx=5)
        
        self.test_api_btn = ttk.Button(button_frame, text="🧪 测试API", 
                                      command=self.test_api_direct)
        self.test_api_btn.pack(side=tk.LEFT, padx=5)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="验证日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=22, width=100)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def test_sign_service(self):
        """测试签名服务"""
        def test():
            self.log("🔐 检查签名服务...")
            try:
                config = self.get_request_config()
                response = requests.get("http://localhost:5107/health", timeout=5, **config)
                if response.status_code == 200:
                    result = response.json()
                    self.log("✅ 签名服务连接正常")
                    self.log(f"   服务状态: {result.get('status', 'N/A')}")
                    self.log(f"   初始化状态: {result.get('initialized', 'N/A')}")
                    return True
                else:
                    self.log(f"❌ 签名服务响应异常: {response.status_code}")
                    return False
            except Exception as e:
                self.log(f"❌ 无法连接签名服务: {e}")
                self.log("💡 请先启动签名服务: python3 webmsxyw_signature_service.py")
                return False
                
        threading.Thread(target=test, daemon=True).start()
        
    def test_api_direct(self):
        """直接测试API调用"""
        def test():
            cookie = self.cookie_entry.get().strip()
            if not cookie:
                self.log("❌ 请先输入Cookie")
                return
                
            self.log("🧪 开始直接API测试...")
            
            # 1. 测试签名生成
            self.log("🔐 步骤1: 生成签名...")
            try:
                signature_request = {
                    "url": "/api/sns/web/v2/user/me",
                    "data": {}
                }
                
                config = self.get_request_config()
                response = requests.post(
                    "http://localhost:5107/signature/generate",
                    json=signature_request,
                    timeout=30,
                    **config
                )
                
                if response.status_code != 200:
                    self.log(f"❌ 签名生成失败: {response.status_code} - {response.text}")
                    return
                
                signature_result = response.json()
                if not signature_result.get('success'):
                    self.log(f"❌ 签名生成失败: {signature_result.get('error')}")
                    return
                
                signature = signature_result['signature']
                self.log(f"✅ 签名生成成功:")
                self.log(f"   X-s: {signature.get('X-s', '')[:50]}...")
                self.log(f"   X-t: {signature.get('X-t')}")
                self.log(f"   x-s-common: {signature.get('x-s-common', '')[:50]}...")
                
            except Exception as e:
                self.log(f"❌ 签名生成异常: {e}")
                return
            
            # 2. 测试API调用
            self.log("\n🚀 步骤2: 调用用户信息API...")
            try:
                headers = {
                    'Accept': 'application/json, text/plain, */*',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Content-Type': 'application/json;charset=UTF-8',
                    'Origin': 'https://www.xiaohongshu.com',
                    'Referer': 'https://www.xiaohongshu.com/',
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'X-s': signature.get('X-s', ''),
                    'X-t': str(signature.get('X-t', '')),
                    'x-s-common': signature.get('x-s-common', ''),
                    'Cookie': cookie
                }
                
                config = self.get_request_config()
                api_response = requests.get(
                    "https://edith.xiaohongshu.com/api/sns/web/v2/user/me",
                    headers=headers,
                    timeout=15,
                    **config
                )
                
                self.log(f"📊 API调用结果:")
                self.log(f"   状态码: {api_response.status_code}")
                self.log(f"   响应大小: {len(api_response.content)} 字节")
                
                if api_response.status_code == 200:
                    try:
                        user_data = api_response.json()
                        self.log(f"✅ API调用成功:")
                        
                        if user_data.get('success'):
                            user_info = user_data.get('data', {})
                            self.log(f"   👤 用户ID: {user_info.get('user_id', 'N/A')}")
                            self.log(f"   👤 昵称: {user_info.get('nickname', 'N/A')}")
                            self.log(f"   📧 邮箱: {user_info.get('email', 'N/A')}")
                            self.log(f"   📱 手机: {user_info.get('phone', 'N/A')}")
                            
                            self.log("\n🎉 Cookie验证成功！用户信息获取正常")
                            messagebox.showinfo("验证成功", f"Cookie验证成功！\n用户: {user_info.get('nickname', 'N/A')}")
                        else:
                            self.log(f"❌ API返回失败: {user_data.get('msg', 'N/A')}")
                            
                    except json.JSONDecodeError:
                        self.log(f"❌ JSON解析失败: {api_response.text[:200]}...")
                        
                else:
                    self.log(f"❌ API调用失败: {api_response.status_code}")
                    self.log(f"   响应内容: {api_response.text[:200]}...")
                    
            except Exception as e:
                self.log(f"❌ API调用异常: {e}")
                
        threading.Thread(target=test, daemon=True).start()
        
    def start_validation(self):
        """开始完整验证"""
        cookie = self.cookie_entry.get().strip()
        if not cookie:
            messagebox.showerror("错误", "请输入Cookie")
            return
            
        self.validate_btn.config(state='disabled')
        self.progress.start()
        
        def validate():
            try:
                self.log("🍪 开始完整Cookie验证...")
                self.log("="*60)
                
                # 解析Cookie信息
                cookie_dict = {}
                for item in cookie.split(';'):
                    if '=' in item:
                        key, value = item.strip().split('=', 1)
                        cookie_dict[key] = value
                
                self.log("📋 Cookie信息:")
                self.log(f"   a1: {cookie_dict.get('a1', 'N/A')}")
                self.log(f"   webId: {cookie_dict.get('webId', 'N/A')}")
                self.log(f"   web_session: {cookie_dict.get('web_session', 'N/A')[:20]}...")
                
                # 测试签名服务
                self.log("\n🔐 检查签名服务...")
                config = self.get_request_config()
                response = requests.get("http://localhost:5107/health", timeout=5, **config)
                if response.status_code == 200:
                    self.log("✅ 签名服务正常")
                else:
                    self.log("❌ 签名服务异常")
                    return
                
                # 测试API调用
                self.test_api_direct()
                
            except Exception as e:
                self.log(f"❌ 验证过程出错: {e}")
            finally:
                self.root.after(0, lambda: [
                    self.progress.stop(),
                    self.validate_btn.config(state='normal')
                ])
                
        threading.Thread(target=validate, daemon=True).start()


def main():
    """主函数"""
    root = tk.Tk()
    app = SimpleCookieValidatorGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
