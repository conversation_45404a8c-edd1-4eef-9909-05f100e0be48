# 小红书 X-S-Common 请求头生成器

基于完整逆向分析的小红书 `x-s-common` 请求头生成工具。

## 🎯 功能特性

- ✅ **完整算法实现** - 基于逆向分析的完整 `xsCommon` 函数
- ✅ **CRC32算法** - 正确实现的 `mcr` 函数 (替代原始的 `O` 函数)
- ✅ **自定义Base64** - 使用小红书特有的Base64编码表
- ✅ **环境模拟** - 完整的浏览器环境模拟 (localStorage, sessionStorage等)
- ✅ **URL匹配** - 准确的URL模式匹配逻辑
- ✅ **平台支持** - 支持多平台代码生成
- ✅ **签名计数** - 正确的签名计数器实现

## 📋 核心组件

### 1. 主要函数
- `xsCommon(config, request)` - 核心生成函数
- `generateXSCommon(url, options)` - 简化调用接口
- `mcr(inputStr)` - CRC32算法实现
- `b64Encode(bytes)` - 自定义Base64编码

### 2. 辅助函数
- `getPlatformCode(platform)` - 平台代码获取
- `getSigCount(hasSign)` - 签名计数管理
- `utils_shouldSign(url)` - URL签名判断
- `encodeUtf8(str)` - UTF-8编码

### 3. 环境模拟
- `localStorage` - 本地存储模拟
- `sessionStorage` - 会话存储模拟
- `js_cookie` - Cookie访问模拟

## 🚀 使用方法

### 基础使用

```javascript
const { generateXSCommon, setUserData } = require('./xs_common_generator');

// 设置用户数据
setUserData({
    a1: "your_a1_cookie_value",
    b1: "your_device_fingerprint",
    b1b1: "1"
});

// 生成 x-s-common
const url = "https://www.xiaohongshu.com/api/sns/web/v1/feed";
const xsCommon = generateXSCommon(url, {
    platform: "PC",
    xT: Date.now().toString(),
    xS: "your_xs_value"
});

console.log("X-S-Common:", xsCommon);
```

### 高级使用

```javascript
const { xsCommon } = require('./xs_common_generator');

const config = {
    platform: "PC"
};

const request = {
    url: "https://www.xiaohongshu.com/fe_api/burdock/v2/note/post",
    headers: {
        "X-Sign": "existing_sign_value",
        "X-t": Date.now().toString(),
        "X-s": "existing_s_value"
    }
};

const result = xsCommon(config, request);
console.log("Updated request:", result);
```

### 命令行使用

```bash
# 直接运行测试
node xs_common_generator.js

# 为特定URL生成
node xs_common_generator.js "https://www.xiaohongshu.com/api/sns/web/v1/feed" "PC"
```

## 📊 数据结构

### X-S-Common JSON结构

```json
{
    "s0": 5,           // 平台代码 (PC=5)
    "s1": "",          // 保留字段
    "x0": "1",         // RC4密钥版本
    "x1": "4.0.8",     // 版本号
    "x2": "PC",        // 平台名称
    "x3": "xhs-pc-web", // 应用标识
    "x4": "4.68.0",    // 应用版本
    "x5": "",          // a1 cookie值
    "x6": "",          // X-t 头值
    "x7": "",          // X-s 头值
    "x8": "device_fp", // 设备指纹 (b1)
    "x9": 1234567890,  // CRC32签名
    "x10": 0,          // 签名计数
    "x11": "normal"    // 状态标识
}
```

### 平台代码映射

```javascript
PlatformCode = {
    Android: 1,
    iOS: 2,
    MacOs: 3,
    Linux: 4,
    other: 5  // PC默认
}
```

## 🔧 配置选项

### setUserData(userData)

```javascript
setUserData({
    a1: "cookie_a1_value",      // a1 cookie值
    b1: "device_fingerprint",   // 设备指纹
    b1b1: "1"                   // RC4密钥版本
});
```

### generateXSCommon(url, options)

```javascript
const options = {
    platform: "PC",           // 平台: PC, Android, iOS, Mac OS, Linux
    xSign: "",               // 现有X-Sign头
    xT: "",                  // 现有X-t头  
    xS: ""                   // 现有X-s头
};
```

## 🧪 测试验证

### 运行测试

```bash
node xs_common_generator.js
```

### 测试输出示例

```
🧪 测试 x-s-common 生成器
==================================================

📝 测试 1: https://www.xiaohongshu.com/api/sns/web/v1/feed
✅ 生成成功: eyJzMCI6NSwiczEiOiIiLCJ4MCI6IjEiLCJ4MSI6IjQuMC44Ii...
📊 解码验证: {
  s0: 5,
  x1: '4.0.8',
  x2: 'PC',
  x3: 'xhs-pc-web',
  x4: '4.68.0',
  x10: 1,
  x11: 'normal'
}
```

## 📝 注意事项

1. **Cookie值** - 需要提供真实的 `a1` cookie值
2. **设备指纹** - `b1` 值应该保持一致性
3. **URL匹配** - 只有匹配的URL才会生成x-s-common
4. **签名计数** - 会自动递增，模拟真实行为
5. **平台一致性** - 平台代码应与User-Agent保持一致

## 🔍 调试技巧

### 启用详细日志

```javascript
// 在生成前添加调试信息
console.log("URL:", url);
console.log("Platform:", platform);
console.log("User Data:", { a1, b1, b1b1 });
```

### 验证生成结果

```javascript
// 解码验证
const decoded = Buffer.from(xsCommon, 'base64').toString('utf-8');
const parsed = JSON.parse(decoded);
console.log("Decoded X-S-Common:", parsed);
```

## 🚨 常见问题

### Q: 生成的x-s-common无效？
A: 检查a1 cookie值是否正确，URL是否在支持列表中

### Q: 签名验证失败？
A: 确保x9字段的CRC32计算正确，输入字符串格式为 `${xT}${xS}${b1}`

### Q: 平台代码错误？
A: 检查platform参数是否正确，PC默认使用代码5

## 📚 相关文件

- `xs_common_generator.js` - 主要生成器
- `extract_xscommon.py` - 原始提取工具
- `extract_missing_variables.py` - 变量提取工具
- `analysis/` - 分析结果目录

## 🎉 更新日志

- **v1.0.0** - 初始版本，完整功能实现
- 基于逆向分析的完整算法
- 支持所有主要平台
- 包含完整的测试套件
