#!/usr/bin/env python3
"""
查找 xsCommon 函数的依赖函数
"""

import os
import re
import json
from datetime import datetime


def search_dependencies(directory):
    """搜索关键依赖函数"""
    
    # 需要查找的关键函数和变量
    targets = {
        'getPlatformCode': r'function\s+getPlatformCode\s*\([^)]*\)\s*\{[^}]*\}',
        'getSigCount': r'function\s+getSigCount\s*\([^)]*\)\s*\{[^}]*\}',
        'O_function': r'function\s+O\s*\([^)]*\)\s*\{[^}]*\}',
        'utils_shouldSign': r'function\s+utils_shouldSign\s*\([^)]*\)\s*\{[^}]*\}',
        'b64Encode': r'function\s+b64Encode\s*\([^)]*\)\s*\{[^}]*\}',
        'encodeUtf8': r'function\s+encodeUtf8\s*\([^)]*\)\s*\{[^}]*\}',
        'xhsFingerprintV3': r'xhsFingerprintV3[^;]*',
        'localStorage_b1': r'localStorage\.(?:getItem|setItem)\s*\(\s*["\']b1["\']',
        'localStorage_b1b1': r'localStorage\.(?:getItem|setItem)\s*\(\s*["\']b1b1["\']',
        'variable_C': r'(?:var|let|const)\s+C\s*=\s*[^;]+',
        'variable_S': r'(?:var|let|const)\s+S\s*=\s*\[[^\]]*\]',
        'variable_k': r'(?:var|let|const)\s+k\s*=\s*\[[^\]]*\]',
        'l_Z_get': r'l\.Z\.get\s*\(\s*["\']a1["\']',
    }
    
    results = {}
    
    # 获取所有JS文件
    js_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    
    print(f"🔍 在 {len(js_files)} 个JS文件中搜索依赖函数...")
    
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            file_results = {}
            
            # 搜索每个目标
            for target_name, pattern in targets.items():
                matches = re.finditer(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
                
                for match in matches:
                    if target_name not in file_results:
                        file_results[target_name] = []
                    
                    # 获取匹配的行号
                    lines_before = content[:match.start()].count('\n')
                    line_number = lines_before + 1
                    
                    # 获取匹配的文本（限制长度）
                    matched_text = match.group()
                    if len(matched_text) > 200:
                        matched_text = matched_text[:200] + "..."
                    
                    file_results[target_name].append({
                        'line_number': line_number,
                        'matched_text': matched_text,
                        'full_match': match.group()
                    })
            
            if file_results:
                results[os.path.basename(js_file)] = file_results
                
        except Exception as e:
            print(f"❌ 读取文件 {js_file} 时出错: {e}")
    
    return results


def print_results(results):
    """打印搜索结果"""
    if not results:
        print("❌ 没有找到相关的依赖函数")
        return
    
    print(f"\n✅ 在 {len(results)} 个文件中找到依赖函数:")
    print("="*80)
    
    for file_name, file_results in results.items():
        print(f"\n📄 文件: {file_name}")
        print("-" * 60)
        
        for target_name, matches in file_results.items():
            print(f"  🎯 {target_name}: 找到 {len(matches)} 个匹配")
            for match in matches:
                print(f"     行 {match['line_number']}: {match['matched_text']}")
            print()


def extract_key_functions(results):
    """提取关键函数的完整代码"""
    key_functions = {}
    
    for file_name, file_results in results.items():
        for target_name, matches in file_results.items():
            if target_name in ['getPlatformCode', 'getSigCount', 'O_function', 'b64Encode', 'encodeUtf8']:
                for match in matches:
                    if target_name not in key_functions:
                        key_functions[target_name] = []
                    
                    key_functions[target_name].append({
                        'file': file_name,
                        'line': match['line_number'],
                        'code': match['full_match']
                    })
    
    return key_functions


def save_analysis(results, key_functions, output_dir):
    """保存分析结果"""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # 保存完整结果
    with open(os.path.join(output_dir, f"dependency_analysis_{timestamp}.json"), 'w', encoding='utf-8') as f:
        json.dump({
            'analysis_time': datetime.now().isoformat(),
            'results': results,
            'key_functions': key_functions
        }, f, indent=2, ensure_ascii=False)
    
    # 保存关键函数代码
    with open(os.path.join(output_dir, f"key_functions_{timestamp}.js"), 'w', encoding='utf-8') as f:
        f.write("// 提取的关键函数代码\n")
        f.write(f"// 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for func_name, func_list in key_functions.items():
            f.write(f"// ========== {func_name} ==========\n")
            for func_info in func_list:
                f.write(f"// 文件: {func_info['file']} 行: {func_info['line']}\n")
                f.write(func_info['code'])
                f.write("\n\n")
    
    # 保存文本报告
    with open(os.path.join(output_dir, f"dependency_report_{timestamp}.txt"), 'w', encoding='utf-8') as f:
        f.write("X-S-Common 依赖函数分析报告\n")
        f.write("="*50 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("发现的依赖函数:\n")
        f.write("-"*30 + "\n")
        
        for file_name, file_results in results.items():
            f.write(f"\n文件: {file_name}\n")
            for target_name, matches in file_results.items():
                f.write(f"  {target_name}: {len(matches)} 个匹配\n")
                for match in matches:
                    f.write(f"    行 {match['line_number']}: {match['matched_text'][:100]}...\n")
        
        f.write(f"\n\n关键函数代码:\n")
        f.write("-"*30 + "\n")
        
        for func_name, func_list in key_functions.items():
            f.write(f"\n{func_name}:\n")
            for func_info in func_list:
                f.write(f"  文件: {func_info['file']} 行: {func_info['line']}\n")
                f.write(f"  代码: {func_info['code'][:200]}...\n\n")
    
    print(f"✅ 分析结果已保存到 {output_dir} 目录")


def main():
    """主函数"""
    print("🔍 X-S-Common 依赖函数分析工具")
    print("="*50)
    
    # 搜索目录
    js_dir = os.path.join(os.getcwd(), "data", "js")
    output_dir = os.path.join(os.getcwd(), "analysis")
    
    if not os.path.exists(js_dir):
        print(f"❌ JS文件目录不存在: {js_dir}")
        return
    
    print(f"📁 搜索目录: {js_dir}")
    
    # 搜索依赖函数
    results = search_dependencies(js_dir)
    
    # 显示结果
    print_results(results)
    
    # 提取关键函数
    key_functions = extract_key_functions(results)
    
    if key_functions:
        print("\n🔑 提取的关键函数:")
        print("-"*30)
        for func_name, func_list in key_functions.items():
            print(f"  {func_name}: {len(func_list)} 个实现")
    
    # 保存分析结果
    if results:
        save_analysis(results, key_functions, output_dir)
    
    print("\n💡 建议下一步:")
    print("1. 查看生成的关键函数代码文件")
    print("2. 分析 getPlatformCode 和 getSigCount 的实现逻辑")
    print("3. 查找变量 C、S、k 的定义")
    print("4. 分析指纹生成逻辑 xhsFingerprintV3")


if __name__ == '__main__':
    main()
