#!/usr/bin/env node
/**
 * 简化的 x-s-common 生成器测试
 */

// 真实的Cookie数据
const REAL_COOKIE = "abRequestId=41b545ec-e397-57bd-9d91-3569da43d3d0; xsecappid=xhs-pc-web; a1=19729ca3ed9f71apxjzx5inaa61xgcarnmjwbkgum50000568152; webId=00ec9cba945a033e639b90fd1084ed06; gid=yjWJjS02JiIYyjWJjS0qd6TkfjiWy0UCVuC2V763I00KyE28CkS0FD8882KYy2J804SS4KWf";

// 解析Cookie
function parseCookie(cookieString) {
    const cookies = {};
    cookieString.split(';').forEach(cookie => {
        const [name, value] = cookie.trim().split('=');
        if (name && value) {
            cookies[name] = decodeURIComponent(value);
        }
    });
    return cookies;
}

// CRC32算法
function mcr(inputStr) {
    const crcTable = [
        0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685, 2657392035,
        249268274, 2044508324, 3772115230, 2547177864, 162941995, 2125561021, 3887607047, 2428444049,
        498536548, 1789927666, 4089016648, 2227061214, 450548861, 1843258603, 4107580753, 2211677639,
        325883990, 1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755, 2366115317,
        997073096, 1281953886, 3579855332, 2724688242, 1006888145, 1258607687, 3524101629, 2768942443,
        901097722, 1119000684, 3686517206, 2898065728, 853044451, 1172266101, 3705015759, 2882616665,
        651767980, 1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705, 3099436303,
        671266974, 1594198024, 3322730930, 2970347812, 795835527, 1483230225, 3244367275, 3060149565,
        1994146192, 31158534, 2563907772, 4023717930, 1907459465, 112637215, 2680153253, 3904427059,
        2013776290, 251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719, 3865271297,
        1802195444, 476864866, 2238001368, 4066508878, 1812370925, 453092731, 2181625025, 4111451223,
        1706088902, 314042704, 2344532202, 4240017532, 1658658271, 366619977, 2362670323, 4224994405,
        1303535960, 984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733, 3554079995,
        1131014506, 879679996, 2909243462, 3663771856, 1141124467, 855842277, 2852801631, 3708648649,
        1342533948, 654459306, 3188396048, 3373015174, 1466479909, 544179635, 3110523913, 3462522015,
        1591671054, 702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443, 3233442989,
        3988292384
    ];
    
    let crc = -1;
    const maxLen = Math.min(57, inputStr.length);
    
    for (let i = 0; i < maxLen; i++) {
        const byte = inputStr.charCodeAt(i);
        const tableIndex = (crc & 255) ^ byte;
        crc = crcTable[tableIndex] ^ (crc >>> 8);
    }
    
    return (crc ^ -1 ^ 3988292384) >>> 0;
}

// UTF-8编码
function encodeUtf8(str) {
    const encoded = encodeURIComponent(str);
    const bytes = [];
    
    for (let i = 0; i < encoded.length; i++) {
        const char = encoded.charAt(i);
        if (char === '%') {
            const hex = encoded.charAt(i + 1) + encoded.charAt(i + 2);
            const byte = parseInt(hex, 16);
            bytes.push(byte);
            i += 2;
        } else {
            bytes.push(char.charCodeAt(0));
        }
    }
    
    return bytes;
}

// 自定义Base64编码
function b64Encode(bytes) {
    const code = "ZmserbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5";
    const lookup = [];
    for (let i = 0; i < code.length; i++) {
        lookup[i] = code.charAt(i);
    }
    
    const len = bytes.length;
    const remainder = len % 3;
    const chunks = [];
    
    // 处理完整的3字节块
    for (let i = 0; i < len - remainder; i += 3) {
        const a = bytes[i] || 0;
        const b = bytes[i + 1] || 0;
        const c = bytes[i + 2] || 0;
        
        const triplet = (a << 16) | (b << 8) | c;
        
        chunks.push(
            lookup[(triplet >> 18) & 63] +
            lookup[(triplet >> 12) & 63] +
            lookup[(triplet >> 6) & 63] +
            lookup[triplet & 63]
        );
    }
    
    // 处理剩余字节
    if (remainder === 1) {
        const lastByte = bytes[len - 1];
        chunks.push(
            lookup[lastByte >> 2] +
            lookup[(lastByte << 4) & 63] +
            "=="
        );
    } else if (remainder === 2) {
        const secondLast = bytes[len - 2];
        const last = bytes[len - 1];
        const combined = (secondLast << 8) + last;
        chunks.push(
            lookup[combined >> 10] +
            lookup[(combined >> 4) & 63] +
            lookup[(combined << 2) & 63] +
            "="
        );
    }
    
    return chunks.join('');
}

// 生成 x-s-common
function generateXSCommon(url, options = {}) {
    const cookies = parseCookie(REAL_COOKIE);
    
    // 构建数据结构
    const commonData = {
        s0: 5,  // PC平台代码
        s1: "",
        x0: "1",  // RC4版本
        x1: "4.0.8",  // 版本号
        x2: "PC",
        x3: "xhs-pc-web",
        x4: "4.68.0",
        x5: cookies.a1 || "",  // a1 cookie
        x6: options.xT || "",  // X-t头
        x7: options.xS || "",  // X-s头
        x8: cookies.webId || "",  // 设备指纹
        x9: 0,  // CRC32值，稍后计算
        x10: 1,  // 签名计数
        x11: "normal"
    };
    
    // 计算CRC32
    const crcInput = `${commonData.x6}${commonData.x7}${commonData.x8}`;
    commonData.x9 = mcr(crcInput);
    
    // 编码
    const jsonStr = JSON.stringify(commonData);
    const utf8Bytes = encodeUtf8(jsonStr);
    const base64Result = b64Encode(utf8Bytes);
    
    return base64Result;
}

// 测试函数
function test() {
    console.log("🚀 简化版 x-s-common 生成器测试");
    console.log("============================================");
    
    const cookies = parseCookie(REAL_COOKIE);
    console.log("\n📋 Cookie数据:");
    console.log("  a1:", cookies.a1);
    console.log("  webId:", cookies.webId);
    
    // 测试URL
    const testUrl = "https://www.xiaohongshu.com/api/sns/web/v1/feed";
    const timestamp = Date.now();
    
    console.log(`\n🧪 测试URL: ${testUrl}`);
    console.log(`⏰ 时间戳: ${timestamp}`);
    
    // 生成 x-s-common
    const xsCommon = generateXSCommon(testUrl, {
        xT: timestamp.toString(),
        xS: `test_xs_${timestamp}`
    });
    
    console.log(`\n✅ 生成的 x-s-common:`);
    console.log(xsCommon);
    console.log(`\n📏 长度: ${xsCommon.length} 字符`);
    
    // 验证解码
    try {
        const decoded = Buffer.from(xsCommon, 'base64').toString('utf-8');
        const parsed = JSON.parse(decoded);
        
        console.log(`\n📊 解码验证:`);
        console.log("  s0 (平台):", parsed.s0);
        console.log("  x1 (版本):", parsed.x1);
        console.log("  x2 (平台名):", parsed.x2);
        console.log("  x3 (应用):", parsed.x3);
        console.log("  x4 (应用版本):", parsed.x4);
        console.log("  x5 (a1):", parsed.x5 ? parsed.x5.substring(0, 20) + "..." : "空");
        console.log("  x8 (设备指纹):", parsed.x8);
        console.log("  x9 (CRC32):", parsed.x9);
        console.log("  x10 (计数):", parsed.x10);
        console.log("  x11 (状态):", parsed.x11);
        
        // 验证CRC32
        const expectedCrc = mcr(`${parsed.x6}${parsed.x7}${parsed.x8}`);
        console.log(`\n🔍 CRC32验证:`);
        console.log("  输入字符串:", `"${parsed.x6}${parsed.x7}${parsed.x8}"`);
        console.log("  期望值:", expectedCrc);
        console.log("  实际值:", parsed.x9);
        console.log("  匹配:", expectedCrc === parsed.x9 ? "✅ 是" : "❌ 否");
        
    } catch (e) {
        console.log(`❌ 解码失败: ${e.message}`);
    }
    
    // 测试CRC32算法
    console.log(`\n🔧 CRC32算法测试:`);
    const testStrings = ["test", "hello", "", "123456"];
    testStrings.forEach(str => {
        console.log(`  "${str}" -> ${mcr(str)}`);
    });
    
    console.log(`\n🎯 测试完成！`);
    console.log(`\n💡 使用方法:`);
    console.log(`const xsCommon = generateXSCommon(url, { xT: timestamp, xS: xsValue });`);
}

// 运行测试
if (require.main === module) {
    test();
}

module.exports = {
    generateXSCommon,
    mcr,
    b64Encode,
    encodeUtf8,
    parseCookie
};
