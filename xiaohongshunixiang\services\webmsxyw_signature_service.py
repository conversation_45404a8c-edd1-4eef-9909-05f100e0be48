#!/usr/bin/env python3
"""
基于 _webmsxyw 函数的小红书签名服务
使用真实浏览器环境中的 _webmsxyw 函数生成签名
"""

import asyncio
import json
import time
import os
from flask import Flask, request, jsonify
from playwright.async_api import async_playwright
import threading
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebmsxywSignatureService:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
        self.is_initialized = False
        self.loop = None
        self.user_cookie = "a1=1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399;webId=77532cb79e595615769bcc5df41c0386;web_session=040069b937bf843c7b6e1952713a4befbda53e"
        
    async def init(self):
        """初始化浏览器环境"""
        if self.is_initialized:
            return True
            
        logger.info("🚀 初始化 _webmsxyw 签名服务...")

        try:
            # 保存当前事件循环
            self.loop = asyncio.get_event_loop()

            self.playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await self.playwright.chromium.launch(
                headless=True,  # 后台运行
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            # 创建页面
            self.page = await self.browser.new_page()
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                             '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            await self.page.set_viewport_size({"width": 1920, "height": 1080})
            
            # 设置 Cookie
            await self._set_cookies()
            
            # 加载小红书页面
            await self.page.goto('https://www.xiaohongshu.com', wait_until='networkidle')
            await asyncio.sleep(3)  # 等待页面完全加载
            
            # 验证 _webmsxyw 函数是否可用
            is_available = await self._verify_webmsxyw_function()
            if not is_available:
                raise Exception("_webmsxyw 函数不可用")
            
            self.is_initialized = True
            logger.info("✅ _webmsxyw 签名服务初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"❌ 签名服务初始化失败: {e}")
            await self.cleanup()
            return False
    
    async def _set_cookies(self):
        """设置用户 Cookie"""
        cookies = []
        for cookie_pair in self.user_cookie.split(';'):
            if '=' in cookie_pair:
                name, value = cookie_pair.strip().split('=', 1)
                cookies.append({
                    'name': name.strip(),
                    'value': value.strip(),
                    'domain': '.xiaohongshu.com',
                    'path': '/'
                })
        
        await self.page.context.add_cookies(cookies)
        logger.info(f"🍪 已设置 {len(cookies)} 个 Cookie")
    
    async def _verify_webmsxyw_function(self):
        """验证 _webmsxyw 函数是否可用"""
        try:
            result = await self.page.evaluate("""
                (() => {
                    if (typeof window._webmsxyw !== 'function') {
                        return { available: false, error: '_webmsxyw 函数不存在' };
                    }
                    
                    try {
                        const testResult = window._webmsxyw('/test', {});
                        return { 
                            available: true, 
                            test_result: testResult,
                            function_type: typeof window._webmsxyw
                        };
                    } catch (e) {
                        return { available: false, error: e.message };
                    }
                })()
            """)
            
            if result['available']:
                logger.info("✅ _webmsxyw 函数验证成功")
                return True
            else:
                logger.error(f"❌ _webmsxyw 函数验证失败: {result.get('error')}")
                return False
                
        except Exception as e:
            logger.error(f"❌ _webmsxyw 函数验证异常: {e}")
            return False
    
    async def generate_signature(self, url, data=None):
        """生成签名"""
        if not self.is_initialized:
            await self.init()

        if not self.is_initialized:
            raise Exception("签名服务未初始化")

        try:
            # 确保 data 是字典
            if data is None:
                data = {}

            # 调用 _webmsxyw 函数生成签名
            result = await self.page.evaluate("""
                (params) => {
                    try {
                        const signature = window._webmsxyw(params.url, params.data);
                        return {
                            success: true,
                            signature: signature,
                            timestamp: Date.now()
                        };
                    } catch (e) {
                        return {
                            success: false,
                            error: e.message,
                            timestamp: Date.now()
                        };
                    }
                }
            """, {'url': url, 'data': data})

            if result['success']:
                signature = result['signature']

                # 生成 x-s-common
                x_s_common = self._generate_x_s_common(
                    signature.get('X-s', ''),
                    signature.get('X-t', ''),
                    url,
                    data
                )

                # 添加 x-s-common 到签名结果
                signature['x-s-common'] = x_s_common

                logger.info(f"✅ 签名生成成功: {url}")
                return signature
            else:
                logger.error(f"❌ 签名生成失败: {result.get('error')}")
                raise Exception(f"签名生成失败: {result.get('error')}")

        except Exception as e:
            logger.error(f"❌ 签名生成异常: {e}")
            raise e

    def _generate_x_s_common(self, x_s, x_t, url=None, data=None):
        """生成 x-s-common 签名 - 使用真实设备指纹和正确的算法"""
        try:
            # 从用户 cookie 中提取 a1 值
            a1_value = self._extract_a1_from_cookie()

            # 使用真实的设备指纹（已验证成功的）
            real_fingerprint = "I38rHdgsjopgIvesdVwgIC+oIELmBZ5e3VwXLgFTIxS3bqwErFeexd0ekncAzMFYnqthIhJeSnMDKutRI3KsYorWHPtGrbV0P9WfIi/eWc6eYqtyQApPI37ekmR1QL+5Ii6sdneeSfqYHqwl2qt5B0DBIx+PGDi/sVtkIxdsxuwr4qtiIhuaIE3e3LV0I3VTIC7e0utl2ADmsLveDSKsSPw5IEvsiVtJOqw8BuwfPpdeTFWOIx4TIiu6ZPwrPut5IvlaLbgs3qtxIxes1VwHIkumIkIyejgsY/WTge7eSqte/D7sDcpipedeYrDtIC6eDVw2IENsSqtlnlSuNjVtIvoekqt3cZ7sVo4gIESyIhE2QfquIxhnqz8gIkIfoqwkICqWG73sdlOeVPw3IvAe0fgedfDQIi5s3MHM2utAIiKsidvekZNeTPt4nAOeWPwEIvT8zeveSVwAg9osfPwZI34rIxE5Luwwaqw+rekrPI5eDo/eVPwmIhJsSnAekmuvIiAsfI/sxBidIkve3PwlIhQk2VtqOqt1IxesTVtjIk0siqwdIh/sjut3wutnsPw5ICclI3l4wA4jwIAsWVw4IE4qIhOsSqtZBbTt/A0ejjp1IkGPGutPoqwhIvveVPtf+Dee3l5s1rELIE0s6edsiPtzcPwrICJefVwfIkgs60WrICKedo/eWVt3I37eVqwf8BYrIhQIIvKeVL3e60vejcge1qteIEqXICSEpPw8Ii+AIk6e1ImMJ7defVweIkPIgPwhOYNefW=="

            # 生成x9值 (使用CRC32哈希x8值)
            x9_value = ''
            if real_fingerprint:
                x9_hash = self._calculate_crc32_hash(real_fingerprint)
                x9_value = str(x9_hash)

            # 构建 x-s-common 数据结构 (基于逆向分析的真实结构)
            common_data = {
                's0': 'web',                    # getPlatformCode('PC') -> 'web'
                's1': '',                       # 固定空字符串
                'x1': '',                       # 变量C，通常为空
                'x2': 'PC',                     # 平台名称
                'x3': 'xhs-pc-web',            # 应用标识
                'x4': '4.68.0',                # 版本号
                'x5': a1_value,                # l.Z.get("a1") - cookie中的a1值
                'x6': '',                      # 固定空字符串
                'x7': '',                      # 固定空字符串
                'x8': real_fingerprint,        # 变量p - 真实设备指纹
                'x9': x9_value,                # O函数处理x8的CRC32结果
                'x10': '',                     # 变量d - 签名计数，首次为空字符串
                'x11': 'normal'                # 固定值
            }

            # 序列化为JSON (不包含空格，与原始实现一致)
            common_json = json.dumps(common_data, separators=(',', ':'), ensure_ascii=False)

            logger.debug(f"🔧 使用真实设备指纹生成x-s-common")
            logger.debug(f"   设备指纹长度: {len(real_fingerprint)} 字符")
            logger.debug(f"   CRC32哈希: {x9_value}")
            logger.debug(f"   JSON数据: {common_json[:100]}...")

            # UTF-8编码
            utf8_data = self._encode_utf8_simple(common_json)
            logger.debug(f"   UTF8编码长度: {len(utf8_data)}")

            # Base64编码
            x_s_common = self._b64_encode_simple(utf8_data)
            logger.debug(f"   Base64编码长度: {len(x_s_common)}")

            logger.info(f"✅ x-s-common 生成成功: {x_s_common[:50]}...")
            return x_s_common

        except Exception as e:
            logger.error(f"❌ x-s-common 生成失败: {e}")
            import traceback
            logger.error(f"   错误详情: {traceback.format_exc()}")
            # 返回一个简化的 x-s-common
            return self._generate_fallback_x_s_common(x_s, x_t)

    def _calculate_crc32_hash(self, input_str):
        """计算CRC32哈希值 - 与真实算法一致""" 
        import zlib
        # 使用标准的CRC32算法
        crc32_result = zlib.crc32(input_str.encode('utf-8')) & 0xffffffff
        return crc32_result

    def _encode_utf8_simple(self, text):
        """简单的UTF-8编码"""
        return text.encode('utf-8')

    def _b64_encode_simple(self, data):
        """简单的Base64编码"""
        import base64
        if isinstance(data, str):
            data = data.encode('utf-8')
        return base64.b64encode(data).decode('ascii')

    def _extract_a1_from_cookie(self):
        """从用户 cookie 中提取 a1 值"""
        try:
            for cookie_pair in self.user_cookie.split(';'):
                if '=' in cookie_pair:
                    name, value = cookie_pair.strip().split('=', 1)
                    if name.strip() == 'a1':
                        return value.strip()
            return ""
        except Exception:
            return ""



    def _calculate_mrc(self, input_str):
        """计算 MRC 值 - 使用正确的CRC32算法"""
        import ctypes

        # CRC32查找表
        ie = [
            0, 1996959894, 3993919788, 2567524794, 124634137, 1886057615, 3915621685,
            2657392035, 249268274, 2044508324, 3772115230, 2547177864, 162941995,
            2125561021, 3887607047, 2428444049, 498536548, 1789927666, 4089016648,
            2227061214, 450548861, 1843258603, 4107580753, 2211677639, 325883990,
            1684777152, 4251122042, 2321926636, 335633487, 1661365465, 4195302755,
            2366115317, 997073096, 1281953886, 3579855332, 2724688242, 1006888145,
            1258607687, 3524101629, 2768942443, 901097722, 1119000684, 3686517206,
            2898065728, 853044451, 1172266101, 3705015759, 2882616665, 651767980,
            1373503546, 3369554304, 3218104598, 565507253, 1454621731, 3485111705,
            3099436303, 671266974, 1594198024, 3322730930, 2970347812, 795835527,
            1483230225, 3244367275, 3060149565, 1994146192, 31158534, 2563907772,
            4023717930, 1907459465, 112637215, 2680153253, 3904427059, 2013776290,
            251722036, 2517215374, 3775830040, 2137656763, 141376813, 2439277719,
            3865271297, 1802195444, 476864866, 2238001368, 4066508878, 1812370925,
            453092731, 2181625025, 4111451223, 1706088902, 314042704, 2344532202,
            4240017532, 1658658271, 366619977, 2362670323, 4224994405, 1303535960,
            984961486, 2747007092, 3569037538, 1256170817, 1037604311, 2765210733,
            3554079995, 1131014506, 879679996, 2909243462, 3663771856, 1141124467,
            855842277, 2852801631, 3708648649, 1342533948, 654459306, 3188396048,
            3373015174, 1466479909, 544179635, 3110523913, 3462522015, 1591671054,
            702138776, 2966460450, 3352799412, 1504918807, 783551873, 3082640443,
            3233442989, 3988292384,
        ]

        def right_without_sign(num, bit=0) -> int:
            val = ctypes.c_uint32(num).value >> bit
            MAX32INT = 4294967295
            return (val + (MAX32INT + 1)) % (2 * (MAX32INT + 1)) - MAX32INT - 1

        o = -1
        for n in range(min(57, len(input_str))):
            o = ie[(o & 255) ^ ord(input_str[n])] ^ right_without_sign(o, 8)

        return o ^ -1 ^ 3988292384

    def _encode_utf8(self, e):
        """UTF8编码函数"""
        import urllib.parse

        b = []
        m = urllib.parse.quote(e, safe='~()*!.\'')
        w = 0
        while w < len(m):
            T = m[w]
            if T == "%":
                E = m[w + 1] + m[w + 2]
                S = int(E, 16)
                b.append(S)
                w += 2
            else:
                b.append(ord(T[0]))
            w += 1
        return b

    def _b64_encode(self, e):
        """自定义Base64编码函数"""
        lookup = [
            "Z", "m", "s", "e", "r", "b", "B", "o", "H", "Q", "t", "N", "P", "+", "w", "O",
            "c", "z", "a", "/", "L", "p", "n", "g", "G", "8", "y", "J", "q", "4", "2", "K",
            "W", "Y", "j", "0", "D", "S", "f", "d", "i", "k", "x", "3", "V", "T", "1", "6",
            "I", "l", "U", "A", "F", "M", "9", "7", "h", "E", "C", "v", "u", "R", "X", "5",
        ]

        def triplet_to_base64(e):
            return (
                lookup[63 & (e >> 18)] + lookup[63 & (e >> 12)] + lookup[(e >> 6) & 63] + lookup[e & 63]
            )

        def encode_chunk(e, t, r):
            m = []
            for b in range(t, r, 3):
                n = (16711680 & (e[b] << 16)) + \
                    ((e[b + 1] << 8) & 65280) + (e[b + 2] & 255)
                m.append(triplet_to_base64(n))
            return ''.join(m)

        P = len(e)
        W = P % 3
        U = []
        z = 16383
        H = 0
        Z = P - W
        while H < Z:
            U.append(encode_chunk(e, H, Z if H + z > Z else H + z))
            H += z
        if 1 == W:
            F = e[P - 1]
            U.append(lookup[F >> 2] + lookup[(F << 4) & 63] + "==")
        elif 2 == W:
            F = (e[P - 2] << 8) + e[P - 1]
            U.append(lookup[F >> 10] + lookup[63 & (F >> 4)] + lookup[(F << 2) & 63] + "=")
        return "".join(U)

    def _generate_fallback_x_s_common(self, x_s, x_t):
        """生成备用的 x-s-common"""
        logger.error("🚨 使用了fallback方法生成x-s-common！这不应该发生！")
        import base64

        # 使用正确的结构作为fallback
        fallback_data = {
            "s0": "web",
            "s1": "",
            "x1": "",
            "x2": "PC",
            "x3": "xhs-pc-web",
            "x4": "4.68.0",
            "x5": self._extract_a1_from_cookie(),
            "x6": "",
            "x7": "",
            "x8": "",  # 空的设备指纹
            "x9": "",  # 空的CRC32
            "x10": "",
            "x11": "normal"
        }

        fallback_json = json.dumps(fallback_data, separators=(',', ':'))
        return base64.b64encode(fallback_json.encode()).decode()

    async def batch_generate_signatures(self, requests):
        """批量生成签名"""
        results = []
        
        for req in requests:
            try:
                url = req.get('url', '')
                data = req.get('data', {})
                
                signature = await self.generate_signature(url, data)
                
                results.append({
                    'url': url,
                    'data': data,
                    'signature': signature,
                    'success': True
                })
                
                # 短暂延迟避免过快调用
                await asyncio.sleep(0.1)
                
            except Exception as e:
                results.append({
                    'url': req.get('url', ''),
                    'data': req.get('data', {}),
                    'error': str(e),
                    'success': False
                })
        
        return results
    
    async def analyze_signature_structure(self, url, data=None):
        """分析签名结构"""
        try:
            signature = await self.generate_signature(url, data)
            
            # 解析 X-s 头部
            x_s = signature.get('X-s', '')
            parsed_xs = None
            
            if x_s.startswith('XYW_'):
                try:
                    import base64
                    base64_data = x_s[4:]  # 去掉 XYW_ 前缀
                    decoded = base64.b64decode(base64_data).decode('utf-8')
                    parsed_xs = json.loads(decoded)
                except Exception as e:
                    logger.warning(f"解析 X-s 失败: {e}")
            
            return {
                'url': url,
                'data': data,
                'signature': signature,
                'parsed_xs': parsed_xs,
                'analysis': {
                    'x_s_length': len(x_s),
                    'x_t': signature.get('X-t'),
                    'payload_length': len(parsed_xs.get('payload', '')) if parsed_xs else 0,
                    'sign_svn': parsed_xs.get('signSvn') if parsed_xs else None,
                    'sign_type': parsed_xs.get('signType') if parsed_xs else None,
                    'app_id': parsed_xs.get('appId') if parsed_xs else None
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 签名结构分析失败: {e}")
            raise e
    
    async def cleanup(self):
        """清理资源"""
        logger.info("🧹 清理签名服务资源...")
        
        if self.page:
            await self.page.close()
        
        if self.browser:
            await self.browser.close()
        
        if self.playwright:
            await self.playwright.stop()
        
        self.is_initialized = False
        logger.info("✅ 签名服务资源清理完成")

# 全局签名服务实例
signature_service = WebmsxywSignatureService()

# Flask 应用
app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'service': 'webmsxyw_signature_service',
        'initialized': signature_service.is_initialized,
        'timestamp': int(time.time())
    })

@app.route('/signature/generate', methods=['POST'])
def generate_signature():
    """生成单个签名"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        url = data.get('url')
        request_data = data.get('data', {})

        if not url:
            return jsonify({'error': 'URL 参数必需'}), 400

        # 检查服务是否初始化
        if not signature_service.is_initialized:
            return jsonify({
                'success': False,
                'error': '签名服务未初始化',
                'timestamp': int(time.time())
            }), 503

        # 使用 asyncio.run_coroutine_threadsafe 在主事件循环中运行
        future = asyncio.run_coroutine_threadsafe(
            signature_service.generate_signature(url, request_data),
            signature_service.loop
        )

        # 等待结果，设置超时
        signature = future.result(timeout=30)

        return jsonify({
            'success': True,
            'url': url,
            'data': request_data,
            'signature': signature,
            'timestamp': int(time.time())
        })

    except Exception as e:
        logger.error(f"❌ 签名生成API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': int(time.time())
        }), 500

@app.route('/signature/batch', methods=['POST'])
def batch_generate_signatures():
    """批量生成签名"""
    try:
        data = request.get_json()
        if not data or 'requests' not in data:
            return jsonify({'error': '请求数据格式错误'}), 400

        requests_list = data['requests']

        # 检查服务是否初始化
        if not signature_service.is_initialized:
            return jsonify({
                'success': False,
                'error': '签名服务未初始化',
                'timestamp': int(time.time())
            }), 503

        # 使用 asyncio.run_coroutine_threadsafe 在主事件循环中运行
        future = asyncio.run_coroutine_threadsafe(
            signature_service.batch_generate_signatures(requests_list),
            signature_service.loop
        )

        # 等待结果，设置超时
        results = future.result(timeout=60)

        return jsonify({
            'success': True,
            'results': results,
            'total': len(results),
            'timestamp': int(time.time())
        })

    except Exception as e:
        logger.error(f"❌ 批量签名生成API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': int(time.time())
        }), 500

@app.route('/signature/analyze', methods=['POST'])
def analyze_signature():
    """分析签名结构"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据为空'}), 400

        url = data.get('url')
        request_data = data.get('data', {})

        if not url:
            return jsonify({'error': 'URL 参数必需'}), 400

        # 检查服务是否初始化
        if not signature_service.is_initialized:
            return jsonify({
                'success': False,
                'error': '签名服务未初始化',
                'timestamp': int(time.time())
            }), 503

        # 使用 asyncio.run_coroutine_threadsafe 在主事件循环中运行
        future = asyncio.run_coroutine_threadsafe(
            signature_service.analyze_signature_structure(url, request_data),
            signature_service.loop
        )

        # 等待结果，设置超时
        analysis = future.result(timeout=30)

        return jsonify({
            'success': True,
            'analysis': analysis,
            'timestamp': int(time.time())
        })

    except Exception as e:
        logger.error(f"❌ 签名分析API错误: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': int(time.time())
        }), 500

def run_flask_app():
    """运行 Flask 应用"""
    app.run(host='0.0.0.0', port=5107, debug=False, threaded=True)

async def main():
    """主函数"""
    print("🚀 启动基于 _webmsxyw 的签名服务")
    print("=" * 60)
    
    # 初始化签名服务
    success = await signature_service.init()
    if not success:
        print("❌ 签名服务初始化失败")
        return
    
    print("✅ 签名服务初始化成功")
    print("🌐 启动 HTTP 服务器...")
    print("📡 服务地址: http://localhost:5107")
    print("📋 API 端点:")
    print("   GET  /health - 健康检查")
    print("   POST /signature/generate - 生成单个签名")
    print("   POST /signature/batch - 批量生成签名")
    print("   POST /signature/analyze - 分析签名结构")
    
    # 在单独线程中运行 Flask 应用
    flask_thread = threading.Thread(target=run_flask_app, daemon=True)
    flask_thread.start()
    
    try:
        # 保持主线程运行
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 收到停止信号")
    finally:
        await signature_service.cleanup()
        print("👋 签名服务已停止")

if __name__ == '__main__':
    asyncio.run(main())
