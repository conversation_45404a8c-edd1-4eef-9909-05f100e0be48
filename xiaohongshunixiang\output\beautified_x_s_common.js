f=localStorage.getItem("b1b1")||"1",
v={
  s0:getPlatformCode(s),
  s1:"",
  x0:f,
  x1:C,
  x2:s||"PC",
  x3:"xhs-pc-web",
  x4:"4.68.0",
  x5:l.Z.get("a1"),
  x6:"",
  x7:"",
  x8:p,
  x9:O("".concat("").concat("").concat(p)),
  x10:d,
  x11:"normal"
}
,
h=k.map(function (e){
  return new RegExp(e)
}
).some(function (e){
  return e.test(u)
}
);
(null===(i=window.xhsFingerprintV3)||void 0===i?void 0:i.getCurMiniUa)&&h?null===(a=window.xhsFingerprintV3)||void 0===a||a.getCurMiniUa(function (e){
  v.x8=e,
  v.x9=O("".concat("").concat("").concat(e)),
  r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
):r.headers["X-S-Common"]=b64Encode(encodeUtf8(JSON.stringify(v)))
}
catch(e){
}
return r
}
function getSigCount(e){
  var r=Number(sessionStorage.getItem("sc"))||0;
  return e&&(r++,
  sessionStorage.setItem("sc",
  r.toString())),
  r
}
i(42876),
i(33933);
var genDeviceFingerprint_awaiter=function (e,
r,
i,
a){
  function adopt(e){
    return e instanceof i?e:new i(function (r){
      r(e)
    }
    )
  }
  return new(i||(i=Promise))(function (i,
  s){
    function fulfilled(e){
      try{
        step(a.next(e))
      }
      catch(e){
        s(