#!/usr/bin/env python3
"""
快速JS代码搜索工具 - 专门搜索x-s-common相关代码
"""

import os
import re
import json
from datetime import datetime


def search_js_files(directory, patterns, case_sensitive=False):
    """搜索JS文件中的指定模式"""
    results = []
    
    # 获取所有JS文件
    js_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.js'):
                js_files.append(os.path.join(root, file))
    
    print(f"🔍 找到 {len(js_files)} 个JS文件，开始搜索...")
    
    for js_file in js_files:
        try:
            with open(js_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
                lines = content.split('\n')
            
            # 搜索每一行
            for line_num, line in enumerate(lines, 1):
                for pattern_name, pattern in patterns.items():
                    flags = 0 if case_sensitive else re.IGNORECASE
                    matches = re.finditer(pattern, line, flags)
                    
                    for match in matches:
                        # 获取上下文（前后3行）
                        start_line = max(0, line_num - 3)
                        end_line = min(len(lines), line_num + 3)
                        context_lines = lines[start_line:end_line]
                        context = '\n'.join(f"{start_line + i + 1:4d}: {l}" for i, l in enumerate(context_lines))
                        
                        result = {
                            'file': os.path.basename(js_file),
                            'full_path': js_file,
                            'line_number': line_num,
                            'pattern_name': pattern_name,
                            'matched_text': match.group(),
                            'full_line': line.strip(),
                            'context': context
                        }
                        
                        results.append(result)
                        
        except Exception as e:
            print(f"❌ 读取文件 {js_file} 时出错: {e}")
    
    return results


def print_results(results):
    """打印搜索结果"""
    if not results:
        print("❌ 没有找到匹配的代码")
        return
    
    print(f"\n✅ 找到 {len(results)} 个匹配项:")
    print("="*80)
    
    # 按文件分组显示
    files_dict = {}
    for result in results:
        file_name = result['file']
        if file_name not in files_dict:
            files_dict[file_name] = []
        files_dict[file_name].append(result)
    
    for file_name, file_results in files_dict.items():
        print(f"\n📄 文件: {file_name}")
        print("-" * 60)
        
        for result in file_results:
            print(f"  🎯 行 {result['line_number']}: {result['pattern_name']}")
            print(f"     匹配: {result['matched_text']}")
            print(f"     内容: {result['full_line']}")
            print()


def save_results(results, output_file):
    """保存结果到文件"""
    export_data = {
        'search_time': datetime.now().isoformat(),
        'total_matches': len(results),
        'results': results
    }
    
    if output_file.endswith('.json'):
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
    else:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"JavaScript代码搜索结果\n")
            f.write(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总匹配数: {len(results)}\n")
            f.write("="*80 + "\n\n")
            
            for i, result in enumerate(results, 1):
                f.write(f"{i}. {result['file']} (行 {result['line_number']})\n")
                f.write(f"   模式: {result['pattern_name']}\n")
                f.write(f"   匹配: {result['matched_text']}\n")
                f.write(f"   内容: {result['full_line']}\n")
                f.write(f"   上下文:\n{result['context']}\n")
                f.write("-" * 60 + "\n\n")
    
    print(f"✅ 结果已保存到: {output_file}")


def main():
    """主函数"""
    print("🔍 JavaScript代码快速搜索工具")
    print("专门搜索 x-s-common 相关代码")
    print("="*50)
    
    # 默认搜索目录
    default_dir = os.path.join(os.getcwd(), "data", "js")
    
    # 搜索模式
    patterns = {
        'x-s-common': r'["\']?x-s-common["\']?',
        'x_s_common': r'x_s_common',
        'xscommon': r'xscommon',
        'XS_COMMON': r'XS_COMMON',
        'signature': r'signature',
        'getSign': r'getSign',
        'generateSign': r'generateSign',
        'createSign': r'createSign',
        'encrypt': r'encrypt',
        'encode': r'encode',
        'hash': r'hash',
        'md5': r'md5',
        'sha': r'sha\d*',
        'crypto': r'crypto'
    }
    
    # 检查目录是否存在
    if os.path.exists(default_dir):
        print(f"📁 搜索目录: {default_dir}")
        
        # 开始搜索
        results = search_js_files(default_dir, patterns)
        
        # 显示结果
        print_results(results)
        
        # 保存结果
        if results:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"js_search_results_{timestamp}.txt"
            save_results(results, output_file)
            
            # 也保存JSON格式
            json_file = f"js_search_results_{timestamp}.json"
            save_results(results, json_file)
            
            # 提取关键代码
            print("\n🔑 关键代码提取:")
            print("="*50)
            
            x_s_common_results = [r for r in results if 'x-s-common' in r['pattern_name'].lower()]
            if x_s_common_results:
                print(f"\n📍 找到 {len(x_s_common_results)} 个 x-s-common 相关匹配:")
                for result in x_s_common_results[:5]:  # 显示前5个
                    print(f"  📄 {result['file']} 行 {result['line_number']}")
                    print(f"     {result['full_line']}")
                    print()
            
            signature_results = [r for r in results if 'sign' in r['pattern_name'].lower()]
            if signature_results:
                print(f"\n📍 找到 {len(signature_results)} 个签名相关匹配:")
                for result in signature_results[:5]:  # 显示前5个
                    print(f"  📄 {result['file']} 行 {result['line_number']}")
                    print(f"     {result['full_line']}")
                    print()
        
    else:
        print(f"❌ 目录不存在: {default_dir}")
        print("请确保JS文件存在于 data/js/ 目录中")


if __name__ == '__main__':
    main()
