stApiSnsWebV1LoginActivate
}
,
du:function (){
  return getApiSnsWebV1SearchQuerytrending
}
,
Vn:function (){
  return getApiSnsWebV2NoteCollectPage
}
,
yC:function (){
  return getApiSnsWebV1BoardUser
}
,
tF:function (){
  return getApiSnsWebV1IntimacyIntimacyList
}
}
),
function registerHttp(e){
  a=e
}
(i(65266).dJ)
}
,
34134:function (e,
r,
i){
  "use strict";
  i.d(r,
  {
    Z:function (){
      return s
    }
  }
  );
  class a{
    static _calculate(e=[]){
      if(0===e.length)return null;
      if(1===e.length)return e[0].t;
      let r=e[0];
      for(...) {
        let a=e[i];
        a.layoutSignificance>r.layoutSignificance&&(r=a)
      }
      return r.t
    }
    static get(){
      return new Promise(e=>{
        if(...) {
          e({
            firstPaint:window.__FP__,
            firstContentfulPaint:window.__FCP__,
            firstScreen:window.__FIRST_SCREEN__,
            firstMeaningfulPaint:a._calculate(window.__FMP_OBSERVED_POINTS__),
            fullyLoaded:window.__FULLY_LOADED__
          }
          );
          return
        }
        window.addEventListener("__fullyloaded__",
        r=>{
          e({
            firstPaint:r.detail.firstPaint,
            firstContentfulPaint:r.detail.firstContentfulPaint,
            firstScree