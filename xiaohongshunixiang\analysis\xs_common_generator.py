#!/usr/bin/env python3
"""
X-S-Common 生成器 - 基于逆向分析的实现
"""

import json
import base64
import re
from urllib.parse import quote

class XSCommonGenerator:
    def __init__(self):
        # 基于逆向分析的平台代码映射
        # 来源: function getPlatformCode(e){switch(e){case"Android":return s.Android;case"iOS":return s.iOS;case"Mac OS":return s.MacOs;case"Linux":return s.Linux;default:return s.other}
        self.platform_codes = {
            'Android': 'android',  # s.Android
            'iOS': 'ios',          # s.iOS
            'Mac OS': 'macos',     # s.MacOs
            'Linux': 'linux',      # s.Linux
            'PC': 'web',           # default: s.other (推测为web)
            'web': 'web'
        }

        # CRC32 函数实现 (用于生成x9)
        # 来源: var O=function(e){for(var r,i,a=256,s=[];a--;s[a]=r>>>0)for(i=8,r=a;i--;)r=1&r?r>>>1^0xedb88320:r>>>1;return function(e){if("string"==typeof e){for(var r=0,i=-1;r<e.length;++r)i=s[255&i^e.charCodeAt(r)]^i>>>8;return -1^i^0xedb88320}
        self._init_crc32_table()

    def _init_crc32_table(self):
        """初始化CRC32查找表"""
        self.crc32_table = []
        for a in range(256):
            r = a
            for i in range(8):
                if r & 1:
                    r = (r >> 1) ^ 0xedb88320
                else:
                    r = r >> 1
            self.crc32_table.append(r & 0xffffffff)

    def crc32_hash(self, text):
        """
        CRC32哈希函数 (对应JavaScript中的O函数)
        用于生成x9参数
        """
        if isinstance(text, str):
            crc = 0xffffffff
            for char in text:
                crc = self.crc32_table[(crc ^ ord(char)) & 0xff] ^ (crc >> 8)
            return (crc ^ 0xedb88320) & 0xffffffff
        return 0
        
    def get_platform_code(self, platform):
        """获取平台代码"""
        return self.platform_codes.get(platform, 'web')
        
    def encode_utf8(self, text):
        """UTF-8 编码"""
        return text.encode('utf-8')
        
    def b64_encode(self, data):
        """Base64 编码"""
        if isinstance(data, str):
            data = data.encode('utf-8')
        return base64.b64encode(data).decode('ascii')
        
    def generate_xs_common(self, platform='PC', a1_cookie='', x8_value='', x10_value=None, x1_value='', x0_value='1', include_x0=True):
        """
        生成 X-S-Common 值 (基于完整逆向分析)

        Args:
            platform: 平台类型 (PC/iOS/Android/Mac OS/Linux)
            a1_cookie: 从cookie中获取的a1值
            x8_value: x8参数值 (设备指纹，可以是localStorage.getItem("b1")或动态指纹)
            x10_value: x10参数值 (签名计数，None表示自动生成，""表示空字符串)
            x1_value: x1参数值 (变量C，通常为空)
            x0_value: x0参数值 (localStorage.getItem("b1b1")||"1")
            include_x0: 是否包含x0参数 (某些情况下可能不包含)

        Returns:
            str: Base64编码的X-S-Common值
        """

        # 生成x9值 (使用CRC32哈希x8值)
        x9_value = ''
        if x8_value:
            # 根据逆向分析: x9 = O("".concat("").concat("").concat(x8))
            # 实际上就是对x8字符串进行CRC32哈希
            x9_hash = self.crc32_hash(x8_value)
            x9_value = str(x9_hash)

        # 处理x10值 (签名计数)
        if x10_value is None:
            # 自动生成签名计数，首次访问返回空字符串
            x10_final = self.generate_sig_count(increment=False, return_empty_for_first=True)
        else:
            # 使用提供的值
            x10_final = str(x10_value) if x10_value != "" else ""

        # 构建参数对象 v (完全基于逆向分析的真实结构)
        v = {
            's0': self.get_platform_code(platform),  # getPlatformCode(s)
            's1': '',                                 # 固定空字符串
            'x1': x1_value,                          # 变量C，通常为空
            'x2': platform or 'PC',                 # 平台名称
            'x3': 'xhs-pc-web',                     # 应用标识
            'x4': '4.68.0',                         # 版本号
            'x5': a1_cookie,                        # l.Z.get("a1") - cookie中的a1值
            'x6': '',                               # 固定空字符串
            'x7': '',                               # 固定空字符串
            'x8': x8_value,                         # 变量p - 设备指纹
            'x9': x9_value,                         # O函数处理x8的CRC32结果
            'x10': x10_final,                       # 变量d - 签名计数
            'x11': 'normal'                         # 固定值
        }

        # 根据 include_x0 参数决定是否包含 x0
        if include_x0:
            v['x0'] = x0_value  # localStorage.getItem("b1b1")||"1"

        # 序列化为JSON (不包含空格，与原始实现一致)
        json_str = json.dumps(v, separators=(',', ':'))

        # UTF-8编码
        utf8_data = self.encode_utf8(json_str)

        # Base64编码
        xs_common = self.b64_encode(utf8_data)

        return xs_common

    def generate_sig_count(self, increment=True, return_empty_for_first=True):
        """
        模拟 getSigCount 函数

        Args:
            increment: 是否增加计数
            return_empty_for_first: 首次访问时是否返回空字符串

        Returns:
            str: 签名计数 (字符串格式)
        """
        # 模拟 sessionStorage 的行为
        if not hasattr(self, '_sig_count'):
            self._sig_count = 0

        # 如果是首次访问且设置了返回空字符串
        if self._sig_count == 0 and return_empty_for_first and not increment:
            return ""

        if increment:
            self._sig_count += 1

        # 如果计数为0且设置了首次返回空，则返回空字符串
        if self._sig_count == 0 and return_empty_for_first:
            return ""

        return str(self._sig_count)
        
    def decode_xs_common(self, xs_common_value):
        """
        解码 X-S-Common 值
        
        Args:
            xs_common_value: Base64编码的X-S-Common值
            
        Returns:
            dict: 解码后的参数对象
        """
        try:
            # Base64解码
            decoded_data = base64.b64decode(xs_common_value)
            
            # UTF-8解码
            json_str = decoded_data.decode('utf-8')
            
            # JSON解析
            params = json.loads(json_str)
            
            return params
            
        except Exception as e:
            print(f"解码失败: {e}")
            return None
            
    def analyze_existing_xs_common(self, xs_common_value):
        """分析现有的X-S-Common值"""
        print(f"🔍 分析 X-S-Common: {xs_common_value}")
        
        decoded = self.decode_xs_common(xs_common_value)
        if decoded:
            print("📊 解码结果:")
            for key, value in decoded.items():
                print(f"   {key}: {value}")
            return decoded
        else:
            print("❌ 解码失败")
            return None

def test_generator():
    """测试生成器"""
    print("🧪 测试 X-S-Common 生成器 (基于逆向分析)")
    print("=" * 60)

    generator = XSCommonGenerator()

    # 测试CRC32函数
    print("\n🔧 测试CRC32函数:")
    test_strings = ['', 'test', 'hello world']
    for test_str in test_strings:
        crc_result = generator.crc32_hash(test_str)
        print(f"   CRC32('{test_str}') = {crc_result}")

    # 测试平台代码
    print("\n🖥️ 测试平台代码:")
    platforms = ['PC', 'Android', 'iOS', 'Mac OS', 'Linux', 'unknown']
    for platform in platforms:
        code = generator.get_platform_code(platform)
        print(f"   {platform} -> {code}")

    # 测试生成 (使用真实的a1值和完整参数)
    print("\n📝 生成测试:")

    # 测试1: 首次访问生成 (x10应该为空字符串)
    xs_common1 = generator.generate_xs_common(
        platform='PC',
        a1_cookie='1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399',
        x8_value='',  # 空指纹
        x10_value=None,  # 自动生成，首次应该为空
        x1_value='',
        x0_value='1'
    )
    print(f"首次访问 (空指纹): {xs_common1}")

    # 测试2: 显式设置x10为空字符串
    xs_common2 = generator.generate_xs_common(
        platform='PC',
        a1_cookie='1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399',
        x8_value='',  # 空指纹
        x10_value='',  # 显式设置为空字符串
        x1_value='',
        x0_value='1'
    )
    print(f"显式空x10: {xs_common2}")

    # 测试3: 带设备指纹的生成
    xs_common3 = generator.generate_xs_common(
        platform='PC',
        a1_cookie='1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399',
        x8_value='mock_device_fingerprint_12345',  # 模拟设备指纹
        x10_value='',  # 空字符串
        x1_value='',
        x0_value='1'
    )
    print(f"带设备指纹: {xs_common3}")

    # 测试4: 带计数的生成
    xs_common4 = generator.generate_xs_common(
        platform='PC',
        a1_cookie='1974a062852bjkf284i5lvmj6yj8ps7sro1h9qxdq50000978399',
        x8_value='localStorage_b1_value',  # 模拟localStorage.getItem("b1")
        x10_value=1,  # 显式设置计数
        x1_value='',
        x0_value='custom_b1b1_value'  # 模拟localStorage.getItem("b1b1")
    )
    print(f"带计数值: {xs_common4}")

    # 测试解码
    print(f"\n🔍 解码测试:")
    for i, xs_common in enumerate([xs_common1, xs_common2], 1):
        print(f"\n解码测试 {i}:")
        decoded = generator.decode_xs_common(xs_common)
        if decoded:
            for key, value in decoded.items():
                print(f"   {key}: {value}")

    # 分析已知的X-S-Common值
    print(f"\n📊 分析已知值:")
    known_values = [
        "eyJzMCI6IndlYiIsInMxIjoiIiwieDEiOiIiLCJ4MiI6IlBDIiwieDMiOiJ4aHMtcGMtd2ViIiwieDQiOiI0LjY4LjAiLCJ4NSI6IjE5NzRhMDYyODUyYmprZjI4NGk1bHZtajZ5ajhwczdzcm8xaDlxeGRxNTAwMDA5NzgzOTkiLCJ4NiI6IiIsIng3IjoiIiwieDgiOiIiLCJ4OSI6IiIsIngxMCI6IiIsIngxMSI6Im5vcm1hbCJ9",
    ]

    for i, value in enumerate(known_values):
        print(f"\n已知值 {i+1}:")
        generator.analyze_existing_xs_common(value)

def extract_from_browser():
    """从浏览器环境中提取参数"""
    print("\n🌐 从浏览器环境提取参数的建议:")
    print("1. 在浏览器控制台中执行以下代码来获取参数:")
    print("   - localStorage.getItem('b1b1')  // 获取x0值")
    print("   - document.cookie.match(/a1=([^;]*)/)?.[1]  // 获取a1值")
    print("   - 观察网络请求中的X-S-Common值进行对比分析")
    print("\n2. 需要进一步分析的函数:")
    print("   - getPlatformCode() 函数的具体实现")
    print("   - O() 函数的具体实现 (用于生成x9)")
    print("   - x1, x8, x10 参数的来源和生成逻辑")

if __name__ == '__main__':
    test_generator()
    extract_from_browser()
