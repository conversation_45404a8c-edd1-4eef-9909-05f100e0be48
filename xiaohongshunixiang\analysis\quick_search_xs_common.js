// 🔍 快速搜索 X-S-Common - 简化版

// 直接在控制台执行这段代码
console.log('🚀 快速搜索 X-S-Common...');

// 搜索所有可能的变体
const variants = ['X-S-Common', 'x-s-common', 'X-s-common', 'x-S-Common', 'X-S-COMMON', 'XSCommon', 'xsCommon'];

// 1. 搜索全局函数
console.log('🔍 搜索全局函数...');
for (let key in window) {
    try {
        if (typeof window[key] === 'function') {
            const funcStr = window[key].toString();
            for (let variant of variants) {
                if (funcStr.includes(variant)) {
                    console.log(`✅ 函数 ${key} 包含 ${variant}`);
                    console.log('📝 函数源码:', funcStr.substring(0, 300) + '...');
                    break;
                }
            }
        }
    } catch (e) {}
}

// 2. 搜索所有脚本
console.log('🔍 搜索脚本内容...');
document.querySelectorAll('script').forEach((script, index) => {
    if (script.textContent) {
        for (let variant of variants) {
            if (script.textContent.includes(variant)) {
                console.log(`✅ 脚本 ${index} 包含 ${variant}`);
                
                // 找到包含该变体的具体行
                const lines = script.textContent.split('\n');
                lines.forEach((line, lineIndex) => {
                    if (line.includes(variant)) {
                        console.log(`   行 ${lineIndex + 1}: ${line.trim()}`);
                    }
                });
                break;
            }
        }
    }
});

// 3. 搜索当前页面的所有文本
console.log('🔍 搜索页面文本...');
const pageText = document.documentElement.outerHTML;
for (let variant of variants) {
    if (pageText.includes(variant)) {
        console.log(`✅ 页面包含 ${variant}`);
        
        // 使用正则表达式找到上下文
        const regex = new RegExp(`.{0,50}${variant.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')}.{0,50}`, 'gi');
        const matches = pageText.match(regex);
        if (matches) {
            matches.slice(0, 5).forEach((match, index) => {
                console.log(`   匹配 ${index + 1}: ${match}`);
            });
        }
    }
}

console.log('✅ 快速搜索完成！');
