(self.webpackChunkxhs_pc_web=self.webpackChunkxhs_pc_web||[]).push([["971"],{37565:function(){},26446:function(A,B){"use strict";B.byteLength=byteLength,B.toByteArray=toByteArray,B.fromByteArray=fromByteArray;for(var N=[],U=[],H="undefined"!=typeof Uint8Array?Uint8Array:Array,W="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=0,V=W.length;j<V;++j)N[j]=W[j],U[W.charCodeAt(j)]=j;function getLens(A){var B=A.length;if(B%4>0)throw Error("Invalid string. Length must be a multiple of 4");var N=A.indexOf("=");-1===N&&(N=B);var U=N===B?0:4-N%4;return[N,U]}function byteLength(A){var B=getLens(A),N=B[0],U=B[1];return(N+U)*3/4-U}function _byteLength(A,B,N){return(B+N)*3/4-N}function toByteArray(A){var B,N,W=getLens(A),j=W[0],V=W[1],K=new H(_byteLength(A,j,V)),X=0,J=V>0?j-4:j;for(N=0;N<J;N+=4)B=U[A.charCodeAt(N)]<<18|U[A.charCodeAt(N+1)]<<12|U[A.charCodeAt(N+2)]<<6|U[A.charCodeAt(N+3)],K[X++]=B>>16&255,K[X++]=B>>8&255,K[X++]=255&B;return 2===V&&(B=U[A.charCodeAt(N)]<<2|U[A.charCodeAt(N+1)]>>4,K[X++]=255&B),1===V&&(B=U[A.charCodeAt(N)]<<10|U[A.charCodeAt(N+1)]<<4|U[A.charCodeAt(N+2)]>>2,K[X++]=B>>8&255,K[X++]=255&B),K}function tripletToBase64(A){return N[A>>18&63]+N[A>>12&63]+N[A>>6&63]+N[63&A]}function encodeChunk(A,B,N){for(var U,H=[],W=B;W<N;W+=3)U=(A[W]<<16&0xff0000)+(A[W+1]<<8&65280)+(255&A[W+2]),H.push(tripletToBase64(U));return H.join("")}function fromByteArray(A){for(var B,U=A.length,H=U%3,W=[],j=16383,V=0,K=U-H;V<K;V+=j)W.push(encodeChunk(A,V,V+j>K?K:V+j));return 1===H?(B=A[U-1],W.push(N[B>>2]+N[B<<4&63]+"==")):2===H&&(B=(A[U-2]<<8)+A[U-1],W.push(N[B>>10]+N[B>>4&63]+N[B<<2&63]+"=")),W.join("")}U["-".charCodeAt(0)]=62,U["_".charCodeAt(0)]=63},15313:function(A,B,N){"use strict";var U;let H=N(26446),W=N(47164),j="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;B.Buffer=Buffer,U=SlowBuffer,B.INSPECT_MAX_BYTES=50;let V=0x7fffffff;function typedArraySupport(){try{let A=new Uint8Array(1),B={foo:function(){return 42}};return Object.setPrototypeOf(B,Uint8Array.prototype),Object.setPrototypeOf(A,B),42===A.foo()}catch(A){return!1}}function createBuffer(A){if(A>V)throw RangeError('The value "'+A+'" is invalid for option "size"');let B=new Uint8Array(A);return Object.setPrototypeOf(B,Buffer.prototype),B}function Buffer(A,B,N){if("number"==typeof A){if("string"==typeof B)throw TypeError('The "string" argument must be of type string. Received type number');return allocUnsafe(A)}return from(A,B,N)}function from(A,B,N){if("string"==typeof A)return fromString(A,B);if(ArrayBuffer.isView(A))return fromArrayView(A);if(null==A)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof A);if(isInstance(A,ArrayBuffer)||A&&isInstance(A.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(isInstance(A,SharedArrayBuffer)||A&&isInstance(A.buffer,SharedArrayBuffer)))return fromArrayBuffer(A,B,N);if("number"==typeof A)throw TypeError('The "value" argument must not be of type number. Received type number');let U=A.valueOf&&A.valueOf();if(null!=U&&U!==A)return Buffer.from(U,B,N);let H=fromObject(A);if(H)return H;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof A[Symbol.toPrimitive])return Buffer.from(A[Symbol.toPrimitive]("string"),B,N);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof A)}function assertSize(A){if("number"!=typeof A)throw TypeError('"size" argument must be of type number');if(A<0)throw RangeError('The value "'+A+'" is invalid for option "size"')}function alloc(A,B,N){return(assertSize(A),A<=0)?createBuffer(A):void 0!==B?"string"==typeof N?createBuffer(A).fill(B,N):createBuffer(A).fill(B):createBuffer(A)}function allocUnsafe(A){return assertSize(A),createBuffer(A<0?0:0|checked(A))}function fromString(A,B){if(("string"!=typeof B||""===B)&&(B="utf8"),!Buffer.isEncoding(B))throw TypeError("Unknown encoding: "+B);let N=0|byteLength(A,B),U=createBuffer(N),H=U.write(A,B);return H!==N&&(U=U.slice(0,H)),U}function fromArrayLike(A){let B=A.length<0?0:0|checked(A.length),N=createBuffer(B);for(let U=0;U<B;U+=1)N[U]=255&A[U];return N}function fromArrayView(A){if(isInstance(A,Uint8Array)){let B=new Uint8Array(A);return fromArrayBuffer(B.buffer,B.byteOffset,B.byteLength)}return fromArrayLike(A)}function fromArrayBuffer(A,B,N){let U;if(B<0||A.byteLength<B)throw RangeError('"offset" is outside of buffer bounds');if(A.byteLength<B+(N||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(U=void 0===B&&void 0===N?new Uint8Array(A):void 0===N?new Uint8Array(A,B):new Uint8Array(A,B,N),Buffer.prototype),U}function fromObject(A){if(Buffer.isBuffer(A)){let B=0|checked(A.length),N=createBuffer(B);return 0===N.length?N:(A.copy(N,0,0,B),N)}if(void 0!==A.length)return"number"!=typeof A.length||numberIsNaN(A.length)?createBuffer(0):fromArrayLike(A);if("Buffer"===A.type&&Array.isArray(A.data))return fromArrayLike(A.data)}function checked(A){if(A>=V)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+V.toString(16)+" bytes");return 0|A}function SlowBuffer(A){return+A!=A&&(A=0),Buffer.alloc(+A)}function byteLength(A,B){if(Buffer.isBuffer(A))return A.length;if(ArrayBuffer.isView(A)||isInstance(A,ArrayBuffer))return A.byteLength;if("string"!=typeof A)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof A);let N=A.length,U=arguments.length>2&&!0===arguments[2];if(!U&&0===N)return 0;let H=!1;for(;;)switch(B){case"ascii":case"latin1":case"binary":return N;case"utf8":case"utf-8":return utf8ToBytes(A).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*N;case"hex":return N>>>1;case"base64":return base64ToBytes(A).length;default:if(H)return U?-1:utf8ToBytes(A).length;B=(""+B).toLowerCase(),H=!0}}function slowToString(A,B,N){let U=!1;if((void 0===B||B<0)&&(B=0),B>this.length)return"";if((void 0===N||N>this.length)&&(N=this.length),N<=0||(N>>>=0)<=(B>>>=0))return"";for(!A&&(A="utf8");;)switch(A){case"hex":return hexSlice(this,B,N);case"utf8":case"utf-8":return utf8Slice(this,B,N);case"ascii":return asciiSlice(this,B,N);case"latin1":case"binary":return latin1Slice(this,B,N);case"base64":return base64Slice(this,B,N);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return utf16leSlice(this,B,N);default:if(U)throw TypeError("Unknown encoding: "+A);A=(A+"").toLowerCase(),U=!0}}function swap(A,B,N){let U=A[B];A[B]=A[N],A[N]=U}function bidirectionalIndexOf(A,B,N,U,H){if(0===A.length)return -1;if("string"==typeof N?(U=N,N=0):N>0x7fffffff?N=0x7fffffff:N<-0x80000000&&(N=-0x80000000),numberIsNaN(N*=1)&&(N=H?0:A.length-1),N<0&&(N=A.length+N),N>=A.length){if(H)return -1;N=A.length-1}else if(N<0){if(!H)return -1;N=0}if("string"==typeof B&&(B=Buffer.from(B,U)),Buffer.isBuffer(B))return 0===B.length?-1:arrayIndexOf(A,B,N,U,H);if("number"==typeof B){if(B&=255,"function"==typeof Uint8Array.prototype.indexOf)return H?Uint8Array.prototype.indexOf.call(A,B,N):Uint8Array.prototype.lastIndexOf.call(A,B,N);return arrayIndexOf(A,[B],N,U,H)}throw TypeError("val must be string, number or Buffer")}function arrayIndexOf(A,B,N,U,H){let W,j=1,V=A.length,K=B.length;if(void 0!==U&&("ucs2"===(U=String(U).toLowerCase())||"ucs-2"===U||"utf16le"===U||"utf-16le"===U)){if(A.length<2||B.length<2)return -1;j=2,V/=2,K/=2,N/=2}function read(A,B){return 1===j?A[B]:A.readUInt16BE(B*j)}if(H){let U=-1;for(W=N;W<V;W++)if(read(A,W)===read(B,-1===U?0:W-U)){if(-1===U&&(U=W),W-U+1===K)return U*j}else -1!==U&&(W-=W-U),U=-1}else for(N+K>V&&(N=V-K),W=N;W>=0;W--){let N=!0;for(let U=0;U<K;U++)if(read(A,W+U)!==read(B,U)){N=!1;break}if(N)return W}return -1}function hexWrite(A,B,N,U){let H;N=Number(N)||0;let W=A.length-N;U?(U=Number(U))>W&&(U=W):U=W;let j=B.length;for(U>j/2&&(U=j/2),H=0;H<U;++H){let U=parseInt(B.substr(2*H,2),16);if(numberIsNaN(U))break;A[N+H]=U}return H}function utf8Write(A,B,N,U){return blitBuffer(utf8ToBytes(B,A.length-N),A,N,U)}function asciiWrite(A,B,N,U){return blitBuffer(asciiToBytes(B),A,N,U)}function base64Write(A,B,N,U){return blitBuffer(base64ToBytes(B),A,N,U)}function ucs2Write(A,B,N,U){return blitBuffer(utf16leToBytes(B,A.length-N),A,N,U)}function base64Slice(A,B,N){return 0===B&&N===A.length?H.fromByteArray(A):H.fromByteArray(A.slice(B,N))}function utf8Slice(A,B,N){N=Math.min(A.length,N);let U=[],H=B;for(;H<N;){let B=A[H],W=null,j=B>239?4:B>223?3:B>191?2:1;if(H+j<=N){let N,U,V,K;switch(j){case 1:B<128&&(W=B);break;case 2:(192&(N=A[H+1]))==128&&(K=(31&B)<<6|63&N)>127&&(W=K);break;case 3:N=A[H+1],U=A[H+2],(192&N)==128&&(192&U)==128&&(K=(15&B)<<12|(63&N)<<6|63&U)>2047&&(K<55296||K>57343)&&(W=K);break;case 4:N=A[H+1],U=A[H+2],V=A[H+3],(192&N)==128&&(192&U)==128&&(192&V)==128&&(K=(15&B)<<18|(63&N)<<12|(63&U)<<6|63&V)>65535&&K<1114112&&(W=K)}}null===W?(W=65533,j=1):W>65535&&(W-=65536,U.push(W>>>10&1023|55296),W=56320|1023&W),U.push(W),H+=j}return decodeCodePointsArray(U)}U=0x7fffffff,Buffer.TYPED_ARRAY_SUPPORT=typedArraySupport(),!Buffer.TYPED_ARRAY_SUPPORT&&"undefined"!=typeof console&&"function"==typeof console.error&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(Buffer.prototype,"parent",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.buffer}}),Object.defineProperty(Buffer.prototype,"offset",{enumerable:!0,get:function(){if(Buffer.isBuffer(this))return this.byteOffset}}),Buffer.poolSize=8192,Buffer.from=function(A,B,N){return from(A,B,N)},Object.setPrototypeOf(Buffer.prototype,Uint8Array.prototype),Object.setPrototypeOf(Buffer,Uint8Array),Buffer.alloc=function(A,B,N){return alloc(A,B,N)},Buffer.allocUnsafe=function(A){return allocUnsafe(A)},Buffer.allocUnsafeSlow=function(A){return allocUnsafe(A)},Buffer.isBuffer=function isBuffer(A){return null!=A&&!0===A._isBuffer&&A!==Buffer.prototype},Buffer.compare=function compare(A,B){if(isInstance(A,Uint8Array)&&(A=Buffer.from(A,A.offset,A.byteLength)),isInstance(B,Uint8Array)&&(B=Buffer.from(B,B.offset,B.byteLength)),!Buffer.isBuffer(A)||!Buffer.isBuffer(B))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(A===B)return 0;let N=A.length,U=B.length;for(let H=0,W=Math.min(N,U);H<W;++H)if(A[H]!==B[H]){N=A[H],U=B[H];break}return N<U?-1:1*!!(U<N)},Buffer.isEncoding=function isEncoding(A){switch(String(A).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},Buffer.concat=function concat(A,B){let N;if(!Array.isArray(A))throw TypeError('"list" argument must be an Array of Buffers');if(0===A.length)return Buffer.alloc(0);if(void 0===B)for(N=0,B=0;N<A.length;++N)B+=A[N].length;let U=Buffer.allocUnsafe(B),H=0;for(N=0;N<A.length;++N){let B=A[N];if(isInstance(B,Uint8Array))H+B.length>U.length?(!Buffer.isBuffer(B)&&(B=Buffer.from(B)),B.copy(U,H)):Uint8Array.prototype.set.call(U,B,H);else if(Buffer.isBuffer(B))B.copy(U,H);else throw TypeError('"list" argument must be an Array of Buffers');H+=B.length}return U},Buffer.byteLength=byteLength,Buffer.prototype._isBuffer=!0,Buffer.prototype.swap16=function swap16(){let A=this.length;if(A%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(let B=0;B<A;B+=2)swap(this,B,B+1);return this},Buffer.prototype.swap32=function swap32(){let A=this.length;if(A%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(let B=0;B<A;B+=4)swap(this,B,B+3),swap(this,B+1,B+2);return this},Buffer.prototype.swap64=function swap64(){let A=this.length;if(A%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(let B=0;B<A;B+=8)swap(this,B,B+7),swap(this,B+1,B+6),swap(this,B+2,B+5),swap(this,B+3,B+4);return this},Buffer.prototype.toString=function toString(){let A=this.length;return 0===A?"":0==arguments.length?utf8Slice(this,0,A):slowToString.apply(this,arguments)},Buffer.prototype.toLocaleString=Buffer.prototype.toString,Buffer.prototype.equals=function equals(A){if(!Buffer.isBuffer(A))throw TypeError("Argument must be a Buffer");return this===A||0===Buffer.compare(this,A)},Buffer.prototype.inspect=function inspect(){let A="",N=B.INSPECT_MAX_BYTES;return A=this.toString("hex",0,N).replace(/(.{2})/g,"$1 ").trim(),this.length>N&&(A+=" ... "),"<Buffer "+A+">"},j&&(Buffer.prototype[j]=Buffer.prototype.inspect),Buffer.prototype.compare=function compare(A,B,N,U,H){if(isInstance(A,Uint8Array)&&(A=Buffer.from(A,A.offset,A.byteLength)),!Buffer.isBuffer(A))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof A);if(void 0===B&&(B=0),void 0===N&&(N=A?A.length:0),void 0===U&&(U=0),void 0===H&&(H=this.length),B<0||N>A.length||U<0||H>this.length)throw RangeError("out of range index");if(U>=H&&B>=N)return 0;if(U>=H)return -1;if(B>=N)return 1;if(B>>>=0,N>>>=0,U>>>=0,H>>>=0,this===A)return 0;let W=H-U,j=N-B,V=Math.min(W,j),K=this.slice(U,H),X=A.slice(B,N);for(let A=0;A<V;++A)if(K[A]!==X[A]){W=K[A],j=X[A];break}return W<j?-1:1*!!(j<W)},Buffer.prototype.includes=function includes(A,B,N){return -1!==this.indexOf(A,B,N)},Buffer.prototype.indexOf=function indexOf(A,B,N){return bidirectionalIndexOf(this,A,B,N,!0)},Buffer.prototype.lastIndexOf=function lastIndexOf(A,B,N){return bidirectionalIndexOf(this,A,B,N,!1)},Buffer.prototype.write=function write(A,B,N,U){if(void 0===B)U="utf8",N=this.length,B=0;else if(void 0===N&&"string"==typeof B)U=B,N=this.length,B=0;else if(isFinite(B))B>>>=0,isFinite(N)?(N>>>=0,void 0===U&&(U="utf8")):(U=N,N=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let H=this.length-B;if((void 0===N||N>H)&&(N=H),A.length>0&&(N<0||B<0)||B>this.length)throw RangeError("Attempt to write outside buffer bounds");!U&&(U="utf8");let W=!1;for(;;)switch(U){case"hex":return hexWrite(this,A,B,N);case"utf8":case"utf-8":return utf8Write(this,A,B,N);case"ascii":case"latin1":case"binary":return asciiWrite(this,A,B,N);case"base64":return base64Write(this,A,B,N);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return ucs2Write(this,A,B,N);default:if(W)throw TypeError("Unknown encoding: "+U);U=(""+U).toLowerCase(),W=!0}},Buffer.prototype.toJSON=function toJSON(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};let K=4096;function decodeCodePointsArray(A){let B=A.length;if(B<=K)return String.fromCharCode.apply(String,A);let N="",U=0;for(;U<B;)N+=String.fromCharCode.apply(String,A.slice(U,U+=K));return N}function asciiSlice(A,B,N){let U="";N=Math.min(A.length,N);for(let H=B;H<N;++H)U+=String.fromCharCode(127&A[H]);return U}function latin1Slice(A,B,N){let U="";N=Math.min(A.length,N);for(let H=B;H<N;++H)U+=String.fromCharCode(A[H]);return U}function hexSlice(A,B,N){let U=A.length;(!B||B<0)&&(B=0),(!N||N<0||N>U)&&(N=U);let H="";for(let U=B;U<N;++U)H+=ee[A[U]];return H}function utf16leSlice(A,B,N){let U=A.slice(B,N),H="";for(let A=0;A<U.length-1;A+=2)H+=String.fromCharCode(U[A]+256*U[A+1]);return H}function checkOffset(A,B,N){if(A%1!=0||A<0)throw RangeError("offset is not uint");if(A+B>N)throw RangeError("Trying to access beyond buffer length")}function checkInt(A,B,N,U,H,W){if(!Buffer.isBuffer(A))throw TypeError('"buffer" argument must be a Buffer instance');if(B>H||B<W)throw RangeError('"value" argument is out of bounds');if(N+U>A.length)throw RangeError("Index out of range")}function wrtBigUInt64LE(A,B,N,U,H){checkIntBI(B,U,H,A,N,7);let W=Number(B&BigInt(0xffffffff));A[N++]=W,W>>=8,A[N++]=W,W>>=8,A[N++]=W,W>>=8,A[N++]=W;let j=Number(B>>BigInt(32)&BigInt(0xffffffff));return A[N++]=j,j>>=8,A[N++]=j,j>>=8,A[N++]=j,j>>=8,A[N++]=j,N}function wrtBigUInt64BE(A,B,N,U,H){checkIntBI(B,U,H,A,N,7);let W=Number(B&BigInt(0xffffffff));A[N+7]=W,W>>=8,A[N+6]=W,W>>=8,A[N+5]=W,W>>=8,A[N+4]=W;let j=Number(B>>BigInt(32)&BigInt(0xffffffff));return A[N+3]=j,j>>=8,A[N+2]=j,j>>=8,A[N+1]=j,j>>=8,A[N]=j,N+8}function checkIEEE754(A,B,N,U,H,W){if(N+U>A.length||N<0)throw RangeError("Index out of range")}function writeFloat(A,B,N,U,H){return B*=1,N>>>=0,!H&&checkIEEE754(A,B,N,4,34028234663852886e22,-34028234663852886e22),W.write(A,B,N,U,23,4),N+4}function writeDouble(A,B,N,U,H){return B*=1,N>>>=0,!H&&checkIEEE754(A,B,N,8,17976931348623157e292,-17976931348623157e292),W.write(A,B,N,U,52,8),N+8}Buffer.prototype.slice=function slice(A,B){let N=this.length;A=~~A,B=void 0===B?N:~~B,A<0?(A+=N)<0&&(A=0):A>N&&(A=N),B<0?(B+=N)<0&&(B=0):B>N&&(B=N),B<A&&(B=A);let U=this.subarray(A,B);return Object.setPrototypeOf(U,Buffer.prototype),U},Buffer.prototype.readUintLE=Buffer.prototype.readUIntLE=function readUIntLE(A,B,N){A>>>=0,B>>>=0,!N&&checkOffset(A,B,this.length);let U=this[A],H=1,W=0;for(;++W<B&&(H*=256);)U+=this[A+W]*H;return U},Buffer.prototype.readUintBE=Buffer.prototype.readUIntBE=function readUIntBE(A,B,N){A>>>=0,B>>>=0,!N&&checkOffset(A,B,this.length);let U=this[A+--B],H=1;for(;B>0&&(H*=256);)U+=this[A+--B]*H;return U},Buffer.prototype.readUint8=Buffer.prototype.readUInt8=function readUInt8(A,B){return A>>>=0,!B&&checkOffset(A,1,this.length),this[A]},Buffer.prototype.readUint16LE=Buffer.prototype.readUInt16LE=function readUInt16LE(A,B){return A>>>=0,!B&&checkOffset(A,2,this.length),this[A]|this[A+1]<<8},Buffer.prototype.readUint16BE=Buffer.prototype.readUInt16BE=function readUInt16BE(A,B){return A>>>=0,!B&&checkOffset(A,2,this.length),this[A]<<8|this[A+1]},Buffer.prototype.readUint32LE=Buffer.prototype.readUInt32LE=function readUInt32LE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),(this[A]|this[A+1]<<8|this[A+2]<<16)+0x1000000*this[A+3]},Buffer.prototype.readUint32BE=Buffer.prototype.readUInt32BE=function readUInt32BE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),0x1000000*this[A]+(this[A+1]<<16|this[A+2]<<8|this[A+3])},Buffer.prototype.readBigUInt64LE=defineBigIntMethod(function readBigUInt64LE(A){validateNumber(A>>>=0,"offset");let B=this[A],N=this[A+7];(void 0===B||void 0===N)&&boundsError(A,this.length-8);let U=B+256*this[++A]+65536*this[++A]+0x1000000*this[++A],H=this[++A]+256*this[++A]+65536*this[++A]+0x1000000*N;return BigInt(U)+(BigInt(H)<<BigInt(32))}),Buffer.prototype.readBigUInt64BE=defineBigIntMethod(function readBigUInt64BE(A){validateNumber(A>>>=0,"offset");let B=this[A],N=this[A+7];(void 0===B||void 0===N)&&boundsError(A,this.length-8);let U=0x1000000*B+65536*this[++A]+256*this[++A]+this[++A],H=0x1000000*this[++A]+65536*this[++A]+256*this[++A]+N;return(BigInt(U)<<BigInt(32))+BigInt(H)}),Buffer.prototype.readIntLE=function readIntLE(A,B,N){A>>>=0,B>>>=0,!N&&checkOffset(A,B,this.length);let U=this[A],H=1,W=0;for(;++W<B&&(H*=256);)U+=this[A+W]*H;return U>=(H*=128)&&(U-=Math.pow(2,8*B)),U},Buffer.prototype.readIntBE=function readIntBE(A,B,N){A>>>=0,B>>>=0,!N&&checkOffset(A,B,this.length);let U=B,H=1,W=this[A+--U];for(;U>0&&(H*=256);)W+=this[A+--U]*H;return W>=(H*=128)&&(W-=Math.pow(2,8*B)),W},Buffer.prototype.readInt8=function readInt8(A,B){return(A>>>=0,!B&&checkOffset(A,1,this.length),128&this[A])?-((255-this[A]+1)*1):this[A]},Buffer.prototype.readInt16LE=function readInt16LE(A,B){A>>>=0,!B&&checkOffset(A,2,this.length);let N=this[A]|this[A+1]<<8;return 32768&N?0xffff0000|N:N},Buffer.prototype.readInt16BE=function readInt16BE(A,B){A>>>=0,!B&&checkOffset(A,2,this.length);let N=this[A+1]|this[A]<<8;return 32768&N?0xffff0000|N:N},Buffer.prototype.readInt32LE=function readInt32LE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),this[A]|this[A+1]<<8|this[A+2]<<16|this[A+3]<<24},Buffer.prototype.readInt32BE=function readInt32BE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),this[A]<<24|this[A+1]<<16|this[A+2]<<8|this[A+3]},Buffer.prototype.readBigInt64LE=defineBigIntMethod(function readBigInt64LE(A){validateNumber(A>>>=0,"offset");let B=this[A],N=this[A+7];return(void 0===B||void 0===N)&&boundsError(A,this.length-8),(BigInt(this[A+4]+256*this[A+5]+65536*this[A+6]+(N<<24))<<BigInt(32))+BigInt(B+256*this[++A]+65536*this[++A]+0x1000000*this[++A])}),Buffer.prototype.readBigInt64BE=defineBigIntMethod(function readBigInt64BE(A){validateNumber(A>>>=0,"offset");let B=this[A],N=this[A+7];return(void 0===B||void 0===N)&&boundsError(A,this.length-8),(BigInt((B<<24)+65536*this[++A]+256*this[++A]+this[++A])<<BigInt(32))+BigInt(0x1000000*this[++A]+65536*this[++A]+256*this[++A]+N)}),Buffer.prototype.readFloatLE=function readFloatLE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),W.read(this,A,!0,23,4)},Buffer.prototype.readFloatBE=function readFloatBE(A,B){return A>>>=0,!B&&checkOffset(A,4,this.length),W.read(this,A,!1,23,4)},Buffer.prototype.readDoubleLE=function readDoubleLE(A,B){return A>>>=0,!B&&checkOffset(A,8,this.length),W.read(this,A,!0,52,8)},Buffer.prototype.readDoubleBE=function readDoubleBE(A,B){return A>>>=0,!B&&checkOffset(A,8,this.length),W.read(this,A,!1,52,8)},Buffer.prototype.writeUintLE=Buffer.prototype.writeUIntLE=function writeUIntLE(A,B,N,U){if(A*=1,B>>>=0,N>>>=0,!U){let U=Math.pow(2,8*N)-1;checkInt(this,A,B,N,U,0)}let H=1,W=0;for(this[B]=255&A;++W<N&&(H*=256);)this[B+W]=A/H&255;return B+N},Buffer.prototype.writeUintBE=Buffer.prototype.writeUIntBE=function writeUIntBE(A,B,N,U){if(A*=1,B>>>=0,N>>>=0,!U){let U=Math.pow(2,8*N)-1;checkInt(this,A,B,N,U,0)}let H=N-1,W=1;for(this[B+H]=255&A;--H>=0&&(W*=256);)this[B+H]=A/W&255;return B+N},Buffer.prototype.writeUint8=Buffer.prototype.writeUInt8=function writeUInt8(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,1,255,0),this[B]=255&A,B+1},Buffer.prototype.writeUint16LE=Buffer.prototype.writeUInt16LE=function writeUInt16LE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,2,65535,0),this[B]=255&A,this[B+1]=A>>>8,B+2},Buffer.prototype.writeUint16BE=Buffer.prototype.writeUInt16BE=function writeUInt16BE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,2,65535,0),this[B]=A>>>8,this[B+1]=255&A,B+2},Buffer.prototype.writeUint32LE=Buffer.prototype.writeUInt32LE=function writeUInt32LE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,4,0xffffffff,0),this[B+3]=A>>>24,this[B+2]=A>>>16,this[B+1]=A>>>8,this[B]=255&A,B+4},Buffer.prototype.writeUint32BE=Buffer.prototype.writeUInt32BE=function writeUInt32BE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,4,0xffffffff,0),this[B]=A>>>24,this[B+1]=A>>>16,this[B+2]=A>>>8,this[B+3]=255&A,B+4},Buffer.prototype.writeBigUInt64LE=defineBigIntMethod(function writeBigUInt64LE(A,B=0){return wrtBigUInt64LE(this,A,B,BigInt(0),BigInt("0xffffffffffffffff"))}),Buffer.prototype.writeBigUInt64BE=defineBigIntMethod(function writeBigUInt64BE(A,B=0){return wrtBigUInt64BE(this,A,B,BigInt(0),BigInt("0xffffffffffffffff"))}),Buffer.prototype.writeIntLE=function writeIntLE(A,B,N,U){if(A*=1,B>>>=0,!U){let U=Math.pow(2,8*N-1);checkInt(this,A,B,N,U-1,-U)}let H=0,W=1,j=0;for(this[B]=255&A;++H<N&&(W*=256);)A<0&&0===j&&0!==this[B+H-1]&&(j=1),this[B+H]=(A/W>>0)-j&255;return B+N},Buffer.prototype.writeIntBE=function writeIntBE(A,B,N,U){if(A*=1,B>>>=0,!U){let U=Math.pow(2,8*N-1);checkInt(this,A,B,N,U-1,-U)}let H=N-1,W=1,j=0;for(this[B+H]=255&A;--H>=0&&(W*=256);)A<0&&0===j&&0!==this[B+H+1]&&(j=1),this[B+H]=(A/W>>0)-j&255;return B+N},Buffer.prototype.writeInt8=function writeInt8(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,1,127,-128),A<0&&(A=255+A+1),this[B]=255&A,B+1},Buffer.prototype.writeInt16LE=function writeInt16LE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,2,32767,-32768),this[B]=255&A,this[B+1]=A>>>8,B+2},Buffer.prototype.writeInt16BE=function writeInt16BE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,2,32767,-32768),this[B]=A>>>8,this[B+1]=255&A,B+2},Buffer.prototype.writeInt32LE=function writeInt32LE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,4,0x7fffffff,-0x80000000),this[B]=255&A,this[B+1]=A>>>8,this[B+2]=A>>>16,this[B+3]=A>>>24,B+4},Buffer.prototype.writeInt32BE=function writeInt32BE(A,B,N){return A*=1,B>>>=0,!N&&checkInt(this,A,B,4,0x7fffffff,-0x80000000),A<0&&(A=0xffffffff+A+1),this[B]=A>>>24,this[B+1]=A>>>16,this[B+2]=A>>>8,this[B+3]=255&A,B+4},Buffer.prototype.writeBigInt64LE=defineBigIntMethod(function writeBigInt64LE(A,B=0){return wrtBigUInt64LE(this,A,B,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),Buffer.prototype.writeBigInt64BE=defineBigIntMethod(function writeBigInt64BE(A,B=0){return wrtBigUInt64BE(this,A,B,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))}),Buffer.prototype.writeFloatLE=function writeFloatLE(A,B,N){return writeFloat(this,A,B,!0,N)},Buffer.prototype.writeFloatBE=function writeFloatBE(A,B,N){return writeFloat(this,A,B,!1,N)},Buffer.prototype.writeDoubleLE=function writeDoubleLE(A,B,N){return writeDouble(this,A,B,!0,N)},Buffer.prototype.writeDoubleBE=function writeDoubleBE(A,B,N){return writeDouble(this,A,B,!1,N)},Buffer.prototype.copy=function copy(A,B,N,U){if(!Buffer.isBuffer(A))throw TypeError("argument should be a Buffer");if(!N&&(N=0),!U&&0!==U&&(U=this.length),B>=A.length&&(B=A.length),!B&&(B=0),U>0&&U<N&&(U=N),U===N||0===A.length||0===this.length)return 0;if(B<0)throw RangeError("targetStart out of bounds");if(N<0||N>=this.length)throw RangeError("Index out of range");if(U<0)throw RangeError("sourceEnd out of bounds");U>this.length&&(U=this.length),A.length-B<U-N&&(U=A.length-B+N);let H=U-N;return this===A&&"function"==typeof Uint8Array.prototype.copyWithin?this.copyWithin(B,N,U):Uint8Array.prototype.set.call(A,this.subarray(N,U),B),H},Buffer.prototype.fill=function fill(A,B,N,U){let H;if("string"==typeof A){if("string"==typeof B?(U=B,B=0,N=this.length):"string"==typeof N&&(U=N,N=this.length),void 0!==U&&"string"!=typeof U)throw TypeError("encoding must be a string");if("string"==typeof U&&!Buffer.isEncoding(U))throw TypeError("Unknown encoding: "+U);if(1===A.length){let B=A.charCodeAt(0);("utf8"===U&&B<128||"latin1"===U)&&(A=B)}}else"number"==typeof A?A&=255:"boolean"==typeof A&&(A=Number(A));if(B<0||this.length<B||this.length<N)throw RangeError("Out of range index");if(N<=B)return this;if(B>>>=0,N=void 0===N?this.length:N>>>0,!A&&(A=0),"number"==typeof A)for(H=B;H<N;++H)this[H]=A;else{let W=Buffer.isBuffer(A)?A:Buffer.from(A,U),j=W.length;if(0===j)throw TypeError('The value "'+A+'" is invalid for argument "value"');for(H=0;H<N-B;++H)this[H+B]=W[H%j]}return this};let X={};function E(A,B,N){X[A]=class extends N{constructor(){super(),Object.defineProperty(this,"message",{value:B.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${A}]`,this.stack,delete this.name}get code(){return A}set code(A){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:A,writable:!0})}toString(){return`${this.name} [${A}]: ${this.message}`}}}function addNumericalSeparator(A){let B="",N=A.length,U=+("-"===A[0]);for(;N>=U+4;N-=3)B=`_${A.slice(N-3,N)}${B}`;return`${A.slice(0,N)}${B}`}function checkBounds(A,B,N){validateNumber(B,"offset"),(void 0===A[B]||void 0===A[B+N])&&boundsError(B,A.length-(N+1))}function checkIntBI(A,B,N,U,H,W){if(A>N||A<B){let U;let H="bigint"==typeof B?"n":"";throw U=W>3?0===B||B===BigInt(0)?`>= 0${H} and < 2${H} ** ${(W+1)*8}${H}`:`>= -(2${H} ** ${(W+1)*8-1}${H}) and < 2 ** ${(W+1)*8-1}${H}`:`>= ${B}${H} and <= ${N}${H}`,new X.ERR_OUT_OF_RANGE("value",U,A)}checkBounds(U,H,W)}function validateNumber(A,B){if("number"!=typeof A)throw new X.ERR_INVALID_ARG_TYPE(B,"number",A)}function boundsError(A,B,N){if(Math.floor(A)!==A)throw validateNumber(A,N),new X.ERR_OUT_OF_RANGE(N||"offset","an integer",A);if(B<0)throw new X.ERR_BUFFER_OUT_OF_BOUNDS;throw new X.ERR_OUT_OF_RANGE(N||"offset",`>= ${+!!N} and <= ${B}`,A)}E("ERR_BUFFER_OUT_OF_BOUNDS",function(A){return A?`${A} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError),E("ERR_INVALID_ARG_TYPE",function(A,B){return`The "${A}" argument must be of type number. Received type ${typeof B}`},TypeError),E("ERR_OUT_OF_RANGE",function(A,B,N){let U=`The value of "${A}" is out of range.`,H=N;return Number.isInteger(N)&&Math.abs(N)>0x100000000?H=addNumericalSeparator(String(N)):"bigint"==typeof N&&(H=String(N),(N>BigInt(2)**BigInt(32)||N<-(BigInt(2)**BigInt(32)))&&(H=addNumericalSeparator(H)),H+="n"),U+=` It must be ${B}. Received ${H}`},RangeError);let J=/[^+/0-9A-Za-z-_]/g;function base64clean(A){if((A=(A=A.split("=")[0]).trim().replace(J,"")).length<2)return"";for(;A.length%4!=0;)A+="=";return A}function utf8ToBytes(A,B){let N;B=B||1/0;let U=A.length,H=null,W=[];for(let j=0;j<U;++j){if((N=A.charCodeAt(j))>55295&&N<57344){if(!H){if(N>56319){(B-=3)>-1&&W.push(239,191,189);continue}if(j+1===U){(B-=3)>-1&&W.push(239,191,189);continue}H=N;continue}if(N<56320){(B-=3)>-1&&W.push(239,191,189),H=N;continue}N=(H-55296<<10|N-56320)+65536}else H&&(B-=3)>-1&&W.push(239,191,189);if(H=null,N<128){if((B-=1)<0)break;W.push(N)}else if(N<2048){if((B-=2)<0)break;W.push(N>>6|192,63&N|128)}else if(N<65536){if((B-=3)<0)break;W.push(N>>12|224,N>>6&63|128,63&N|128)}else if(N<1114112){if((B-=4)<0)break;W.push(N>>18|240,N>>12&63|128,N>>6&63|128,63&N|128)}else throw Error("Invalid code point")}return W}function asciiToBytes(A){let B=[];for(let N=0;N<A.length;++N)B.push(255&A.charCodeAt(N));return B}function utf16leToBytes(A,B){let N,U,H;let W=[];for(let j=0;j<A.length&&!((B-=2)<0);++j)U=(N=A.charCodeAt(j))>>8,H=N%256,W.push(H),W.push(U);return W}function base64ToBytes(A){return H.toByteArray(base64clean(A))}function blitBuffer(A,B,N,U){let H;for(H=0;H<U&&!(H+N>=B.length)&&!(H>=A.length);++H)B[H+N]=A[H];return H}function isInstance(A,B){return A instanceof B||null!=A&&null!=A.constructor&&null!=A.constructor.name&&A.constructor.name===B.name}function numberIsNaN(A){return A!=A}let ee=function(){let A="0123456789abcdef",B=Array(256);for(let N=0;N<16;++N){let U=16*N;for(let H=0;H<16;++H)B[U+H]=A[N]+A[H]}return B}();function defineBigIntMethod(A){return"undefined"==typeof BigInt?BufferBigIntNotDefined:A}function BufferBigIntNotDefined(){throw Error("BigInt not supported")}},29203:function(A){var B={utf8:{stringToBytes:function(A){return B.bin.stringToBytes(unescape(encodeURIComponent(A)))},bytesToString:function(A){return decodeURIComponent(escape(B.bin.bytesToString(A)))}},bin:{stringToBytes:function(A){for(var B=[],N=0;N<A.length;N++)B.push(255&A.charCodeAt(N));return B},bytesToString:function(A){for(var B=[],N=0;N<A.length;N++)B.push(String.fromCharCode(A[N]));return B.join("")}}};A.exports=B},54274:function(A){!function(){var B="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",N={rotl:function(A,B){return A<<B|A>>>32-B},rotr:function(A,B){return A<<32-B|A>>>B},endian:function(A){if(A.constructor==Number)return 0xff00ff&N.rotl(A,8)|0xff00ff00&N.rotl(A,24);for(var B=0;B<A.length;B++)A[B]=N.endian(A[B]);return A},randomBytes:function(A){for(var B=[];A>0;A--)B.push(Math.floor(256*Math.random()));return B},bytesToWords:function(A){for(var B=[],N=0,U=0;N<A.length;N++,U+=8)B[U>>>5]|=A[N]<<24-U%32;return B},wordsToBytes:function(A){for(var B=[],N=0;N<32*A.length;N+=8)B.push(A[N>>>5]>>>24-N%32&255);return B},bytesToHex:function(A){for(var B=[],N=0;N<A.length;N++)B.push((A[N]>>>4).toString(16)),B.push((15&A[N]).toString(16));return B.join("")},hexToBytes:function(A){for(var B=[],N=0;N<A.length;N+=2)B.push(parseInt(A.substr(N,2),16));return B},bytesToBase64:function(A){for(var N=[],U=0;U<A.length;U+=3){for(var H=A[U]<<16|A[U+1]<<8|A[U+2],W=0;W<4;W++)8*U+6*W<=8*A.length?N.push(B.charAt(H>>>6*(3-W)&63)):N.push("=")}return N.join("")},base64ToBytes:function(A){A=A.replace(/[^A-Z0-9+\/]/ig,"");for(var N=[],U=0,H=0;U<A.length;H=++U%4)0!=H&&N.push((B.indexOf(A.charAt(U-1))&Math.pow(2,-2*H+8)-1)<<2*H|B.indexOf(A.charAt(U))>>>6-2*H);return N}};A.exports=N}()},28977:function(A){!function(B,N){A.exports=N()}(0,function(){"use strict";var A=1e3,B=6e4,N=36e5,U="millisecond",H="second",W="minute",j="hour",V="day",K="week",X="month",J="quarter",ee="year",et="date",er="Invalid Date",en=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,ei=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,eo={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(A){var B=["th","st","nd","rd"],N=A%100;return"["+A+(B[(N-20)%10]||B[N]||B[0])+"]"}},m=function(A,B,N){var U=String(A);return!U||U.length>=B?A:""+Array(B+1-U.length).join(N)+A},ea={s:m,z:function(A){var B=-A.utcOffset(),N=Math.abs(B),U=Math.floor(N/60),H=N%60;return(B<=0?"+":"-")+m(U,2,"0")+":"+m(H,2,"0")},m:function t(A,B){if(A.date()<B.date())return-t(B,A);var N=12*(B.year()-A.year())+(B.month()-A.month()),U=A.clone().add(N,X),H=B-U<0,W=A.clone().add(N+(H?-1:1),X);return+(-(N+(B-U)/(H?U-W:W-U))||0)},a:function(A){return A<0?Math.ceil(A)||0:Math.floor(A)},p:function(A){return({M:X,y:ee,w:K,d:V,D:et,h:j,m:W,s:H,ms:U,Q:J})[A]||String(A||"").toLowerCase().replace(/s$/,"")},u:function(A){return void 0===A}},es="en",eu={};eu[es]=eo;var el="$isDayjsObject",S=function(A){return A instanceof ef||!(!A||!A[el])},w=function t(A,B,N){var U;if(!A)return es;if("string"==typeof A){var H=A.toLowerCase();eu[H]&&(U=H),B&&(eu[H]=B,U=H);var W=A.split("-");if(!U&&W.length>1)return t(W[0])}else{var j=A.name;eu[j]=A,U=j}return!N&&U&&(es=U),U||!N&&es},O=function(A,B){if(S(A))return A.clone();var N="object"==typeof B?B:{};return N.date=A,N.args=arguments,new ef(N)},ec=ea;ec.l=w,ec.i=S,ec.w=function(A,B){return O(A,{locale:B.$L,utc:B.$u,x:B.$x,$offset:B.$offset})};var ef=function(){function M(A){this.$L=w(A.locale,null,!0),this.parse(A),this.$x=this.$x||A.x||{},this[el]=!0}var eo=M.prototype;return eo.parse=function(A){this.$d=function(A){var B=A.date,N=A.utc;if(null===B)return new Date(NaN);if(ec.u(B))return new Date;if(B instanceof Date)return new Date(B);if("string"==typeof B&&!/Z$/i.test(B)){var U=B.match(en);if(U){var H=U[2]-1||0,W=(U[7]||"0").substring(0,3);return N?new Date(Date.UTC(U[1],H,U[3]||1,U[4]||0,U[5]||0,U[6]||0,W)):new Date(U[1],H,U[3]||1,U[4]||0,U[5]||0,U[6]||0,W)}}return new Date(B)}(A),this.init()},eo.init=function(){var A=this.$d;this.$y=A.getFullYear(),this.$M=A.getMonth(),this.$D=A.getDate(),this.$W=A.getDay(),this.$H=A.getHours(),this.$m=A.getMinutes(),this.$s=A.getSeconds(),this.$ms=A.getMilliseconds()},eo.$utils=function(){return ec},eo.isValid=function(){return this.$d.toString()!==er},eo.isSame=function(A,B){var N=O(A);return this.startOf(B)<=N&&N<=this.endOf(B)},eo.isAfter=function(A,B){return O(A)<this.startOf(B)},eo.isBefore=function(A,B){return this.endOf(B)<O(A)},eo.$g=function(A,B,N){return ec.u(A)?this[B]:this.set(N,A)},eo.unix=function(){return Math.floor(this.valueOf()/1e3)},eo.valueOf=function(){return this.$d.getTime()},eo.startOf=function(A,B){var N=this,U=!!ec.u(B)||B,J=ec.p(A),l=function(A,B){var H=ec.w(N.$u?Date.UTC(N.$y,B,A):new Date(N.$y,B,A),N);return U?H:H.endOf(V)},$=function(A,B){return ec.w(N.toDate()[A].apply(N.toDate("s"),(U?[0,0,0,0]:[23,59,59,999]).slice(B)),N)},er=this.$W,en=this.$M,ei=this.$D,eo="set"+(this.$u?"UTC":"");switch(J){case ee:return U?l(1,0):l(31,11);case X:return U?l(1,en):l(0,en+1);case K:var ea=this.$locale().weekStart||0,es=(er<ea?er+7:er)-ea;return l(U?ei-es:ei+(6-es),en);case V:case et:return $(eo+"Hours",0);case j:return $(eo+"Minutes",1);case W:return $(eo+"Seconds",2);case H:return $(eo+"Milliseconds",3);default:return this.clone()}},eo.endOf=function(A){return this.startOf(A,!1)},eo.$set=function(A,B){var N,K=ec.p(A),J="set"+(this.$u?"UTC":""),er=((N={})[V]=J+"Date",N[et]=J+"Date",N[X]=J+"Month",N[ee]=J+"FullYear",N[j]=J+"Hours",N[W]=J+"Minutes",N[H]=J+"Seconds",N[U]=J+"Milliseconds",N)[K],en=K===V?this.$D+(B-this.$W):B;if(K===X||K===ee){var ei=this.clone().set(et,1);ei.$d[er](en),ei.init(),this.$d=ei.set(et,Math.min(this.$D,ei.daysInMonth())).$d}else er&&this.$d[er](en);return this.init(),this},eo.set=function(A,B){return this.clone().$set(A,B)},eo.get=function(A){return this[ec.p(A)]()},eo.add=function(U,J){var et,er=this;U=Number(U);var en=ec.p(J),y=function(A){var B=O(er);return ec.w(B.date(B.date()+Math.round(A*U)),er)};if(en===X)return this.set(X,this.$M+U);if(en===ee)return this.set(ee,this.$y+U);if(en===V)return y(1);if(en===K)return y(7);var ei=((et={})[W]=B,et[j]=N,et[H]=A,et)[en]||1,eo=this.$d.getTime()+U*ei;return ec.w(eo,this)},eo.subtract=function(A,B){return this.add(-1*A,B)},eo.format=function(A){var B=this,N=this.$locale();if(!this.isValid())return N.invalidDate||er;var U=A||"YYYY-MM-DDTHH:mm:ssZ",H=ec.z(this),W=this.$H,j=this.$m,V=this.$M,K=N.weekdays,X=N.months,J=N.meridiem,h=function(A,N,H,W){return A&&(A[N]||A(B,U))||H[N].slice(0,W)},d=function(A){return ec.s(W%12||12,A,"0")},ee=J||function(A,B,N){var U=A<12?"AM":"PM";return N?U.toLowerCase():U};return U.replace(ei,function(A,U){return U||function(A){switch(A){case"YY":return String(B.$y).slice(-2);case"YYYY":return ec.s(B.$y,4,"0");case"M":return V+1;case"MM":return ec.s(V+1,2,"0");case"MMM":return h(N.monthsShort,V,X,3);case"MMMM":return h(X,V);case"D":return B.$D;case"DD":return ec.s(B.$D,2,"0");case"d":return String(B.$W);case"dd":return h(N.weekdaysMin,B.$W,K,2);case"ddd":return h(N.weekdaysShort,B.$W,K,3);case"dddd":return K[B.$W];case"H":return String(W);case"HH":return ec.s(W,2,"0");case"h":return d(1);case"hh":return d(2);case"a":return ee(W,j,!0);case"A":return ee(W,j,!1);case"m":return String(j);case"mm":return ec.s(j,2,"0");case"s":return String(B.$s);case"ss":return ec.s(B.$s,2,"0");case"SSS":return ec.s(B.$ms,3,"0");case"Z":return H}return null}(A)||H.replace(":","")})},eo.utcOffset=function(){return-(15*Math.round(this.$d.getTimezoneOffset()/15))},eo.diff=function(U,et,er){var en,ei=this,eo=ec.p(et),ea=O(U),es=(ea.utcOffset()-this.utcOffset())*B,eu=this-ea,D=function(){return ec.m(ei,ea)};switch(eo){case ee:en=D()/12;break;case X:en=D();break;case J:en=D()/3;break;case K:en=(eu-es)/6048e5;break;case V:en=(eu-es)/864e5;break;case j:en=eu/N;break;case W:en=eu/B;break;case H:en=eu/A;break;default:en=eu}return er?en:ec.a(en)},eo.daysInMonth=function(){return this.endOf(X).$D},eo.$locale=function(){return eu[this.$L]},eo.locale=function(A,B){if(!A)return this.$L;var N=this.clone(),U=w(A,B,!0);return U&&(N.$L=U),N},eo.clone=function(){return ec.w(this.$d,this)},eo.toDate=function(){return new Date(this.valueOf())},eo.toJSON=function(){return this.isValid()?this.toISOString():null},eo.toISOString=function(){return this.$d.toISOString()},eo.toString=function(){return this.$d.toUTCString()},M}(),ed=ef.prototype;return O.prototype=ed,[["$ms",U],["$s",H],["$m",W],["$H",j],["$W",V],["$M",X],["$y",ee],["$D",et]].forEach(function(A){ed[A[1]]=function(B){return this.$g(B,A[0],A[1])}}),O.extend=function(A,B){return A.$i||(A(B,ef,O),A.$i=!0),O},O.locale=w,O.isDayjs=S,O.unix=function(A){return O(1e3*A)},O.en=eu[es],O.Ls=eu,O.p={},O})},88986:function(A){!function(B,N){A.exports=N()}(0,function(){"use strict";var A,B,N=1e3,U=6e4,H=36e5,W=864e5,j=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,V=31536e6,K=2628e6,X=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,J={years:31536e6,months:2628e6,days:864e5,hours:36e5,minutes:6e4,seconds:1e3,milliseconds:1,weeks:6048e5},c=function(A){return A instanceof ee},f=function(A,B,N){return new ee(A,N,B.$l)},m=function(A){return B.p(A)+"s"},l=function(A){return A<0},$=function(A){return l(A)?Math.ceil(A):Math.floor(A)},y=function(A){return Math.abs(A)},v=function(A,B){return A?l(A)?{negative:!0,format:""+y(A)+B}:{negative:!1,format:""+A+B}:{negative:!1,format:""}},ee=function(){function l(A,B,N){var U=this;if(this.$d={},this.$l=N,void 0===A&&(this.$ms=0,this.parseFromMilliseconds()),B)return f(A*J[m(B)],this);if("number"==typeof A)return this.$ms=A,this.parseFromMilliseconds(),this;if("object"==typeof A)return Object.keys(A).forEach(function(B){U.$d[m(B)]=A[B]}),this.calMilliseconds(),this;if("string"==typeof A){var H=A.match(X);if(H){var W=H.slice(2).map(function(A){return null!=A?Number(A):0});return this.$d.years=W[0],this.$d.months=W[1],this.$d.weeks=W[2],this.$d.days=W[3],this.$d.hours=W[4],this.$d.minutes=W[5],this.$d.seconds=W[6],this.calMilliseconds(),this}}return this}var ee=l.prototype;return ee.calMilliseconds=function(){var A=this;this.$ms=Object.keys(this.$d).reduce(function(B,N){return B+(A.$d[N]||0)*J[N]},0)},ee.parseFromMilliseconds=function(){var A=this.$ms;this.$d.years=$(A/V),A%=V,this.$d.months=$(A/K),A%=K,this.$d.days=$(A/W),A%=W,this.$d.hours=$(A/H),A%=H,this.$d.minutes=$(A/U),A%=U,this.$d.seconds=$(A/N),A%=N,this.$d.milliseconds=A},ee.toISOString=function(){var A=v(this.$d.years,"Y"),B=v(this.$d.months,"M"),N=+this.$d.days||0;this.$d.weeks&&(N+=7*this.$d.weeks);var U=v(N,"D"),H=v(this.$d.hours,"H"),W=v(this.$d.minutes,"M"),j=this.$d.seconds||0;this.$d.milliseconds&&(j+=this.$d.milliseconds/1e3,j=Math.round(1e3*j)/1e3);var V=v(j,"S"),K=A.negative||B.negative||U.negative||H.negative||W.negative||V.negative,X=H.format||W.format||V.format?"T":"",J=(K?"-":"")+"P"+A.format+B.format+U.format+X+H.format+W.format+V.format;return"P"===J||"-P"===J?"P0D":J},ee.toJSON=function(){return this.toISOString()},ee.format=function(A){var N=A||"YYYY-MM-DDTHH:mm:ss",U={Y:this.$d.years,YY:B.s(this.$d.years,2,"0"),YYYY:B.s(this.$d.years,4,"0"),M:this.$d.months,MM:B.s(this.$d.months,2,"0"),D:this.$d.days,DD:B.s(this.$d.days,2,"0"),H:this.$d.hours,HH:B.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:B.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:B.s(this.$d.seconds,2,"0"),SSS:B.s(this.$d.milliseconds,3,"0")};return N.replace(j,function(A,B){return B||String(U[A])})},ee.as=function(A){return this.$ms/J[m(A)]},ee.get=function(A){var B=this.$ms,N=m(A);return"milliseconds"===N?B%=1e3:B="weeks"===N?$(B/J[N]):this.$d[N],B||0},ee.add=function(A,B,N){var U;return U=B?A*J[m(B)]:c(A)?A.$ms:f(A,this).$ms,f(this.$ms+U*(N?-1:1),this)},ee.subtract=function(A,B){return this.add(A,B,!0)},ee.locale=function(A){var B=this.clone();return B.$l=A,B},ee.clone=function(){return f(this.$ms,this)},ee.humanize=function(B){return A().add(this.$ms,"ms").locale(this.$l).fromNow(!B)},ee.valueOf=function(){return this.asMilliseconds()},ee.milliseconds=function(){return this.get("milliseconds")},ee.asMilliseconds=function(){return this.as("milliseconds")},ee.seconds=function(){return this.get("seconds")},ee.asSeconds=function(){return this.as("seconds")},ee.minutes=function(){return this.get("minutes")},ee.asMinutes=function(){return this.as("minutes")},ee.hours=function(){return this.get("hours")},ee.asHours=function(){return this.as("hours")},ee.days=function(){return this.get("days")},ee.asDays=function(){return this.as("days")},ee.weeks=function(){return this.get("weeks")},ee.asWeeks=function(){return this.as("weeks")},ee.months=function(){return this.get("months")},ee.asMonths=function(){return this.as("months")},ee.years=function(){return this.get("years")},ee.asYears=function(){return this.as("years")},l}(),p=function(A,B,N){return A.add(B.years()*N,"y").add(B.months()*N,"M").add(B.days()*N,"d").add(B.hours()*N,"h").add(B.minutes()*N,"m").add(B.seconds()*N,"s").add(B.milliseconds()*N,"ms")};return function(N,U,H){A=H,B=H().$utils(),H.duration=function(A,B){return f(A,{$l:H.locale()},B)},H.isDuration=c;var W=U.prototype.add,j=U.prototype.subtract;U.prototype.add=function(A,B){return c(A)?p(this,A,1):W.bind(this)(A,B)},U.prototype.subtract=function(A,B){return c(A)?p(this,A,-1):j.bind(this)(A,B)}}})},39420:function(A){!function(B,N){A.exports=N()}(0,function(){return function(A,B,N){B.prototype.isBetween=function(A,B,U,H){var W=N(A),j=N(B),V="("===(H=H||"()")[0],K=")"===H[1];return(V?this.isAfter(W,U):!this.isBefore(W,U))&&(K?this.isBefore(j,U):!this.isAfter(j,U))||(V?this.isBefore(W,U):!this.isAfter(W,U))&&(K?this.isAfter(j,U):!this.isBefore(j,U))}}})},63878:function(A){!function(B,N){A.exports=N()}(0,function(){return function(A,B,N){B.prototype.isToday=function(){var A="YYYY-MM-DD",B=N();return this.format(A)===B.format(A)}}})},88960:function(A,B,N){var U=N(73656);function useColors(){let A;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!(navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&(document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||navigator.userAgent&&(A=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(A[1],10)>=31||navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))}function formatArgs(B){if(B[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+B[0]+(this.useColors?"%c ":" ")+"+"+A.exports.humanize(this.diff),!this.useColors)return;let N="color: "+this.color;B.splice(1,0,N,"color: inherit");let U=0,H=0;B[0].replace(/%[a-zA-Z%]/g,A=>{if("%%"!==A)U++,"%c"===A&&(H=U)}),B.splice(H,0,N)}function save(A){try{A?B.storage.setItem("debug",A):B.storage.removeItem("debug")}catch(A){}}function load(){let A;try{A=B.storage.getItem("debug")}catch(A){}return!A&&void 0!==U&&"env"in U&&(A=U.env.DEBUG),A}function localstorage(){try{return localStorage}catch(A){}}B.formatArgs=formatArgs,B.save=save,B.load=load,B.useColors=useColors,B.storage=localstorage(),B.destroy=(()=>{let A=!1;return()=>{!A&&(A=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),B.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],B.log=console.debug||console.log||(()=>{}),A.exports=N(30267)(B);let{formatters:H}=A.exports;H.j=function(A){try{return JSON.stringify(A)}catch(A){return"[UnexpectedJSONParseError]: "+A.message}}},30267:function(A,B,N){function setup(A){function selectColor(A){let B=0;for(let N=0;N<A.length;N++)B=(B<<5)-B+A.charCodeAt(N)|0;return createDebug.colors[Math.abs(B)%createDebug.colors.length]}function createDebug(A){let B,N,U;let H=null;function debug(...A){if(!debug.enabled)return;let N=debug,U=Number(new Date),H=U-(B||U);N.diff=H,N.prev=B,N.curr=U,B=U,A[0]=createDebug.coerce(A[0]),"string"!=typeof A[0]&&A.unshift("%O");let W=0;A[0]=A[0].replace(/%([a-zA-Z%])/g,(B,U)=>{if("%%"===B)return"%";W++;let H=createDebug.formatters[U];if("function"==typeof H){let U=A[W];B=H.call(N,U),A.splice(W,1),W--}return B}),createDebug.formatArgs.call(N,A),(N.log||createDebug.log).apply(N,A)}return debug.namespace=A,debug.useColors=createDebug.useColors(),debug.color=createDebug.selectColor(A),debug.extend=extend,debug.destroy=createDebug.destroy,Object.defineProperty(debug,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==H?H:(N!==createDebug.namespaces&&(N=createDebug.namespaces,U=createDebug.enabled(A)),U),set:A=>{H=A}}),"function"==typeof createDebug.init&&createDebug.init(debug),debug}function extend(A,B){let N=createDebug(this.namespace+(void 0===B?":":B)+A);return N.log=this.log,N}function enable(A){for(let B of(createDebug.save(A),createDebug.namespaces=A,createDebug.names=[],createDebug.skips=[],("string"==typeof A?A:"").trim().replace(" ",",").split(",").filter(Boolean)))"-"===B[0]?createDebug.skips.push(B.slice(1)):createDebug.names.push(B)}function matchesTemplate(A,B){let N=0,U=0,H=-1,W=0;for(;N<A.length;)if(U<B.length&&(B[U]===A[N]||"*"===B[U]))"*"===B[U]?(H=U,W=N):N++,U++;else{if(-1===H)return!1;U=H+1,N=++W}for(;U<B.length&&"*"===B[U];)U++;return U===B.length}function disable(){let A=[...createDebug.names,...createDebug.skips.map(A=>"-"+A)].join(",");return createDebug.enable(""),A}function enabled(A){for(let B of createDebug.skips)if(matchesTemplate(A,B))return!1;for(let B of createDebug.names)if(matchesTemplate(A,B))return!0;return!1}function coerce(A){return A instanceof Error?A.stack||A.message:A}function destroy(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return createDebug.debug=createDebug,createDebug.default=createDebug,createDebug.coerce=coerce,createDebug.disable=disable,createDebug.enable=enable,createDebug.enabled=enabled,createDebug.humanize=N(88514),createDebug.destroy=destroy,Object.keys(A).forEach(B=>{createDebug[B]=A[B]}),createDebug.names=[],createDebug.skips=[],createDebug.formatters={},createDebug.selectColor=selectColor,createDebug.enable(createDebug.load()),createDebug}A.exports=setup},759:function(A){"use strict";var B={single_source_shortest_paths:function(A,N,U){var H,W,j,V,K,X,J,ee={},et={};et[N]=0;var er=B.PriorityQueue.make();for(er.push(N,0);!er.empty();)for(j in W=(H=er.pop()).value,V=H.cost,K=A[W]||{})K.hasOwnProperty(j)&&(X=V+K[j],J=et[j],(void 0===et[j]||J>X)&&(et[j]=X,er.push(j,X),ee[j]=W));if(void 0!==U&&void 0===et[U])throw Error(["Could not find a path from ",N," to ",U,"."].join(""));return ee},extract_shortest_path_from_predecessor_list:function(A,B){for(var N,U=[],H=B;H;)U.push(H),N=A[H],H=A[H];return U.reverse(),U},find_path:function(A,N,U){var H=B.single_source_shortest_paths(A,N,U);return B.extract_shortest_path_from_predecessor_list(H,U)},PriorityQueue:{make:function(A){var N,U=B.PriorityQueue,H={};for(N in A=A||{},U)U.hasOwnProperty(N)&&(H[N]=U[N]);return H.queue=[],H.sorter=A.sorter||U.default_sorter,H},default_sorter:function(A,B){return A.cost-B.cost},push:function(A,B){var N={value:A,cost:B};this.queue.push(N),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};A.exports=B},47324:function(A){"use strict";var B,N="object"==typeof Reflect?Reflect:null,U=N&&"function"==typeof N.apply?N.apply:function ReflectApply(A,B,N){return Function.prototype.apply.call(A,B,N)};function ProcessEmitWarning(A){console&&console.warn&&console.warn(A)}B=N&&"function"==typeof N.ownKeys?N.ownKeys:Object.getOwnPropertySymbols?function ReflectOwnKeys(A){return Object.getOwnPropertyNames(A).concat(Object.getOwnPropertySymbols(A))}:function ReflectOwnKeys(A){return Object.getOwnPropertyNames(A)};var H=Number.isNaN||function NumberIsNaN(A){return A!=A};function EventEmitter(){EventEmitter.init.call(this)}A.exports=EventEmitter,A.exports.once=once,EventEmitter.EventEmitter=EventEmitter,EventEmitter.prototype._events=void 0,EventEmitter.prototype._eventsCount=0,EventEmitter.prototype._maxListeners=void 0;var W=10;function checkListener(A){if("function"!=typeof A)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof A)}function _getMaxListeners(A){return void 0===A._maxListeners?EventEmitter.defaultMaxListeners:A._maxListeners}function _addListener(A,B,N,U){if(checkListener(N),void 0===(W=A._events)?(W=A._events=Object.create(null),A._eventsCount=0):(void 0!==W.newListener&&(A.emit("newListener",B,N.listener?N.listener:N),W=A._events),j=W[B]),void 0===j)j=W[B]=N,++A._eventsCount;else if("function"==typeof j?j=W[B]=U?[N,j]:[j,N]:U?j.unshift(N):j.push(N),(H=_getMaxListeners(A))>0&&j.length>H&&!j.warned){j.warned=!0;var H,W,j,V=Error("Possible EventEmitter memory leak detected. "+j.length+" "+String(B)+" listeners added. Use emitter.setMaxListeners() to increase limit");V.name="MaxListenersExceededWarning",V.emitter=A,V.type=B,V.count=j.length,ProcessEmitWarning(V)}return A}function onceWrapper(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function _onceWrap(A,B,N){var U={fired:!1,wrapFn:void 0,target:A,type:B,listener:N},H=onceWrapper.bind(U);return H.listener=N,U.wrapFn=H,H}function _listeners(A,B,N){var U=A._events;if(void 0===U)return[];var H=U[B];return void 0===H?[]:"function"==typeof H?N?[H.listener||H]:[H]:N?unwrapListeners(H):arrayClone(H,H.length)}function listenerCount(A){var B=this._events;if(void 0!==B){var N=B[A];if("function"==typeof N)return 1;if(void 0!==N)return N.length}return 0}function arrayClone(A,B){for(var N=Array(B),U=0;U<B;++U)N[U]=A[U];return N}function spliceOne(A,B){for(;B+1<A.length;B++)A[B]=A[B+1];A.pop()}function unwrapListeners(A){for(var B=Array(A.length),N=0;N<B.length;++N)B[N]=A[N].listener||A[N];return B}function once(A,B){return new Promise(function(N,U){function errorListener(N){A.removeListener(B,resolver),U(N)}function resolver(){"function"==typeof A.removeListener&&A.removeListener("error",errorListener),N([].slice.call(arguments))}eventTargetAgnosticAddListener(A,B,resolver,{once:!0}),"error"!==B&&addErrorHandlerIfEventEmitter(A,errorListener,{once:!0})})}function addErrorHandlerIfEventEmitter(A,B,N){"function"==typeof A.on&&eventTargetAgnosticAddListener(A,"error",B,N)}function eventTargetAgnosticAddListener(A,B,N,U){if("function"==typeof A.on)U.once?A.once(B,N):A.on(B,N);else if("function"==typeof A.addEventListener)A.addEventListener(B,function wrapListener(H){U.once&&A.removeEventListener(B,wrapListener),N(H)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof A)}Object.defineProperty(EventEmitter,"defaultMaxListeners",{enumerable:!0,get:function(){return W},set:function(A){if("number"!=typeof A||A<0||H(A))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+A+".");W=A}}),EventEmitter.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},EventEmitter.prototype.setMaxListeners=function setMaxListeners(A){if("number"!=typeof A||A<0||H(A))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+A+".");return this._maxListeners=A,this},EventEmitter.prototype.getMaxListeners=function getMaxListeners(){return _getMaxListeners(this)},EventEmitter.prototype.emit=function emit(A){for(var B=[],N=1;N<arguments.length;N++)B.push(arguments[N]);var H="error"===A,W=this._events;if(void 0!==W)H=H&&void 0===W.error;else if(!H)return!1;if(H){if(B.length>0&&(j=B[0]),j instanceof Error)throw j;var j,V=Error("Unhandled error."+(j?" ("+j.message+")":""));throw V.context=j,V}var K=W[A];if(void 0===K)return!1;if("function"==typeof K)U(K,this,B);else{for(var X=K.length,J=arrayClone(K,X),N=0;N<X;++N)U(J[N],this,B)}return!0},EventEmitter.prototype.addListener=function addListener(A,B){return _addListener(this,A,B,!1)},EventEmitter.prototype.on=EventEmitter.prototype.addListener,EventEmitter.prototype.prependListener=function prependListener(A,B){return _addListener(this,A,B,!0)},EventEmitter.prototype.once=function once(A,B){return checkListener(B),this.on(A,_onceWrap(this,A,B)),this},EventEmitter.prototype.prependOnceListener=function prependOnceListener(A,B){return checkListener(B),this.prependListener(A,_onceWrap(this,A,B)),this},EventEmitter.prototype.removeListener=function removeListener(A,B){var N,U,H,W,j;if(checkListener(B),void 0===(U=this._events)||void 0===(N=U[A]))return this;if(N===B||N.listener===B)0==--this._eventsCount?this._events=Object.create(null):(delete U[A],U.removeListener&&this.emit("removeListener",A,N.listener||B));else if("function"!=typeof N){for(H=-1,W=N.length-1;W>=0;W--)if(N[W]===B||N[W].listener===B){j=N[W].listener,H=W;break}if(H<0)return this;0===H?N.shift():spliceOne(N,H),1===N.length&&(U[A]=N[0]),void 0!==U.removeListener&&this.emit("removeListener",A,j||B)}return this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.removeAllListeners=function removeAllListeners(A){var B,N,U;if(void 0===(N=this._events))return this;if(void 0===N.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==N[A]&&(0==--this._eventsCount?this._events=Object.create(null):delete N[A]),this;if(0==arguments.length){var H,W=Object.keys(N);for(U=0;U<W.length;++U)"removeListener"!==(H=W[U])&&this.removeAllListeners(H);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(B=N[A]))this.removeListener(A,B);else if(void 0!==B)for(U=B.length-1;U>=0;U--)this.removeListener(A,B[U]);return this},EventEmitter.prototype.listeners=function listeners(A){return _listeners(this,A,!0)},EventEmitter.prototype.rawListeners=function rawListeners(A){return _listeners(this,A,!1)},EventEmitter.listenerCount=function(A,B){return"function"==typeof A.listenerCount?A.listenerCount(B):listenerCount.call(A,B)},EventEmitter.prototype.listenerCount=listenerCount,EventEmitter.prototype.eventNames=function eventNames(){return this._eventsCount>0?B(this._events):[]}},10143:function(A){A.exports="object"==typeof self?self.FormData:window.FormData},94287:function(__unused_webpack_module,exports,__webpack_require__){var $jscomp=$jscomp||{};$jscomp.scope={},$jscomp.findInternal=function(A,B,N){A instanceof String&&(A=String(A));for(var U=A.length,H=0;H<U;H++){var W=A[H];if(B.call(N,W,H,A))return{i:H,v:W}}return{i:-1,v:void 0}},$jscomp.ASSUME_ES5=!1,$jscomp.ASSUME_NO_NATIVE_MAP=!1,$jscomp.ASSUME_NO_NATIVE_SET=!1,$jscomp.SIMPLE_FROUND_POLYFILL=!1,$jscomp.defineProperty=$jscomp.ASSUME_ES5||"function"==typeof Object.defineProperties?Object.defineProperty:function(A,B,N){A!=Array.prototype&&A!=Object.prototype&&(A[B]=N.value)},$jscomp.getGlobal=function(A){return"undefined"!=typeof window&&window===A?A:void 0!==__webpack_require__.g&&null!=__webpack_require__.g?__webpack_require__.g:A},$jscomp.global=$jscomp.getGlobal(this),$jscomp.polyfill=function(A,B,N,U){if(B){for(U=0,N=$jscomp.global,A=A.split(".");U<A.length-1;U++){var H=A[U];H in N||(N[H]={}),N=N[H]}(B=B(U=N[A=A[A.length-1]]))!=U&&null!=B&&$jscomp.defineProperty(N,A,{configurable:!0,writable:!0,value:B})}},$jscomp.polyfill("Array.prototype.findIndex",function(A){return A||function(A,B){return $jscomp.findInternal(this,A,B).i}},"es6","es3"),$jscomp.checkStringArgs=function(A,B,N){if(null==A)throw TypeError("The 'this' value for String.prototype."+N+" must not be null or undefined");if(B instanceof RegExp)throw TypeError("First argument to String.prototype."+N+" must not be a regular expression");return A+""},$jscomp.polyfill("String.prototype.endsWith",function(A){return A||function(A,B){var N=$jscomp.checkStringArgs(this,A,"endsWith");A+="",void 0===B&&(B=N.length),B=Math.max(0,Math.min(0|B,N.length));for(var U=A.length;0<U&&0<B;)if(N[--B]!=A[--U])return!1;return 0>=U}},"es6","es3"),$jscomp.polyfill("Array.prototype.find",function(A){return A||function(A,B){return $jscomp.findInternal(this,A,B).v}},"es6","es3"),$jscomp.polyfill("String.prototype.startsWith",function(A){return A||function(A,B){var N=$jscomp.checkStringArgs(this,A,"startsWith");A+="";var U=N.length,H=A.length;B=Math.max(0,Math.min(0|B,N.length));for(var W=0;W<H&&B<U;)if(N[B++]!=A[W++])return!1;return W>=H}},"es6","es3"),$jscomp.polyfill("String.prototype.repeat",function(A){return A||function(A){var B=$jscomp.checkStringArgs(this,null,"repeat");if(0>A||0x4fffffff<A)throw RangeError("Invalid count value");A|=0;for(var N="";A;)1&A&&(N+=B),(A>>>=1)&&(B+=B);return N}},"es6","es3");var COMPILED=!0,goog=goog||{};goog.global=this||self,goog.exportPath_=function(A,B,N){A=A.split("."),N=N||goog.global,A[0]in N||void 0===N.execScript||N.execScript("var "+A[0]);for(var U;A.length&&(U=A.shift());)A.length||void 0===B?N=N[U]&&N[U]!==Object.prototype[U]?N[U]:N[U]={}:N[U]=B},goog.define=function(A,B){if(!COMPILED){var N=goog.global.CLOSURE_UNCOMPILED_DEFINES,U=goog.global.CLOSURE_DEFINES;N&&void 0===N.nodeType&&Object.prototype.hasOwnProperty.call(N,A)?B=N[A]:U&&void 0===U.nodeType&&Object.prototype.hasOwnProperty.call(U,A)&&(B=U[A])}return B},goog.FEATURESET_YEAR=2012,goog.DEBUG=!0,goog.LOCALE="en",goog.TRUSTED_SITE=!0,goog.STRICT_MODE_COMPATIBLE=!1,goog.DISALLOW_TEST_ONLY_CODE=COMPILED&&!goog.DEBUG,goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING=!1,goog.provide=function(A){if(goog.isInModuleLoader_())throw Error("goog.provide cannot be used within a module.");if(!COMPILED&&goog.isProvided_(A))throw Error('Namespace "'+A+'" already declared.');goog.constructNamespace_(A)},goog.constructNamespace_=function(A,B){if(!COMPILED){delete goog.implicitNamespaces_[A];for(var N=A;(N=N.substring(0,N.lastIndexOf(".")))&&!goog.getObjectByName(N);)goog.implicitNamespaces_[N]=!0}goog.exportPath_(A,B)},goog.getScriptNonce=function(A){return A&&A!=goog.global?goog.getScriptNonce_(A.document):(null===goog.cspNonce_&&(goog.cspNonce_=goog.getScriptNonce_(goog.global.document)),goog.cspNonce_)},goog.NONCE_PATTERN_=/^[\w+/_-]+[=]{0,2}$/,goog.cspNonce_=null,goog.getScriptNonce_=function(A){return(A=A.querySelector&&A.querySelector("script[nonce]"))&&(A=A.nonce||A.getAttribute("nonce"))&&goog.NONCE_PATTERN_.test(A)?A:""},goog.VALID_MODULE_RE_=/^[a-zA-Z_$][a-zA-Z0-9._$]*$/,goog.module=function(A){if("string"!=typeof A||!A||-1==A.search(goog.VALID_MODULE_RE_))throw Error("Invalid module identifier");if(!goog.isInGoogModuleLoader_())throw Error("Module "+A+" has been loaded incorrectly. Note, modules cannot be loaded as normal scripts. They require some kind of pre-processing step. You're likely trying to load a module via a script tag or as a part of a concatenated bundle without rewriting the module. For more info see: https://github.com/google/closure-library/wiki/goog.module:-an-ES6-module-like-alternative-to-goog.provide.");if(goog.moduleLoaderState_.moduleName)throw Error("goog.module may only be called once per module.");if(goog.moduleLoaderState_.moduleName=A,!COMPILED){if(goog.isProvided_(A))throw Error('Namespace "'+A+'" already declared.');delete goog.implicitNamespaces_[A]}},goog.module.get=function(A){return goog.module.getInternal_(A)},goog.module.getInternal_=function(A){if(!COMPILED){if(A in goog.loadedModules_)return goog.loadedModules_[A].exports;if(!goog.implicitNamespaces_[A])return null!=(A=goog.getObjectByName(A))?A:null}return null},goog.ModuleType={ES6:"es6",GOOG:"goog"},goog.moduleLoaderState_=null,goog.isInModuleLoader_=function(){return goog.isInGoogModuleLoader_()||goog.isInEs6ModuleLoader_()},goog.isInGoogModuleLoader_=function(){return!!goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.GOOG},goog.isInEs6ModuleLoader_=function(){if(goog.moduleLoaderState_&&goog.moduleLoaderState_.type==goog.ModuleType.ES6)return!0;var A=goog.global.$jscomp;return!!A&&"function"==typeof A.getCurrentModulePath&&!!A.getCurrentModulePath()},goog.module.declareLegacyNamespace=function(){if(!COMPILED&&!goog.isInGoogModuleLoader_())throw Error("goog.module.declareLegacyNamespace must be called from within a goog.module");if(!COMPILED&&!goog.moduleLoaderState_.moduleName)throw Error("goog.module must be called prior to goog.module.declareLegacyNamespace.");goog.moduleLoaderState_.declareLegacyNamespace=!0},goog.declareModuleId=function(A){if(!COMPILED){if(!goog.isInEs6ModuleLoader_())throw Error("goog.declareModuleId may only be called from within an ES6 module");if(goog.moduleLoaderState_&&goog.moduleLoaderState_.moduleName)throw Error("goog.declareModuleId may only be called once per module.");if(A in goog.loadedModules_)throw Error('Module with namespace "'+A+'" already exists.')}if(goog.moduleLoaderState_)goog.moduleLoaderState_.moduleName=A;else{var B=goog.global.$jscomp;if(!B||"function"!=typeof B.getCurrentModulePath)throw Error('Module with namespace "'+A+'" has been loaded incorrectly.');B=B.require(B.getCurrentModulePath()),goog.loadedModules_[A]={exports:B,type:goog.ModuleType.ES6,moduleId:A}}},goog.setTestOnly=function(A){if(goog.DISALLOW_TEST_ONLY_CODE)throw Error("Importing test-only code into non-debug environment"+((A=A||"")?": "+A:"."))},goog.forwardDeclare=function(A){},COMPILED||(goog.isProvided_=function(A){return A in goog.loadedModules_||!goog.implicitNamespaces_[A]&&null!=goog.getObjectByName(A)},goog.implicitNamespaces_={"goog.module":!0}),goog.getObjectByName=function(A,B){A=A.split("."),B=B||goog.global;for(var N=0;N<A.length;N++)if(null==(B=B[A[N]]))return null;return B},goog.globalize=function(A,B){for(var N in B=B||goog.global,A)B[N]=A[N]},goog.addDependency=function(A,B,N,U){!COMPILED&&goog.DEPENDENCIES_ENABLED&&goog.debugLoader_.addDependency(A,B,N,U)},goog.ENABLE_DEBUG_LOADER=!0,goog.logToConsole_=function(A){goog.global.console&&goog.global.console.error(A)},goog.require=function(A){if(!COMPILED){if(goog.ENABLE_DEBUG_LOADER&&goog.debugLoader_.requested(A),goog.isProvided_(A)){if(goog.isInModuleLoader_())return goog.module.getInternal_(A)}else if(goog.ENABLE_DEBUG_LOADER){var B=goog.moduleLoaderState_;goog.moduleLoaderState_=null;try{goog.debugLoader_.load_(A)}finally{goog.moduleLoaderState_=B}}return null}},goog.requireType=function(A){return{}},goog.basePath="",goog.nullFunction=function(){},goog.abstractMethod=function(){throw Error("unimplemented abstract method")},goog.addSingletonGetter=function(A){A.instance_=void 0,A.getInstance=function(){return A.instance_?A.instance_:(goog.DEBUG&&(goog.instantiatedSingletons_[goog.instantiatedSingletons_.length]=A),A.instance_=new A)}},goog.instantiatedSingletons_=[],goog.LOAD_MODULE_USING_EVAL=!0,goog.SEAL_MODULE_EXPORTS=goog.DEBUG,goog.loadedModules_={},goog.DEPENDENCIES_ENABLED=!COMPILED&&goog.ENABLE_DEBUG_LOADER,goog.TRANSPILE="detect",goog.ASSUME_ES_MODULES_TRANSPILED=!1,goog.TRANSPILE_TO_LANGUAGE="",goog.TRANSPILER="transpile.js",goog.hasBadLetScoping=null,goog.useSafari10Workaround=function(){if(null==goog.hasBadLetScoping){try{var a=!eval('"use strict";let x = 1; function f() { return typeof x; };f() == "number";')}catch(b){a=!1}goog.hasBadLetScoping=a}return goog.hasBadLetScoping},goog.workaroundSafari10EvalBug=function(A){return"(function(){"+A+"\n;})();\n"},goog.loadModule=function(A){var B=goog.moduleLoaderState_;try{if(goog.moduleLoaderState_={moduleName:"",declareLegacyNamespace:!1,type:goog.ModuleType.GOOG},goog.isFunction(A))var N=A.call(void 0,{});else if("string"==typeof A)goog.useSafari10Workaround()&&(A=goog.workaroundSafari10EvalBug(A)),N=goog.loadModuleFromSource_.call(void 0,A);else throw Error("Invalid module definition");var U=goog.moduleLoaderState_.moduleName;if("string"==typeof U&&U)goog.moduleLoaderState_.declareLegacyNamespace?goog.constructNamespace_(U,N):goog.SEAL_MODULE_EXPORTS&&Object.seal&&"object"==typeof N&&null!=N&&Object.seal(N),goog.loadedModules_[U]={exports:N,type:goog.ModuleType.GOOG,moduleId:goog.moduleLoaderState_.moduleName};else throw Error('Invalid module name "'+U+'"')}finally{goog.moduleLoaderState_=B}},goog.loadModuleFromSource_=function(a){return eval(a),{}},goog.normalizePath_=function(A){A=A.split("/");for(var B=0;B<A.length;)"."==A[B]?A.splice(B,1):B&&".."==A[B]&&A[B-1]&&".."!=A[B-1]?A.splice(--B,2):B++;return A.join("/")},goog.loadFileSync_=function(A){if(goog.global.CLOSURE_LOAD_FILE_SYNC)return goog.global.CLOSURE_LOAD_FILE_SYNC(A);try{var B=new goog.global.XMLHttpRequest;return B.open("get",A,!1),B.send(),0==B.status||200==B.status?B.responseText:null}catch(A){return null}},goog.transpile_=function(A,B,N){var U=goog.global.$jscomp;U||(goog.global.$jscomp=U={});var H=U.transpile;if(!H){var W=goog.basePath+goog.TRANSPILER,j=goog.loadFileSync_(W);if(j){if((function(){(0,eval)(j+"\n//# sourceURL="+W)}).call(goog.global),goog.global.$gwtExport&&goog.global.$gwtExport.$jscomp&&!goog.global.$gwtExport.$jscomp.transpile)throw Error('The transpiler did not properly export the "transpile" method. $gwtExport: '+JSON.stringify(goog.global.$gwtExport));goog.global.$jscomp.transpile=goog.global.$gwtExport.$jscomp.transpile,H=(U=goog.global.$jscomp).transpile}}return H||(H=U.transpile=function(A,B){return goog.logToConsole_(B+" requires transpilation but no transpiler was found."),A}),H(A,B,N)},goog.typeOf=function(A){var B=typeof A;if("object"==B){if(!A)return"null";else{if(A instanceof Array)return"array";if(A instanceof Object)return B;var N=Object.prototype.toString.call(A);if("[object Window]"==N)return"object";if("[object Array]"==N||"number"==typeof A.length&&void 0!==A.splice&&void 0!==A.propertyIsEnumerable&&!A.propertyIsEnumerable("splice"))return"array";if("[object Function]"==N||void 0!==A.call&&void 0!==A.propertyIsEnumerable&&!A.propertyIsEnumerable("call"))return"function"}}else if("function"==B&&void 0===A.call)return"object";return B},goog.isArray=function(A){return"array"==goog.typeOf(A)},goog.isArrayLike=function(A){var B=goog.typeOf(A);return"array"==B||"object"==B&&"number"==typeof A.length},goog.isDateLike=function(A){return goog.isObject(A)&&"function"==typeof A.getFullYear},goog.isFunction=function(A){return"function"==goog.typeOf(A)},goog.isObject=function(A){var B=typeof A;return"object"==B&&null!=A||"function"==B},goog.getUid=function(A){return Object.prototype.hasOwnProperty.call(A,goog.UID_PROPERTY_)&&A[goog.UID_PROPERTY_]||(A[goog.UID_PROPERTY_]=++goog.uidCounter_)},goog.hasUid=function(A){return!!A[goog.UID_PROPERTY_]},goog.removeUid=function(A){null!==A&&"removeAttribute"in A&&A.removeAttribute(goog.UID_PROPERTY_);try{delete A[goog.UID_PROPERTY_]}catch(A){}},goog.UID_PROPERTY_="closure_uid_"+(1e9*Math.random()>>>0),goog.uidCounter_=0,goog.getHashCode=goog.getUid,goog.removeHashCode=goog.removeUid,goog.cloneObject=function(A){var B=goog.typeOf(A);if("object"==B||"array"==B){if("function"==typeof A.clone)return A.clone();for(var N in B="array"==B?[]:{},A)B[N]=goog.cloneObject(A[N]);return B}return A},goog.bindNative_=function(A,B,N){return A.call.apply(A.bind,arguments)},goog.bindJs_=function(A,B,N){if(!A)throw Error();if(2<arguments.length){var U=Array.prototype.slice.call(arguments,2);return function(){var N=Array.prototype.slice.call(arguments);return Array.prototype.unshift.apply(N,U),A.apply(B,N)}}return function(){return A.apply(B,arguments)}},goog.bind=function(A,B,N){return Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?goog.bind=goog.bindNative_:goog.bind=goog.bindJs_,goog.bind.apply(null,arguments)},goog.partial=function(A,B){var N=Array.prototype.slice.call(arguments,1);return function(){var B=N.slice();return B.push.apply(B,arguments),A.apply(this,B)}},goog.mixin=function(A,B){for(var N in B)A[N]=B[N]},goog.now=goog.TRUSTED_SITE&&Date.now||function(){return+new Date},goog.globalEval=function(A){if(goog.global.execScript)goog.global.execScript(A,"JavaScript");else if(goog.global.eval){if(null==goog.evalWorksForGlobals_){try{goog.global.eval("var _evalTest_ = 1;")}catch(A){}if(void 0!==goog.global._evalTest_){try{delete goog.global._evalTest_}catch(A){}goog.evalWorksForGlobals_=!0}else goog.evalWorksForGlobals_=!1}if(goog.evalWorksForGlobals_)goog.global.eval(A);else{var B=goog.global.document,N=B.createElement("script");N.type="text/javascript",N.defer=!1,N.appendChild(B.createTextNode(A)),B.head.appendChild(N),B.head.removeChild(N)}}else throw Error("goog.globalEval not available")},goog.evalWorksForGlobals_=null,goog.getCssName=function(A,B){if("."==String(A).charAt(0))throw Error('className passed in goog.getCssName must not start with ".". You passed: '+A);var c=function(A){return goog.cssNameMapping_[A]||A},d=function(A){A=A.split("-");for(var B=[],N=0;N<A.length;N++)B.push(c(A[N]));return B.join("-")};return d=goog.cssNameMapping_?"BY_WHOLE"==goog.cssNameMappingStyle_?c:d:function(A){return A},A=B?A+"-"+d(B):d(A),goog.global.CLOSURE_CSS_NAME_MAP_FN?goog.global.CLOSURE_CSS_NAME_MAP_FN(A):A},goog.setCssNameMapping=function(A,B){goog.cssNameMapping_=A,goog.cssNameMappingStyle_=B},!COMPILED&&goog.global.CLOSURE_CSS_NAME_MAPPING&&(goog.cssNameMapping_=goog.global.CLOSURE_CSS_NAME_MAPPING),goog.getMsg=function(A,B,N){return N&&N.html&&(A=A.replace(/</g,"&lt;")),B&&(A=A.replace(/\{\$([^}]+)}/g,function(A,N){return null!=B&&N in B?B[N]:A})),A},goog.getMsgWithFallback=function(A,B){return A},goog.exportSymbol=function(A,B,N){goog.exportPath_(A,B,N)},goog.exportProperty=function(A,B,N){A[B]=N},goog.inherits=function(A,B){function c(){}c.prototype=B.prototype,A.superClass_=B.prototype,A.prototype=new c,A.prototype.constructor=A,A.base=function(A,N,U){for(var H=Array(arguments.length-2),W=2;W<arguments.length;W++)H[W-2]=arguments[W];return B.prototype[N].apply(A,H)}},goog.scope=function(A){if(goog.isInModuleLoader_())throw Error("goog.scope is not supported within a module.");A.call(goog.global)},COMPILED||(goog.global.COMPILED=COMPILED),goog.defineClass=function(A,B){var N=B.constructor,U=B.statics;return N&&N!=Object.prototype.constructor||(N=function(){throw Error("cannot instantiate an interface (no constructor defined).")}),N=goog.defineClass.createSealingConstructor_(N,A),A&&goog.inherits(N,A),delete B.constructor,delete B.statics,goog.defineClass.applyProperties_(N.prototype,B),null!=U&&(U instanceof Function?U(N):goog.defineClass.applyProperties_(N,U)),N},goog.defineClass.SEAL_CLASS_INSTANCES=goog.DEBUG,goog.defineClass.createSealingConstructor_=function(A,B){if(!goog.defineClass.SEAL_CLASS_INSTANCES)return A;var N=!goog.defineClass.isUnsealable_(B),d=function(){var B=A.apply(this,arguments)||this;return B[goog.UID_PROPERTY_]=B[goog.UID_PROPERTY_],this.constructor===d&&N&&Object.seal instanceof Function&&Object.seal(B),B};return d},goog.defineClass.isUnsealable_=function(A){return A&&A.prototype&&A.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_]},goog.defineClass.OBJECT_PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.defineClass.applyProperties_=function(A,B){for(var N in B)Object.prototype.hasOwnProperty.call(B,N)&&(A[N]=B[N]);for(var U=0;U<goog.defineClass.OBJECT_PROTOTYPE_FIELDS_.length;U++)N=goog.defineClass.OBJECT_PROTOTYPE_FIELDS_[U],Object.prototype.hasOwnProperty.call(B,N)&&(A[N]=B[N])},goog.tagUnsealableClass=function(A){!COMPILED&&goog.defineClass.SEAL_CLASS_INSTANCES&&(A.prototype[goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_]=!0)},goog.UNSEALABLE_CONSTRUCTOR_PROPERTY_="goog_defineClass_legacy_unsealable",!COMPILED&&goog.DEPENDENCIES_ENABLED&&(goog.inHtmlDocument_=function(){var A=goog.global.document;return null!=A&&"write"in A},goog.isDocumentLoading_=function(){var A=goog.global.document;return A.attachEvent?"complete"!=A.readyState:"loading"==A.readyState},goog.findBasePath_=function(){if(void 0!=goog.global.CLOSURE_BASE_PATH&&"string"==typeof goog.global.CLOSURE_BASE_PATH)goog.basePath=goog.global.CLOSURE_BASE_PATH;else if(goog.inHtmlDocument_()){var A=goog.global.document,B=A.currentScript;for(B=(A=B?[B]:A.getElementsByTagName("SCRIPT")).length-1;0<=B;--B){var N=A[B].src,U=N.lastIndexOf("?");if(U=-1==U?N.length:U,"base.js"==N.substr(U-7,7)){goog.basePath=N.substr(0,U-7);break}}}},goog.findBasePath_(),goog.Transpiler=function(){this.requiresTranspilation_=null,this.transpilationTarget_=goog.TRANSPILE_TO_LANGUAGE},goog.Transpiler.prototype.createRequiresTranspilation_=function(){function a(A,B){e?d[A]=!0:B()?(c=A,d[A]=!1):e=d[A]=!0}function b(a){try{return!!eval(a)}catch(h){return!1}}var c="es3",d={es3:!1},e=!1,f=goog.global.navigator&&goog.global.navigator.userAgent?goog.global.navigator.userAgent:"";return a("es5",function(){return b("[1,].length==1")}),a("es6",function(){return!f.match(/Edge\/(\d+)(\.\d)*/i)&&b('(()=>{"use strict";class X{constructor(){if(new.target!=String)throw 1;this.x=42}}let q=Reflect.construct(X,[],String);if(q.x!=42||!(q instanceof String))throw 1;for(const a of[2,3]){if(a==2)continue;function f(z={a}){let a=0;return z.a}{function f(){return 0;}}return f()==3}})()')}),a("es7",function(){return b("2 ** 2 == 4")}),a("es8",function(){return b("async () => 1, true")}),a("es9",function(){return b("({...rest} = {}), true")}),a("es_next",function(){return!1}),{target:c,map:d}},goog.Transpiler.prototype.needsTranspile=function(A,B){if("always"==goog.TRANSPILE)return!0;if("never"==goog.TRANSPILE)return!1;if(!this.requiresTranspilation_){var N=this.createRequiresTranspilation_();this.requiresTranspilation_=N.map,this.transpilationTarget_=this.transpilationTarget_||N.target}if(A in this.requiresTranspilation_)return!!this.requiresTranspilation_[A]||!(!goog.inHtmlDocument_()||"es6"!=B||"noModule"in goog.global.document.createElement("script"));throw Error("Unknown language mode: "+A)},goog.Transpiler.prototype.transpile=function(A,B){return goog.transpile_(A,B,this.transpilationTarget_)},goog.transpiler_=new goog.Transpiler,goog.protectScriptTag_=function(A){return A.replace(/<\/(SCRIPT)/ig,"\\x3c/$1")},goog.DebugLoader_=function(){this.dependencies_={},this.idToPath_={},this.written_={},this.loadingDeps_=[],this.depsToLoad_=[],this.paused_=!1,this.factory_=new goog.DependencyFactory(goog.transpiler_),this.deferredCallbacks_={},this.deferredQueue_=[]},goog.DebugLoader_.prototype.bootstrap=function(A,B){function c(){N&&(goog.global.setTimeout(N,0),N=null)}var N=B;if(A.length){B=[];for(var U=0;U<A.length;U++){var H=this.getPathFromDeps_(A[U]);if(!H)throw Error("Unregonized namespace: "+A[U]);B.push(this.dependencies_[H])}H=goog.require;var W=0;for(U=0;U<A.length;U++)H(A[U]),B[U].onLoad(function(){++W==A.length&&c()})}else c()},goog.DebugLoader_.prototype.loadClosureDeps=function(){this.depsToLoad_.push(this.factory_.createDependency(goog.normalizePath_(goog.basePath+"deps.js"),"deps.js",[],[],{},!1)),this.loadDeps_()},goog.DebugLoader_.prototype.requested=function(A,B){(A=this.getPathFromDeps_(A))&&(B||this.areDepsLoaded_(this.dependencies_[A].requires))&&(B=this.deferredCallbacks_[A])&&(delete this.deferredCallbacks_[A],B())},goog.DebugLoader_.prototype.setDependencyFactory=function(A){this.factory_=A},goog.DebugLoader_.prototype.load_=function(A){if(this.getPathFromDeps_(A)){var B=this,N=[],d=function(A){var U=B.getPathFromDeps_(A);if(!U)throw Error("Bad dependency path or symbol: "+A);if(!B.written_[U]){for(B.written_[U]=!0,A=B.dependencies_[U],U=0;U<A.requires.length;U++)goog.isProvided_(A.requires[U])||d(A.requires[U]);N.push(A)}};d(A),A=!!this.depsToLoad_.length,this.depsToLoad_=this.depsToLoad_.concat(N),this.paused_||A||this.loadDeps_()}else throw A="goog.require could not find: "+A,goog.logToConsole_(A),Error(A)},goog.DebugLoader_.prototype.loadDeps_=function(){for(var A=this,B=this.paused_;this.depsToLoad_.length&&!B;)(function(){var N=!1,U=A.depsToLoad_.shift(),H=!1;A.loading_(U);var W={pause:function(){if(N)throw Error("Cannot call pause after the call to load.");B=!0},resume:function(){N?A.resume_():B=!1},loaded:function(){if(H)throw Error("Double call to loaded.");H=!0,A.loaded_(U)},pending:function(){for(var B=[],N=0;N<A.loadingDeps_.length;N++)B.push(A.loadingDeps_[N]);return B},setModuleState:function(A){goog.moduleLoaderState_={type:A,moduleName:"",declareLegacyNamespace:!1}},registerEs6ModuleExports:function(A,B,N){N&&(goog.loadedModules_[N]={exports:B,type:goog.ModuleType.ES6,moduleId:N||""})},registerGoogModuleExports:function(A,B){goog.loadedModules_[A]={exports:B,type:goog.ModuleType.GOOG,moduleId:A}},clearModuleState:function(){goog.moduleLoaderState_=null},defer:function(B){if(N)throw Error("Cannot register with defer after the call to load.");A.defer_(U,B)},areDepsLoaded:function(){return A.areDepsLoaded_(U.requires)}};try{U.load(W)}finally{N=!0}})();B&&this.pause_()},goog.DebugLoader_.prototype.pause_=function(){this.paused_=!0},goog.DebugLoader_.prototype.resume_=function(){this.paused_&&(this.paused_=!1,this.loadDeps_())},goog.DebugLoader_.prototype.loading_=function(A){this.loadingDeps_.push(A)},goog.DebugLoader_.prototype.loaded_=function(A){for(var B=0;B<this.loadingDeps_.length;B++)if(this.loadingDeps_[B]==A){this.loadingDeps_.splice(B,1);break}for(B=0;B<this.deferredQueue_.length;B++)if(this.deferredQueue_[B]==A.path){this.deferredQueue_.splice(B,1);break}if(this.loadingDeps_.length==this.deferredQueue_.length&&!this.depsToLoad_.length)for(;this.deferredQueue_.length;)this.requested(this.deferredQueue_.shift(),!0);A.loaded()},goog.DebugLoader_.prototype.areDepsLoaded_=function(A){for(var B=0;B<A.length;B++){var N=this.getPathFromDeps_(A[B]);if(!N||!(N in this.deferredCallbacks_||goog.isProvided_(A[B])))return!1}return!0},goog.DebugLoader_.prototype.getPathFromDeps_=function(A){return A in this.idToPath_?this.idToPath_[A]:A in this.dependencies_?A:null},goog.DebugLoader_.prototype.defer_=function(A,B){this.deferredCallbacks_[A.path]=B,this.deferredQueue_.push(A.path)},goog.LoadController=function(){},goog.LoadController.prototype.pause=function(){},goog.LoadController.prototype.resume=function(){},goog.LoadController.prototype.loaded=function(){},goog.LoadController.prototype.pending=function(){},goog.LoadController.prototype.registerEs6ModuleExports=function(A,B,N){},goog.LoadController.prototype.setModuleState=function(A){},goog.LoadController.prototype.clearModuleState=function(){},goog.LoadController.prototype.defer=function(A){},goog.LoadController.prototype.areDepsLoaded=function(){},goog.Dependency=function(A,B,N,U,H){this.path=A,this.relativePath=B,this.provides=N,this.requires=U,this.loadFlags=H,this.loaded_=!1,this.loadCallbacks_=[]},goog.Dependency.prototype.getPathName=function(){var A=this.path,B=A.indexOf("://");return 0<=B&&0<=(B=(A=A.substring(B+3)).indexOf("/"))&&(A=A.substring(B+1)),A},goog.Dependency.prototype.onLoad=function(A){this.loaded_?A():this.loadCallbacks_.push(A)},goog.Dependency.prototype.loaded=function(){this.loaded_=!0;var A=this.loadCallbacks_;this.loadCallbacks_=[];for(var B=0;B<A.length;B++)A[B]()},goog.Dependency.defer_=!1,goog.Dependency.callbackMap_={},goog.Dependency.registerCallback_=function(A){var B=Math.random().toString(32);return goog.Dependency.callbackMap_[B]=A,B},goog.Dependency.unregisterCallback_=function(A){delete goog.Dependency.callbackMap_[A]},goog.Dependency.callback_=function(A,B){if(A in goog.Dependency.callbackMap_){for(var N=goog.Dependency.callbackMap_[A],U=[],H=1;H<arguments.length;H++)U.push(arguments[H]);N.apply(void 0,U)}else throw Error("Callback key "+A+" does not exist (was base.js loaded more than once?).")},goog.Dependency.prototype.load=function(A){if(goog.global.CLOSURE_IMPORT_SCRIPT)goog.global.CLOSURE_IMPORT_SCRIPT(this.path)?A.loaded():A.pause();else if(goog.inHtmlDocument_()){var B=goog.global.document;if("complete"==B.readyState&&!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING){if(/\bdeps.js$/.test(this.path)){A.loaded();return}throw Error('Cannot write "'+this.path+'" after document load')}if(!goog.ENABLE_CHROME_APP_SAFE_SCRIPT_LOADING&&goog.isDocumentLoading_()){var N=goog.Dependency.registerCallback_(function(B){goog.DebugLoader_.IS_OLD_IE_&&"complete"!=B.readyState||(goog.Dependency.unregisterCallback_(N),A.loaded())}),U=!goog.DebugLoader_.IS_OLD_IE_&&goog.getScriptNonce()?' nonce="'+goog.getScriptNonce()+'"':"";U='<script src="'+this.path+'" '+(goog.DebugLoader_.IS_OLD_IE_?"onreadystatechange":"onload")+"=\"goog.Dependency.callback_('"+N+'\', this)" type="text/javascript" '+(goog.Dependency.defer_?"defer":"")+U+"><\/script>",B.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(U):U)}else{var H=B.createElement("script");H.defer=goog.Dependency.defer_,H.async=!1,H.type="text/javascript",(U=goog.getScriptNonce())&&H.setAttribute("nonce",U),goog.DebugLoader_.IS_OLD_IE_?(A.pause(),H.onreadystatechange=function(){("loaded"==H.readyState||"complete"==H.readyState)&&(A.loaded(),A.resume())}):H.onload=function(){H.onload=null,A.loaded()},H.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(this.path):this.path,B.head.appendChild(H)}}else goog.logToConsole_("Cannot use default debug loader outside of HTML documents."),"deps.js"==this.relativePath?(goog.logToConsole_("Consider setting CLOSURE_IMPORT_SCRIPT before loading base.js, or setting CLOSURE_NO_DEPS to true."),A.loaded()):A.pause()},goog.Es6ModuleDependency=function(A,B,N,U,H){goog.Dependency.call(this,A,B,N,U,H)},goog.inherits(goog.Es6ModuleDependency,goog.Dependency),goog.Es6ModuleDependency.prototype.load=function(A){function b(A,N){A=N?'<script type="module" crossorigin>'+N+"<\/script>":'<script type="module" crossorigin src="'+A+'"><\/script>',B.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(A):A)}function c(A,N){var U=B.createElement("script");U.defer=!0,U.async=!1,U.type="module",U.setAttribute("crossorigin",!0);var H=goog.getScriptNonce();H&&U.setAttribute("nonce",H),N?U.textContent=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScript(N):N:U.src=goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createScriptURL(A):A,B.head.appendChild(U)}if(goog.global.CLOSURE_IMPORT_SCRIPT)goog.global.CLOSURE_IMPORT_SCRIPT(this.path)?A.loaded():A.pause();else if(goog.inHtmlDocument_()){var B=goog.global.document,N=this;if(goog.isDocumentLoading_()){var U=b;goog.Dependency.defer_=!0}else U=c;var H=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(H),A.setModuleState(goog.ModuleType.ES6)});U(void 0,'goog.Dependency.callback_("'+H+'")'),U(this.path,void 0);var W=goog.Dependency.registerCallback_(function(B){goog.Dependency.unregisterCallback_(W),A.registerEs6ModuleExports(N.path,B,goog.moduleLoaderState_.moduleName)});U(void 0,'import * as m from "'+this.path+'"; goog.Dependency.callback_("'+W+'", m)');var j=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(j),A.clearModuleState(),A.loaded()});U(void 0,'goog.Dependency.callback_("'+j+'")')}else goog.logToConsole_("Cannot use default debug loader outside of HTML documents."),A.pause()},goog.TransformedDependency=function(A,B,N,U,H){goog.Dependency.call(this,A,B,N,U,H),this.contents_=null,this.lazyFetch_=!goog.inHtmlDocument_()||!("noModule"in goog.global.document.createElement("script"))},goog.inherits(goog.TransformedDependency,goog.Dependency),goog.TransformedDependency.prototype.load=function(A){function b(){B.contents_=goog.loadFileSync_(B.path),B.contents_&&(B.contents_=B.transform(B.contents_),B.contents_&&(B.contents_+="\n//# sourceURL="+B.path))}function c(){if(B.lazyFetch_&&b(),B.contents_){N&&A.setModuleState(goog.ModuleType.ES6);try{var U=B.contents_;if(B.contents_=null,goog.globalEval(U),N)var H=goog.moduleLoaderState_.moduleName}finally{N&&A.clearModuleState()}N&&goog.global.$jscomp.require.ensure([B.getPathName()],function(){A.registerEs6ModuleExports(B.path,goog.global.$jscomp.require(B.getPathName()),H)}),A.loaded()}}function d(){var A=goog.global.document,B=goog.Dependency.registerCallback_(function(){goog.Dependency.unregisterCallback_(B),c()}),N='<script type="text/javascript">'+goog.protectScriptTag_('goog.Dependency.callback_("'+B+'");')+"<\/script>";A.write(goog.TRUSTED_TYPES_POLICY_?goog.TRUSTED_TYPES_POLICY_.createHTML(N):N)}var B=this;if(goog.global.CLOSURE_IMPORT_SCRIPT)b(),this.contents_&&goog.global.CLOSURE_IMPORT_SCRIPT("",this.contents_)?(this.contents_=null,A.loaded()):A.pause();else{var N=this.loadFlags.module==goog.ModuleType.ES6;this.lazyFetch_||b();var U=1<A.pending().length,H=U&&goog.DebugLoader_.IS_OLD_IE_;if(U=goog.Dependency.defer_&&(U||goog.isDocumentLoading_()),H||U)A.defer(function(){c()});else{var W=goog.global.document;if(H=goog.inHtmlDocument_()&&"ActiveXObject"in goog.global,N&&goog.inHtmlDocument_()&&goog.isDocumentLoading_()&&!H){goog.Dependency.defer_=!0,A.pause();var j=W.onreadystatechange;W.onreadystatechange=function(){"interactive"==W.readyState&&(W.onreadystatechange=j,c(),A.resume()),goog.isFunction(j)&&j.apply(void 0,arguments)}}else!goog.DebugLoader_.IS_OLD_IE_&&goog.inHtmlDocument_()&&goog.isDocumentLoading_()?d():c()}}},goog.TransformedDependency.prototype.transform=function(A){},goog.TranspiledDependency=function(A,B,N,U,H,W){goog.TransformedDependency.call(this,A,B,N,U,H),this.transpiler=W},goog.inherits(goog.TranspiledDependency,goog.TransformedDependency),goog.TranspiledDependency.prototype.transform=function(A){return this.transpiler.transpile(A,this.getPathName())},goog.PreTranspiledEs6ModuleDependency=function(A,B,N,U,H){goog.TransformedDependency.call(this,A,B,N,U,H)},goog.inherits(goog.PreTranspiledEs6ModuleDependency,goog.TransformedDependency),goog.PreTranspiledEs6ModuleDependency.prototype.transform=function(A){return A},goog.GoogModuleDependency=function(A,B,N,U,H,W,j){goog.TransformedDependency.call(this,A,B,N,U,H),this.needsTranspile_=W,this.transpiler_=j},goog.inherits(goog.GoogModuleDependency,goog.TransformedDependency),goog.GoogModuleDependency.prototype.transform=function(A){return this.needsTranspile_&&(A=this.transpiler_.transpile(A,this.getPathName())),goog.LOAD_MODULE_USING_EVAL&&void 0!==goog.global.JSON?"goog.loadModule("+goog.global.JSON.stringify(A+"\n//# sourceURL="+this.path+"\n")+");":'goog.loadModule(function(exports) {"use strict";'+A+"\n;return exports});\n//# sourceURL="+this.path+"\n"},goog.DebugLoader_.IS_OLD_IE_=!(goog.global.atob||!goog.global.document||!goog.global.document.all),goog.DebugLoader_.prototype.addDependency=function(A,B,N,U){B=B||[],A=A.replace(/\\/g,"/");var H=goog.normalizePath_(goog.basePath+A);for(U&&"boolean"!=typeof U||(U=U?{module:goog.ModuleType.GOOG}:{}),N=this.factory_.createDependency(H,A,B,N,U,goog.transpiler_.needsTranspile(U.lang||"es3",U.module)),this.dependencies_[H]=N,N=0;N<B.length;N++)this.idToPath_[B[N]]=H;this.idToPath_[A]=H},goog.DependencyFactory=function(A){this.transpiler=A},goog.DependencyFactory.prototype.createDependency=function(A,B,N,U,H,W){return H.module==goog.ModuleType.GOOG?new goog.GoogModuleDependency(A,B,N,U,H,W,this.transpiler):W?new goog.TranspiledDependency(A,B,N,U,H,this.transpiler):H.module==goog.ModuleType.ES6?"never"==goog.TRANSPILE&&goog.ASSUME_ES_MODULES_TRANSPILED?new goog.PreTranspiledEs6ModuleDependency(A,B,N,U,H):new goog.Es6ModuleDependency(A,B,N,U,H):new goog.Dependency(A,B,N,U,H)},goog.debugLoader_=new goog.DebugLoader_,goog.loadClosureDeps=function(){goog.debugLoader_.loadClosureDeps()},goog.setDependencyFactory=function(A){goog.debugLoader_.setDependencyFactory(A)},goog.global.CLOSURE_NO_DEPS||goog.debugLoader_.loadClosureDeps(),goog.bootstrap=function(A,B){goog.debugLoader_.bootstrap(A,B)}),goog.TRUSTED_TYPES_POLICY_NAME="",goog.identity_=function(A){return A},goog.createTrustedTypesPolicy=function(A){var B=null,N=goog.global.trustedTypes||goog.global.TrustedTypes;if(!N||!N.createPolicy)return B;try{B=N.createPolicy(A,{createHTML:goog.identity_,createScript:goog.identity_,createScriptURL:goog.identity_,createURL:goog.identity_})}catch(A){goog.logToConsole_(A.message)}return B},goog.TRUSTED_TYPES_POLICY_=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#base"):null,goog.object={},goog.object.is=function(A,B){return A===B?0!==A||1/A==1/B:A!=A&&B!=B},goog.object.forEach=function(A,B,N){for(var U in A)B.call(N,A[U],U,A)},goog.object.filter=function(A,B,N){var U,H={};for(U in A)B.call(N,A[U],U,A)&&(H[U]=A[U]);return H},goog.object.map=function(A,B,N){var U,H={};for(U in A)H[U]=B.call(N,A[U],U,A);return H},goog.object.some=function(A,B,N){for(var U in A)if(B.call(N,A[U],U,A))return!0;return!1},goog.object.every=function(A,B,N){for(var U in A)if(!B.call(N,A[U],U,A))return!1;return!0},goog.object.getCount=function(A){var B,N=0;for(B in A)N++;return N},goog.object.getAnyKey=function(A){for(var B in A)return B},goog.object.getAnyValue=function(A){for(var B in A)return A[B]},goog.object.contains=function(A,B){return goog.object.containsValue(A,B)},goog.object.getValues=function(A){var B,N=[],U=0;for(B in A)N[U++]=A[B];return N},goog.object.getKeys=function(A){var B,N=[],U=0;for(B in A)N[U++]=B;return N},goog.object.getValueByKeys=function(A,B){var N=goog.isArrayLike(B),U=N?B:arguments;for(N=+!N;N<U.length;N++){if(null==A)return;A=A[U[N]]}return A},goog.object.containsKey=function(A,B){return null!==A&&B in A},goog.object.containsValue=function(A,B){for(var N in A)if(A[N]==B)return!0;return!1},goog.object.findKey=function(A,B,N){for(var U in A)if(B.call(N,A[U],U,A))return U},goog.object.findValue=function(A,B,N){return(B=goog.object.findKey(A,B,N))&&A[B]},goog.object.isEmpty=function(A){for(var B in A)return!1;return!0},goog.object.clear=function(A){for(var B in A)delete A[B]},goog.object.remove=function(A,B){var N;return(N=B in A)&&delete A[B],N},goog.object.add=function(A,B,N){if(null!==A&&B in A)throw Error('The object already contains the key "'+B+'"');goog.object.set(A,B,N)},goog.object.get=function(A,B,N){return null!==A&&B in A?A[B]:N},goog.object.set=function(A,B,N){A[B]=N},goog.object.setIfUndefined=function(A,B,N){return B in A?A[B]:A[B]=N},goog.object.setWithReturnValueIfNotSet=function(A,B,N){return B in A?A[B]:(N=N(),A[B]=N)},goog.object.equals=function(A,B){for(var N in A)if(!(N in B)||A[N]!==B[N])return!1;for(var U in B)if(!(U in A))return!1;return!0},goog.object.clone=function(A){var B,N={};for(B in A)N[B]=A[B];return N},goog.object.unsafeClone=function(A){var B=goog.typeOf(A);if("object"==B||"array"==B){if(goog.isFunction(A.clone))return A.clone();for(var N in B="array"==B?[]:{},A)B[N]=goog.object.unsafeClone(A[N]);return B}return A},goog.object.transpose=function(A){var B,N={};for(B in A)N[A[B]]=B;return N},goog.object.PROTOTYPE_FIELDS_="constructor hasOwnProperty isPrototypeOf propertyIsEnumerable toLocaleString toString valueOf".split(" "),goog.object.extend=function(A,B){for(var N,U,H=1;H<arguments.length;H++){for(N in U=arguments[H],U)A[N]=U[N];for(var W=0;W<goog.object.PROTOTYPE_FIELDS_.length;W++)N=goog.object.PROTOTYPE_FIELDS_[W],Object.prototype.hasOwnProperty.call(U,N)&&(A[N]=U[N])}},goog.object.create=function(A){var B=arguments.length;if(1==B&&Array.isArray(arguments[0]))return goog.object.create.apply(null,arguments[0]);if(B%2)throw Error("Uneven number of arguments");for(var N={},U=0;U<B;U+=2)N[arguments[U]]=arguments[U+1];return N},goog.object.createSet=function(A){var B=arguments.length;if(1==B&&Array.isArray(arguments[0]))return goog.object.createSet.apply(null,arguments[0]);for(var N={},U=0;U<B;U++)N[arguments[U]]=!0;return N},goog.object.createImmutableView=function(A){var B=A;return Object.isFrozen&&!Object.isFrozen(A)&&Object.freeze(B=Object.create(A)),B},goog.object.isImmutableView=function(A){return!!Object.isFrozen&&Object.isFrozen(A)},goog.object.getAllPropertyNames=function(A,B,N){if(!A)return[];if(!Object.getOwnPropertyNames||!Object.getPrototypeOf)return goog.object.getKeys(A);for(var U={};A&&(A!==Object.prototype||B)&&(A!==Function.prototype||N);){for(var H=Object.getOwnPropertyNames(A),W=0;W<H.length;W++)U[H[W]]=!0;A=Object.getPrototypeOf(A)}return goog.object.getKeys(U)},goog.object.getSuperClass=function(A){return(A=Object.getPrototypeOf(A.prototype))&&A.constructor};var jspb={asserts:{}};jspb.asserts.doAssertFailure=function(A,B,N,U){var H="Assertion failed";if(N){H+=": "+N;var W=U}else A&&(H+=": "+A,W=B);throw Error(""+H,W||[])},jspb.asserts.assert=function(A,B,N){for(var U=[],H=2;H<arguments.length;++H)U[H-2]=arguments[H];return A||jspb.asserts.doAssertFailure("",null,B,U),A},jspb.asserts.assertString=function(A,B,N){for(var U=[],H=2;H<arguments.length;++H)U[H-2]=arguments[H];return"string"!=typeof A&&jspb.asserts.doAssertFailure("Expected string but got %s: %s.",[goog.typeOf(A),A],B,U),A},jspb.asserts.assertArray=function(A,B,N){for(var U=[],H=2;H<arguments.length;++H)U[H-2]=arguments[H];return Array.isArray(A)||jspb.asserts.doAssertFailure("Expected array but got %s: %s.",[goog.typeOf(A),A],B,U),A},jspb.asserts.fail=function(A,B){for(var N=[],U=1;U<arguments.length;++U)N[U-1]=arguments[U];throw Error("Failure"+(A?": "+A:""),N)},jspb.asserts.assertInstanceof=function(A,B,N,U){for(var H=[],W=3;W<arguments.length;++W)H[W-3]=arguments[W];return A instanceof B||jspb.asserts.doAssertFailure("Expected instanceof %s but got %s.",[jspb.asserts.getType(B),jspb.asserts.getType(A)],N,H),A},jspb.asserts.getType=function(A){return A instanceof Function?A.displayName||A.name||"unknown type name":A instanceof Object?A.constructor.displayName||A.constructor.name||Object.prototype.toString.call(A):null===A?"null":typeof A},jspb.BinaryConstants={},jspb.ConstBinaryMessage=function(){},jspb.BinaryMessage=function(){},jspb.BinaryConstants.FieldType={INVALID:-1,DOUBLE:1,FLOAT:2,INT64:3,UINT64:4,INT32:5,FIXED64:6,FIXED32:7,BOOL:8,STRING:9,GROUP:10,MESSAGE:11,BYTES:12,UINT32:13,ENUM:14,SFIXED32:15,SFIXED64:16,SINT32:17,SINT64:18,FHASH64:30,VHASH64:31},jspb.BinaryConstants.WireType={INVALID:-1,VARINT:0,FIXED64:1,DELIMITED:2,START_GROUP:3,END_GROUP:4,FIXED32:5},jspb.BinaryConstants.FieldTypeToWireType=function(A){var B=jspb.BinaryConstants.FieldType,N=jspb.BinaryConstants.WireType;switch(A){case B.INT32:case B.INT64:case B.UINT32:case B.UINT64:case B.SINT32:case B.SINT64:case B.BOOL:case B.ENUM:case B.VHASH64:return N.VARINT;case B.DOUBLE:case B.FIXED64:case B.SFIXED64:case B.FHASH64:return N.FIXED64;case B.STRING:case B.MESSAGE:case B.BYTES:return N.DELIMITED;case B.FLOAT:case B.FIXED32:case B.SFIXED32:return N.FIXED32;default:return N.INVALID}},jspb.BinaryConstants.INVALID_FIELD_NUMBER=-1,jspb.BinaryConstants.FLOAT32_EPS=1401298464324817e-60,jspb.BinaryConstants.FLOAT32_MIN=11754943508222875e-54,jspb.BinaryConstants.FLOAT32_MAX=34028234663852886e22,jspb.BinaryConstants.FLOAT64_EPS=5e-324,jspb.BinaryConstants.FLOAT64_MIN=22250738585072014e-324,jspb.BinaryConstants.FLOAT64_MAX=17976931348623157e292,jspb.BinaryConstants.TWO_TO_20=1048576,jspb.BinaryConstants.TWO_TO_23=8388608,jspb.BinaryConstants.TWO_TO_31=0x80000000,jspb.BinaryConstants.TWO_TO_32=0x100000000,jspb.BinaryConstants.TWO_TO_52=0x10000000000000,jspb.BinaryConstants.TWO_TO_63=0x8000000000000000,jspb.BinaryConstants.TWO_TO_64=0xffffffffffffffff,jspb.BinaryConstants.ZERO_HASH="\0\0\0\0\0\0\0\0",goog.debug={},goog.debug.Error=function(A){if(Error.captureStackTrace)Error.captureStackTrace(this,goog.debug.Error);else{var B=Error().stack;B&&(this.stack=B)}A&&(this.message=String(A)),this.reportErrorToServer=!0},goog.inherits(goog.debug.Error,Error),goog.debug.Error.prototype.name="CustomError",goog.dom={},goog.dom.NodeType={ELEMENT:1,ATTRIBUTE:2,TEXT:3,CDATA_SECTION:4,ENTITY_REFERENCE:5,ENTITY:6,PROCESSING_INSTRUCTION:7,COMMENT:8,DOCUMENT:9,DOCUMENT_TYPE:10,DOCUMENT_FRAGMENT:11,NOTATION:12},goog.asserts={},goog.asserts.ENABLE_ASSERTS=goog.DEBUG,goog.asserts.AssertionError=function(A,B){goog.debug.Error.call(this,goog.asserts.subs_(A,B)),this.messagePattern=A},goog.inherits(goog.asserts.AssertionError,goog.debug.Error),goog.asserts.AssertionError.prototype.name="AssertionError",goog.asserts.DEFAULT_ERROR_HANDLER=function(A){throw A},goog.asserts.errorHandler_=goog.asserts.DEFAULT_ERROR_HANDLER,goog.asserts.subs_=function(A,B){A=A.split("%s");for(var N="",U=A.length-1,H=0;H<U;H++)N+=A[H]+(H<B.length?B[H]:"%s");return N+A[U]},goog.asserts.doAssertFailure_=function(A,B,N,U){var H="Assertion failed";if(N){H+=": "+N;var W=U}else A&&(H+=": "+A,W=B);A=new goog.asserts.AssertionError(""+H,W||[]),goog.asserts.errorHandler_(A)},goog.asserts.setErrorHandler=function(A){goog.asserts.ENABLE_ASSERTS&&(goog.asserts.errorHandler_=A)},goog.asserts.assert=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&!A&&goog.asserts.doAssertFailure_("",null,B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertExists=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&null==A&&goog.asserts.doAssertFailure_("Expected to exist: %s.",[A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.fail=function(A,B){goog.asserts.ENABLE_ASSERTS&&goog.asserts.errorHandler_(new goog.asserts.AssertionError("Failure"+(A?": "+A:""),Array.prototype.slice.call(arguments,1)))},goog.asserts.assertNumber=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&"number"!=typeof A&&goog.asserts.doAssertFailure_("Expected number but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertString=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&"string"!=typeof A&&goog.asserts.doAssertFailure_("Expected string but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertFunction=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&!goog.isFunction(A)&&goog.asserts.doAssertFailure_("Expected function but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertObject=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&!goog.isObject(A)&&goog.asserts.doAssertFailure_("Expected object but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertArray=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&!Array.isArray(A)&&goog.asserts.doAssertFailure_("Expected array but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertBoolean=function(A,B,N){return goog.asserts.ENABLE_ASSERTS&&"boolean"!=typeof A&&goog.asserts.doAssertFailure_("Expected boolean but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertElement=function(A,B,N){return!goog.asserts.ENABLE_ASSERTS||goog.isObject(A)&&A.nodeType==goog.dom.NodeType.ELEMENT||goog.asserts.doAssertFailure_("Expected Element but got %s: %s.",[goog.typeOf(A),A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertInstanceof=function(A,B,N,U){return!goog.asserts.ENABLE_ASSERTS||A instanceof B||goog.asserts.doAssertFailure_("Expected instanceof %s but got %s.",[goog.asserts.getType_(B),goog.asserts.getType_(A)],N,Array.prototype.slice.call(arguments,3)),A},goog.asserts.assertFinite=function(A,B,N){return!goog.asserts.ENABLE_ASSERTS||"number"==typeof A&&isFinite(A)||goog.asserts.doAssertFailure_("Expected %s to be a finite number but it is not.",[A],B,Array.prototype.slice.call(arguments,2)),A},goog.asserts.assertObjectPrototypeIsIntact=function(){for(var A in Object.prototype)goog.asserts.fail(A+" should not be enumerable in Object.prototype.")},goog.asserts.getType_=function(A){return A instanceof Function?A.displayName||A.name||"unknown type name":A instanceof Object?A.constructor.displayName||A.constructor.name||Object.prototype.toString.call(A):null===A?"null":typeof A},goog.array={},goog.NATIVE_ARRAY_PROTOTYPES=goog.TRUSTED_SITE,goog.array.ASSUME_NATIVE_FUNCTIONS=2012<goog.FEATURESET_YEAR,goog.array.peek=function(A){return A[A.length-1]},goog.array.last=goog.array.peek,goog.array.indexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.indexOf)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.indexOf.call(A,B,N)}:function(A,B,N){if(N=null==N?0:0>N?Math.max(0,A.length+N):N,"string"==typeof A)return"string"!=typeof B||1!=B.length?-1:A.indexOf(B,N);for(;N<A.length;N++)if(N in A&&A[N]===B)return N;return -1},goog.array.lastIndexOf=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.lastIndexOf)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.lastIndexOf.call(A,B,null==N?A.length-1:N)}:function(A,B,N){if(0>(N=null==N?A.length-1:N)&&(N=Math.max(0,A.length+N)),"string"==typeof A)return"string"!=typeof B||1!=B.length?-1:A.lastIndexOf(B,N);for(;0<=N;N--)if(N in A&&A[N]===B)return N;return -1},goog.array.forEach=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.forEach)?function(A,B,N){goog.asserts.assert(null!=A.length),Array.prototype.forEach.call(A,B,N)}:function(A,B,N){for(var U=A.length,H="string"==typeof A?A.split(""):A,W=0;W<U;W++)W in H&&B.call(N,H[W],W,A)},goog.array.forEachRight=function(A,B,N){var U=A.length,H="string"==typeof A?A.split(""):A;for(--U;0<=U;--U)U in H&&B.call(N,H[U],U,A)},goog.array.filter=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.filter)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.filter.call(A,B,N)}:function(A,B,N){for(var U=A.length,H=[],W=0,j="string"==typeof A?A.split(""):A,V=0;V<U;V++)if(V in j){var K=j[V];B.call(N,K,V,A)&&(H[W++]=K)}return H},goog.array.map=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.map)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.map.call(A,B,N)}:function(A,B,N){for(var U=A.length,H=Array(U),W="string"==typeof A?A.split(""):A,j=0;j<U;j++)j in W&&(H[j]=B.call(N,W[j],j,A));return H},goog.array.reduce=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduce)?function(A,B,N,U){return goog.asserts.assert(null!=A.length),U&&(B=goog.bind(B,U)),Array.prototype.reduce.call(A,B,N)}:function(A,B,N,U){var H=N;return goog.array.forEach(A,function(N,W){H=B.call(U,H,N,W,A)}),H},goog.array.reduceRight=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.reduceRight)?function(A,B,N,U){return goog.asserts.assert(null!=A.length),goog.asserts.assert(null!=B),U&&(B=goog.bind(B,U)),Array.prototype.reduceRight.call(A,B,N)}:function(A,B,N,U){var H=N;return goog.array.forEachRight(A,function(N,W){H=B.call(U,H,N,W,A)}),H},goog.array.some=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.some)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.some.call(A,B,N)}:function(A,B,N){for(var U=A.length,H="string"==typeof A?A.split(""):A,W=0;W<U;W++)if(W in H&&B.call(N,H[W],W,A))return!0;return!1},goog.array.every=goog.NATIVE_ARRAY_PROTOTYPES&&(goog.array.ASSUME_NATIVE_FUNCTIONS||Array.prototype.every)?function(A,B,N){return goog.asserts.assert(null!=A.length),Array.prototype.every.call(A,B,N)}:function(A,B,N){for(var U=A.length,H="string"==typeof A?A.split(""):A,W=0;W<U;W++)if(W in H&&!B.call(N,H[W],W,A))return!1;return!0},goog.array.count=function(A,B,N){var U=0;return goog.array.forEach(A,function(A,H,W){B.call(N,A,H,W)&&++U},N),U},goog.array.find=function(A,B,N){return 0>(B=goog.array.findIndex(A,B,N))?null:"string"==typeof A?A.charAt(B):A[B]},goog.array.findIndex=function(A,B,N){for(var U=A.length,H="string"==typeof A?A.split(""):A,W=0;W<U;W++)if(W in H&&B.call(N,H[W],W,A))return W;return -1},goog.array.findRight=function(A,B,N){return 0>(B=goog.array.findIndexRight(A,B,N))?null:"string"==typeof A?A.charAt(B):A[B]},goog.array.findIndexRight=function(A,B,N){var U=A.length,H="string"==typeof A?A.split(""):A;for(--U;0<=U;U--)if(U in H&&B.call(N,H[U],U,A))return U;return -1},goog.array.contains=function(A,B){return 0<=goog.array.indexOf(A,B)},goog.array.isEmpty=function(A){return 0==A.length},goog.array.clear=function(A){if(!Array.isArray(A))for(var B=A.length-1;0<=B;B--)delete A[B];A.length=0},goog.array.insert=function(A,B){goog.array.contains(A,B)||A.push(B)},goog.array.insertAt=function(A,B,N){goog.array.splice(A,N,0,B)},goog.array.insertArrayAt=function(A,B,N){goog.partial(goog.array.splice,A,N,0).apply(null,B)},goog.array.insertBefore=function(A,B,N){var U;2==arguments.length||0>(U=goog.array.indexOf(A,N))?A.push(B):goog.array.insertAt(A,B,U)},goog.array.remove=function(A,B){var N;return(N=0<=(B=goog.array.indexOf(A,B)))&&goog.array.removeAt(A,B),N},goog.array.removeLast=function(A,B){return 0<=(B=goog.array.lastIndexOf(A,B))&&(goog.array.removeAt(A,B),!0)},goog.array.removeAt=function(A,B){return goog.asserts.assert(null!=A.length),1==Array.prototype.splice.call(A,B,1).length},goog.array.removeIf=function(A,B,N){return 0<=(B=goog.array.findIndex(A,B,N))&&(goog.array.removeAt(A,B),!0)},goog.array.removeAllIf=function(A,B,N){var U=0;return goog.array.forEachRight(A,function(H,W){B.call(N,H,W,A)&&goog.array.removeAt(A,W)&&U++}),U},goog.array.concat=function(A){return Array.prototype.concat.apply([],arguments)},goog.array.join=function(A){return Array.prototype.concat.apply([],arguments)},goog.array.toArray=function(A){var B=A.length;if(0<B){for(var N=Array(B),U=0;U<B;U++)N[U]=A[U];return N}return[]},goog.array.clone=goog.array.toArray,goog.array.extend=function(A,B){for(var N=1;N<arguments.length;N++){var U=arguments[N];if(goog.isArrayLike(U)){var H=A.length||0,W=U.length||0;A.length=H+W;for(var j=0;j<W;j++)A[H+j]=U[j]}else A.push(U)}},goog.array.splice=function(A,B,N,U){return goog.asserts.assert(null!=A.length),Array.prototype.splice.apply(A,goog.array.slice(arguments,1))},goog.array.slice=function(A,B,N){return goog.asserts.assert(null!=A.length),2>=arguments.length?Array.prototype.slice.call(A,B):Array.prototype.slice.call(A,B,N)},goog.array.removeDuplicates=function(A,B,N){B=B||A;var d=function(A){return goog.isObject(A)?"o"+goog.getUid(A):(typeof A).charAt(0)+A};N=N||d,d={};for(var U=0,H=0;H<A.length;){var W=A[H++],j=N(W);Object.prototype.hasOwnProperty.call(d,j)||(d[j]=!0,B[U++]=W)}B.length=U},goog.array.binarySearch=function(A,B,N){return goog.array.binarySearch_(A,N||goog.array.defaultCompare,!1,B)},goog.array.binarySelect=function(A,B,N){return goog.array.binarySearch_(A,B,!0,void 0,N)},goog.array.binarySearch_=function(A,B,N,U,H){for(var W,j=0,V=A.length;j<V;){var K=j+(V-j>>>1),X=N?B.call(H,A[K],K,A):B(U,A[K]);0<X?j=K+1:(V=K,W=!X)}return W?j:-j-1},goog.array.sort=function(A,B){A.sort(B||goog.array.defaultCompare)},goog.array.stableSort=function(A,B){for(var N=Array(A.length),U=0;U<A.length;U++)N[U]={index:U,value:A[U]};var H=B||goog.array.defaultCompare;for(goog.array.sort(N,function(A,B){return H(A.value,B.value)||A.index-B.index}),U=0;U<A.length;U++)A[U]=N[U].value},goog.array.sortByKey=function(A,B,N){var U=N||goog.array.defaultCompare;goog.array.sort(A,function(A,N){return U(B(A),B(N))})},goog.array.sortObjectsByKey=function(A,B,N){goog.array.sortByKey(A,function(A){return A[B]},N)},goog.array.isSorted=function(A,B,N){B=B||goog.array.defaultCompare;for(var U=1;U<A.length;U++){var H=B(A[U-1],A[U]);if(0<H||0==H&&N)return!1}return!0},goog.array.equals=function(A,B,N){if(!goog.isArrayLike(A)||!goog.isArrayLike(B)||A.length!=B.length)return!1;var U=A.length;N=N||goog.array.defaultCompareEquality;for(var H=0;H<U;H++)if(!N(A[H],B[H]))return!1;return!0},goog.array.compare3=function(A,B,N){N=N||goog.array.defaultCompare;for(var U=Math.min(A.length,B.length),H=0;H<U;H++){var W=N(A[H],B[H]);if(0!=W)return W}return goog.array.defaultCompare(A.length,B.length)},goog.array.defaultCompare=function(A,B){return A>B?1:A<B?-1:0},goog.array.inverseDefaultCompare=function(A,B){return-goog.array.defaultCompare(A,B)},goog.array.defaultCompareEquality=function(A,B){return A===B},goog.array.binaryInsert=function(A,B,N){return 0>(N=goog.array.binarySearch(A,B,N))&&(goog.array.insertAt(A,B,-(N+1)),!0)},goog.array.binaryRemove=function(A,B,N){return 0<=(B=goog.array.binarySearch(A,B,N))&&goog.array.removeAt(A,B)},goog.array.bucket=function(A,B,N){for(var U={},H=0;H<A.length;H++){var W=A[H],j=B.call(N,W,H,A);void 0!==j&&(U[j]||(U[j]=[])).push(W)}return U},goog.array.toObject=function(A,B,N){var U={};return goog.array.forEach(A,function(H,W){U[B.call(N,H,W,A)]=H}),U},goog.array.range=function(A,B,N){var U=[],H=0,W=A;if(N=N||1,void 0!==B&&(H=A,W=B),0>N*(W-H))return[];if(0<N)for(A=H;A<W;A+=N)U.push(A);else for(A=H;A>W;A+=N)U.push(A);return U},goog.array.repeat=function(A,B){for(var N=[],U=0;U<B;U++)N[U]=A;return N},goog.array.flatten=function(A){for(var B=[],N=0;N<arguments.length;N++){var U=arguments[N];if(Array.isArray(U))for(var H=0;H<U.length;H+=8192){var W=goog.array.slice(U,H,H+8192);W=goog.array.flatten.apply(null,W);for(var j=0;j<W.length;j++)B.push(W[j])}else B.push(U)}return B},goog.array.rotate=function(A,B){return goog.asserts.assert(null!=A.length),A.length&&(0<(B%=A.length)?Array.prototype.unshift.apply(A,A.splice(-B,B)):0>B&&Array.prototype.push.apply(A,A.splice(0,-B))),A},goog.array.moveItem=function(A,B,N){goog.asserts.assert(0<=B&&B<A.length),goog.asserts.assert(0<=N&&N<A.length),B=Array.prototype.splice.call(A,B,1),Array.prototype.splice.call(A,N,0,B[0])},goog.array.zip=function(A){if(!arguments.length)return[];for(var B=[],N=arguments[0].length,U=1;U<arguments.length;U++)arguments[U].length<N&&(N=arguments[U].length);for(U=0;U<N;U++){for(var H=[],W=0;W<arguments.length;W++)H.push(arguments[W][U]);B.push(H)}return B},goog.array.shuffle=function(A,B){B=B||Math.random;for(var N=A.length-1;0<N;N--){var U=Math.floor(B()*(N+1)),H=A[N];A[N]=A[U],A[U]=H}},goog.array.copyByIndex=function(A,B){var N=[];return goog.array.forEach(B,function(B){N.push(A[B])}),N},goog.array.concatMap=function(A,B,N){return goog.array.concat.apply([],goog.array.map(A,B,N))},goog.crypt={},goog.crypt.stringToByteArray=function(A){for(var B=[],N=0,U=0;U<A.length;U++){var H=A.charCodeAt(U);255<H&&(B[N++]=255&H,H>>=8),B[N++]=H}return B},goog.crypt.byteArrayToString=function(A){if(8192>=A.length)return String.fromCharCode.apply(null,A);for(var B="",N=0;N<A.length;N+=8192){var U=goog.array.slice(A,N,N+8192);B+=String.fromCharCode.apply(null,U)}return B},goog.crypt.byteArrayToHex=function(A,B){return goog.array.map(A,function(A){return 1<(A=A.toString(16)).length?A:"0"+A}).join(B||"")},goog.crypt.hexToByteArray=function(A){goog.asserts.assert(0==A.length%2,"Key string length must be multiple of 2");for(var B=[],N=0;N<A.length;N+=2)B.push(parseInt(A.substring(N,N+2),16));return B},goog.crypt.stringToUtf8ByteArray=function(A){for(var B=[],N=0,U=0;U<A.length;U++){var H=A.charCodeAt(U);128>H?B[N++]=H:(2048>H?B[N++]=H>>6|192:(55296==(64512&H)&&U+1<A.length&&56320==(64512&A.charCodeAt(U+1))?(H=65536+((1023&H)<<10)+(1023&A.charCodeAt(++U)),B[N++]=H>>18|240,B[N++]=H>>12&63|128):B[N++]=H>>12|224,B[N++]=H>>6&63|128),B[N++]=63&H|128)}return B},goog.crypt.utf8ByteArrayToString=function(A){for(var B=[],N=0,U=0;N<A.length;){var H=A[N++];if(128>H)B[U++]=String.fromCharCode(H);else if(191<H&&224>H){var W=A[N++];B[U++]=String.fromCharCode((31&H)<<6|63&W)}else if(239<H&&365>H){W=A[N++];var j=A[N++];H=((7&H)<<18|(63&W)<<12|(63&j)<<6|63&A[N++])-65536,B[U++]=String.fromCharCode(55296+(H>>10)),B[U++]=String.fromCharCode(56320+(1023&H))}else W=A[N++],j=A[N++],B[U++]=String.fromCharCode((15&H)<<12|(63&W)<<6|63&j)}return B.join("")},goog.crypt.xorByteArray=function(A,B){goog.asserts.assert(A.length==B.length,"XOR array lengths must match");for(var N=[],U=0;U<A.length;U++)N.push(A[U]^B[U]);return N},goog.dom.asserts={},goog.dom.asserts.assertIsLocation=function(A){if(goog.asserts.ENABLE_ASSERTS){var B=goog.dom.asserts.getWindow_(A);B&&(!A||!(A instanceof B.Location)&&A instanceof B.Element)&&goog.asserts.fail("Argument is not a Location (or a non-Element mock); got: %s",goog.dom.asserts.debugStringForType_(A))}return A},goog.dom.asserts.assertIsElementType_=function(A,B){if(goog.asserts.ENABLE_ASSERTS){var N=goog.dom.asserts.getWindow_(A);N&&void 0!==N[B]&&(A&&(A instanceof N[B]||!(A instanceof N.Location||A instanceof N.Element))||goog.asserts.fail("Argument is not a %s (or a non-Element, non-Location mock); got: %s",B,goog.dom.asserts.debugStringForType_(A)))}return A},goog.dom.asserts.assertIsHTMLAnchorElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLAnchorElement")},goog.dom.asserts.assertIsHTMLButtonElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLButtonElement")},goog.dom.asserts.assertIsHTMLLinkElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLLinkElement")},goog.dom.asserts.assertIsHTMLImageElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLImageElement")},goog.dom.asserts.assertIsHTMLAudioElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLAudioElement")},goog.dom.asserts.assertIsHTMLVideoElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLVideoElement")},goog.dom.asserts.assertIsHTMLInputElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLInputElement")},goog.dom.asserts.assertIsHTMLTextAreaElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLTextAreaElement")},goog.dom.asserts.assertIsHTMLCanvasElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLCanvasElement")},goog.dom.asserts.assertIsHTMLEmbedElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLEmbedElement")},goog.dom.asserts.assertIsHTMLFormElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLFormElement")},goog.dom.asserts.assertIsHTMLFrameElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLFrameElement")},goog.dom.asserts.assertIsHTMLIFrameElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLIFrameElement")},goog.dom.asserts.assertIsHTMLObjectElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLObjectElement")},goog.dom.asserts.assertIsHTMLScriptElement=function(A){return goog.dom.asserts.assertIsElementType_(A,"HTMLScriptElement")},goog.dom.asserts.debugStringForType_=function(A){if(!goog.isObject(A))return void 0===A?"undefined":null===A?"null":typeof A;try{return A.constructor.displayName||A.constructor.name||Object.prototype.toString.call(A)}catch(A){return"<object could not be stringified>"}},goog.dom.asserts.getWindow_=function(A){try{var B=A&&A.ownerDocument,N=B&&(B.defaultView||B.parentWindow);if((N=N||goog.global).Element&&N.Location)return N}catch(A){}return null},goog.functions={},goog.functions.constant=function(A){return function(){return A}},goog.functions.FALSE=function(){return!1},goog.functions.TRUE=function(){return!0},goog.functions.NULL=function(){return null},goog.functions.identity=function(A,B){return A},goog.functions.error=function(A){return function(){throw Error(A)}},goog.functions.fail=function(A){return function(){throw A}},goog.functions.lock=function(A,B){return B=B||0,function(){return A.apply(this,Array.prototype.slice.call(arguments,0,B))}},goog.functions.nth=function(A){return function(){return arguments[A]}},goog.functions.partialRight=function(A,B){var N=Array.prototype.slice.call(arguments,1);return function(){var B=Array.prototype.slice.call(arguments);return B.push.apply(B,N),A.apply(this,B)}},goog.functions.withReturnValue=function(A,B){return goog.functions.sequence(A,goog.functions.constant(B))},goog.functions.equalTo=function(A,B){return function(N){return B?A==N:A===N}},goog.functions.compose=function(A,B){var N=arguments,U=N.length;return function(){var A;U&&(A=N[U-1].apply(this,arguments));for(var B=U-2;0<=B;B--)A=N[B].call(this,A);return A}},goog.functions.sequence=function(A){var B=arguments,N=B.length;return function(){for(var A,U=0;U<N;U++)A=B[U].apply(this,arguments);return A}},goog.functions.and=function(A){var B=arguments,N=B.length;return function(){for(var A=0;A<N;A++)if(!B[A].apply(this,arguments))return!1;return!0}},goog.functions.or=function(A){var B=arguments,N=B.length;return function(){for(var A=0;A<N;A++)if(B[A].apply(this,arguments))return!0;return!1}},goog.functions.not=function(A){return function(){return!A.apply(this,arguments)}},goog.functions.create=function(A,B){var c=function(){};return c.prototype=A.prototype,c=new c,A.apply(c,Array.prototype.slice.call(arguments,1)),c},goog.functions.CACHE_RETURN_VALUE=!0,goog.functions.cacheReturnValue=function(A){var B,N=!1;return function(){return goog.functions.CACHE_RETURN_VALUE?(N||(B=A(),N=!0),B):A()}},goog.functions.once=function(A){var B=A;return function(){if(B){var A=B;B=null,A()}}},goog.functions.debounce=function(A,B,N){var U=0;return function(H){goog.global.clearTimeout(U);var W=arguments;U=goog.global.setTimeout(function(){A.apply(N,W)},B)}},goog.functions.throttle=function(A,B,N){var U=0,H=!1,W=[],g=function(){U=0,H&&(H=!1,h())},h=function(){U=goog.global.setTimeout(g,B),A.apply(N,W)};return function(A){W=arguments,U?H=!0:h()}},goog.functions.rateLimit=function(A,B,N){var U=0,e=function(){U=0};return function(H){U||(U=goog.global.setTimeout(e,B),A.apply(N,arguments))}},goog.dom.HtmlElement=function(){},goog.dom.TagName=function(A){this.tagName_=A},goog.dom.TagName.prototype.toString=function(){return this.tagName_},goog.dom.TagName.A=new goog.dom.TagName("A"),goog.dom.TagName.ABBR=new goog.dom.TagName("ABBR"),goog.dom.TagName.ACRONYM=new goog.dom.TagName("ACRONYM"),goog.dom.TagName.ADDRESS=new goog.dom.TagName("ADDRESS"),goog.dom.TagName.APPLET=new goog.dom.TagName("APPLET"),goog.dom.TagName.AREA=new goog.dom.TagName("AREA"),goog.dom.TagName.ARTICLE=new goog.dom.TagName("ARTICLE"),goog.dom.TagName.ASIDE=new goog.dom.TagName("ASIDE"),goog.dom.TagName.AUDIO=new goog.dom.TagName("AUDIO"),goog.dom.TagName.B=new goog.dom.TagName("B"),goog.dom.TagName.BASE=new goog.dom.TagName("BASE"),goog.dom.TagName.BASEFONT=new goog.dom.TagName("BASEFONT"),goog.dom.TagName.BDI=new goog.dom.TagName("BDI"),goog.dom.TagName.BDO=new goog.dom.TagName("BDO"),goog.dom.TagName.BIG=new goog.dom.TagName("BIG"),goog.dom.TagName.BLOCKQUOTE=new goog.dom.TagName("BLOCKQUOTE"),goog.dom.TagName.BODY=new goog.dom.TagName("BODY"),goog.dom.TagName.BR=new goog.dom.TagName("BR"),goog.dom.TagName.BUTTON=new goog.dom.TagName("BUTTON"),goog.dom.TagName.CANVAS=new goog.dom.TagName("CANVAS"),goog.dom.TagName.CAPTION=new goog.dom.TagName("CAPTION"),goog.dom.TagName.CENTER=new goog.dom.TagName("CENTER"),goog.dom.TagName.CITE=new goog.dom.TagName("CITE"),goog.dom.TagName.CODE=new goog.dom.TagName("CODE"),goog.dom.TagName.COL=new goog.dom.TagName("COL"),goog.dom.TagName.COLGROUP=new goog.dom.TagName("COLGROUP"),goog.dom.TagName.COMMAND=new goog.dom.TagName("COMMAND"),goog.dom.TagName.DATA=new goog.dom.TagName("DATA"),goog.dom.TagName.DATALIST=new goog.dom.TagName("DATALIST"),goog.dom.TagName.DD=new goog.dom.TagName("DD"),goog.dom.TagName.DEL=new goog.dom.TagName("DEL"),goog.dom.TagName.DETAILS=new goog.dom.TagName("DETAILS"),goog.dom.TagName.DFN=new goog.dom.TagName("DFN"),goog.dom.TagName.DIALOG=new goog.dom.TagName("DIALOG"),goog.dom.TagName.DIR=new goog.dom.TagName("DIR"),goog.dom.TagName.DIV=new goog.dom.TagName("DIV"),goog.dom.TagName.DL=new goog.dom.TagName("DL"),goog.dom.TagName.DT=new goog.dom.TagName("DT"),goog.dom.TagName.EM=new goog.dom.TagName("EM"),goog.dom.TagName.EMBED=new goog.dom.TagName("EMBED"),goog.dom.TagName.FIELDSET=new goog.dom.TagName("FIELDSET"),goog.dom.TagName.FIGCAPTION=new goog.dom.TagName("FIGCAPTION"),goog.dom.TagName.FIGURE=new goog.dom.TagName("FIGURE"),goog.dom.TagName.FONT=new goog.dom.TagName("FONT"),goog.dom.TagName.FOOTER=new goog.dom.TagName("FOOTER"),goog.dom.TagName.FORM=new goog.dom.TagName("FORM"),goog.dom.TagName.FRAME=new goog.dom.TagName("FRAME"),goog.dom.TagName.FRAMESET=new goog.dom.TagName("FRAMESET"),goog.dom.TagName.H1=new goog.dom.TagName("H1"),goog.dom.TagName.H2=new goog.dom.TagName("H2"),goog.dom.TagName.H3=new goog.dom.TagName("H3"),goog.dom.TagName.H4=new goog.dom.TagName("H4"),goog.dom.TagName.H5=new goog.dom.TagName("H5"),goog.dom.TagName.H6=new goog.dom.TagName("H6"),goog.dom.TagName.HEAD=new goog.dom.TagName("HEAD"),goog.dom.TagName.HEADER=new goog.dom.TagName("HEADER"),goog.dom.TagName.HGROUP=new goog.dom.TagName("HGROUP"),goog.dom.TagName.HR=new goog.dom.TagName("HR"),goog.dom.TagName.HTML=new goog.dom.TagName("HTML"),goog.dom.TagName.I=new goog.dom.TagName("I"),goog.dom.TagName.IFRAME=new goog.dom.TagName("IFRAME"),goog.dom.TagName.IMG=new goog.dom.TagName("IMG"),goog.dom.TagName.INPUT=new goog.dom.TagName("INPUT"),goog.dom.TagName.INS=new goog.dom.TagName("INS"),goog.dom.TagName.ISINDEX=new goog.dom.TagName("ISINDEX"),goog.dom.TagName.KBD=new goog.dom.TagName("KBD"),goog.dom.TagName.KEYGEN=new goog.dom.TagName("KEYGEN"),goog.dom.TagName.LABEL=new goog.dom.TagName("LABEL"),goog.dom.TagName.LEGEND=new goog.dom.TagName("LEGEND"),goog.dom.TagName.LI=new goog.dom.TagName("LI"),goog.dom.TagName.LINK=new goog.dom.TagName("LINK"),goog.dom.TagName.MAIN=new goog.dom.TagName("MAIN"),goog.dom.TagName.MAP=new goog.dom.TagName("MAP"),goog.dom.TagName.MARK=new goog.dom.TagName("MARK"),goog.dom.TagName.MATH=new goog.dom.TagName("MATH"),goog.dom.TagName.MENU=new goog.dom.TagName("MENU"),goog.dom.TagName.MENUITEM=new goog.dom.TagName("MENUITEM"),goog.dom.TagName.META=new goog.dom.TagName("META"),goog.dom.TagName.METER=new goog.dom.TagName("METER"),goog.dom.TagName.NAV=new goog.dom.TagName("NAV"),goog.dom.TagName.NOFRAMES=new goog.dom.TagName("NOFRAMES"),goog.dom.TagName.NOSCRIPT=new goog.dom.TagName("NOSCRIPT"),goog.dom.TagName.OBJECT=new goog.dom.TagName("OBJECT"),goog.dom.TagName.OL=new goog.dom.TagName("OL"),goog.dom.TagName.OPTGROUP=new goog.dom.TagName("OPTGROUP"),goog.dom.TagName.OPTION=new goog.dom.TagName("OPTION"),goog.dom.TagName.OUTPUT=new goog.dom.TagName("OUTPUT"),goog.dom.TagName.P=new goog.dom.TagName("P"),goog.dom.TagName.PARAM=new goog.dom.TagName("PARAM"),goog.dom.TagName.PICTURE=new goog.dom.TagName("PICTURE"),goog.dom.TagName.PRE=new goog.dom.TagName("PRE"),goog.dom.TagName.PROGRESS=new goog.dom.TagName("PROGRESS"),goog.dom.TagName.Q=new goog.dom.TagName("Q"),goog.dom.TagName.RP=new goog.dom.TagName("RP"),goog.dom.TagName.RT=new goog.dom.TagName("RT"),goog.dom.TagName.RTC=new goog.dom.TagName("RTC"),goog.dom.TagName.RUBY=new goog.dom.TagName("RUBY"),goog.dom.TagName.S=new goog.dom.TagName("S"),goog.dom.TagName.SAMP=new goog.dom.TagName("SAMP"),goog.dom.TagName.SCRIPT=new goog.dom.TagName("SCRIPT"),goog.dom.TagName.SECTION=new goog.dom.TagName("SECTION"),goog.dom.TagName.SELECT=new goog.dom.TagName("SELECT"),goog.dom.TagName.SMALL=new goog.dom.TagName("SMALL"),goog.dom.TagName.SOURCE=new goog.dom.TagName("SOURCE"),goog.dom.TagName.SPAN=new goog.dom.TagName("SPAN"),goog.dom.TagName.STRIKE=new goog.dom.TagName("STRIKE"),goog.dom.TagName.STRONG=new goog.dom.TagName("STRONG"),goog.dom.TagName.STYLE=new goog.dom.TagName("STYLE"),goog.dom.TagName.SUB=new goog.dom.TagName("SUB"),goog.dom.TagName.SUMMARY=new goog.dom.TagName("SUMMARY"),goog.dom.TagName.SUP=new goog.dom.TagName("SUP"),goog.dom.TagName.SVG=new goog.dom.TagName("SVG"),goog.dom.TagName.TABLE=new goog.dom.TagName("TABLE"),goog.dom.TagName.TBODY=new goog.dom.TagName("TBODY"),goog.dom.TagName.TD=new goog.dom.TagName("TD"),goog.dom.TagName.TEMPLATE=new goog.dom.TagName("TEMPLATE"),goog.dom.TagName.TEXTAREA=new goog.dom.TagName("TEXTAREA"),goog.dom.TagName.TFOOT=new goog.dom.TagName("TFOOT"),goog.dom.TagName.TH=new goog.dom.TagName("TH"),goog.dom.TagName.THEAD=new goog.dom.TagName("THEAD"),goog.dom.TagName.TIME=new goog.dom.TagName("TIME"),goog.dom.TagName.TITLE=new goog.dom.TagName("TITLE"),goog.dom.TagName.TR=new goog.dom.TagName("TR"),goog.dom.TagName.TRACK=new goog.dom.TagName("TRACK"),goog.dom.TagName.TT=new goog.dom.TagName("TT"),goog.dom.TagName.U=new goog.dom.TagName("U"),goog.dom.TagName.UL=new goog.dom.TagName("UL"),goog.dom.TagName.VAR=new goog.dom.TagName("VAR"),goog.dom.TagName.VIDEO=new goog.dom.TagName("VIDEO"),goog.dom.TagName.WBR=new goog.dom.TagName("WBR"),goog.dom.tags={},goog.dom.tags.VOID_TAGS_={area:!0,base:!0,br:!0,col:!0,command:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},goog.dom.tags.isVoidTag=function(A){return!0===goog.dom.tags.VOID_TAGS_[A]},goog.html={},goog.html.trustedtypes={},goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY=goog.TRUSTED_TYPES_POLICY_NAME?goog.createTrustedTypesPolicy(goog.TRUSTED_TYPES_POLICY_NAME+"#html"):null,goog.string={},goog.string.TypedString=function(){},goog.string.Const=function(A,B){this.stringConstValueWithSecurityContract__googStringSecurityPrivate_=A===goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_&&B||"",this.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_=goog.string.Const.TYPE_MARKER_},goog.string.Const.prototype.implementsGoogStringTypedString=!0,goog.string.Const.prototype.getTypedStringValue=function(){return this.stringConstValueWithSecurityContract__googStringSecurityPrivate_},goog.DEBUG&&(goog.string.Const.prototype.toString=function(){return"Const{"+this.stringConstValueWithSecurityContract__googStringSecurityPrivate_+"}"}),goog.string.Const.unwrap=function(A){return A instanceof goog.string.Const&&A.constructor===goog.string.Const&&A.STRING_CONST_TYPE_MARKER__GOOG_STRING_SECURITY_PRIVATE_===goog.string.Const.TYPE_MARKER_?A.stringConstValueWithSecurityContract__googStringSecurityPrivate_:(goog.asserts.fail("expected object of type Const, got '"+A+"'"),"type_error:Const")},goog.string.Const.from=function(A){return new goog.string.Const(goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_,A)},goog.string.Const.TYPE_MARKER_={},goog.string.Const.GOOG_STRING_CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.Const.EMPTY=goog.string.Const.from(""),goog.html.SafeScript=function(){this.privateDoNotAccessOrElseSafeScriptWrappedValue_="",this.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeScript.prototype.implementsGoogStringTypedString=!0,goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeScript.fromConstant=function(A){return 0===(A=goog.string.Const.unwrap(A)).length?goog.html.SafeScript.EMPTY:goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeScript.fromConstantAndArgs=function(A,B){for(var N=[],U=1;U<arguments.length;U++)N.push(goog.html.SafeScript.stringify_(arguments[U]));return goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse("("+goog.string.Const.unwrap(A)+")("+N.join(", ")+");")},goog.html.SafeScript.fromJson=function(A){return goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(goog.html.SafeScript.stringify_(A))},goog.html.SafeScript.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeScriptWrappedValue_.toString()},goog.DEBUG&&(goog.html.SafeScript.prototype.toString=function(){return"SafeScript{"+this.privateDoNotAccessOrElseSafeScriptWrappedValue_+"}"}),goog.html.SafeScript.unwrap=function(A){return goog.html.SafeScript.unwrapTrustedScript(A).toString()},goog.html.SafeScript.unwrapTrustedScript=function(A){return A instanceof goog.html.SafeScript&&A.constructor===goog.html.SafeScript&&A.SAFE_SCRIPT_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeScript.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseSafeScriptWrappedValue_:(goog.asserts.fail("expected object of type SafeScript, got '"+A+"' of type "+goog.typeOf(A)),"type_error:SafeScript")},goog.html.SafeScript.stringify_=function(A){return JSON.stringify(A).replace(/</g,"\\x3c")},goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse=function(A){return(new goog.html.SafeScript).initSecurityPrivateDoNotAccessOrElse_(A)},goog.html.SafeScript.prototype.initSecurityPrivateDoNotAccessOrElse_=function(A){return this.privateDoNotAccessOrElseSafeScriptWrappedValue_=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createScript(A):A,this},goog.html.SafeScript.EMPTY=goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(""),goog.fs={},goog.fs.url={},goog.fs.url.createObjectUrl=function(A){return goog.fs.url.getUrlObject_().createObjectURL(A)},goog.fs.url.revokeObjectUrl=function(A){goog.fs.url.getUrlObject_().revokeObjectURL(A)},goog.fs.url.UrlObject_=function(){},goog.fs.url.UrlObject_.prototype.createObjectURL=function(A){},goog.fs.url.UrlObject_.prototype.revokeObjectURL=function(A){},goog.fs.url.getUrlObject_=function(){var A=goog.fs.url.findUrlObject_();if(null!=A)return A;throw Error("This browser doesn't seem to support blob URLs")},goog.fs.url.findUrlObject_=function(){return void 0!==goog.global.URL&&void 0!==goog.global.URL.createObjectURL?goog.global.URL:void 0!==goog.global.webkitURL&&void 0!==goog.global.webkitURL.createObjectURL?goog.global.webkitURL:void 0!==goog.global.createObjectURL?goog.global:null},goog.fs.url.browserSupportsObjectUrls=function(){return null!=goog.fs.url.findUrlObject_()},goog.fs.blob={},goog.fs.blob.getBlob=function(A){var B=goog.global.BlobBuilder||goog.global.WebKitBlobBuilder;if(void 0!==B){B=new B;for(var N=0;N<arguments.length;N++)B.append(arguments[N]);return B.getBlob()}return goog.fs.blob.getBlobWithProperties(goog.array.toArray(arguments))},goog.fs.blob.getBlobWithProperties=function(A,B,N){var U=goog.global.BlobBuilder||goog.global.WebKitBlobBuilder;if(void 0!==U){U=new U;for(var H=0;H<A.length;H++)U.append(A[H],N);return U.getBlob(B)}if(void 0!==goog.global.Blob)return U={},B&&(U.type=B),N&&(U.endings=N),new Blob(A,U);throw Error("This browser doesn't seem to support creating Blobs")},goog.i18n={},goog.i18n.bidi={},goog.i18n.bidi.FORCE_RTL=!1,goog.i18n.bidi.IS_RTL=goog.i18n.bidi.FORCE_RTL||("ar"==goog.LOCALE.substring(0,2).toLowerCase()||"fa"==goog.LOCALE.substring(0,2).toLowerCase()||"he"==goog.LOCALE.substring(0,2).toLowerCase()||"iw"==goog.LOCALE.substring(0,2).toLowerCase()||"ps"==goog.LOCALE.substring(0,2).toLowerCase()||"sd"==goog.LOCALE.substring(0,2).toLowerCase()||"ug"==goog.LOCALE.substring(0,2).toLowerCase()||"ur"==goog.LOCALE.substring(0,2).toLowerCase()||"yi"==goog.LOCALE.substring(0,2).toLowerCase())&&(2==goog.LOCALE.length||"-"==goog.LOCALE.substring(2,3)||"_"==goog.LOCALE.substring(2,3))||3<=goog.LOCALE.length&&"ckb"==goog.LOCALE.substring(0,3).toLowerCase()&&(3==goog.LOCALE.length||"-"==goog.LOCALE.substring(3,4)||"_"==goog.LOCALE.substring(3,4))||7<=goog.LOCALE.length&&("-"==goog.LOCALE.substring(2,3)||"_"==goog.LOCALE.substring(2,3))&&("adlm"==goog.LOCALE.substring(3,7).toLowerCase()||"arab"==goog.LOCALE.substring(3,7).toLowerCase()||"hebr"==goog.LOCALE.substring(3,7).toLowerCase()||"nkoo"==goog.LOCALE.substring(3,7).toLowerCase()||"rohg"==goog.LOCALE.substring(3,7).toLowerCase()||"thaa"==goog.LOCALE.substring(3,7).toLowerCase())||8<=goog.LOCALE.length&&("-"==goog.LOCALE.substring(3,4)||"_"==goog.LOCALE.substring(3,4))&&("adlm"==goog.LOCALE.substring(4,8).toLowerCase()||"arab"==goog.LOCALE.substring(4,8).toLowerCase()||"hebr"==goog.LOCALE.substring(4,8).toLowerCase()||"nkoo"==goog.LOCALE.substring(4,8).toLowerCase()||"rohg"==goog.LOCALE.substring(4,8).toLowerCase()||"thaa"==goog.LOCALE.substring(4,8).toLowerCase()),goog.i18n.bidi.Format={LRE:"‪",RLE:"‫",PDF:"‬",LRM:"‎",RLM:"‏"},goog.i18n.bidi.Dir={LTR:1,RTL:-1,NEUTRAL:0},goog.i18n.bidi.RIGHT="right",goog.i18n.bidi.LEFT="left",goog.i18n.bidi.I18N_RIGHT=goog.i18n.bidi.IS_RTL?goog.i18n.bidi.LEFT:goog.i18n.bidi.RIGHT,goog.i18n.bidi.I18N_LEFT=goog.i18n.bidi.IS_RTL?goog.i18n.bidi.RIGHT:goog.i18n.bidi.LEFT,goog.i18n.bidi.toDir=function(A,B){return"number"==typeof A?0<A?goog.i18n.bidi.Dir.LTR:0>A?goog.i18n.bidi.Dir.RTL:B?null:goog.i18n.bidi.Dir.NEUTRAL:null==A?null:A?goog.i18n.bidi.Dir.RTL:goog.i18n.bidi.Dir.LTR},goog.i18n.bidi.ltrChars_="A-Za-z\xc0-\xd6\xd8-\xf6\xf8-ʸ̀-֐ऀ-῿‎Ⰰ-\ud801\ud804-\ud839\ud83c-\udbff豈-﬜︀-﹯﻽-￿",goog.i18n.bidi.rtlChars_="֑-ۯۺ-ࣿ‏\ud802-\ud803\ud83a-\ud83bיִ-﷿ﹰ-ﻼ",goog.i18n.bidi.htmlSkipReg_=/<[^>]*>|&[^;]+;/g,goog.i18n.bidi.stripHtmlIfNeeded_=function(A,B){return B?A.replace(goog.i18n.bidi.htmlSkipReg_,""):A},goog.i18n.bidi.rtlCharReg_=RegExp("["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.ltrCharReg_=RegExp("["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.hasAnyRtl=function(A,B){return goog.i18n.bidi.rtlCharReg_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.hasRtlChar=goog.i18n.bidi.hasAnyRtl,goog.i18n.bidi.hasAnyLtr=function(A,B){return goog.i18n.bidi.ltrCharReg_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.ltrRe_=RegExp("^["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.rtlRe_=RegExp("^["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.isRtlChar=function(A){return goog.i18n.bidi.rtlRe_.test(A)},goog.i18n.bidi.isLtrChar=function(A){return goog.i18n.bidi.ltrRe_.test(A)},goog.i18n.bidi.isNeutralChar=function(A){return!goog.i18n.bidi.isLtrChar(A)&&!goog.i18n.bidi.isRtlChar(A)},goog.i18n.bidi.ltrDirCheckRe_=RegExp("^[^"+goog.i18n.bidi.rtlChars_+"]*["+goog.i18n.bidi.ltrChars_+"]"),goog.i18n.bidi.rtlDirCheckRe_=RegExp("^[^"+goog.i18n.bidi.ltrChars_+"]*["+goog.i18n.bidi.rtlChars_+"]"),goog.i18n.bidi.startsWithRtl=function(A,B){return goog.i18n.bidi.rtlDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.isRtlText=goog.i18n.bidi.startsWithRtl,goog.i18n.bidi.startsWithLtr=function(A,B){return goog.i18n.bidi.ltrDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.isLtrText=goog.i18n.bidi.startsWithLtr,goog.i18n.bidi.isRequiredLtrRe_=/^http:\/\/.*/,goog.i18n.bidi.isNeutralText=function(A,B){return A=goog.i18n.bidi.stripHtmlIfNeeded_(A,B),goog.i18n.bidi.isRequiredLtrRe_.test(A)||!goog.i18n.bidi.hasAnyLtr(A)&&!goog.i18n.bidi.hasAnyRtl(A)},goog.i18n.bidi.ltrExitDirCheckRe_=RegExp("["+goog.i18n.bidi.ltrChars_+"][^"+goog.i18n.bidi.rtlChars_+"]*$"),goog.i18n.bidi.rtlExitDirCheckRe_=RegExp("["+goog.i18n.bidi.rtlChars_+"][^"+goog.i18n.bidi.ltrChars_+"]*$"),goog.i18n.bidi.endsWithLtr=function(A,B){return goog.i18n.bidi.ltrExitDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.isLtrExitText=goog.i18n.bidi.endsWithLtr,goog.i18n.bidi.endsWithRtl=function(A,B){return goog.i18n.bidi.rtlExitDirCheckRe_.test(goog.i18n.bidi.stripHtmlIfNeeded_(A,B))},goog.i18n.bidi.isRtlExitText=goog.i18n.bidi.endsWithRtl,goog.i18n.bidi.rtlLocalesRe_=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i,goog.i18n.bidi.isRtlLanguage=function(A){return goog.i18n.bidi.rtlLocalesRe_.test(A)},goog.i18n.bidi.bracketGuardTextRe_=/(\(.*?\)+)|(\[.*?\]+)|(\{.*?\}+)|(<.*?>+)/g,goog.i18n.bidi.guardBracketInText=function(A,B){return B=(void 0===B?goog.i18n.bidi.hasAnyRtl(A):B)?goog.i18n.bidi.Format.RLM:goog.i18n.bidi.Format.LRM,A.replace(goog.i18n.bidi.bracketGuardTextRe_,B+"$&"+B)},goog.i18n.bidi.enforceRtlInHtml=function(A){return"<"==A.charAt(0)?A.replace(/<\w+/,"$& dir=rtl"):"\n<span dir=rtl>"+A+"</span>"},goog.i18n.bidi.enforceRtlInText=function(A){return goog.i18n.bidi.Format.RLE+A+goog.i18n.bidi.Format.PDF},goog.i18n.bidi.enforceLtrInHtml=function(A){return"<"==A.charAt(0)?A.replace(/<\w+/,"$& dir=ltr"):"\n<span dir=ltr>"+A+"</span>"},goog.i18n.bidi.enforceLtrInText=function(A){return goog.i18n.bidi.Format.LRE+A+goog.i18n.bidi.Format.PDF},goog.i18n.bidi.dimensionsRe_=/:\s*([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)\s+([.\d][.\w]*)/g,goog.i18n.bidi.leftRe_=/left/gi,goog.i18n.bidi.rightRe_=/right/gi,goog.i18n.bidi.tempRe_=/%%%%/g,goog.i18n.bidi.mirrorCSS=function(A){return A.replace(goog.i18n.bidi.dimensionsRe_,":$1 $4 $3 $2").replace(goog.i18n.bidi.leftRe_,"%%%%").replace(goog.i18n.bidi.rightRe_,goog.i18n.bidi.LEFT).replace(goog.i18n.bidi.tempRe_,goog.i18n.bidi.RIGHT)},goog.i18n.bidi.doubleQuoteSubstituteRe_=/([\u0591-\u05f2])"/g,goog.i18n.bidi.singleQuoteSubstituteRe_=/([\u0591-\u05f2])'/g,goog.i18n.bidi.normalizeHebrewQuote=function(A){return A.replace(goog.i18n.bidi.doubleQuoteSubstituteRe_,"$1״").replace(goog.i18n.bidi.singleQuoteSubstituteRe_,"$1׳")},goog.i18n.bidi.wordSeparatorRe_=/\s+/,goog.i18n.bidi.hasNumeralsRe_=/[\d\u06f0-\u06f9]/,goog.i18n.bidi.rtlDetectionThreshold_=.4,goog.i18n.bidi.estimateDirection=function(A,B){var N=0,U=0,H=!1;for(A=goog.i18n.bidi.stripHtmlIfNeeded_(A,B).split(goog.i18n.bidi.wordSeparatorRe_),B=0;B<A.length;B++){var W=A[B];goog.i18n.bidi.startsWithRtl(W)?(N++,U++):goog.i18n.bidi.isRequiredLtrRe_.test(W)?H=!0:goog.i18n.bidi.hasAnyLtr(W)?U++:goog.i18n.bidi.hasNumeralsRe_.test(W)&&(H=!0)}return 0==U?H?goog.i18n.bidi.Dir.LTR:goog.i18n.bidi.Dir.NEUTRAL:N/U>goog.i18n.bidi.rtlDetectionThreshold_?goog.i18n.bidi.Dir.RTL:goog.i18n.bidi.Dir.LTR},goog.i18n.bidi.detectRtlDirectionality=function(A,B){return goog.i18n.bidi.estimateDirection(A,B)==goog.i18n.bidi.Dir.RTL},goog.i18n.bidi.setElementDirAndAlign=function(A,B){A&&(B=goog.i18n.bidi.toDir(B))&&(A.style.textAlign=B==goog.i18n.bidi.Dir.RTL?goog.i18n.bidi.RIGHT:goog.i18n.bidi.LEFT,A.dir=B==goog.i18n.bidi.Dir.RTL?"rtl":"ltr")},goog.i18n.bidi.setElementDirByTextDirectionality=function(A,B){switch(goog.i18n.bidi.estimateDirection(B)){case goog.i18n.bidi.Dir.LTR:A.dir="ltr";break;case goog.i18n.bidi.Dir.RTL:A.dir="rtl";break;default:A.removeAttribute("dir")}},goog.i18n.bidi.DirectionalString=function(){},goog.html.TrustedResourceUrl=function(A,B){this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_=A===goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_&&B||"",this.TRUSTED_RESOURCE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.TrustedResourceUrl.prototype.implementsGoogStringTypedString=!0,goog.html.TrustedResourceUrl.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_.toString()},goog.html.TrustedResourceUrl.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.TrustedResourceUrl.prototype.getDirection=function(){return goog.i18n.bidi.Dir.LTR},goog.html.TrustedResourceUrl.prototype.cloneWithParams=function(A,B){var N=goog.html.TrustedResourceUrl.unwrap(this),U=(N=goog.html.TrustedResourceUrl.URL_PARAM_PARSER_.exec(N))[3]||"";return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(N[1]+goog.html.TrustedResourceUrl.stringifyParams_("?",N[2]||"",A)+goog.html.TrustedResourceUrl.stringifyParams_("#",U,B))},goog.DEBUG&&(goog.html.TrustedResourceUrl.prototype.toString=function(){return"TrustedResourceUrl{"+this.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_+"}"}),goog.html.TrustedResourceUrl.unwrap=function(A){return goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(A).toString()},goog.html.TrustedResourceUrl.unwrapTrustedScriptURL=function(A){return A instanceof goog.html.TrustedResourceUrl&&A.constructor===goog.html.TrustedResourceUrl&&A.TRUSTED_RESOURCE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseTrustedResourceUrlWrappedValue_:(goog.asserts.fail("expected object of type TrustedResourceUrl, got '"+A+"' of type "+goog.typeOf(A)),"type_error:TrustedResourceUrl")},goog.html.TrustedResourceUrl.format=function(A,B){var N=goog.string.Const.unwrap(A);if(!goog.html.TrustedResourceUrl.BASE_URL_.test(N))throw Error("Invalid TrustedResourceUrl format: "+N);return A=N.replace(goog.html.TrustedResourceUrl.FORMAT_MARKER_,function(A,U){if(!Object.prototype.hasOwnProperty.call(B,U))throw Error('Found marker, "'+U+'", in format string, "'+N+'", but no valid label mapping found in args: '+JSON.stringify(B));return(A=B[U])instanceof goog.string.Const?goog.string.Const.unwrap(A):encodeURIComponent(String(A))}),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.TrustedResourceUrl.FORMAT_MARKER_=/%{(\w+)}/g,goog.html.TrustedResourceUrl.BASE_URL_=/^((https:)?\/\/[0-9a-z.:[\]-]+\/|\/[^/\\]|[^:/\\%]+\/|[^:/\\%]*[?#]|about:blank#)/i,goog.html.TrustedResourceUrl.URL_PARAM_PARSER_=/^([^?#]*)(\?[^#]*)?(#[\s\S]*)?/,goog.html.TrustedResourceUrl.formatWithParams=function(A,B,N,U){return goog.html.TrustedResourceUrl.format(A,B).cloneWithParams(N,U)},goog.html.TrustedResourceUrl.fromConstant=function(A){return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(goog.string.Const.unwrap(A))},goog.html.TrustedResourceUrl.fromConstants=function(A){for(var B="",N=0;N<A.length;N++)B+=goog.string.Const.unwrap(A[N]);return goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(B)},goog.html.TrustedResourceUrl.fromSafeScript=function(A){return A=goog.fs.blob.getBlobWithProperties([goog.html.SafeScript.unwrap(A)],"text/javascript"),A=goog.fs.url.createObjectUrl(A),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.TrustedResourceUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse=function(A){return A=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createScriptURL(A):A,new goog.html.TrustedResourceUrl(goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_,A)},goog.html.TrustedResourceUrl.stringifyParams_=function(A,B,N){if(null==N)return B;if("string"==typeof N)return N?A+encodeURIComponent(N):"";for(var U in N){var H=N[U];H=Array.isArray(H)?H:[H];for(var W=0;W<H.length;W++){var j=H[W];null!=j&&(B||(B=A),B+=(B.length>A.length?"&":"")+encodeURIComponent(U)+"="+encodeURIComponent(String(j)))}}return B},goog.html.TrustedResourceUrl.CONSTRUCTOR_TOKEN_PRIVATE_={},goog.string.internal={},goog.string.internal.startsWith=function(A,B){return 0==A.lastIndexOf(B,0)},goog.string.internal.endsWith=function(A,B){var N=A.length-B.length;return 0<=N&&A.indexOf(B,N)==N},goog.string.internal.caseInsensitiveStartsWith=function(A,B){return 0==goog.string.internal.caseInsensitiveCompare(B,A.substr(0,B.length))},goog.string.internal.caseInsensitiveEndsWith=function(A,B){return 0==goog.string.internal.caseInsensitiveCompare(B,A.substr(A.length-B.length,B.length))},goog.string.internal.caseInsensitiveEquals=function(A,B){return A.toLowerCase()==B.toLowerCase()},goog.string.internal.isEmptyOrWhitespace=function(A){return/^[\s\xa0]*$/.test(A)},goog.string.internal.trim=goog.TRUSTED_SITE&&String.prototype.trim?function(A){return A.trim()}:function(A){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(A)[1]},goog.string.internal.caseInsensitiveCompare=function(A,B){return A=String(A).toLowerCase(),A<(B=String(B).toLowerCase())?-1:+(A!=B)},goog.string.internal.newLineToBr=function(A,B){return A.replace(/(\r\n|\r|\n)/g,B?"<br />":"<br>")},goog.string.internal.htmlEscape=function(A,B){if(B)A=A.replace(goog.string.internal.AMP_RE_,"&amp;").replace(goog.string.internal.LT_RE_,"&lt;").replace(goog.string.internal.GT_RE_,"&gt;").replace(goog.string.internal.QUOT_RE_,"&quot;").replace(goog.string.internal.SINGLE_QUOTE_RE_,"&#39;").replace(goog.string.internal.NULL_RE_,"&#0;");else{if(!goog.string.internal.ALL_RE_.test(A))return A;-1!=A.indexOf("&")&&(A=A.replace(goog.string.internal.AMP_RE_,"&amp;")),-1!=A.indexOf("<")&&(A=A.replace(goog.string.internal.LT_RE_,"&lt;")),-1!=A.indexOf(">")&&(A=A.replace(goog.string.internal.GT_RE_,"&gt;")),-1!=A.indexOf('"')&&(A=A.replace(goog.string.internal.QUOT_RE_,"&quot;")),-1!=A.indexOf("'")&&(A=A.replace(goog.string.internal.SINGLE_QUOTE_RE_,"&#39;")),-1!=A.indexOf("\0")&&(A=A.replace(goog.string.internal.NULL_RE_,"&#0;"))}return A},goog.string.internal.AMP_RE_=/&/g,goog.string.internal.LT_RE_=/</g,goog.string.internal.GT_RE_=/>/g,goog.string.internal.QUOT_RE_=/"/g,goog.string.internal.SINGLE_QUOTE_RE_=/'/g,goog.string.internal.NULL_RE_=/\x00/g,goog.string.internal.ALL_RE_=/[\x00&<>"']/,goog.string.internal.whitespaceEscape=function(A,B){return goog.string.internal.newLineToBr(A.replace(/  /g," &#160;"),B)},goog.string.internal.contains=function(A,B){return -1!=A.indexOf(B)},goog.string.internal.caseInsensitiveContains=function(A,B){return goog.string.internal.contains(A.toLowerCase(),B.toLowerCase())},goog.string.internal.compareVersions=function(A,B){var N=0;A=goog.string.internal.trim(String(A)).split("."),B=goog.string.internal.trim(String(B)).split(".");for(var U=Math.max(A.length,B.length),H=0;0==N&&H<U;H++){var W=A[H]||"",j=B[H]||"";do{if(W=/(\d*)(\D*)(.*)/.exec(W)||["","","",""],j=/(\d*)(\D*)(.*)/.exec(j)||["","","",""],0==W[0].length&&0==j[0].length)break;N=0==W[1].length?0:parseInt(W[1],10);var V=0==j[1].length?0:parseInt(j[1],10);N=goog.string.internal.compareElements_(N,V)||goog.string.internal.compareElements_(0==W[2].length,0==j[2].length)||goog.string.internal.compareElements_(W[2],j[2]),W=W[3],j=j[3]}while(0==N)}return N},goog.string.internal.compareElements_=function(A,B){return A<B?-1:+(A>B)},goog.html.SafeUrl=function(A,B){this.privateDoNotAccessOrElseSafeUrlWrappedValue_=A===goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_&&B||"",this.SAFE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeUrl.INNOCUOUS_STRING="about:invalid#zClosurez",goog.html.SafeUrl.prototype.implementsGoogStringTypedString=!0,goog.html.SafeUrl.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeUrlWrappedValue_.toString()},goog.html.SafeUrl.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.SafeUrl.prototype.getDirection=function(){return goog.i18n.bidi.Dir.LTR},goog.DEBUG&&(goog.html.SafeUrl.prototype.toString=function(){return"SafeUrl{"+this.privateDoNotAccessOrElseSafeUrlWrappedValue_+"}"}),goog.html.SafeUrl.unwrap=function(A){return A instanceof goog.html.SafeUrl&&A.constructor===goog.html.SafeUrl&&A.SAFE_URL_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseSafeUrlWrappedValue_:(goog.asserts.fail("expected object of type SafeUrl, got '"+A+"' of type "+goog.typeOf(A)),"type_error:SafeUrl")},goog.html.SafeUrl.fromConstant=function(A){return goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(goog.string.Const.unwrap(A))},goog.html.SAFE_MIME_TYPE_PATTERN_=/^(?:audio\/(?:3gpp2|3gpp|aac|L16|midi|mp3|mp4|mpeg|oga|ogg|opus|x-m4a|x-matroska|x-wav|wav|webm)|image\/(?:bmp|gif|jpeg|jpg|png|tiff|webp|x-icon)|text\/csv|video\/(?:mpeg|mp4|ogg|webm|quicktime|x-matroska))(?:;\w+=(?:\w+|"[\w;,= ]+"))*$/i,goog.html.SafeUrl.isSafeMimeType=function(A){return goog.html.SAFE_MIME_TYPE_PATTERN_.test(A)},goog.html.SafeUrl.fromBlob=function(A){return A=goog.html.SafeUrl.isSafeMimeType(A.type)?goog.fs.url.createObjectUrl(A):goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.fromMediaSource=function(A){return goog.asserts.assert("MediaSource"in goog.global,"No support for MediaSource"),A=A instanceof MediaSource?goog.fs.url.createObjectUrl(A):goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.DATA_URL_PATTERN_=/^data:(.*);base64,[a-z0-9+\/]+=*$/i,goog.html.SafeUrl.fromDataUrl=function(A){var B=(A=A.replace(/(%0A|%0D)/g,"")).match(goog.html.DATA_URL_PATTERN_);return B=B&&goog.html.SafeUrl.isSafeMimeType(B[1]),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(B?A:goog.html.SafeUrl.INNOCUOUS_STRING)},goog.html.SafeUrl.fromTelUrl=function(A){return goog.string.internal.caseInsensitiveStartsWith(A,"tel:")||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SIP_URL_PATTERN_=/^sip[s]?:[+a-z0-9_.!$%&'*\/=^`{|}~-]+@([a-z0-9-]+\.)+[a-z0-9]{2,63}$/i,goog.html.SafeUrl.fromSipUrl=function(A){return goog.html.SIP_URL_PATTERN_.test(decodeURIComponent(A))||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.fromFacebookMessengerUrl=function(A){return goog.string.internal.caseInsensitiveStartsWith(A,"fb-messenger://share")||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.fromWhatsAppUrl=function(A){return goog.string.internal.caseInsensitiveStartsWith(A,"whatsapp://send")||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.fromSmsUrl=function(A){return goog.string.internal.caseInsensitiveStartsWith(A,"sms:")&&goog.html.SafeUrl.isSmsUrlBodyValid_(A)||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.isSmsUrlBodyValid_=function(A){var B=A.indexOf("#");if(0<B&&(A=A.substring(0,B)),!(B=A.match(/[?&]body=/gi)))return!0;if(1<B.length)return!1;if(!(A=A.match(/[?&]body=([^&]*)/)[1]))return!0;try{decodeURIComponent(A)}catch(A){return!1}return/^(?:[a-z0-9\-_.~]|%[0-9a-f]{2})+$/i.test(A)},goog.html.SafeUrl.fromSshUrl=function(A){return goog.string.internal.caseInsensitiveStartsWith(A,"ssh://")||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeUrl.sanitizeChromeExtensionUrl=function(A,B){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^chrome-extension:\/\/([^\/]+)\//,A,B)},goog.html.SafeUrl.sanitizeFirefoxExtensionUrl=function(A,B){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^moz-extension:\/\/([^\/]+)\//,A,B)},goog.html.SafeUrl.sanitizeEdgeExtensionUrl=function(A,B){return goog.html.SafeUrl.sanitizeExtensionUrl_(/^ms-browser-extension:\/\/([^\/]+)\//,A,B)},goog.html.SafeUrl.sanitizeExtensionUrl_=function(A,B,N){return(A=A.exec(B))?(A=A[1],-1==(N instanceof goog.string.Const?[goog.string.Const.unwrap(N)]:N.map(function(A){return goog.string.Const.unwrap(A)})).indexOf(A)&&(B=goog.html.SafeUrl.INNOCUOUS_STRING)):B=goog.html.SafeUrl.INNOCUOUS_STRING,goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(B)},goog.html.SafeUrl.fromTrustedResourceUrl=function(A){return goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(goog.html.TrustedResourceUrl.unwrap(A))},goog.html.SAFE_URL_PATTERN_=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i,goog.html.SafeUrl.SAFE_URL_PATTERN=goog.html.SAFE_URL_PATTERN_,goog.html.SafeUrl.sanitize=function(A){return A instanceof goog.html.SafeUrl?A:(A="object"==typeof A&&A.implementsGoogStringTypedString?A.getTypedStringValue():String(A),goog.html.SAFE_URL_PATTERN_.test(A)||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A))},goog.html.SafeUrl.sanitizeAssertUnchanged=function(A,B){return A instanceof goog.html.SafeUrl?A:(A="object"==typeof A&&A.implementsGoogStringTypedString?A.getTypedStringValue():String(A),B&&/^data:/i.test(A)&&(B=goog.html.SafeUrl.fromDataUrl(A)).getTypedStringValue()==A)?B:(goog.asserts.assert(goog.html.SAFE_URL_PATTERN_.test(A),"%s does not match the safe URL pattern",A)||(A=goog.html.SafeUrl.INNOCUOUS_STRING),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(A))},goog.html.SafeUrl.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse=function(A){return new goog.html.SafeUrl(goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_,A)},goog.html.SafeUrl.ABOUT_BLANK=goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse("about:blank"),goog.html.SafeUrl.CONSTRUCTOR_TOKEN_PRIVATE_={},goog.html.SafeStyle=function(){this.privateDoNotAccessOrElseSafeStyleWrappedValue_="",this.SAFE_STYLE_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeStyle.prototype.implementsGoogStringTypedString=!0,goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeStyle.fromConstant=function(A){return 0===(A=goog.string.Const.unwrap(A)).length?goog.html.SafeStyle.EMPTY:(goog.asserts.assert(goog.string.internal.endsWith(A,";"),"Last character of style string is not ';': "+A),goog.asserts.assert(goog.string.internal.contains(A,":"),"Style string must contain at least one ':', to specify a \"name: value\" pair: "+A),goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(A))},goog.html.SafeStyle.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeStyleWrappedValue_},goog.DEBUG&&(goog.html.SafeStyle.prototype.toString=function(){return"SafeStyle{"+this.privateDoNotAccessOrElseSafeStyleWrappedValue_+"}"}),goog.html.SafeStyle.unwrap=function(A){return A instanceof goog.html.SafeStyle&&A.constructor===goog.html.SafeStyle&&A.SAFE_STYLE_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeStyle.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseSafeStyleWrappedValue_:(goog.asserts.fail("expected object of type SafeStyle, got '"+A+"' of type "+goog.typeOf(A)),"type_error:SafeStyle")},goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse=function(A){return(new goog.html.SafeStyle).initSecurityPrivateDoNotAccessOrElse_(A)},goog.html.SafeStyle.prototype.initSecurityPrivateDoNotAccessOrElse_=function(A){return this.privateDoNotAccessOrElseSafeStyleWrappedValue_=A,this},goog.html.SafeStyle.EMPTY=goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(""),goog.html.SafeStyle.INNOCUOUS_STRING="zClosurez",goog.html.SafeStyle.create=function(A){var B,N="";for(B in A){if(!/^[-_a-zA-Z0-9]+$/.test(B))throw Error("Name allows only [-_a-zA-Z0-9], got: "+B);var U=A[B];null!=U&&(N+=B+":"+(U=Array.isArray(U)?goog.array.map(U,goog.html.SafeStyle.sanitizePropertyValue_).join(" "):goog.html.SafeStyle.sanitizePropertyValue_(U))+";")}return N?goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(N):goog.html.SafeStyle.EMPTY},goog.html.SafeStyle.sanitizePropertyValue_=function(A){if(A instanceof goog.html.SafeUrl)return'url("'+goog.html.SafeUrl.unwrap(A).replace(/</g,"%3c").replace(/[\\"]/g,"\\$&")+'")';if(A=A instanceof goog.string.Const?goog.string.Const.unwrap(A):goog.html.SafeStyle.sanitizePropertyValueString_(String(A)),/[{;}]/.test(A))throw new goog.asserts.AssertionError("Value does not allow [{;}], got: %s.",[A]);return A},goog.html.SafeStyle.sanitizePropertyValueString_=function(A){var B=A.replace(goog.html.SafeStyle.FUNCTIONS_RE_,"$1").replace(goog.html.SafeStyle.FUNCTIONS_RE_,"$1").replace(goog.html.SafeStyle.URL_RE_,"url");if(!goog.html.SafeStyle.VALUE_RE_.test(B))return goog.asserts.fail("String value allows only "+goog.html.SafeStyle.VALUE_ALLOWED_CHARS_+" and simple functions, got: "+A),goog.html.SafeStyle.INNOCUOUS_STRING;if(goog.html.SafeStyle.COMMENT_RE_.test(A))return goog.asserts.fail("String value disallows comments, got: "+A),goog.html.SafeStyle.INNOCUOUS_STRING;if(!goog.html.SafeStyle.hasBalancedQuotes_(A))return goog.asserts.fail("String value requires balanced quotes, got: "+A),goog.html.SafeStyle.INNOCUOUS_STRING;if(!goog.html.SafeStyle.hasBalancedSquareBrackets_(A))return goog.asserts.fail("String value requires balanced square brackets and one identifier per pair of brackets, got: "+A),goog.html.SafeStyle.INNOCUOUS_STRING;return goog.html.SafeStyle.sanitizeUrl_(A)},goog.html.SafeStyle.hasBalancedQuotes_=function(A){for(var B=!0,N=!0,U=0;U<A.length;U++){var H=A.charAt(U);"'"==H&&N?B=!B:'"'==H&&B&&(N=!N)}return B&&N},goog.html.SafeStyle.hasBalancedSquareBrackets_=function(A){for(var B=!0,N=/^[-_a-zA-Z0-9]$/,U=0;U<A.length;U++){var H=A.charAt(U);if("]"==H){if(B)return!1;B=!0}else if("["==H){if(!B)return!1;B=!1}else if(!B&&!N.test(H))return!1}return B},goog.html.SafeStyle.VALUE_ALLOWED_CHARS_="[-,.\"'%_!# a-zA-Z0-9\\[\\]]",goog.html.SafeStyle.VALUE_RE_=RegExp("^"+goog.html.SafeStyle.VALUE_ALLOWED_CHARS_+"+$"),goog.html.SafeStyle.URL_RE_=/\b(url\([ \t\n]*)('[ -&(-\[\]-~]*'|"[ !#-\[\]-~]*"|[!#-&*-\[\]-~]*)([ \t\n]*\))/g,goog.html.SafeStyle.ALLOWED_FUNCTIONS_="calc cubic-bezier fit-content hsl hsla linear-gradient matrix minmax repeat rgb rgba (rotate|scale|translate)(X|Y|Z|3d)?".split(" "),goog.html.SafeStyle.FUNCTIONS_RE_=RegExp("\\b("+goog.html.SafeStyle.ALLOWED_FUNCTIONS_.join("|")+")\\([-+*/0-9a-z.%\\[\\], ]+\\)","g"),goog.html.SafeStyle.COMMENT_RE_=/\/\*/,goog.html.SafeStyle.sanitizeUrl_=function(A){return A.replace(goog.html.SafeStyle.URL_RE_,function(A,B,N,U){var H="";return N=N.replace(/^(['"])(.*)\1$/,function(A,B,N){return H=B,N}),A=goog.html.SafeUrl.sanitize(N).getTypedStringValue(),B+H+A+H+U})},goog.html.SafeStyle.concat=function(A){var B="",c=function(A){Array.isArray(A)?goog.array.forEach(A,c):B+=goog.html.SafeStyle.unwrap(A)};return goog.array.forEach(arguments,c),B?goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(B):goog.html.SafeStyle.EMPTY},goog.html.SafeStyleSheet=function(){this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_="",this.SAFE_STYLE_SHEET_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_},goog.html.SafeStyleSheet.prototype.implementsGoogStringTypedString=!0,goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeStyleSheet.createRule=function(A,B){if(goog.string.internal.contains(A,"<"))throw Error("Selector does not allow '<', got: "+A);var N=A.replace(/('|")((?!\1)[^\r\n\f\\]|\\[\s\S])*\1/g,"");if(!/^[-_a-zA-Z0-9#.:* ,>+~[\]()=^$|]+$/.test(N))throw Error("Selector allows only [-_a-zA-Z0-9#.:* ,>+~[\\]()=^$|] and strings, got: "+A);if(!goog.html.SafeStyleSheet.hasBalancedBrackets_(N))throw Error("() and [] in selector must be balanced, got: "+A);return B instanceof goog.html.SafeStyle||(B=goog.html.SafeStyle.create(B)),A=A+"{"+goog.html.SafeStyle.unwrap(B).replace(/</g,"\\3C ")+"}",goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(A)},goog.html.SafeStyleSheet.hasBalancedBrackets_=function(A){for(var B={"(":")","[":"]"},N=[],U=0;U<A.length;U++){var H=A[U];if(B[H])N.push(B[H]);else if(goog.object.contains(B,H)&&N.pop()!=H)return!1}return 0==N.length},goog.html.SafeStyleSheet.concat=function(A){var B="",c=function(A){Array.isArray(A)?goog.array.forEach(A,c):B+=goog.html.SafeStyleSheet.unwrap(A)};return goog.array.forEach(arguments,c),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(B)},goog.html.SafeStyleSheet.fromConstant=function(A){return 0===(A=goog.string.Const.unwrap(A)).length?goog.html.SafeStyleSheet.EMPTY:(goog.asserts.assert(!goog.string.internal.contains(A,"<"),"Forbidden '<' character in style sheet string: "+A),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(A))},goog.html.SafeStyleSheet.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_},goog.DEBUG&&(goog.html.SafeStyleSheet.prototype.toString=function(){return"SafeStyleSheet{"+this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_+"}"}),goog.html.SafeStyleSheet.unwrap=function(A){return A instanceof goog.html.SafeStyleSheet&&A.constructor===goog.html.SafeStyleSheet&&A.SAFE_STYLE_SHEET_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeStyleSheet.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_:(goog.asserts.fail("expected object of type SafeStyleSheet, got '"+A+"' of type "+goog.typeOf(A)),"type_error:SafeStyleSheet")},goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse=function(A){return(new goog.html.SafeStyleSheet).initSecurityPrivateDoNotAccessOrElse_(A)},goog.html.SafeStyleSheet.prototype.initSecurityPrivateDoNotAccessOrElse_=function(A){return this.privateDoNotAccessOrElseSafeStyleSheetWrappedValue_=A,this},goog.html.SafeStyleSheet.EMPTY=goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(""),goog.labs={},goog.labs.userAgent={},goog.labs.userAgent.util={},goog.labs.userAgent.util.getNativeUserAgentString_=function(){var A=goog.labs.userAgent.util.getNavigator_();return A&&(A=A.userAgent)?A:""},goog.labs.userAgent.util.getNavigator_=function(){return goog.global.navigator},goog.labs.userAgent.util.userAgent_=goog.labs.userAgent.util.getNativeUserAgentString_(),goog.labs.userAgent.util.setUserAgent=function(A){goog.labs.userAgent.util.userAgent_=A||goog.labs.userAgent.util.getNativeUserAgentString_()},goog.labs.userAgent.util.getUserAgent=function(){return goog.labs.userAgent.util.userAgent_},goog.labs.userAgent.util.matchUserAgent=function(A){var B=goog.labs.userAgent.util.getUserAgent();return goog.string.internal.contains(B,A)},goog.labs.userAgent.util.matchUserAgentIgnoreCase=function(A){var B=goog.labs.userAgent.util.getUserAgent();return goog.string.internal.caseInsensitiveContains(B,A)},goog.labs.userAgent.util.extractVersionTuples=function(A){for(var B,N=/(\w[\w ]+)\/([^\s]+)\s*(?:\((.*?)\))?/g,U=[];B=N.exec(A);)U.push([B[1],B[2],B[3]||void 0]);return U},goog.labs.userAgent.browser={},goog.labs.userAgent.browser.matchOpera_=function(){return goog.labs.userAgent.util.matchUserAgent("Opera")},goog.labs.userAgent.browser.matchIE_=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.browser.matchEdgeHtml_=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.browser.matchEdgeChromium_=function(){return goog.labs.userAgent.util.matchUserAgent("Edg/")},goog.labs.userAgent.browser.matchOperaChromium_=function(){return goog.labs.userAgent.util.matchUserAgent("OPR")},goog.labs.userAgent.browser.matchFirefox_=function(){return goog.labs.userAgent.util.matchUserAgent("Firefox")||goog.labs.userAgent.util.matchUserAgent("FxiOS")},goog.labs.userAgent.browser.matchSafari_=function(){return goog.labs.userAgent.util.matchUserAgent("Safari")&&!(goog.labs.userAgent.browser.matchChrome_()||goog.labs.userAgent.browser.matchCoast_()||goog.labs.userAgent.browser.matchOpera_()||goog.labs.userAgent.browser.matchEdgeHtml_()||goog.labs.userAgent.browser.matchEdgeChromium_()||goog.labs.userAgent.browser.matchOperaChromium_()||goog.labs.userAgent.browser.matchFirefox_()||goog.labs.userAgent.browser.isSilk()||goog.labs.userAgent.util.matchUserAgent("Android"))},goog.labs.userAgent.browser.matchCoast_=function(){return goog.labs.userAgent.util.matchUserAgent("Coast")},goog.labs.userAgent.browser.matchIosWebview_=function(){return(goog.labs.userAgent.util.matchUserAgent("iPad")||goog.labs.userAgent.util.matchUserAgent("iPhone"))&&!goog.labs.userAgent.browser.matchSafari_()&&!goog.labs.userAgent.browser.matchChrome_()&&!goog.labs.userAgent.browser.matchCoast_()&&!goog.labs.userAgent.browser.matchFirefox_()&&goog.labs.userAgent.util.matchUserAgent("AppleWebKit")},goog.labs.userAgent.browser.matchChrome_=function(){return(goog.labs.userAgent.util.matchUserAgent("Chrome")||goog.labs.userAgent.util.matchUserAgent("CriOS"))&&!goog.labs.userAgent.browser.matchEdgeHtml_()},goog.labs.userAgent.browser.matchAndroidBrowser_=function(){return goog.labs.userAgent.util.matchUserAgent("Android")&&!(goog.labs.userAgent.browser.isChrome()||goog.labs.userAgent.browser.isFirefox()||goog.labs.userAgent.browser.isOpera()||goog.labs.userAgent.browser.isSilk())},goog.labs.userAgent.browser.isOpera=goog.labs.userAgent.browser.matchOpera_,goog.labs.userAgent.browser.isIE=goog.labs.userAgent.browser.matchIE_,goog.labs.userAgent.browser.isEdge=goog.labs.userAgent.browser.matchEdgeHtml_,goog.labs.userAgent.browser.isEdgeChromium=goog.labs.userAgent.browser.matchEdgeChromium_,goog.labs.userAgent.browser.isOperaChromium=goog.labs.userAgent.browser.matchOperaChromium_,goog.labs.userAgent.browser.isFirefox=goog.labs.userAgent.browser.matchFirefox_,goog.labs.userAgent.browser.isSafari=goog.labs.userAgent.browser.matchSafari_,goog.labs.userAgent.browser.isCoast=goog.labs.userAgent.browser.matchCoast_,goog.labs.userAgent.browser.isIosWebview=goog.labs.userAgent.browser.matchIosWebview_,goog.labs.userAgent.browser.isChrome=goog.labs.userAgent.browser.matchChrome_,goog.labs.userAgent.browser.isAndroidBrowser=goog.labs.userAgent.browser.matchAndroidBrowser_,goog.labs.userAgent.browser.isSilk=function(){return goog.labs.userAgent.util.matchUserAgent("Silk")},goog.labs.userAgent.browser.getVersion=function(){function a(A){return B[A=goog.array.find(A,N)]||""}var A=goog.labs.userAgent.util.getUserAgent();if(goog.labs.userAgent.browser.isIE())return goog.labs.userAgent.browser.getIEVersion_(A);A=goog.labs.userAgent.util.extractVersionTuples(A);var B={};goog.array.forEach(A,function(A){B[A[0]]=A[1]});var N=goog.partial(goog.object.containsKey,B);return goog.labs.userAgent.browser.isOpera()?a(["Version","Opera"]):goog.labs.userAgent.browser.isEdge()?a(["Edge"]):goog.labs.userAgent.browser.isEdgeChromium()?a(["Edg"]):goog.labs.userAgent.browser.isChrome()?a(["Chrome","CriOS","HeadlessChrome"]):(A=A[2])&&A[1]||""},goog.labs.userAgent.browser.isVersionOrHigher=function(A){return 0<=goog.string.internal.compareVersions(goog.labs.userAgent.browser.getVersion(),A)},goog.labs.userAgent.browser.getIEVersion_=function(A){var B=/rv: *([\d\.]*)/.exec(A);if(B&&B[1])return B[1];B="";var N=/MSIE +([\d\.]+)/.exec(A);if(N&&N[1]){if(A=/Trident\/(\d.\d)/.exec(A),"7.0"==N[1]){if(A&&A[1])switch(A[1]){case"4.0":B="8.0";break;case"5.0":B="9.0";break;case"6.0":B="10.0";break;case"7.0":B="11.0"}else B="7.0"}else B=N[1]}return B},goog.html.SafeHtml=function(){this.privateDoNotAccessOrElseSafeHtmlWrappedValue_="",this.SAFE_HTML_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_=goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_,this.dir_=null},goog.html.SafeHtml.ENABLE_ERROR_MESSAGES=goog.DEBUG,goog.html.SafeHtml.SUPPORT_STYLE_ATTRIBUTE=!0,goog.html.SafeHtml.prototype.implementsGoogI18nBidiDirectionalString=!0,goog.html.SafeHtml.prototype.getDirection=function(){return this.dir_},goog.html.SafeHtml.prototype.implementsGoogStringTypedString=!0,goog.html.SafeHtml.prototype.getTypedStringValue=function(){return this.privateDoNotAccessOrElseSafeHtmlWrappedValue_.toString()},goog.DEBUG&&(goog.html.SafeHtml.prototype.toString=function(){return"SafeHtml{"+this.privateDoNotAccessOrElseSafeHtmlWrappedValue_+"}"}),goog.html.SafeHtml.unwrap=function(A){return goog.html.SafeHtml.unwrapTrustedHTML(A).toString()},goog.html.SafeHtml.unwrapTrustedHTML=function(A){return A instanceof goog.html.SafeHtml&&A.constructor===goog.html.SafeHtml&&A.SAFE_HTML_TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_===goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_?A.privateDoNotAccessOrElseSafeHtmlWrappedValue_:(goog.asserts.fail("expected object of type SafeHtml, got '"+A+"' of type "+goog.typeOf(A)),"type_error:SafeHtml")},goog.html.SafeHtml.htmlEscape=function(A){if(A instanceof goog.html.SafeHtml)return A;var B="object"==typeof A,N=null;return B&&A.implementsGoogI18nBidiDirectionalString&&(N=A.getDirection()),A=B&&A.implementsGoogStringTypedString?A.getTypedStringValue():String(A),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.htmlEscape(A),N)},goog.html.SafeHtml.htmlEscapePreservingNewlines=function(A){return A instanceof goog.html.SafeHtml?A:(A=goog.html.SafeHtml.htmlEscape(A),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.newLineToBr(goog.html.SafeHtml.unwrap(A)),A.getDirection()))},goog.html.SafeHtml.htmlEscapePreservingNewlinesAndSpaces=function(A){return A instanceof goog.html.SafeHtml?A:(A=goog.html.SafeHtml.htmlEscape(A),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(goog.string.internal.whitespaceEscape(goog.html.SafeHtml.unwrap(A)),A.getDirection()))},goog.html.SafeHtml.from=goog.html.SafeHtml.htmlEscape,goog.html.SafeHtml.comment=function(A){return goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("\x3c!--"+goog.string.internal.htmlEscape(A)+"--\x3e",null)},goog.html.SafeHtml.VALID_NAMES_IN_TAG_=/^[a-zA-Z0-9-]+$/,goog.html.SafeHtml.URL_ATTRIBUTES_={action:!0,cite:!0,data:!0,formaction:!0,href:!0,manifest:!0,poster:!0,src:!0},goog.html.SafeHtml.NOT_ALLOWED_TAG_NAMES_={APPLET:!0,BASE:!0,EMBED:!0,IFRAME:!0,LINK:!0,MATH:!0,META:!0,OBJECT:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},goog.html.SafeHtml.create=function(A,B,N){return goog.html.SafeHtml.verifyTagName(String(A)),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse(String(A),B,N)},goog.html.SafeHtml.verifyTagName=function(A){if(!goog.html.SafeHtml.VALID_NAMES_IN_TAG_.test(A))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"Invalid tag name <"+A+">.":"");if(A.toUpperCase()in goog.html.SafeHtml.NOT_ALLOWED_TAG_NAMES_)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"Tag name <"+A+"> is not allowed for SafeHtml.":"")},goog.html.SafeHtml.createIframe=function(A,B,N,U){A&&goog.html.TrustedResourceUrl.unwrap(A);var H={};return H.src=A||null,H.srcdoc=B&&goog.html.SafeHtml.unwrap(B),A=goog.html.SafeHtml.combineAttributes(H,{sandbox:""},N),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("iframe",A,U)},goog.html.SafeHtml.createSandboxIframe=function(A,B,N,U){if(!goog.html.SafeHtml.canUseSandboxIframe())throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?"The browser does not support sandboxed iframes.":"");var H={};return H.src=A?goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitize(A)):null,H.srcdoc=B||null,H.sandbox="",A=goog.html.SafeHtml.combineAttributes(H,{},N),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("iframe",A,U)},goog.html.SafeHtml.canUseSandboxIframe=function(){return goog.global.HTMLIFrameElement&&"sandbox"in goog.global.HTMLIFrameElement.prototype},goog.html.SafeHtml.createScriptSrc=function(A,B){return goog.html.TrustedResourceUrl.unwrap(A),A=goog.html.SafeHtml.combineAttributes({src:A},{},B),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("script",A)},goog.html.SafeHtml.createScript=function(A,B){for(var N in B){var U=N.toLowerCase();if("language"==U||"src"==U||"text"==U||"type"==U)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Cannot set "'+U+'" attribute':"")}for(U=0,N="",A=goog.array.concat(A);U<A.length;U++)N+=goog.html.SafeScript.unwrap(A[U]);return A=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(N,goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("script",B,A)},goog.html.SafeHtml.createStyle=function(A,B){B=goog.html.SafeHtml.combineAttributes({type:"text/css"},{},B);var N="";A=goog.array.concat(A);for(var U=0;U<A.length;U++)N+=goog.html.SafeStyleSheet.unwrap(A[U]);return A=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(N,goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("style",B,A)},goog.html.SafeHtml.createMetaRefresh=function(A,B){return A=goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitize(A)),(goog.labs.userAgent.browser.isIE()||goog.labs.userAgent.browser.isEdge())&&goog.string.internal.contains(A,";")&&(A="'"+A.replace(/'/g,"%27")+"'"),goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse("meta",{"http-equiv":"refresh",content:(B||0)+"; url="+A})},goog.html.SafeHtml.getAttrNameAndValue_=function(A,B,N){if(N instanceof goog.string.Const)N=goog.string.Const.unwrap(N);else if("style"==B.toLowerCase()){if(goog.html.SafeHtml.SUPPORT_STYLE_ATTRIBUTE)N=goog.html.SafeHtml.getStyleValue_(N);else throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "style" not supported.':"")}else{if(/^on/i.test(B))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "'+B+'" requires goog.string.Const value, "'+N+'" given.':"");if(B.toLowerCase()in goog.html.SafeHtml.URL_ATTRIBUTES_){if(N instanceof goog.html.TrustedResourceUrl)N=goog.html.TrustedResourceUrl.unwrap(N);else if(N instanceof goog.html.SafeUrl)N=goog.html.SafeUrl.unwrap(N);else if("string"==typeof N)N=goog.html.SafeUrl.sanitize(N).getTypedStringValue();else throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Attribute "'+B+'" on tag "'+A+'" requires goog.html.SafeUrl, goog.string.Const, or string, value "'+N+'" given.':"")}}return N.implementsGoogStringTypedString&&(N=N.getTypedStringValue()),goog.asserts.assert("string"==typeof N||"number"==typeof N,"String or number value expected, got "+typeof N+" with value: "+N),B+'="'+goog.string.internal.htmlEscape(String(N))+'"'},goog.html.SafeHtml.getStyleValue_=function(A){if(!goog.isObject(A))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'The "style" attribute requires goog.html.SafeStyle or map of style properties, '+typeof A+" given: "+A:"");return A instanceof goog.html.SafeStyle||(A=goog.html.SafeStyle.create(A)),goog.html.SafeStyle.unwrap(A)},goog.html.SafeHtml.createWithDir=function(A,B,N,U){return(B=goog.html.SafeHtml.create(B,N,U)).dir_=A,B},goog.html.SafeHtml.join=function(A,B){var N=(A=goog.html.SafeHtml.htmlEscape(A)).getDirection(),U=[],e=function(A){Array.isArray(A)?goog.array.forEach(A,e):(A=goog.html.SafeHtml.htmlEscape(A),U.push(goog.html.SafeHtml.unwrap(A)),A=A.getDirection(),N==goog.i18n.bidi.Dir.NEUTRAL?N=A:A!=goog.i18n.bidi.Dir.NEUTRAL&&N!=A&&(N=null))};return goog.array.forEach(B,e),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(U.join(goog.html.SafeHtml.unwrap(A)),N)},goog.html.SafeHtml.concat=function(A){return goog.html.SafeHtml.join(goog.html.SafeHtml.EMPTY,Array.prototype.slice.call(arguments))},goog.html.SafeHtml.concatWithDir=function(A,B){var N=goog.html.SafeHtml.concat(goog.array.slice(arguments,1));return N.dir_=A,N},goog.html.SafeHtml.TYPE_MARKER_GOOG_HTML_SECURITY_PRIVATE_={},goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse=function(A,B){return(new goog.html.SafeHtml).initSecurityPrivateDoNotAccessOrElse_(A,B)},goog.html.SafeHtml.prototype.initSecurityPrivateDoNotAccessOrElse_=function(A,B){return this.privateDoNotAccessOrElseSafeHtmlWrappedValue_=goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY?goog.html.trustedtypes.PRIVATE_DO_NOT_ACCESS_OR_ELSE_POLICY.createHTML(A):A,this.dir_=B,this},goog.html.SafeHtml.createSafeHtmlTagSecurityPrivateDoNotAccessOrElse=function(A,B,N){var U=null,H="<"+A+goog.html.SafeHtml.stringifyAttributes(A,B);return null==N?N=[]:Array.isArray(N)||(N=[N]),goog.dom.tags.isVoidTag(A.toLowerCase())?(goog.asserts.assert(!N.length,"Void tag <"+A+"> does not allow content."),H+=">"):(U=goog.html.SafeHtml.concat(N),H+=">"+goog.html.SafeHtml.unwrap(U)+"</"+A+">",U=U.getDirection()),(A=B&&B.dir)&&(U=/^(ltr|rtl|auto)$/i.test(A)?goog.i18n.bidi.Dir.NEUTRAL:null),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(H,U)},goog.html.SafeHtml.stringifyAttributes=function(A,B){var N="";if(B)for(var U in B){if(!goog.html.SafeHtml.VALID_NAMES_IN_TAG_.test(U))throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Invalid attribute name "'+U+'".':"");var H=B[U];null!=H&&(N+=" "+goog.html.SafeHtml.getAttrNameAndValue_(A,U,H))}return N},goog.html.SafeHtml.combineAttributes=function(A,B,N){var U,H={};for(U in A)goog.asserts.assert(U.toLowerCase()==U,"Must be lower case"),H[U]=A[U];for(U in B)goog.asserts.assert(U.toLowerCase()==U,"Must be lower case"),H[U]=B[U];if(N)for(U in N){var W=U.toLowerCase();if(W in A)throw Error(goog.html.SafeHtml.ENABLE_ERROR_MESSAGES?'Cannot override "'+W+'" attribute, got "'+U+'" with value "'+N[U]+'"':"");W in B&&delete H[W],H[U]=N[U]}return H},goog.html.SafeHtml.DOCTYPE_HTML=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("<!DOCTYPE html>",goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.EMPTY=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("",goog.i18n.bidi.Dir.NEUTRAL),goog.html.SafeHtml.BR=goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse("<br>",goog.i18n.bidi.Dir.NEUTRAL),goog.html.uncheckedconversions={},goog.html.uncheckedconversions.safeHtmlFromStringKnownToSatisfyTypeContract=function(A,B,N){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.SafeHtml.createSafeHtmlSecurityPrivateDoNotAccessOrElse(B,N||null)},goog.html.uncheckedconversions.safeScriptFromStringKnownToSatisfyTypeContract=function(A,B){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.SafeScript.createSafeScriptSecurityPrivateDoNotAccessOrElse(B)},goog.html.uncheckedconversions.safeStyleFromStringKnownToSatisfyTypeContract=function(A,B){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.SafeStyle.createSafeStyleSecurityPrivateDoNotAccessOrElse(B)},goog.html.uncheckedconversions.safeStyleSheetFromStringKnownToSatisfyTypeContract=function(A,B){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.SafeStyleSheet.createSafeStyleSheetSecurityPrivateDoNotAccessOrElse(B)},goog.html.uncheckedconversions.safeUrlFromStringKnownToSatisfyTypeContract=function(A,B){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.SafeUrl.createSafeUrlSecurityPrivateDoNotAccessOrElse(B)},goog.html.uncheckedconversions.trustedResourceUrlFromStringKnownToSatisfyTypeContract=function(A,B){return goog.asserts.assertString(goog.string.Const.unwrap(A),"must provide justification"),goog.asserts.assert(!goog.string.internal.isEmptyOrWhitespace(goog.string.Const.unwrap(A)),"must provide non-empty justification"),goog.html.TrustedResourceUrl.createTrustedResourceUrlSecurityPrivateDoNotAccessOrElse(B)},goog.dom.safe={},goog.dom.safe.InsertAdjacentHtmlPosition={AFTERBEGIN:"afterbegin",AFTEREND:"afterend",BEFOREBEGIN:"beforebegin",BEFOREEND:"beforeend"},goog.dom.safe.insertAdjacentHtml=function(A,B,N){A.insertAdjacentHTML(B,goog.html.SafeHtml.unwrapTrustedHTML(N))},goog.dom.safe.SET_INNER_HTML_DISALLOWED_TAGS_={MATH:!0,SCRIPT:!0,STYLE:!0,SVG:!0,TEMPLATE:!0},goog.dom.safe.isInnerHtmlCleanupRecursive_=goog.functions.cacheReturnValue(function(){goog.DEBUG;var A=document.createElement("div"),B=document.createElement("div");return B.appendChild(document.createElement("div")),A.appendChild(B),(!goog.DEBUG||!!A.firstChild)&&(B=A.firstChild.firstChild,A.innerHTML=goog.html.SafeHtml.unwrapTrustedHTML(goog.html.SafeHtml.EMPTY),!B.parentElement)}),goog.dom.safe.unsafeSetInnerHtmlDoNotUseOrElse=function(A,B){if(goog.dom.safe.isInnerHtmlCleanupRecursive_())for(;A.lastChild;)A.removeChild(A.lastChild);A.innerHTML=goog.html.SafeHtml.unwrapTrustedHTML(B)},goog.dom.safe.setInnerHtml=function(A,B){if(goog.asserts.ENABLE_ASSERTS){var N=A.tagName.toUpperCase();if(goog.dom.safe.SET_INNER_HTML_DISALLOWED_TAGS_[N])throw Error("goog.dom.safe.setInnerHtml cannot be used to set content of "+A.tagName+".")}goog.dom.safe.unsafeSetInnerHtmlDoNotUseOrElse(A,B)},goog.dom.safe.setOuterHtml=function(A,B){A.outerHTML=goog.html.SafeHtml.unwrapTrustedHTML(B)},goog.dom.safe.setFormElementAction=function(A,B){B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),goog.dom.asserts.assertIsHTMLFormElement(A).action=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setButtonFormAction=function(A,B){B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),goog.dom.asserts.assertIsHTMLButtonElement(A).formAction=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setInputFormAction=function(A,B){B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),goog.dom.asserts.assertIsHTMLInputElement(A).formAction=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setStyle=function(A,B){A.style.cssText=goog.html.SafeStyle.unwrap(B)},goog.dom.safe.documentWrite=function(A,B){A.write(goog.html.SafeHtml.unwrapTrustedHTML(B))},goog.dom.safe.setAnchorHref=function(A,B){goog.dom.asserts.assertIsHTMLAnchorElement(A),B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),A.href=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setImageSrc=function(A,B){if(goog.dom.asserts.assertIsHTMLImageElement(A),!(B instanceof goog.html.SafeUrl)){var N=/^data:image\//i.test(B);B=goog.html.SafeUrl.sanitizeAssertUnchanged(B,N)}A.src=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setAudioSrc=function(A,B){if(goog.dom.asserts.assertIsHTMLAudioElement(A),!(B instanceof goog.html.SafeUrl)){var N=/^data:audio\//i.test(B);B=goog.html.SafeUrl.sanitizeAssertUnchanged(B,N)}A.src=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setVideoSrc=function(A,B){if(goog.dom.asserts.assertIsHTMLVideoElement(A),!(B instanceof goog.html.SafeUrl)){var N=/^data:video\//i.test(B);B=goog.html.SafeUrl.sanitizeAssertUnchanged(B,N)}A.src=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.setEmbedSrc=function(A,B){goog.dom.asserts.assertIsHTMLEmbedElement(A),A.src=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(B)},goog.dom.safe.setFrameSrc=function(A,B){goog.dom.asserts.assertIsHTMLFrameElement(A),A.src=goog.html.TrustedResourceUrl.unwrap(B)},goog.dom.safe.setIframeSrc=function(A,B){goog.dom.asserts.assertIsHTMLIFrameElement(A),A.src=goog.html.TrustedResourceUrl.unwrap(B)},goog.dom.safe.setIframeSrcdoc=function(A,B){goog.dom.asserts.assertIsHTMLIFrameElement(A),A.srcdoc=goog.html.SafeHtml.unwrapTrustedHTML(B)},goog.dom.safe.setLinkHrefAndRel=function(A,B,N){goog.dom.asserts.assertIsHTMLLinkElement(A),A.rel=N,goog.string.internal.caseInsensitiveContains(N,"stylesheet")?(goog.asserts.assert(B instanceof goog.html.TrustedResourceUrl,'URL must be TrustedResourceUrl because "rel" contains "stylesheet"'),A.href=goog.html.TrustedResourceUrl.unwrap(B)):A.href=B instanceof goog.html.TrustedResourceUrl?goog.html.TrustedResourceUrl.unwrap(B):B instanceof goog.html.SafeUrl?goog.html.SafeUrl.unwrap(B):goog.html.SafeUrl.unwrap(goog.html.SafeUrl.sanitizeAssertUnchanged(B))},goog.dom.safe.setObjectData=function(A,B){goog.dom.asserts.assertIsHTMLObjectElement(A),A.data=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(B)},goog.dom.safe.setScriptSrc=function(A,B){goog.dom.asserts.assertIsHTMLScriptElement(A),A.src=goog.html.TrustedResourceUrl.unwrapTrustedScriptURL(B),(B=goog.getScriptNonce())&&A.setAttribute("nonce",B)},goog.dom.safe.setScriptContent=function(A,B){goog.dom.asserts.assertIsHTMLScriptElement(A),A.text=goog.html.SafeScript.unwrapTrustedScript(B),(B=goog.getScriptNonce())&&A.setAttribute("nonce",B)},goog.dom.safe.setLocationHref=function(A,B){goog.dom.asserts.assertIsLocation(A),B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),A.href=goog.html.SafeUrl.unwrap(B)},goog.dom.safe.assignLocation=function(A,B){goog.dom.asserts.assertIsLocation(A),B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),A.assign(goog.html.SafeUrl.unwrap(B))},goog.dom.safe.replaceLocation=function(A,B){B=B instanceof goog.html.SafeUrl?B:goog.html.SafeUrl.sanitizeAssertUnchanged(B),A.replace(goog.html.SafeUrl.unwrap(B))},goog.dom.safe.openInWindow=function(A,B,N,U,H){return A=A instanceof goog.html.SafeUrl?A:goog.html.SafeUrl.sanitizeAssertUnchanged(A),B=B||goog.global,N=N instanceof goog.string.Const?goog.string.Const.unwrap(N):N||"",B.open(goog.html.SafeUrl.unwrap(A),N,U,H)},goog.dom.safe.parseFromStringHtml=function(A,B){return goog.dom.safe.parseFromString(A,B,"text/html")},goog.dom.safe.parseFromString=function(A,B,N){return A.parseFromString(goog.html.SafeHtml.unwrapTrustedHTML(B),N)},goog.dom.safe.createImageFromBlob=function(A){if(!/^image\/.*/g.test(A.type))throw Error("goog.dom.safe.createImageFromBlob only accepts MIME type image/.*.");var B=goog.global.URL.createObjectURL(A);return(A=new goog.global.Image).onload=function(){goog.global.URL.revokeObjectURL(B)},goog.dom.safe.setImageSrc(A,goog.html.uncheckedconversions.safeUrlFromStringKnownToSatisfyTypeContract(goog.string.Const.from("Image blob URL."),B)),A},goog.string.DETECT_DOUBLE_ESCAPING=!1,goog.string.FORCE_NON_DOM_HTML_UNESCAPING=!1,goog.string.Unicode={NBSP:"\xa0"},goog.string.startsWith=goog.string.internal.startsWith,goog.string.endsWith=goog.string.internal.endsWith,goog.string.caseInsensitiveStartsWith=goog.string.internal.caseInsensitiveStartsWith,goog.string.caseInsensitiveEndsWith=goog.string.internal.caseInsensitiveEndsWith,goog.string.caseInsensitiveEquals=goog.string.internal.caseInsensitiveEquals,goog.string.subs=function(A,B){for(var N=A.split("%s"),U="",H=Array.prototype.slice.call(arguments,1);H.length&&1<N.length;)U+=N.shift()+H.shift();return U+N.join("%s")},goog.string.collapseWhitespace=function(A){return A.replace(/[\s\xa0]+/g," ").replace(/^\s+|\s+$/g,"")},goog.string.isEmptyOrWhitespace=goog.string.internal.isEmptyOrWhitespace,goog.string.isEmptyString=function(A){return 0==A.length},goog.string.isEmpty=goog.string.isEmptyOrWhitespace,goog.string.isEmptyOrWhitespaceSafe=function(A){return goog.string.isEmptyOrWhitespace(goog.string.makeSafe(A))},goog.string.isEmptySafe=goog.string.isEmptyOrWhitespaceSafe,goog.string.isBreakingWhitespace=function(A){return!/[^\t\n\r ]/.test(A)},goog.string.isAlpha=function(A){return!/[^a-zA-Z]/.test(A)},goog.string.isNumeric=function(A){return!/[^0-9]/.test(A)},goog.string.isAlphaNumeric=function(A){return!/[^a-zA-Z0-9]/.test(A)},goog.string.isSpace=function(A){return" "==A},goog.string.isUnicodeChar=function(A){return 1==A.length&&" "<=A&&"~">=A||"\x80"<=A&&"�">=A},goog.string.stripNewlines=function(A){return A.replace(/(\r\n|\r|\n)+/g," ")},goog.string.canonicalizeNewlines=function(A){return A.replace(/(\r\n|\r|\n)/g,"\n")},goog.string.normalizeWhitespace=function(A){return A.replace(/\xa0|\s/g," ")},goog.string.normalizeSpaces=function(A){return A.replace(/\xa0|[ \t]+/g," ")},goog.string.collapseBreakingSpaces=function(A){return A.replace(/[\t\r\n ]+/g," ").replace(/^[\t\r\n ]+|[\t\r\n ]+$/g,"")},goog.string.trim=goog.string.internal.trim,goog.string.trimLeft=function(A){return A.replace(/^[\s\xa0]+/,"")},goog.string.trimRight=function(A){return A.replace(/[\s\xa0]+$/,"")},goog.string.caseInsensitiveCompare=goog.string.internal.caseInsensitiveCompare,goog.string.numberAwareCompare_=function(A,B,N){if(A==B)return 0;if(!A)return -1;if(!B)return 1;for(var U=A.toLowerCase().match(N),H=B.toLowerCase().match(N),W=Math.min(U.length,H.length),j=0;j<W;j++){N=U[j];var V=H[j];if(N!=V)return isNaN(A=parseInt(N,10))||isNaN(B=parseInt(V,10))||!(A-B)?N<V?-1:1:A-B}return U.length!=H.length?U.length-H.length:A<B?-1:1},goog.string.intAwareCompare=function(A,B){return goog.string.numberAwareCompare_(A,B,/\d+|\D+/g)},goog.string.floatAwareCompare=function(A,B){return goog.string.numberAwareCompare_(A,B,/\d+|\.\d+|\D+/g)},goog.string.numerateCompare=goog.string.floatAwareCompare,goog.string.urlEncode=function(A){return encodeURIComponent(String(A))},goog.string.urlDecode=function(A){return decodeURIComponent(A.replace(/\+/g," "))},goog.string.newLineToBr=goog.string.internal.newLineToBr,goog.string.htmlEscape=function(A,B){return A=goog.string.internal.htmlEscape(A,B),goog.string.DETECT_DOUBLE_ESCAPING&&(A=A.replace(goog.string.E_RE_,"&#101;")),A},goog.string.E_RE_=/e/g,goog.string.unescapeEntities=function(A){return goog.string.contains(A,"&")?!goog.string.FORCE_NON_DOM_HTML_UNESCAPING&&"document"in goog.global?goog.string.unescapeEntitiesUsingDom_(A):goog.string.unescapePureXmlEntities_(A):A},goog.string.unescapeEntitiesWithDocument=function(A,B){return goog.string.contains(A,"&")?goog.string.unescapeEntitiesUsingDom_(A,B):A},goog.string.unescapeEntitiesUsingDom_=function(A,B){var N={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"'},U=B?B.createElement("div"):goog.global.document.createElement("div");return A.replace(goog.string.HTML_ENTITY_PATTERN_,function(A,B){var H=N[A];return H?H:("#"==B.charAt(0)&&(isNaN(B=Number("0"+B.substr(1)))||(H=String.fromCharCode(B))),H||(goog.dom.safe.setInnerHtml(U,goog.html.uncheckedconversions.safeHtmlFromStringKnownToSatisfyTypeContract(goog.string.Const.from("Single HTML entity."),A+" ")),H=U.firstChild.nodeValue.slice(0,-1)),N[A]=H)})},goog.string.unescapePureXmlEntities_=function(A){return A.replace(/&([^;]+);/g,function(A,B){switch(B){case"amp":return"&";case"lt":return"<";case"gt":return">";case"quot":return'"';default:return"#"!=B.charAt(0)||isNaN(B=Number("0"+B.substr(1)))?A:String.fromCharCode(B)}})},goog.string.HTML_ENTITY_PATTERN_=/&([^;\s<&]+);?/g,goog.string.whitespaceEscape=function(A,B){return goog.string.newLineToBr(A.replace(/  /g," &#160;"),B)},goog.string.preserveSpaces=function(A){return A.replace(/(^|[\n ]) /g,"$1"+goog.string.Unicode.NBSP)},goog.string.stripQuotes=function(A,B){for(var N=B.length,U=0;U<N;U++){var H=1==N?B:B.charAt(U);if(A.charAt(0)==H&&A.charAt(A.length-1)==H)return A.substring(1,A.length-1)}return A},goog.string.truncate=function(A,B,N){return N&&(A=goog.string.unescapeEntities(A)),A.length>B&&(A=A.substring(0,B-3)+"..."),N&&(A=goog.string.htmlEscape(A)),A},goog.string.truncateMiddle=function(A,B,N,U){if(N&&(A=goog.string.unescapeEntities(A)),U&&A.length>B){U>B&&(U=B);var H=A.length-U;A=A.substring(0,B-U)+"..."+A.substring(H)}else A.length>B&&(U=Math.floor(B/2),H=A.length-U,A=A.substring(0,U+B%2)+"..."+A.substring(H));return N&&(A=goog.string.htmlEscape(A)),A},goog.string.specialEscapeChars_={"\0":"\\0","\b":"\\b","\f":"\\f","\n":"\\n","\r":"\\r","	":"\\t","\v":"\\x0B",'"':'\\"',"\\":"\\\\","<":"\\u003C"},goog.string.jsEscapeCache_={"'":"\\'"},goog.string.quote=function(A){A=String(A);for(var B=['"'],N=0;N<A.length;N++){var U=A.charAt(N),H=U.charCodeAt(0);B[N+1]=goog.string.specialEscapeChars_[U]||(31<H&&127>H?U:goog.string.escapeChar(U))}return B.push('"'),B.join("")},goog.string.escapeString=function(A){for(var B=[],N=0;N<A.length;N++)B[N]=goog.string.escapeChar(A.charAt(N));return B.join("")},goog.string.escapeChar=function(A){if(A in goog.string.jsEscapeCache_)return goog.string.jsEscapeCache_[A];if(A in goog.string.specialEscapeChars_)return goog.string.jsEscapeCache_[A]=goog.string.specialEscapeChars_[A];var B=A.charCodeAt(0);if(31<B&&127>B)var N=A;else 256>B?(N="\\x",(16>B||256<B)&&(N+="0")):(N="\\u",4096>B&&(N+="0")),N+=B.toString(16).toUpperCase();return goog.string.jsEscapeCache_[A]=N},goog.string.contains=goog.string.internal.contains,goog.string.caseInsensitiveContains=goog.string.internal.caseInsensitiveContains,goog.string.countOf=function(A,B){return A&&B?A.split(B).length-1:0},goog.string.removeAt=function(A,B,N){var U=A;return 0<=B&&B<A.length&&0<N&&(U=A.substr(0,B)+A.substr(B+N,A.length-B-N)),U},goog.string.remove=function(A,B){return A.replace(B,"")},goog.string.removeAll=function(A,B){return B=RegExp(goog.string.regExpEscape(B),"g"),A.replace(B,"")},goog.string.replaceAll=function(A,B,N){return B=RegExp(goog.string.regExpEscape(B),"g"),A.replace(B,N.replace(/\$/g,"$$$$"))},goog.string.regExpEscape=function(A){return String(A).replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1").replace(/\x08/g,"\\x08")},goog.string.repeat=String.prototype.repeat?function(A,B){return A.repeat(B)}:function(A,B){return Array(B+1).join(A)},goog.string.padNumber=function(A,B,N){return -1==(N=(A=void 0!==N?A.toFixed(N):String(A)).indexOf("."))&&(N=A.length),goog.string.repeat("0",Math.max(0,B-N))+A},goog.string.makeSafe=function(A){return null==A?"":String(A)},goog.string.buildString=function(A){return Array.prototype.join.call(arguments,"")},goog.string.getRandomString=function(){return Math.floor(0x80000000*Math.random()).toString(36)+Math.abs(Math.floor(0x80000000*Math.random())^goog.now()).toString(36)},goog.string.compareVersions=goog.string.internal.compareVersions,goog.string.hashCode=function(A){for(var B=0,N=0;N<A.length;++N)B=31*B+A.charCodeAt(N)>>>0;return B},goog.string.uniqueStringCounter_=0x80000000*Math.random()|0,goog.string.createUniqueString=function(){return"goog_"+goog.string.uniqueStringCounter_++},goog.string.toNumber=function(A){var B=Number(A);return 0==B&&goog.string.isEmptyOrWhitespace(A)?NaN:B},goog.string.isLowerCamelCase=function(A){return/^[a-z]+([A-Z][a-z]*)*$/.test(A)},goog.string.isUpperCamelCase=function(A){return/^([A-Z][a-z]*)+$/.test(A)},goog.string.toCamelCase=function(A){return String(A).replace(/\-([a-z])/g,function(A,B){return B.toUpperCase()})},goog.string.toSelectorCase=function(A){return String(A).replace(/([A-Z])/g,"-$1").toLowerCase()},goog.string.toTitleCase=function(A,B){return B="string"==typeof B?goog.string.regExpEscape(B):"\\s",A.replace(RegExp("(^"+(B?"|["+B+"]+":"")+")([a-z])","g"),function(A,B,N){return B+N.toUpperCase()})},goog.string.capitalize=function(A){return String(A.charAt(0)).toUpperCase()+String(A.substr(1)).toLowerCase()},goog.string.parseInt=function(A){return isFinite(A)&&(A=String(A)),"string"==typeof A?/^\s*-?0x/i.test(A)?parseInt(A,16):parseInt(A,10):NaN},goog.string.splitLimit=function(A,B,N){A=A.split(B);for(var U=[];0<N&&A.length;)U.push(A.shift()),N--;return A.length&&U.push(A.join(B)),U},goog.string.lastComponent=function(A,B){if(!B)return A;"string"==typeof B&&(B=[B]);for(var N=-1,U=0;U<B.length;U++)if(""!=B[U]){var H=A.lastIndexOf(B[U]);H>N&&(N=H)}return -1==N?A:A.slice(N+1)},goog.string.editDistance=function(A,B){var N=[],U=[];if(A==B)return 0;if(!A.length||!B.length)return Math.max(A.length,B.length);for(var H=0;H<B.length+1;H++)N[H]=H;for(H=0;H<A.length;H++){U[0]=H+1;for(var W=0;W<B.length;W++)U[W+1]=Math.min(U[W]+1,N[W+1]+1,N[W]+Number(A[H]!=B[W]));for(W=0;W<N.length;W++)N[W]=U[W]}return U[B.length]},goog.labs.userAgent.engine={},goog.labs.userAgent.engine.isPresto=function(){return goog.labs.userAgent.util.matchUserAgent("Presto")},goog.labs.userAgent.engine.isTrident=function(){return goog.labs.userAgent.util.matchUserAgent("Trident")||goog.labs.userAgent.util.matchUserAgent("MSIE")},goog.labs.userAgent.engine.isEdge=function(){return goog.labs.userAgent.util.matchUserAgent("Edge")},goog.labs.userAgent.engine.isWebKit=function(){return goog.labs.userAgent.util.matchUserAgentIgnoreCase("WebKit")&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.isGecko=function(){return goog.labs.userAgent.util.matchUserAgent("Gecko")&&!goog.labs.userAgent.engine.isWebKit()&&!goog.labs.userAgent.engine.isTrident()&&!goog.labs.userAgent.engine.isEdge()},goog.labs.userAgent.engine.getVersion=function(){var A=goog.labs.userAgent.util.getUserAgent();if(A){A=goog.labs.userAgent.util.extractVersionTuples(A);var B,N=goog.labs.userAgent.engine.getEngineTuple_(A);if(N)return"Gecko"==N[0]?goog.labs.userAgent.engine.getVersionForKey_(A,"Firefox"):N[1];if((A=A[0])&&(B=A[2])&&(B=/Trident\/([^\s;]+)/.exec(B)))return B[1]}return""},goog.labs.userAgent.engine.getEngineTuple_=function(A){if(!goog.labs.userAgent.engine.isEdge())return A[1];for(var B=0;B<A.length;B++){var N=A[B];if("Edge"==N[0])return N}},goog.labs.userAgent.engine.isVersionOrHigher=function(A){return 0<=goog.string.compareVersions(goog.labs.userAgent.engine.getVersion(),A)},goog.labs.userAgent.engine.getVersionForKey_=function(A,B){return(A=goog.array.find(A,function(A){return B==A[0]}))&&A[1]||""},goog.labs.userAgent.platform={},goog.labs.userAgent.platform.isAndroid=function(){return goog.labs.userAgent.util.matchUserAgent("Android")},goog.labs.userAgent.platform.isIpod=function(){return goog.labs.userAgent.util.matchUserAgent("iPod")},goog.labs.userAgent.platform.isIphone=function(){return goog.labs.userAgent.util.matchUserAgent("iPhone")&&!goog.labs.userAgent.util.matchUserAgent("iPod")&&!goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIpad=function(){return goog.labs.userAgent.util.matchUserAgent("iPad")},goog.labs.userAgent.platform.isIos=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpad()||goog.labs.userAgent.platform.isIpod()},goog.labs.userAgent.platform.isMacintosh=function(){return goog.labs.userAgent.util.matchUserAgent("Macintosh")},goog.labs.userAgent.platform.isLinux=function(){return goog.labs.userAgent.util.matchUserAgent("Linux")},goog.labs.userAgent.platform.isWindows=function(){return goog.labs.userAgent.util.matchUserAgent("Windows")},goog.labs.userAgent.platform.isChromeOS=function(){return goog.labs.userAgent.util.matchUserAgent("CrOS")},goog.labs.userAgent.platform.isChromecast=function(){return goog.labs.userAgent.util.matchUserAgent("CrKey")},goog.labs.userAgent.platform.isKaiOS=function(){return goog.labs.userAgent.util.matchUserAgentIgnoreCase("KaiOS")},goog.labs.userAgent.platform.getVersion=function(){var A=goog.labs.userAgent.util.getUserAgent(),B="";return goog.labs.userAgent.platform.isWindows()?B=(A=(B=/Windows (?:NT|Phone) ([0-9.]+)/).exec(A))?A[1]:"0.0":goog.labs.userAgent.platform.isIos()?B=(A=(B=/(?:iPhone|iPod|iPad|CPU)\s+OS\s+(\S+)/).exec(A))&&A[1].replace(/_/g,"."):goog.labs.userAgent.platform.isMacintosh()?B=(A=(B=/Mac OS X ([0-9_.]+)/).exec(A))?A[1].replace(/_/g,"."):"10":goog.labs.userAgent.platform.isKaiOS()?B=(A=(B=/(?:KaiOS)\/(\S+)/i).exec(A))&&A[1]:goog.labs.userAgent.platform.isAndroid()?B=(A=(B=/Android\s+([^\);]+)(\)|;)/).exec(A))&&A[1]:goog.labs.userAgent.platform.isChromeOS()&&(B=(A=(B=/(?:CrOS\s+(?:i686|x86_64)\s+([0-9.]+))/).exec(A))&&A[1]),B||""},goog.labs.userAgent.platform.isVersionOrHigher=function(A){return 0<=goog.string.compareVersions(goog.labs.userAgent.platform.getVersion(),A)},goog.reflect={},goog.reflect.object=function(A,B){return B},goog.reflect.objectProperty=function(A,B){return A},goog.reflect.sinkValue=function(A){return goog.reflect.sinkValue[" "](A),A},goog.reflect.sinkValue[" "]=goog.nullFunction,goog.reflect.canAccessProperty=function(A,B){try{return goog.reflect.sinkValue(A[B]),!0}catch(A){}return!1},goog.reflect.cache=function(A,B,N,U){return U=U?U(B):B,Object.prototype.hasOwnProperty.call(A,U)?A[U]:A[U]=N(B)},goog.userAgent={},goog.userAgent.ASSUME_IE=!1,goog.userAgent.ASSUME_EDGE=!1,goog.userAgent.ASSUME_GECKO=!1,goog.userAgent.ASSUME_WEBKIT=!1,goog.userAgent.ASSUME_MOBILE_WEBKIT=!1,goog.userAgent.ASSUME_OPERA=!1,goog.userAgent.ASSUME_ANY_VERSION=!1,goog.userAgent.BROWSER_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_GECKO||goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_OPERA,goog.userAgent.getUserAgentString=function(){return goog.labs.userAgent.util.getUserAgent()},goog.userAgent.getNavigatorTyped=function(){return goog.global.navigator||null},goog.userAgent.getNavigator=function(){return goog.userAgent.getNavigatorTyped()},goog.userAgent.OPERA=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_OPERA:goog.labs.userAgent.browser.isOpera(),goog.userAgent.IE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_IE:goog.labs.userAgent.browser.isIE(),goog.userAgent.EDGE=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_EDGE:goog.labs.userAgent.engine.isEdge(),goog.userAgent.EDGE_OR_IE=goog.userAgent.EDGE||goog.userAgent.IE,goog.userAgent.GECKO=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_GECKO:goog.labs.userAgent.engine.isGecko(),goog.userAgent.WEBKIT=goog.userAgent.BROWSER_KNOWN_?goog.userAgent.ASSUME_WEBKIT||goog.userAgent.ASSUME_MOBILE_WEBKIT:goog.labs.userAgent.engine.isWebKit(),goog.userAgent.isMobile_=function(){return goog.userAgent.WEBKIT&&goog.labs.userAgent.util.matchUserAgent("Mobile")},goog.userAgent.MOBILE=goog.userAgent.ASSUME_MOBILE_WEBKIT||goog.userAgent.isMobile_(),goog.userAgent.SAFARI=goog.userAgent.WEBKIT,goog.userAgent.determinePlatform_=function(){var A=goog.userAgent.getNavigatorTyped();return A&&A.platform||""},goog.userAgent.PLATFORM=goog.userAgent.determinePlatform_(),goog.userAgent.ASSUME_MAC=!1,goog.userAgent.ASSUME_WINDOWS=!1,goog.userAgent.ASSUME_LINUX=!1,goog.userAgent.ASSUME_X11=!1,goog.userAgent.ASSUME_ANDROID=!1,goog.userAgent.ASSUME_IPHONE=!1,goog.userAgent.ASSUME_IPAD=!1,goog.userAgent.ASSUME_IPOD=!1,goog.userAgent.ASSUME_KAIOS=!1,goog.userAgent.PLATFORM_KNOWN_=goog.userAgent.ASSUME_MAC||goog.userAgent.ASSUME_WINDOWS||goog.userAgent.ASSUME_LINUX||goog.userAgent.ASSUME_X11||goog.userAgent.ASSUME_ANDROID||goog.userAgent.ASSUME_IPHONE||goog.userAgent.ASSUME_IPAD||goog.userAgent.ASSUME_IPOD,goog.userAgent.MAC=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_MAC:goog.labs.userAgent.platform.isMacintosh(),goog.userAgent.WINDOWS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_WINDOWS:goog.labs.userAgent.platform.isWindows(),goog.userAgent.isLegacyLinux_=function(){return goog.labs.userAgent.platform.isLinux()||goog.labs.userAgent.platform.isChromeOS()},goog.userAgent.LINUX=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_LINUX:goog.userAgent.isLegacyLinux_(),goog.userAgent.isX11_=function(){var A=goog.userAgent.getNavigatorTyped();return!!A&&goog.string.contains(A.appVersion||"","X11")},goog.userAgent.X11=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_X11:goog.userAgent.isX11_(),goog.userAgent.ANDROID=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_ANDROID:goog.labs.userAgent.platform.isAndroid(),goog.userAgent.IPHONE=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPHONE:goog.labs.userAgent.platform.isIphone(),goog.userAgent.IPAD=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.IPOD=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPOD:goog.labs.userAgent.platform.isIpod(),goog.userAgent.IOS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_IPHONE||goog.userAgent.ASSUME_IPAD||goog.userAgent.ASSUME_IPOD:goog.labs.userAgent.platform.isIos(),goog.userAgent.KAIOS=goog.userAgent.PLATFORM_KNOWN_?goog.userAgent.ASSUME_KAIOS:goog.labs.userAgent.platform.isKaiOS(),goog.userAgent.determineVersion_=function(){var A="",B=goog.userAgent.getVersionRegexResult_();return B&&(A=B?B[1]:""),goog.userAgent.IE&&null!=(B=goog.userAgent.getDocumentMode_())&&B>parseFloat(A)?String(B):A},goog.userAgent.getVersionRegexResult_=function(){var A=goog.userAgent.getUserAgentString();return goog.userAgent.GECKO?/rv:([^\);]+)(\)|;)/.exec(A):goog.userAgent.EDGE?/Edge\/([\d\.]+)/.exec(A):goog.userAgent.IE?/\b(?:MSIE|rv)[: ]([^\);]+)(\)|;)/.exec(A):goog.userAgent.WEBKIT?/WebKit\/(\S+)/.exec(A):goog.userAgent.OPERA?/(?:Version)[ \/]?(\S+)/.exec(A):void 0},goog.userAgent.getDocumentMode_=function(){var A=goog.global.document;return A?A.documentMode:void 0},goog.userAgent.VERSION=goog.userAgent.determineVersion_(),goog.userAgent.compare=function(A,B){return goog.string.compareVersions(A,B)},goog.userAgent.isVersionOrHigherCache_={},goog.userAgent.isVersionOrHigher=function(A){return goog.userAgent.ASSUME_ANY_VERSION||goog.reflect.cache(goog.userAgent.isVersionOrHigherCache_,A,function(){return 0<=goog.string.compareVersions(goog.userAgent.VERSION,A)})},goog.userAgent.isVersion=goog.userAgent.isVersionOrHigher,goog.userAgent.isDocumentModeOrHigher=function(A){return Number(goog.userAgent.DOCUMENT_MODE)>=A},goog.userAgent.isDocumentMode=goog.userAgent.isDocumentModeOrHigher,goog.userAgent.DOCUMENT_MODE=function(){if(goog.global.document&&goog.userAgent.IE){var A=goog.userAgent.getDocumentMode_();return A||parseInt(goog.userAgent.VERSION,10)||void 0}}(),goog.userAgent.product={},goog.userAgent.product.ASSUME_FIREFOX=!1,goog.userAgent.product.ASSUME_IPHONE=!1,goog.userAgent.product.ASSUME_IPAD=!1,goog.userAgent.product.ASSUME_ANDROID=!1,goog.userAgent.product.ASSUME_CHROME=!1,goog.userAgent.product.ASSUME_SAFARI=!1,goog.userAgent.product.PRODUCT_KNOWN_=goog.userAgent.ASSUME_IE||goog.userAgent.ASSUME_EDGE||goog.userAgent.ASSUME_OPERA||goog.userAgent.product.ASSUME_FIREFOX||goog.userAgent.product.ASSUME_IPHONE||goog.userAgent.product.ASSUME_IPAD||goog.userAgent.product.ASSUME_ANDROID||goog.userAgent.product.ASSUME_CHROME||goog.userAgent.product.ASSUME_SAFARI,goog.userAgent.product.OPERA=goog.userAgent.OPERA,goog.userAgent.product.IE=goog.userAgent.IE,goog.userAgent.product.EDGE=goog.userAgent.EDGE,goog.userAgent.product.FIREFOX=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_FIREFOX:goog.labs.userAgent.browser.isFirefox(),goog.userAgent.product.isIphoneOrIpod_=function(){return goog.labs.userAgent.platform.isIphone()||goog.labs.userAgent.platform.isIpod()},goog.userAgent.product.IPHONE=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPHONE:goog.userAgent.product.isIphoneOrIpod_(),goog.userAgent.product.IPAD=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_IPAD:goog.labs.userAgent.platform.isIpad(),goog.userAgent.product.ANDROID=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_ANDROID:goog.labs.userAgent.browser.isAndroidBrowser(),goog.userAgent.product.CHROME=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_CHROME:goog.labs.userAgent.browser.isChrome(),goog.userAgent.product.isSafariDesktop_=function(){return goog.labs.userAgent.browser.isSafari()&&!goog.labs.userAgent.platform.isIos()},goog.userAgent.product.SAFARI=goog.userAgent.product.PRODUCT_KNOWN_?goog.userAgent.product.ASSUME_SAFARI:goog.userAgent.product.isSafariDesktop_(),goog.crypt.base64={},goog.crypt.base64.DEFAULT_ALPHABET_COMMON_="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",goog.crypt.base64.ENCODED_VALS=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_+"+/=",goog.crypt.base64.ENCODED_VALS_WEBSAFE=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_+"-_.",goog.crypt.base64.Alphabet={DEFAULT:0,NO_PADDING:1,WEBSAFE:2,WEBSAFE_DOT_PADDING:3,WEBSAFE_NO_PADDING:4},goog.crypt.base64.paddingChars_="=.",goog.crypt.base64.isPadding_=function(A){return goog.string.contains(goog.crypt.base64.paddingChars_,A)},goog.crypt.base64.byteToCharMaps_={},goog.crypt.base64.charToByteMap_=null,goog.crypt.base64.ASSUME_NATIVE_SUPPORT_=goog.userAgent.GECKO||goog.userAgent.WEBKIT&&!goog.userAgent.product.SAFARI||goog.userAgent.OPERA,goog.crypt.base64.HAS_NATIVE_ENCODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||"function"==typeof goog.global.btoa,goog.crypt.base64.HAS_NATIVE_DECODE_=goog.crypt.base64.ASSUME_NATIVE_SUPPORT_||!goog.userAgent.product.SAFARI&&!goog.userAgent.IE&&"function"==typeof goog.global.atob,goog.crypt.base64.encodeByteArray=function(A,B){goog.asserts.assert(goog.isArrayLike(A),"encodeByteArray takes an array as a parameter"),void 0===B&&(B=goog.crypt.base64.Alphabet.DEFAULT),goog.crypt.base64.init_(),B=goog.crypt.base64.byteToCharMaps_[B];for(var N=[],U=0;U<A.length;U+=3){var H=A[U],W=U+1<A.length,j=W?A[U+1]:0,V=U+2<A.length,K=V?A[U+2]:0,X=H>>2;H=(3&H)<<4|j>>4,j=(15&j)<<2|K>>6,K&=63,V||(K=64,W||(j=64)),N.push(B[X],B[H],B[j]||"",B[K]||"")}return N.join("")},goog.crypt.base64.encodeString=function(A,B){return goog.crypt.base64.HAS_NATIVE_ENCODE_&&!B?goog.global.btoa(A):goog.crypt.base64.encodeByteArray(goog.crypt.stringToByteArray(A),B)},goog.crypt.base64.decodeString=function(A,B){if(goog.crypt.base64.HAS_NATIVE_DECODE_&&!B)return goog.global.atob(A);var N="";return goog.crypt.base64.decodeStringInternal_(A,function(A){N+=String.fromCharCode(A)}),N},goog.crypt.base64.decodeStringToByteArray=function(A,B){var N=[];return goog.crypt.base64.decodeStringInternal_(A,function(A){N.push(A)}),N},goog.crypt.base64.decodeStringToUint8Array=function(A){goog.asserts.assert(!goog.userAgent.IE||goog.userAgent.isVersionOrHigher("10"),"Browser does not support typed arrays");var B=A.length,N=3*B/4;N%3?N=Math.floor(N):goog.crypt.base64.isPadding_(A[B-1])&&(N=goog.crypt.base64.isPadding_(A[B-2])?N-2:N-1);var U=new Uint8Array(N),H=0;return goog.crypt.base64.decodeStringInternal_(A,function(A){U[H++]=A}),U.subarray(0,H)},goog.crypt.base64.decodeStringInternal_=function(A,B){function c(B){for(;N<A.length;){var U=A.charAt(N++),H=goog.crypt.base64.charToByteMap_[U];if(null!=H)return H;if(!goog.string.isEmptyOrWhitespace(U))throw Error("Unknown base64 encoding at char: "+U)}return B}goog.crypt.base64.init_();for(var N=0;;){var U=c(-1),H=c(0),W=c(64),j=c(64);if(64===j&&-1===U)break;B(U<<2|H>>4),64!=W&&(B(H<<4&240|W>>2),64!=j&&B(W<<6&192|j))}},goog.crypt.base64.init_=function(){if(!goog.crypt.base64.charToByteMap_){goog.crypt.base64.charToByteMap_={};for(var A=goog.crypt.base64.DEFAULT_ALPHABET_COMMON_.split(""),B=["+/=","+/","-_=","-_.","-_"],N=0;5>N;N++){var U=A.concat(B[N].split(""));goog.crypt.base64.byteToCharMaps_[N]=U;for(var H=0;H<U.length;H++){var W=U[H],j=goog.crypt.base64.charToByteMap_[W];void 0===j?goog.crypt.base64.charToByteMap_[W]=H:goog.asserts.assert(j===H)}}}},jspb.utils={},jspb.utils.split64Low=0,jspb.utils.split64High=0,jspb.utils.splitUint64=function(A){var B=A>>>0;A=Math.floor((A-B)/jspb.BinaryConstants.TWO_TO_32)>>>0,jspb.utils.split64Low=B,jspb.utils.split64High=A},jspb.utils.splitInt64=function(A){var B=0>A,N=(A=Math.abs(A))>>>0;A=Math.floor((A-N)/jspb.BinaryConstants.TWO_TO_32)>>>0,B&&(A=~A>>>0,0xffffffff<(N=(~N>>>0)+1)&&(N=0,0xffffffff<++A&&(A=0))),jspb.utils.split64Low=N,jspb.utils.split64High=A},jspb.utils.splitZigzag64=function(A){var B=0>A;A=2*Math.abs(A),jspb.utils.splitUint64(A),A=jspb.utils.split64Low;var N=jspb.utils.split64High;B&&(0==A?0==N?N=A=0xffffffff:(N--,A=0xffffffff):A--),jspb.utils.split64Low=A,jspb.utils.split64High=N},jspb.utils.splitFloat32=function(A){var B=+(0>A);if(0===(A=B?-A:A))0<1/A?(jspb.utils.split64High=0,jspb.utils.split64Low=0):(jspb.utils.split64High=0,jspb.utils.split64Low=0x80000000);else if(isNaN(A))jspb.utils.split64High=0,jspb.utils.split64Low=0x7fffffff;else if(A>jspb.BinaryConstants.FLOAT32_MAX)jspb.utils.split64High=0,jspb.utils.split64Low=(B<<31|0x7f800000)>>>0;else if(A<jspb.BinaryConstants.FLOAT32_MIN)A=Math.round(A/1401298464324817e-60),jspb.utils.split64High=0,jspb.utils.split64Low=(B<<31|A)>>>0;else{var N=Math.floor(Math.log(A)/Math.LN2);A*=Math.pow(2,-N),0x1000000<=(A=Math.round(A*jspb.BinaryConstants.TWO_TO_23))&&++N,jspb.utils.split64High=0,jspb.utils.split64Low=(B<<31|N+127<<23|8388607&A)>>>0}},jspb.utils.splitFloat64=function(A){var B=+(0>A);if(0===(A=B?-A:A))jspb.utils.split64High=0<1/A?0:0x80000000,jspb.utils.split64Low=0;else if(isNaN(A))jspb.utils.split64High=0x7fffffff,jspb.utils.split64Low=0xffffffff;else if(A>jspb.BinaryConstants.FLOAT64_MAX)jspb.utils.split64High=(B<<31|0x7ff00000)>>>0,jspb.utils.split64Low=0;else if(A<jspb.BinaryConstants.FLOAT64_MIN){var N=A/5e-324;A=N/jspb.BinaryConstants.TWO_TO_32,jspb.utils.split64High=(B<<31|A)>>>0,jspb.utils.split64Low=N>>>0}else{var U=0;if(2<=(N=A))for(;2<=N&&1023>U;)U++,N/=2;else for(;1>N&&-1022<U;)N*=2,U--;A=(N=A*Math.pow(2,-U))*jspb.BinaryConstants.TWO_TO_20&1048575,N=N*jspb.BinaryConstants.TWO_TO_52>>>0,jspb.utils.split64High=(B<<31|U+1023<<20|A)>>>0,jspb.utils.split64Low=N}},jspb.utils.splitHash64=function(A){var B=A.charCodeAt(0),N=A.charCodeAt(1),U=A.charCodeAt(2),H=A.charCodeAt(3),W=A.charCodeAt(4),j=A.charCodeAt(5),V=A.charCodeAt(6);A=A.charCodeAt(7),jspb.utils.split64Low=B+(N<<8)+(U<<16)+(H<<24)>>>0,jspb.utils.split64High=W+(j<<8)+(V<<16)+(A<<24)>>>0},jspb.utils.joinUint64=function(A,B){return B*jspb.BinaryConstants.TWO_TO_32+(A>>>0)},jspb.utils.joinInt64=function(A,B){var N=0x80000000&B;return N&&(B=~B>>>0,0==(A=~A+1>>>0)&&(B=B+1>>>0)),A=jspb.utils.joinUint64(A,B),N?-A:A},jspb.utils.toZigzag64=function(A,B,N){var U=B>>31;return N(A<<1^U,(B<<1|A>>>31)^U)},jspb.utils.joinZigzag64=function(A,B){return jspb.utils.fromZigzag64(A,B,jspb.utils.joinInt64)},jspb.utils.fromZigzag64=function(A,B,N){var U=-(1&A);return N((A>>>1|B<<31)^U,B>>>1^U)},jspb.utils.joinFloat32=function(A,B){B=2*(A>>31)+1;var N=A>>>23&255;return A&=8388607,255==N?A?NaN:1/0*B:0==N?1401298464324817e-60*B*A:B*Math.pow(2,N-150)*(A+8388608)},jspb.utils.joinFloat64=function(A,B){var N=2*(B>>31)+1,U=B>>>20&2047;return A=jspb.BinaryConstants.TWO_TO_32*(1048575&B)+A,2047==U?A?NaN:1/0*N:0==U?5e-324*N*A:N*Math.pow(2,U-1075)*(A+jspb.BinaryConstants.TWO_TO_52)},jspb.utils.joinHash64=function(A,B){return String.fromCharCode(A>>>0&255,A>>>8&255,A>>>16&255,A>>>24&255,B>>>0&255,B>>>8&255,B>>>16&255,B>>>24&255)},jspb.utils.DIGITS="0123456789abcdef".split(""),jspb.utils.ZERO_CHAR_CODE_=48,jspb.utils.A_CHAR_CODE_=97,jspb.utils.joinUnsignedDecimalString=function(A,B){function c(A,B){return A=A?String(A):"",B?"0000000".slice(A.length)+A:A}if(2097151>=B)return""+jspb.utils.joinUint64(A,B);var N=(A>>>24|B<<8)>>>0&0xffffff;return A=(0xffffff&A)+6777216*N+6710656*(B=B>>16&65535),N+=8147497*B,B*=2,1e7<=A&&(N+=Math.floor(A/1e7),A%=1e7),1e7<=N&&(B+=Math.floor(N/1e7),N%=1e7),c(B,0)+c(N,B)+c(A,1)},jspb.utils.joinSignedDecimalString=function(A,B){var N=0x80000000&B;return N&&(B=~B+ +(0==(A=~A+1>>>0))>>>0),A=jspb.utils.joinUnsignedDecimalString(A,B),N?"-"+A:A},jspb.utils.hash64ToDecimalString=function(A,B){jspb.utils.splitHash64(A),A=jspb.utils.split64Low;var N=jspb.utils.split64High;return B?jspb.utils.joinSignedDecimalString(A,N):jspb.utils.joinUnsignedDecimalString(A,N)},jspb.utils.hash64ArrayToDecimalStrings=function(A,B){for(var N=Array(A.length),U=0;U<A.length;U++)N[U]=jspb.utils.hash64ToDecimalString(A[U],B);return N},jspb.utils.decimalStringToHash64=function(A){function b(A,B){for(var U=0;8>U&&(1!==A||0<B);U++)B=A*N[U]+B,N[U]=255&B,B>>>=8}function c(){for(var A=0;8>A;A++)N[A]=255&~N[A]}jspb.asserts.assert(0<A.length);var B=!1;"-"===A[0]&&(B=!0,A=A.slice(1));for(var N=[0,0,0,0,0,0,0,0],U=0;U<A.length;U++)b(10,A.charCodeAt(U)-jspb.utils.ZERO_CHAR_CODE_);return B&&(c(),b(1,1)),goog.crypt.byteArrayToString(N)},jspb.utils.splitDecimalString=function(A){jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(A))},jspb.utils.toHexDigit_=function(A){return String.fromCharCode(10>A?jspb.utils.ZERO_CHAR_CODE_+A:jspb.utils.A_CHAR_CODE_-10+A)},jspb.utils.fromHexCharCode_=function(A){return A>=jspb.utils.A_CHAR_CODE_?A-jspb.utils.A_CHAR_CODE_+10:A-jspb.utils.ZERO_CHAR_CODE_},jspb.utils.hash64ToHexString=function(A){var B=Array(18);B[0]="0",B[1]="x";for(var N=0;8>N;N++){var U=A.charCodeAt(7-N);B[2*N+2]=jspb.utils.toHexDigit_(U>>4),B[2*N+3]=jspb.utils.toHexDigit_(15&U)}return B.join("")},jspb.utils.hexStringToHash64=function(A){A=A.toLowerCase(),jspb.asserts.assert(18==A.length),jspb.asserts.assert("0"==A[0]),jspb.asserts.assert("x"==A[1]);for(var B="",N=0;8>N;N++)B=String.fromCharCode(16*jspb.utils.fromHexCharCode_(A.charCodeAt(2*N+2))+jspb.utils.fromHexCharCode_(A.charCodeAt(2*N+3)))+B;return B},jspb.utils.hash64ToNumber=function(A,B){jspb.utils.splitHash64(A),A=jspb.utils.split64Low;var N=jspb.utils.split64High;return B?jspb.utils.joinInt64(A,N):jspb.utils.joinUint64(A,N)},jspb.utils.numberToHash64=function(A){return jspb.utils.splitInt64(A),jspb.utils.joinHash64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.utils.countVarints=function(A,B,N){for(var U=0,H=B;H<N;H++)U+=A[H]>>7;return N-B-U},jspb.utils.countVarintFields=function(A,B,N,U){var H=0;if(128>(U=8*U+jspb.BinaryConstants.WireType.VARINT))for(;B<N&&A[B++]==U;)for(H++;;){var W=A[B++];if(0==(128&W))break}else for(;B<N;){for(W=U;128<W;){if(A[B]!=(127&W|128))return H;B++,W>>=7}if(A[B++]!=W)break;for(H++;0!=(128&(W=A[B++])););}return H},jspb.utils.countFixedFields_=function(A,B,N,U,H){var W=0;if(128>U)for(;B<N&&A[B++]==U;)W++,B+=H;else for(;B<N;){for(var j=U;128<j;){if(A[B++]!=(127&j|128))return W;j>>=7}if(A[B++]!=j)break;W++,B+=H}return W},jspb.utils.countFixed32Fields=function(A,B,N,U){return jspb.utils.countFixedFields_(A,B,N,8*U+jspb.BinaryConstants.WireType.FIXED32,4)},jspb.utils.countFixed64Fields=function(A,B,N,U){return jspb.utils.countFixedFields_(A,B,N,8*U+jspb.BinaryConstants.WireType.FIXED64,8)},jspb.utils.countDelimitedFields=function(A,B,N,U){var H=0;for(U=8*U+jspb.BinaryConstants.WireType.DELIMITED;B<N;){for(var W=U;128<W;){if(A[B++]!=(127&W|128))return H;W>>=7}if(A[B++]!=W)break;H++;for(var j=0,V=1;j+=(127&(W=A[B++]))*V,V*=128,0!=(128&W););B+=j}return H},jspb.utils.debugBytesToTextFormat=function(A){var B='"';if(A){A=jspb.utils.byteSourceToUint8Array(A);for(var N=0;N<A.length;N++)B+="\\x",16>A[N]&&(B+="0"),B+=A[N].toString(16)}return B+'"'},jspb.utils.debugScalarToTextFormat=function(A){return"string"==typeof A?goog.string.quote(A):A.toString()},jspb.utils.stringToByteArray=function(A){for(var B=new Uint8Array(A.length),N=0;N<A.length;N++){var U=A.charCodeAt(N);if(255<U)throw Error("Conversion error: string contains codepoint outside of byte range");B[N]=U}return B},jspb.utils.byteSourceToUint8Array=function(A){return A.constructor===Uint8Array?A:A.constructor===ArrayBuffer||A.constructor===Array?new Uint8Array(A):A.constructor===String?goog.crypt.base64.decodeStringToUint8Array(A):A instanceof Uint8Array?new Uint8Array(A.buffer,A.byteOffset,A.byteLength):(jspb.asserts.fail("Type not convertible to Uint8Array."),new Uint8Array(0))},jspb.BinaryDecoder=function(A,B,N){this.bytes_=null,this.cursor_=this.end_=this.start_=0,this.error_=!1,A&&this.setBlock(A,B,N)},jspb.BinaryDecoder.instanceCache_=[],jspb.BinaryDecoder.alloc=function(A,B,N){if(jspb.BinaryDecoder.instanceCache_.length){var U=jspb.BinaryDecoder.instanceCache_.pop();return A&&U.setBlock(A,B,N),U}return new jspb.BinaryDecoder(A,B,N)},jspb.BinaryDecoder.prototype.free=function(){this.clear(),100>jspb.BinaryDecoder.instanceCache_.length&&jspb.BinaryDecoder.instanceCache_.push(this)},jspb.BinaryDecoder.prototype.clone=function(){return jspb.BinaryDecoder.alloc(this.bytes_,this.start_,this.end_-this.start_)},jspb.BinaryDecoder.prototype.clear=function(){this.bytes_=null,this.cursor_=this.end_=this.start_=0,this.error_=!1},jspb.BinaryDecoder.prototype.getBuffer=function(){return this.bytes_},jspb.BinaryDecoder.prototype.setBlock=function(A,B,N){this.bytes_=jspb.utils.byteSourceToUint8Array(A),this.start_=void 0!==B?B:0,this.end_=void 0!==N?this.start_+N:this.bytes_.length,this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getEnd=function(){return this.end_},jspb.BinaryDecoder.prototype.setEnd=function(A){this.end_=A},jspb.BinaryDecoder.prototype.reset=function(){this.cursor_=this.start_},jspb.BinaryDecoder.prototype.getCursor=function(){return this.cursor_},jspb.BinaryDecoder.prototype.setCursor=function(A){this.cursor_=A},jspb.BinaryDecoder.prototype.advance=function(A){this.cursor_+=A,jspb.asserts.assert(this.cursor_<=this.end_)},jspb.BinaryDecoder.prototype.atEnd=function(){return this.cursor_==this.end_},jspb.BinaryDecoder.prototype.pastEnd=function(){return this.cursor_>this.end_},jspb.BinaryDecoder.prototype.getError=function(){return this.error_||0>this.cursor_||this.cursor_>this.end_},jspb.BinaryDecoder.prototype.readSplitVarint64=function(A){for(var B=128,N=0,U=0,H=0;4>H&&128<=B;H++)N|=(127&(B=this.bytes_[this.cursor_++]))<<7*H;if(128<=B&&(N|=(127&(B=this.bytes_[this.cursor_++]))<<28,U|=(127&B)>>4),128<=B)for(H=0;5>H&&128<=B;H++)U|=(127&(B=this.bytes_[this.cursor_++]))<<7*H+3;if(128>B)return A(N>>>0,U>>>0);jspb.asserts.fail("Failed to read varint, encoding is invalid."),this.error_=!0},jspb.BinaryDecoder.prototype.readSplitZigzagVarint64=function(A){return this.readSplitVarint64(function(B,N){return jspb.utils.fromZigzag64(B,N,A)})},jspb.BinaryDecoder.prototype.readSplitFixed64=function(A){var B=this.bytes_,N=this.cursor_;this.cursor_+=8;for(var U=0,H=0,W=N+7;W>=N;W--)U=U<<8|B[W],H=H<<8|B[W+4];return A(U,H)},jspb.BinaryDecoder.prototype.skipVarint=function(){for(;128&this.bytes_[this.cursor_];)this.cursor_++;this.cursor_++},jspb.BinaryDecoder.prototype.unskipVarint=function(A){for(;128<A;)this.cursor_--,A>>>=7;this.cursor_--},jspb.BinaryDecoder.prototype.readUnsignedVarint32=function(){var A=this.bytes_,B=A[this.cursor_+0],N=127&B;return 128>B?(this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),N):(N|=(127&(B=A[this.cursor_+1]))<<7,128>B)?(this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),N):(N|=(127&(B=A[this.cursor_+2]))<<14,128>B)?(this.cursor_+=3,jspb.asserts.assert(this.cursor_<=this.end_),N):(N|=(127&(B=A[this.cursor_+3]))<<21,128>B)?(this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),N):(N|=(15&(B=A[this.cursor_+4]))<<28,128>B)?(this.cursor_+=5,jspb.asserts.assert(this.cursor_<=this.end_),N>>>0):(this.cursor_+=5,128<=A[this.cursor_++]&&128<=A[this.cursor_++]&&128<=A[this.cursor_++]&&128<=A[this.cursor_++]&&128<=A[this.cursor_++]&&jspb.asserts.assert(!1),jspb.asserts.assert(this.cursor_<=this.end_),N)},jspb.BinaryDecoder.prototype.readSignedVarint32=function(){return~~this.readUnsignedVarint32()},jspb.BinaryDecoder.prototype.readUnsignedVarint32String=function(){return this.readUnsignedVarint32().toString()},jspb.BinaryDecoder.prototype.readSignedVarint32String=function(){return this.readSignedVarint32().toString()},jspb.BinaryDecoder.prototype.readZigzagVarint32=function(){var A=this.readUnsignedVarint32();return A>>>1^-(1&A)},jspb.BinaryDecoder.prototype.readUnsignedVarint64=function(){return this.readSplitVarint64(jspb.utils.joinUint64)},jspb.BinaryDecoder.prototype.readUnsignedVarint64String=function(){return this.readSplitVarint64(jspb.utils.joinUnsignedDecimalString)},jspb.BinaryDecoder.prototype.readSignedVarint64=function(){return this.readSplitVarint64(jspb.utils.joinInt64)},jspb.BinaryDecoder.prototype.readSignedVarint64String=function(){return this.readSplitVarint64(jspb.utils.joinSignedDecimalString)},jspb.BinaryDecoder.prototype.readZigzagVarint64=function(){return this.readSplitVarint64(jspb.utils.joinZigzag64)},jspb.BinaryDecoder.prototype.readZigzagVarintHash64=function(){return this.readSplitZigzagVarint64(jspb.utils.joinHash64)},jspb.BinaryDecoder.prototype.readZigzagVarint64String=function(){return this.readSplitZigzagVarint64(jspb.utils.joinSignedDecimalString)},jspb.BinaryDecoder.prototype.readUint8=function(){var A=this.bytes_[this.cursor_+0];return this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),A},jspb.BinaryDecoder.prototype.readUint16=function(){var A=this.bytes_[this.cursor_+0],B=this.bytes_[this.cursor_+1];return this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),A<<0|B<<8},jspb.BinaryDecoder.prototype.readUint32=function(){var A=this.bytes_[this.cursor_+0],B=this.bytes_[this.cursor_+1],N=this.bytes_[this.cursor_+2],U=this.bytes_[this.cursor_+3];return this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),(A<<0|B<<8|N<<16|U<<24)>>>0},jspb.BinaryDecoder.prototype.readUint64=function(){var A=this.readUint32(),B=this.readUint32();return jspb.utils.joinUint64(A,B)},jspb.BinaryDecoder.prototype.readUint64String=function(){var A=this.readUint32(),B=this.readUint32();return jspb.utils.joinUnsignedDecimalString(A,B)},jspb.BinaryDecoder.prototype.readInt8=function(){var A=this.bytes_[this.cursor_+0];return this.cursor_+=1,jspb.asserts.assert(this.cursor_<=this.end_),A<<24>>24},jspb.BinaryDecoder.prototype.readInt16=function(){var A=this.bytes_[this.cursor_+0],B=this.bytes_[this.cursor_+1];return this.cursor_+=2,jspb.asserts.assert(this.cursor_<=this.end_),(A<<0|B<<8)<<16>>16},jspb.BinaryDecoder.prototype.readInt32=function(){var A=this.bytes_[this.cursor_+0],B=this.bytes_[this.cursor_+1],N=this.bytes_[this.cursor_+2],U=this.bytes_[this.cursor_+3];return this.cursor_+=4,jspb.asserts.assert(this.cursor_<=this.end_),A<<0|B<<8|N<<16|U<<24},jspb.BinaryDecoder.prototype.readInt64=function(){var A=this.readUint32(),B=this.readUint32();return jspb.utils.joinInt64(A,B)},jspb.BinaryDecoder.prototype.readInt64String=function(){var A=this.readUint32(),B=this.readUint32();return jspb.utils.joinSignedDecimalString(A,B)},jspb.BinaryDecoder.prototype.readFloat=function(){var A=this.readUint32();return jspb.utils.joinFloat32(A,0)},jspb.BinaryDecoder.prototype.readDouble=function(){var A=this.readUint32(),B=this.readUint32();return jspb.utils.joinFloat64(A,B)},jspb.BinaryDecoder.prototype.readBool=function(){return!!this.bytes_[this.cursor_++]},jspb.BinaryDecoder.prototype.readEnum=function(){return this.readSignedVarint32()},jspb.BinaryDecoder.prototype.readString=function(A){var B=this.bytes_,N=this.cursor_;A=N+A;for(var U=[],H="";N<A;){var W=B[N++];if(128>W)U.push(W);else if(192>W)continue;else if(224>W){var j=B[N++];U.push((31&W)<<6|63&j)}else if(240>W){j=B[N++];var V=B[N++];U.push((15&W)<<12|(63&j)<<6|63&V)}else 248>W&&(j=B[N++],V=B[N++],W=((7&W)<<18|(63&j)<<12|(63&V)<<6|63&B[N++])-65536,U.push((W>>10&1023)+55296,(1023&W)+56320));8192<=U.length&&(H+=String.fromCharCode.apply(null,U),U.length=0)}return H+=goog.crypt.byteArrayToString(U),this.cursor_=N,H},jspb.BinaryDecoder.prototype.readStringWithLength=function(){var A=this.readUnsignedVarint32();return this.readString(A)},jspb.BinaryDecoder.prototype.readBytes=function(A){if(0>A||this.cursor_+A>this.bytes_.length)return this.error_=!0,jspb.asserts.fail("Invalid byte length!"),new Uint8Array(0);var B=this.bytes_.subarray(this.cursor_,this.cursor_+A);return this.cursor_+=A,jspb.asserts.assert(this.cursor_<=this.end_),B},jspb.BinaryDecoder.prototype.readVarintHash64=function(){return this.readSplitVarint64(jspb.utils.joinHash64)},jspb.BinaryDecoder.prototype.readFixedHash64=function(){var A=this.bytes_,B=this.cursor_,N=A[B+0],U=A[B+1],H=A[B+2],W=A[B+3],j=A[B+4],V=A[B+5],K=A[B+6];return A=A[B+7],this.cursor_+=8,String.fromCharCode(N,U,H,W,j,V,K,A)},jspb.BinaryReader=function(A,B,N){this.decoder_=jspb.BinaryDecoder.alloc(A,B,N),this.fieldCursor_=this.decoder_.getCursor(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null},jspb.BinaryReader.instanceCache_=[],jspb.BinaryReader.alloc=function(A,B,N){if(jspb.BinaryReader.instanceCache_.length){var U=jspb.BinaryReader.instanceCache_.pop();return A&&U.decoder_.setBlock(A,B,N),U}return new jspb.BinaryReader(A,B,N)},jspb.BinaryReader.prototype.alloc=jspb.BinaryReader.alloc,jspb.BinaryReader.prototype.free=function(){this.decoder_.clear(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID,this.error_=!1,this.readCallbacks_=null,100>jspb.BinaryReader.instanceCache_.length&&jspb.BinaryReader.instanceCache_.push(this)},jspb.BinaryReader.prototype.getFieldCursor=function(){return this.fieldCursor_},jspb.BinaryReader.prototype.getCursor=function(){return this.decoder_.getCursor()},jspb.BinaryReader.prototype.getBuffer=function(){return this.decoder_.getBuffer()},jspb.BinaryReader.prototype.getFieldNumber=function(){return this.nextField_},goog.exportProperty(jspb.BinaryReader.prototype,"getFieldNumber",jspb.BinaryReader.prototype.getFieldNumber),jspb.BinaryReader.prototype.getWireType=function(){return this.nextWireType_},jspb.BinaryReader.prototype.isDelimited=function(){return this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED},goog.exportProperty(jspb.BinaryReader.prototype,"isDelimited",jspb.BinaryReader.prototype.isDelimited),jspb.BinaryReader.prototype.isEndGroup=function(){return this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP},goog.exportProperty(jspb.BinaryReader.prototype,"isEndGroup",jspb.BinaryReader.prototype.isEndGroup),jspb.BinaryReader.prototype.getError=function(){return this.error_||this.decoder_.getError()},jspb.BinaryReader.prototype.setBlock=function(A,B,N){this.decoder_.setBlock(A,B,N),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.reset=function(){this.decoder_.reset(),this.nextField_=jspb.BinaryConstants.INVALID_FIELD_NUMBER,this.nextWireType_=jspb.BinaryConstants.WireType.INVALID},jspb.BinaryReader.prototype.advance=function(A){this.decoder_.advance(A)},jspb.BinaryReader.prototype.nextField=function(){if(this.decoder_.atEnd())return!1;if(this.getError())return jspb.asserts.fail("Decoder hit an error"),!1;this.fieldCursor_=this.decoder_.getCursor();var A=this.decoder_.readUnsignedVarint32(),B=A>>>3;return(A&=7)!=jspb.BinaryConstants.WireType.VARINT&&A!=jspb.BinaryConstants.WireType.FIXED32&&A!=jspb.BinaryConstants.WireType.FIXED64&&A!=jspb.BinaryConstants.WireType.DELIMITED&&A!=jspb.BinaryConstants.WireType.START_GROUP&&A!=jspb.BinaryConstants.WireType.END_GROUP?(jspb.asserts.fail("Invalid wire type: %s (at position %s)",A,this.fieldCursor_),this.error_=!0,!1):(this.nextField_=B,this.nextWireType_=A,!0)},goog.exportProperty(jspb.BinaryReader.prototype,"nextField",jspb.BinaryReader.prototype.nextField),jspb.BinaryReader.prototype.unskipHeader=function(){this.decoder_.unskipVarint(this.nextField_<<3|this.nextWireType_)},jspb.BinaryReader.prototype.skipMatchingFields=function(){var A=this.nextField_;for(this.unskipHeader();this.nextField()&&this.getFieldNumber()==A;)this.skipField();this.decoder_.atEnd()||this.unskipHeader()},jspb.BinaryReader.prototype.skipVarintField=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.VARINT?(jspb.asserts.fail("Invalid wire type for skipVarintField"),this.skipField()):this.decoder_.skipVarint()},jspb.BinaryReader.prototype.skipDelimitedField=function(){if(this.nextWireType_!=jspb.BinaryConstants.WireType.DELIMITED)jspb.asserts.fail("Invalid wire type for skipDelimitedField"),this.skipField();else{var A=this.decoder_.readUnsignedVarint32();this.decoder_.advance(A)}},jspb.BinaryReader.prototype.skipFixed32Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED32?(jspb.asserts.fail("Invalid wire type for skipFixed32Field"),this.skipField()):this.decoder_.advance(4)},jspb.BinaryReader.prototype.skipFixed64Field=function(){this.nextWireType_!=jspb.BinaryConstants.WireType.FIXED64?(jspb.asserts.fail("Invalid wire type for skipFixed64Field"),this.skipField()):this.decoder_.advance(8)},jspb.BinaryReader.prototype.skipGroup=function(){for(var A=this.nextField_;;){if(!this.nextField()){jspb.asserts.fail("Unmatched start-group tag: stream EOF"),this.error_=!0;break}if(this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP){this.nextField_!=A&&(jspb.asserts.fail("Unmatched end-group tag"),this.error_=!0);break}this.skipField()}},jspb.BinaryReader.prototype.skipField=function(){switch(this.nextWireType_){case jspb.BinaryConstants.WireType.VARINT:this.skipVarintField();break;case jspb.BinaryConstants.WireType.FIXED64:this.skipFixed64Field();break;case jspb.BinaryConstants.WireType.DELIMITED:this.skipDelimitedField();break;case jspb.BinaryConstants.WireType.FIXED32:this.skipFixed32Field();break;case jspb.BinaryConstants.WireType.START_GROUP:this.skipGroup();break;default:jspb.asserts.fail("Invalid wire encoding for field.")}},jspb.BinaryReader.prototype.registerReadCallback=function(A,B){null===this.readCallbacks_&&(this.readCallbacks_={}),jspb.asserts.assert(!this.readCallbacks_[A]),this.readCallbacks_[A]=B},jspb.BinaryReader.prototype.runReadCallback=function(A){return jspb.asserts.assert(null!==this.readCallbacks_),A=this.readCallbacks_[A],jspb.asserts.assert(A),A(this)},jspb.BinaryReader.prototype.readAny=function(A){this.nextWireType_=jspb.BinaryConstants.FieldTypeToWireType(A);var B=jspb.BinaryConstants.FieldType;switch(A){case B.DOUBLE:return this.readDouble();case B.FLOAT:return this.readFloat();case B.INT64:return this.readInt64();case B.UINT64:return this.readUint64();case B.INT32:return this.readInt32();case B.FIXED64:return this.readFixed64();case B.FIXED32:return this.readFixed32();case B.BOOL:return this.readBool();case B.STRING:return this.readString();case B.GROUP:jspb.asserts.fail("Group field type not supported in readAny()");case B.MESSAGE:jspb.asserts.fail("Message field type not supported in readAny()");case B.BYTES:return this.readBytes();case B.UINT32:return this.readUint32();case B.ENUM:return this.readEnum();case B.SFIXED32:return this.readSfixed32();case B.SFIXED64:return this.readSfixed64();case B.SINT32:return this.readSint32();case B.SINT64:return this.readSint64();case B.FHASH64:return this.readFixedHash64();case B.VHASH64:return this.readVarintHash64();default:jspb.asserts.fail("Invalid field type in readAny()")}return 0},jspb.BinaryReader.prototype.readMessage=function(A,B){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var N=this.decoder_.getEnd(),U=this.decoder_.readUnsignedVarint32();U=this.decoder_.getCursor()+U,this.decoder_.setEnd(U),B(A,this),this.decoder_.setCursor(U),this.decoder_.setEnd(N)},goog.exportProperty(jspb.BinaryReader.prototype,"readMessage",jspb.BinaryReader.prototype.readMessage),jspb.BinaryReader.prototype.readGroup=function(A,B,N){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.START_GROUP),jspb.asserts.assert(this.nextField_==A),N(B,this),this.error_||this.nextWireType_==jspb.BinaryConstants.WireType.END_GROUP||(jspb.asserts.fail("Group submessage did not end with an END_GROUP tag"),this.error_=!0)},goog.exportProperty(jspb.BinaryReader.prototype,"readGroup",jspb.BinaryReader.prototype.readGroup),jspb.BinaryReader.prototype.getFieldDecoder=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var A=this.decoder_.readUnsignedVarint32(),B=this.decoder_.getCursor(),N=B+A;return A=jspb.BinaryDecoder.alloc(this.decoder_.getBuffer(),B,A),this.decoder_.setCursor(N),A},jspb.BinaryReader.prototype.readInt32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readInt32",jspb.BinaryReader.prototype.readInt32),jspb.BinaryReader.prototype.readInt32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint32String()},jspb.BinaryReader.prototype.readInt64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readInt64",jspb.BinaryReader.prototype.readInt64),jspb.BinaryReader.prototype.readInt64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64String()},jspb.BinaryReader.prototype.readUint32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readUint32",jspb.BinaryReader.prototype.readUint32),jspb.BinaryReader.prototype.readUint32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint32String()},jspb.BinaryReader.prototype.readUint64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readUint64",jspb.BinaryReader.prototype.readUint64),jspb.BinaryReader.prototype.readUint64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readUnsignedVarint64String()},jspb.BinaryReader.prototype.readSint32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readSint32",jspb.BinaryReader.prototype.readSint32),jspb.BinaryReader.prototype.readSint64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readSint64",jspb.BinaryReader.prototype.readSint64),jspb.BinaryReader.prototype.readSint64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarint64String()},jspb.BinaryReader.prototype.readFixed32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readUint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readFixed32",jspb.BinaryReader.prototype.readFixed32),jspb.BinaryReader.prototype.readFixed64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readFixed64",jspb.BinaryReader.prototype.readFixed64),jspb.BinaryReader.prototype.readFixed64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readUint64String()},jspb.BinaryReader.prototype.readSfixed32=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32()},goog.exportProperty(jspb.BinaryReader.prototype,"readSfixed32",jspb.BinaryReader.prototype.readSfixed32),jspb.BinaryReader.prototype.readSfixed32String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readInt32().toString()},jspb.BinaryReader.prototype.readSfixed64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64()},goog.exportProperty(jspb.BinaryReader.prototype,"readSfixed64",jspb.BinaryReader.prototype.readSfixed64),jspb.BinaryReader.prototype.readSfixed64String=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readInt64String()},jspb.BinaryReader.prototype.readFloat=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED32),this.decoder_.readFloat()},goog.exportProperty(jspb.BinaryReader.prototype,"readFloat",jspb.BinaryReader.prototype.readFloat),jspb.BinaryReader.prototype.readDouble=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readDouble()},goog.exportProperty(jspb.BinaryReader.prototype,"readDouble",jspb.BinaryReader.prototype.readDouble),jspb.BinaryReader.prototype.readBool=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),!!this.decoder_.readUnsignedVarint32()},goog.exportProperty(jspb.BinaryReader.prototype,"readBool",jspb.BinaryReader.prototype.readBool),jspb.BinaryReader.prototype.readEnum=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSignedVarint64()},goog.exportProperty(jspb.BinaryReader.prototype,"readEnum",jspb.BinaryReader.prototype.readEnum),jspb.BinaryReader.prototype.readString=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var A=this.decoder_.readUnsignedVarint32();return this.decoder_.readString(A)},goog.exportProperty(jspb.BinaryReader.prototype,"readString",jspb.BinaryReader.prototype.readString),jspb.BinaryReader.prototype.readBytes=function(){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var A=this.decoder_.readUnsignedVarint32();return this.decoder_.readBytes(A)},goog.exportProperty(jspb.BinaryReader.prototype,"readBytes",jspb.BinaryReader.prototype.readBytes),jspb.BinaryReader.prototype.readVarintHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readVarintHash64()},jspb.BinaryReader.prototype.readSintHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readZigzagVarintHash64()},jspb.BinaryReader.prototype.readSplitVarint64=function(A){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSplitVarint64(A)},jspb.BinaryReader.prototype.readSplitZigzagVarint64=function(A){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.VARINT),this.decoder_.readSplitVarint64(function(B,N){return jspb.utils.fromZigzag64(B,N,A)})},jspb.BinaryReader.prototype.readFixedHash64=function(){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readFixedHash64()},jspb.BinaryReader.prototype.readSplitFixed64=function(A){return jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.FIXED64),this.decoder_.readSplitFixed64(A)},jspb.BinaryReader.prototype.readPackedField_=function(A){jspb.asserts.assert(this.nextWireType_==jspb.BinaryConstants.WireType.DELIMITED);var B=this.decoder_.readUnsignedVarint32();B=this.decoder_.getCursor()+B;for(var N=[];this.decoder_.getCursor()<B;)N.push(A.call(this.decoder_));return N},jspb.BinaryReader.prototype.readPackedInt32=function(){return this.readPackedField_(this.decoder_.readSignedVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedInt32",jspb.BinaryReader.prototype.readPackedInt32),jspb.BinaryReader.prototype.readPackedInt32String=function(){return this.readPackedField_(this.decoder_.readSignedVarint32String)},jspb.BinaryReader.prototype.readPackedInt64=function(){return this.readPackedField_(this.decoder_.readSignedVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedInt64",jspb.BinaryReader.prototype.readPackedInt64),jspb.BinaryReader.prototype.readPackedInt64String=function(){return this.readPackedField_(this.decoder_.readSignedVarint64String)},jspb.BinaryReader.prototype.readPackedUint32=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedUint32",jspb.BinaryReader.prototype.readPackedUint32),jspb.BinaryReader.prototype.readPackedUint32String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint32String)},jspb.BinaryReader.prototype.readPackedUint64=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedUint64",jspb.BinaryReader.prototype.readPackedUint64),jspb.BinaryReader.prototype.readPackedUint64String=function(){return this.readPackedField_(this.decoder_.readUnsignedVarint64String)},jspb.BinaryReader.prototype.readPackedSint32=function(){return this.readPackedField_(this.decoder_.readZigzagVarint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSint32",jspb.BinaryReader.prototype.readPackedSint32),jspb.BinaryReader.prototype.readPackedSint64=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSint64",jspb.BinaryReader.prototype.readPackedSint64),jspb.BinaryReader.prototype.readPackedSint64String=function(){return this.readPackedField_(this.decoder_.readZigzagVarint64String)},jspb.BinaryReader.prototype.readPackedFixed32=function(){return this.readPackedField_(this.decoder_.readUint32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFixed32",jspb.BinaryReader.prototype.readPackedFixed32),jspb.BinaryReader.prototype.readPackedFixed64=function(){return this.readPackedField_(this.decoder_.readUint64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFixed64",jspb.BinaryReader.prototype.readPackedFixed64),jspb.BinaryReader.prototype.readPackedFixed64String=function(){return this.readPackedField_(this.decoder_.readUint64String)},jspb.BinaryReader.prototype.readPackedSfixed32=function(){return this.readPackedField_(this.decoder_.readInt32)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSfixed32",jspb.BinaryReader.prototype.readPackedSfixed32),jspb.BinaryReader.prototype.readPackedSfixed64=function(){return this.readPackedField_(this.decoder_.readInt64)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedSfixed64",jspb.BinaryReader.prototype.readPackedSfixed64),jspb.BinaryReader.prototype.readPackedSfixed64String=function(){return this.readPackedField_(this.decoder_.readInt64String)},jspb.BinaryReader.prototype.readPackedFloat=function(){return this.readPackedField_(this.decoder_.readFloat)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedFloat",jspb.BinaryReader.prototype.readPackedFloat),jspb.BinaryReader.prototype.readPackedDouble=function(){return this.readPackedField_(this.decoder_.readDouble)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedDouble",jspb.BinaryReader.prototype.readPackedDouble),jspb.BinaryReader.prototype.readPackedBool=function(){return this.readPackedField_(this.decoder_.readBool)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedBool",jspb.BinaryReader.prototype.readPackedBool),jspb.BinaryReader.prototype.readPackedEnum=function(){return this.readPackedField_(this.decoder_.readEnum)},goog.exportProperty(jspb.BinaryReader.prototype,"readPackedEnum",jspb.BinaryReader.prototype.readPackedEnum),jspb.BinaryReader.prototype.readPackedVarintHash64=function(){return this.readPackedField_(this.decoder_.readVarintHash64)},jspb.BinaryReader.prototype.readPackedFixedHash64=function(){return this.readPackedField_(this.decoder_.readFixedHash64)},jspb.BinaryEncoder=function(){this.buffer_=[]},jspb.BinaryEncoder.prototype.length=function(){return this.buffer_.length},jspb.BinaryEncoder.prototype.end=function(){var A=this.buffer_;return this.buffer_=[],A},jspb.BinaryEncoder.prototype.writeSplitVarint64=function(A,B){for(jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(B==Math.floor(B)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_32),jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_32);0<B||127<A;)this.buffer_.push(127&A|128),A=(A>>>7|B<<25)>>>0,B>>>=7;this.buffer_.push(A)},jspb.BinaryEncoder.prototype.writeSplitFixed64=function(A,B){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(B==Math.floor(B)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_32),jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_32),this.writeUint32(A),this.writeUint32(B)},jspb.BinaryEncoder.prototype.writeUnsignedVarint32=function(A){for(jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_32);127<A;)this.buffer_.push(127&A|128),A>>>=7;this.buffer_.push(A)},jspb.BinaryEncoder.prototype.writeSignedVarint32=function(A){if(jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_31&&A<jspb.BinaryConstants.TWO_TO_31),0<=A)this.writeUnsignedVarint32(A);else{for(var B=0;9>B;B++)this.buffer_.push(127&A|128),A>>=7;this.buffer_.push(1)}},jspb.BinaryEncoder.prototype.writeUnsignedVarint64=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitInt64(A),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeSignedVarint64=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_63&&A<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(A),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint32=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_31&&A<jspb.BinaryConstants.TWO_TO_31),this.writeUnsignedVarint32((A<<1^A>>31)>>>0)},jspb.BinaryEncoder.prototype.writeZigzagVarint64=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_63&&A<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitZigzag64(A),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeZigzagVarint64String=function(A){this.writeZigzagVarintHash64(jspb.utils.decimalStringToHash64(A))},jspb.BinaryEncoder.prototype.writeZigzagVarintHash64=function(A){var B=this;jspb.utils.splitHash64(A),jspb.utils.toZigzag64(jspb.utils.split64Low,jspb.utils.split64High,function(A,N){B.writeSplitVarint64(A>>>0,N>>>0)})},jspb.BinaryEncoder.prototype.writeUint8=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&256>A),this.buffer_.push(A>>>0&255)},jspb.BinaryEncoder.prototype.writeUint16=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&65536>A),this.buffer_.push(A>>>0&255),this.buffer_.push(A>>>8&255)},jspb.BinaryEncoder.prototype.writeUint32=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_32),this.buffer_.push(A>>>0&255),this.buffer_.push(A>>>8&255),this.buffer_.push(A>>>16&255),this.buffer_.push(A>>>24&255)},jspb.BinaryEncoder.prototype.writeUint64=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(0<=A&&A<jspb.BinaryConstants.TWO_TO_64),jspb.utils.splitUint64(A),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt8=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(-128<=A&&128>A),this.buffer_.push(A>>>0&255)},jspb.BinaryEncoder.prototype.writeInt16=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(-32768<=A&&32768>A),this.buffer_.push(A>>>0&255),this.buffer_.push(A>>>8&255)},jspb.BinaryEncoder.prototype.writeInt32=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_31&&A<jspb.BinaryConstants.TWO_TO_31),this.buffer_.push(A>>>0&255),this.buffer_.push(A>>>8&255),this.buffer_.push(A>>>16&255),this.buffer_.push(A>>>24&255)},jspb.BinaryEncoder.prototype.writeInt64=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_63&&A<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitInt64(A),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeInt64String=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(+A>=-jspb.BinaryConstants.TWO_TO_63&&+A<jspb.BinaryConstants.TWO_TO_63),jspb.utils.splitHash64(jspb.utils.decimalStringToHash64(A)),this.writeSplitFixed64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFloat=function(A){jspb.asserts.assert(1/0===A||-1/0===A||isNaN(A)||A>=-jspb.BinaryConstants.FLOAT32_MAX&&A<=jspb.BinaryConstants.FLOAT32_MAX),jspb.utils.splitFloat32(A),this.writeUint32(jspb.utils.split64Low)},jspb.BinaryEncoder.prototype.writeDouble=function(A){jspb.asserts.assert(1/0===A||-1/0===A||isNaN(A)||A>=-jspb.BinaryConstants.FLOAT64_MAX&&A<=jspb.BinaryConstants.FLOAT64_MAX),jspb.utils.splitFloat64(A),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeBool=function(A){jspb.asserts.assert("boolean"==typeof A||"number"==typeof A),this.buffer_.push(+!!A)},jspb.BinaryEncoder.prototype.writeEnum=function(A){jspb.asserts.assert(A==Math.floor(A)),jspb.asserts.assert(A>=-jspb.BinaryConstants.TWO_TO_31&&A<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32(A)},jspb.BinaryEncoder.prototype.writeBytes=function(A){this.buffer_.push.apply(this.buffer_,A)},jspb.BinaryEncoder.prototype.writeVarintHash64=function(A){jspb.utils.splitHash64(A),this.writeSplitVarint64(jspb.utils.split64Low,jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeFixedHash64=function(A){jspb.utils.splitHash64(A),this.writeUint32(jspb.utils.split64Low),this.writeUint32(jspb.utils.split64High)},jspb.BinaryEncoder.prototype.writeString=function(A){var B=this.buffer_.length;jspb.asserts.assertString(A);for(var N=0;N<A.length;N++){var U=A.charCodeAt(N);if(128>U)this.buffer_.push(U);else if(2048>U)this.buffer_.push(U>>6|192),this.buffer_.push(63&U|128);else if(65536>U){if(55296<=U&&56319>=U&&N+1<A.length){var H=A.charCodeAt(N+1);56320<=H&&57343>=H&&(U=1024*(U-55296)+H-56320+65536,this.buffer_.push(U>>18|240),this.buffer_.push(U>>12&63|128),this.buffer_.push(U>>6&63|128),this.buffer_.push(63&U|128),N++)}else this.buffer_.push(U>>12|224),this.buffer_.push(U>>6&63|128),this.buffer_.push(63&U|128)}}return this.buffer_.length-B},jspb.arith={},jspb.arith.UInt64=function(A,B){this.lo=A,this.hi=B},jspb.arith.UInt64.prototype.cmp=function(A){return this.hi<A.hi||this.hi==A.hi&&this.lo<A.lo?-1:+(this.hi!=A.hi||this.lo!=A.lo)},jspb.arith.UInt64.prototype.rightShift=function(){return new jspb.arith.UInt64((this.lo>>>1|(1&this.hi)<<31)>>>0,this.hi>>>1>>>0)},jspb.arith.UInt64.prototype.leftShift=function(){return new jspb.arith.UInt64(this.lo<<1>>>0,(this.hi<<1|this.lo>>>31)>>>0)},jspb.arith.UInt64.prototype.msb=function(){return!!(0x80000000&this.hi)},jspb.arith.UInt64.prototype.lsb=function(){return!!(1&this.lo)},jspb.arith.UInt64.prototype.zero=function(){return 0==this.lo&&0==this.hi},jspb.arith.UInt64.prototype.add=function(A){return new jspb.arith.UInt64((this.lo+A.lo&0xffffffff)>>>0>>>0,((this.hi+A.hi&0xffffffff)>>>0)+ +(0x100000000<=this.lo+A.lo)>>>0)},jspb.arith.UInt64.prototype.sub=function(A){return new jspb.arith.UInt64((this.lo-A.lo&0xffffffff)>>>0>>>0,((this.hi-A.hi&0xffffffff)>>>0)-+(0>this.lo-A.lo)>>>0)},jspb.arith.UInt64.mul32x32=function(A,B){var N=65535&A;A>>>=16;var U=65535&B,H=B>>>16;for(B=N*U+65536*(N*H&65535)+65536*(A*U&65535),N=A*H+(N*H>>>16)+(A*U>>>16);0x100000000<=B;)B-=0x100000000,N+=1;return new jspb.arith.UInt64(B>>>0,N>>>0)},jspb.arith.UInt64.prototype.mul=function(A){var B=jspb.arith.UInt64.mul32x32(this.lo,A);return(A=jspb.arith.UInt64.mul32x32(this.hi,A)).hi=A.lo,A.lo=0,B.add(A)},jspb.arith.UInt64.prototype.div=function(A){if(0==A)return[];var B=new jspb.arith.UInt64(0,0),N=new jspb.arith.UInt64(this.lo,this.hi);A=new jspb.arith.UInt64(A,0);for(var U=new jspb.arith.UInt64(1,0);!A.msb();)A=A.leftShift(),U=U.leftShift();for(;!U.zero();)0>=A.cmp(N)&&(B=B.add(U),N=N.sub(A)),A=A.rightShift(),U=U.rightShift();return[B,N]},jspb.arith.UInt64.prototype.toString=function(){for(var A="",B=this;!B.zero();){var N=(B=B.div(10))[0];A=B[1].lo+A,B=N}return""==A&&(A="0"),A},jspb.arith.UInt64.fromString=function(A){for(var B=new jspb.arith.UInt64(0,0),N=new jspb.arith.UInt64(0,0),U=0;U<A.length;U++){if("0">A[U]||"9"<A[U])return null;var H=parseInt(A[U],10);N.lo=H,B=B.mul(10).add(N)}return B},jspb.arith.UInt64.prototype.clone=function(){return new jspb.arith.UInt64(this.lo,this.hi)},jspb.arith.Int64=function(A,B){this.lo=A,this.hi=B},jspb.arith.Int64.prototype.add=function(A){return new jspb.arith.Int64((this.lo+A.lo&0xffffffff)>>>0>>>0,((this.hi+A.hi&0xffffffff)>>>0)+ +(0x100000000<=this.lo+A.lo)>>>0)},jspb.arith.Int64.prototype.sub=function(A){return new jspb.arith.Int64((this.lo-A.lo&0xffffffff)>>>0>>>0,((this.hi-A.hi&0xffffffff)>>>0)-+(0>this.lo-A.lo)>>>0)},jspb.arith.Int64.prototype.clone=function(){return new jspb.arith.Int64(this.lo,this.hi)},jspb.arith.Int64.prototype.toString=function(){var A=0!=(0x80000000&this.hi),B=new jspb.arith.UInt64(this.lo,this.hi);return A&&(B=new jspb.arith.UInt64(0,0).sub(B)),(A?"-":"")+B.toString()},jspb.arith.Int64.fromString=function(A){var B=0<A.length&&"-"==A[0];return(B&&(A=A.substring(1)),null===(A=jspb.arith.UInt64.fromString(A)))?null:(B&&(A=new jspb.arith.UInt64(0,0).sub(A)),new jspb.arith.Int64(A.lo,A.hi))},jspb.BinaryWriter=function(){this.blocks_=[],this.totalLength_=0,this.encoder_=new jspb.BinaryEncoder,this.bookmarks_=[]},jspb.BinaryWriter.prototype.appendUint8Array_=function(A){var B=this.encoder_.end();this.blocks_.push(B),this.blocks_.push(A),this.totalLength_+=B.length+A.length},jspb.BinaryWriter.prototype.beginDelimited_=function(A){return this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),A=this.encoder_.end(),this.blocks_.push(A),this.totalLength_+=A.length,A.push(this.totalLength_),A},jspb.BinaryWriter.prototype.endDelimited_=function(A){var B=A.pop();for(B=this.totalLength_+this.encoder_.length()-B,jspb.asserts.assert(0<=B);127<B;)A.push(127&B|128),B>>>=7,this.totalLength_++;A.push(B),this.totalLength_++},jspb.BinaryWriter.prototype.writeSerializedMessage=function(A,B,N){this.appendUint8Array_(A.subarray(B,N))},jspb.BinaryWriter.prototype.maybeWriteSerializedMessage=function(A,B,N){null!=A&&null!=B&&null!=N&&this.writeSerializedMessage(A,B,N)},jspb.BinaryWriter.prototype.reset=function(){this.blocks_=[],this.encoder_.end(),this.totalLength_=0,this.bookmarks_=[]},jspb.BinaryWriter.prototype.getResultBuffer=function(){jspb.asserts.assert(0==this.bookmarks_.length);for(var A=new Uint8Array(this.totalLength_+this.encoder_.length()),B=this.blocks_,N=B.length,U=0,H=0;H<N;H++){var W=B[H];A.set(W,U),U+=W.length}return B=this.encoder_.end(),A.set(B,U),U+=B.length,jspb.asserts.assert(U==A.length),this.blocks_=[A],A},goog.exportProperty(jspb.BinaryWriter.prototype,"getResultBuffer",jspb.BinaryWriter.prototype.getResultBuffer),jspb.BinaryWriter.prototype.getResultBase64String=function(A){return goog.crypt.base64.encodeByteArray(this.getResultBuffer(),A)},jspb.BinaryWriter.prototype.beginSubMessage=function(A){this.bookmarks_.push(this.beginDelimited_(A))},jspb.BinaryWriter.prototype.endSubMessage=function(){jspb.asserts.assert(0<=this.bookmarks_.length),this.endDelimited_(this.bookmarks_.pop())},jspb.BinaryWriter.prototype.writeFieldHeader_=function(A,B){jspb.asserts.assert(1<=A&&A==Math.floor(A)),this.encoder_.writeUnsignedVarint32(8*A+B)},jspb.BinaryWriter.prototype.writeAny=function(A,B,N){var U=jspb.BinaryConstants.FieldType;switch(A){case U.DOUBLE:this.writeDouble(B,N);break;case U.FLOAT:this.writeFloat(B,N);break;case U.INT64:this.writeInt64(B,N);break;case U.UINT64:this.writeUint64(B,N);break;case U.INT32:this.writeInt32(B,N);break;case U.FIXED64:this.writeFixed64(B,N);break;case U.FIXED32:this.writeFixed32(B,N);break;case U.BOOL:this.writeBool(B,N);break;case U.STRING:this.writeString(B,N);break;case U.GROUP:jspb.asserts.fail("Group field type not supported in writeAny()");break;case U.MESSAGE:jspb.asserts.fail("Message field type not supported in writeAny()");break;case U.BYTES:this.writeBytes(B,N);break;case U.UINT32:this.writeUint32(B,N);break;case U.ENUM:this.writeEnum(B,N);break;case U.SFIXED32:this.writeSfixed32(B,N);break;case U.SFIXED64:this.writeSfixed64(B,N);break;case U.SINT32:this.writeSint32(B,N);break;case U.SINT64:this.writeSint64(B,N);break;case U.FHASH64:this.writeFixedHash64(B,N);break;case U.VHASH64:this.writeVarintHash64(B,N);break;default:jspb.asserts.fail("Invalid field type in writeAny()")}},jspb.BinaryWriter.prototype.writeUnsignedVarint32_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint32(B))},jspb.BinaryWriter.prototype.writeSignedVarint32_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(B))},jspb.BinaryWriter.prototype.writeUnsignedVarint64_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeUnsignedVarint64(B))},jspb.BinaryWriter.prototype.writeSignedVarint64_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint64(B))},jspb.BinaryWriter.prototype.writeZigzagVarint32_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint32(B))},jspb.BinaryWriter.prototype.writeZigzagVarint64_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64(B))},jspb.BinaryWriter.prototype.writeZigzagVarint64String_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarint64String(B))},jspb.BinaryWriter.prototype.writeZigzagVarintHash64_=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeZigzagVarintHash64(B))},jspb.BinaryWriter.prototype.writeInt32=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_31&&B<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeInt32",jspb.BinaryWriter.prototype.writeInt32),jspb.BinaryWriter.prototype.writeInt32String=function(A,B){null!=B&&(B=parseInt(B,10),jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_31&&B<jspb.BinaryConstants.TWO_TO_31),this.writeSignedVarint32_(A,B))},jspb.BinaryWriter.prototype.writeInt64=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_63&&B<jspb.BinaryConstants.TWO_TO_63),this.writeSignedVarint64_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeInt64",jspb.BinaryWriter.prototype.writeInt64),jspb.BinaryWriter.prototype.writeInt64String=function(A,B){null!=B&&(B=jspb.arith.Int64.fromString(B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(B.lo,B.hi))},jspb.BinaryWriter.prototype.writeUint32=function(A,B){null!=B&&(jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeUint32",jspb.BinaryWriter.prototype.writeUint32),jspb.BinaryWriter.prototype.writeUint32String=function(A,B){null!=B&&(B=parseInt(B,10),jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_32),this.writeUnsignedVarint32_(A,B))},jspb.BinaryWriter.prototype.writeUint64=function(A,B){null!=B&&(jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_64),this.writeUnsignedVarint64_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeUint64",jspb.BinaryWriter.prototype.writeUint64),jspb.BinaryWriter.prototype.writeUint64String=function(A,B){null!=B&&(B=jspb.arith.UInt64.fromString(B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(B.lo,B.hi))},jspb.BinaryWriter.prototype.writeSint32=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_31&&B<jspb.BinaryConstants.TWO_TO_31),this.writeZigzagVarint32_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSint32",jspb.BinaryWriter.prototype.writeSint32),jspb.BinaryWriter.prototype.writeSint64=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_63&&B<jspb.BinaryConstants.TWO_TO_63),this.writeZigzagVarint64_(A,B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSint64",jspb.BinaryWriter.prototype.writeSint64),jspb.BinaryWriter.prototype.writeSintHash64=function(A,B){null!=B&&this.writeZigzagVarintHash64_(A,B)},jspb.BinaryWriter.prototype.writeSint64String=function(A,B){null!=B&&this.writeZigzagVarint64String_(A,B)},jspb.BinaryWriter.prototype.writeFixed32=function(A,B){null!=B&&(jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_32),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeUint32(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFixed32",jspb.BinaryWriter.prototype.writeFixed32),jspb.BinaryWriter.prototype.writeFixed64=function(A,B){null!=B&&(jspb.asserts.assert(0<=B&&B<jspb.BinaryConstants.TWO_TO_64),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeUint64(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFixed64",jspb.BinaryWriter.prototype.writeFixed64),jspb.BinaryWriter.prototype.writeFixed64String=function(A,B){null!=B&&(B=jspb.arith.UInt64.fromString(B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(B.lo,B.hi))},jspb.BinaryWriter.prototype.writeSfixed32=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_31&&B<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeInt32(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSfixed32",jspb.BinaryWriter.prototype.writeSfixed32),jspb.BinaryWriter.prototype.writeSfixed64=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_63&&B<jspb.BinaryConstants.TWO_TO_63),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeInt64(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeSfixed64",jspb.BinaryWriter.prototype.writeSfixed64),jspb.BinaryWriter.prototype.writeSfixed64String=function(A,B){null!=B&&(B=jspb.arith.Int64.fromString(B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(B.lo,B.hi))},jspb.BinaryWriter.prototype.writeFloat=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED32),this.encoder_.writeFloat(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeFloat",jspb.BinaryWriter.prototype.writeFloat),jspb.BinaryWriter.prototype.writeDouble=function(A,B){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeDouble(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeDouble",jspb.BinaryWriter.prototype.writeDouble),jspb.BinaryWriter.prototype.writeBool=function(A,B){null!=B&&(jspb.asserts.assert("boolean"==typeof B||"number"==typeof B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeBool(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeBool",jspb.BinaryWriter.prototype.writeBool),jspb.BinaryWriter.prototype.writeEnum=function(A,B){null!=B&&(jspb.asserts.assert(B>=-jspb.BinaryConstants.TWO_TO_31&&B<jspb.BinaryConstants.TWO_TO_31),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeEnum",jspb.BinaryWriter.prototype.writeEnum),jspb.BinaryWriter.prototype.writeString=function(A,B){null!=B&&(A=this.beginDelimited_(A),this.encoder_.writeString(B),this.endDelimited_(A))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeString",jspb.BinaryWriter.prototype.writeString),jspb.BinaryWriter.prototype.writeBytes=function(A,B){null!=B&&(B=jspb.utils.byteSourceToUint8Array(B),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(B.length),this.appendUint8Array_(B))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeBytes",jspb.BinaryWriter.prototype.writeBytes),jspb.BinaryWriter.prototype.writeMessage=function(A,B,N){null!=B&&(A=this.beginDelimited_(A),N(B,this),this.endDelimited_(A))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeMessage",jspb.BinaryWriter.prototype.writeMessage),jspb.BinaryWriter.prototype.writeMessageSet=function(A,B,N){null!=B&&(this.writeFieldHeader_(1,jspb.BinaryConstants.WireType.START_GROUP),this.writeFieldHeader_(2,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSignedVarint32(A),A=this.beginDelimited_(3),N(B,this),this.endDelimited_(A),this.writeFieldHeader_(1,jspb.BinaryConstants.WireType.END_GROUP))},jspb.BinaryWriter.prototype.writeGroup=function(A,B,N){null!=B&&(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.START_GROUP),N(B,this),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.END_GROUP))},goog.exportProperty(jspb.BinaryWriter.prototype,"writeGroup",jspb.BinaryWriter.prototype.writeGroup),jspb.BinaryWriter.prototype.writeFixedHash64=function(A,B){null!=B&&(jspb.asserts.assert(8==B.length),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeFixedHash64(B))},jspb.BinaryWriter.prototype.writeVarintHash64=function(A,B){null!=B&&(jspb.asserts.assert(8==B.length),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeVarintHash64(B))},jspb.BinaryWriter.prototype.writeSplitFixed64=function(A,B,N){this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.FIXED64),this.encoder_.writeSplitFixed64(B,N)},jspb.BinaryWriter.prototype.writeSplitVarint64=function(A,B,N){this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT),this.encoder_.writeSplitVarint64(B,N)},jspb.BinaryWriter.prototype.writeSplitZigzagVarint64=function(A,B,N){this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.VARINT);var U=this.encoder_;jspb.utils.toZigzag64(B,N,function(A,B){U.writeSplitVarint64(A>>>0,B>>>0)})},jspb.BinaryWriter.prototype.writeRepeatedInt32=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeSignedVarint32_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedInt32",jspb.BinaryWriter.prototype.writeRepeatedInt32),jspb.BinaryWriter.prototype.writeRepeatedInt32String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeInt32String(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedInt64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeSignedVarint64_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedInt64",jspb.BinaryWriter.prototype.writeRepeatedInt64),jspb.BinaryWriter.prototype.writeRepeatedSplitFixed64=function(A,B,N,U){if(null!=B)for(var H=0;H<B.length;H++)this.writeSplitFixed64(A,N(B[H]),U(B[H]))},jspb.BinaryWriter.prototype.writeRepeatedSplitVarint64=function(A,B,N,U){if(null!=B)for(var H=0;H<B.length;H++)this.writeSplitVarint64(A,N(B[H]),U(B[H]))},jspb.BinaryWriter.prototype.writeRepeatedSplitZigzagVarint64=function(A,B,N,U){if(null!=B)for(var H=0;H<B.length;H++)this.writeSplitZigzagVarint64(A,N(B[H]),U(B[H]))},jspb.BinaryWriter.prototype.writeRepeatedInt64String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeInt64String(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedUint32=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeUnsignedVarint32_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedUint32",jspb.BinaryWriter.prototype.writeRepeatedUint32),jspb.BinaryWriter.prototype.writeRepeatedUint32String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeUint32String(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedUint64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeUnsignedVarint64_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedUint64",jspb.BinaryWriter.prototype.writeRepeatedUint64),jspb.BinaryWriter.prototype.writeRepeatedUint64String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeUint64String(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedSint32=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeZigzagVarint32_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSint32",jspb.BinaryWriter.prototype.writeRepeatedSint32),jspb.BinaryWriter.prototype.writeRepeatedSint64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeZigzagVarint64_(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSint64",jspb.BinaryWriter.prototype.writeRepeatedSint64),jspb.BinaryWriter.prototype.writeRepeatedSint64String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeZigzagVarint64String_(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedSintHash64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeZigzagVarintHash64_(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedFixed32=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeFixed32(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed32",jspb.BinaryWriter.prototype.writeRepeatedFixed32),jspb.BinaryWriter.prototype.writeRepeatedFixed64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeFixed64(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed64",jspb.BinaryWriter.prototype.writeRepeatedFixed64),jspb.BinaryWriter.prototype.writeRepeatedFixed64String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeFixed64String(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFixed64String",jspb.BinaryWriter.prototype.writeRepeatedFixed64String),jspb.BinaryWriter.prototype.writeRepeatedSfixed32=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeSfixed32(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSfixed32",jspb.BinaryWriter.prototype.writeRepeatedSfixed32),jspb.BinaryWriter.prototype.writeRepeatedSfixed64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeSfixed64(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedSfixed64",jspb.BinaryWriter.prototype.writeRepeatedSfixed64),jspb.BinaryWriter.prototype.writeRepeatedSfixed64String=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeSfixed64String(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedFloat=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeFloat(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedFloat",jspb.BinaryWriter.prototype.writeRepeatedFloat),jspb.BinaryWriter.prototype.writeRepeatedDouble=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeDouble(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedDouble",jspb.BinaryWriter.prototype.writeRepeatedDouble),jspb.BinaryWriter.prototype.writeRepeatedBool=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeBool(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedBool",jspb.BinaryWriter.prototype.writeRepeatedBool),jspb.BinaryWriter.prototype.writeRepeatedEnum=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeEnum(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedEnum",jspb.BinaryWriter.prototype.writeRepeatedEnum),jspb.BinaryWriter.prototype.writeRepeatedString=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeString(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedString",jspb.BinaryWriter.prototype.writeRepeatedString),jspb.BinaryWriter.prototype.writeRepeatedBytes=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeBytes(A,B[N])},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedBytes",jspb.BinaryWriter.prototype.writeRepeatedBytes),jspb.BinaryWriter.prototype.writeRepeatedMessage=function(A,B,N){if(null!=B)for(var U=0;U<B.length;U++){var H=this.beginDelimited_(A);N(B[U],this),this.endDelimited_(H)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedMessage",jspb.BinaryWriter.prototype.writeRepeatedMessage),jspb.BinaryWriter.prototype.writeRepeatedGroup=function(A,B,N){if(null!=B)for(var U=0;U<B.length;U++)this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.START_GROUP),N(B[U],this),this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.END_GROUP)},goog.exportProperty(jspb.BinaryWriter.prototype,"writeRepeatedGroup",jspb.BinaryWriter.prototype.writeRepeatedGroup),jspb.BinaryWriter.prototype.writeRepeatedFixedHash64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeFixedHash64(A,B[N])},jspb.BinaryWriter.prototype.writeRepeatedVarintHash64=function(A,B){if(null!=B)for(var N=0;N<B.length;N++)this.writeVarintHash64(A,B[N])},jspb.BinaryWriter.prototype.writePackedInt32=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeSignedVarint32(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedInt32",jspb.BinaryWriter.prototype.writePackedInt32),jspb.BinaryWriter.prototype.writePackedInt32String=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeSignedVarint32(parseInt(B[N],10));this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedInt64=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeSignedVarint64(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedInt64",jspb.BinaryWriter.prototype.writePackedInt64),jspb.BinaryWriter.prototype.writePackedSplitFixed64=function(A,B,N,U){if(null!=B){A=this.beginDelimited_(A);for(var H=0;H<B.length;H++)this.encoder_.writeSplitFixed64(N(B[H]),U(B[H]));this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedSplitVarint64=function(A,B,N,U){if(null!=B){A=this.beginDelimited_(A);for(var H=0;H<B.length;H++)this.encoder_.writeSplitVarint64(N(B[H]),U(B[H]));this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedSplitZigzagVarint64=function(A,B,N,U){if(null!=B){A=this.beginDelimited_(A);for(var H=this.encoder_,W=0;W<B.length;W++)jspb.utils.toZigzag64(N(B[W]),U(B[W]),function(A,B){H.writeSplitVarint64(A>>>0,B>>>0)});this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedInt64String=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++){var U=jspb.arith.Int64.fromString(B[N]);this.encoder_.writeSplitVarint64(U.lo,U.hi)}this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedUint32=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeUnsignedVarint32(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedUint32",jspb.BinaryWriter.prototype.writePackedUint32),jspb.BinaryWriter.prototype.writePackedUint32String=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeUnsignedVarint32(parseInt(B[N],10));this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedUint64=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeUnsignedVarint64(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedUint64",jspb.BinaryWriter.prototype.writePackedUint64),jspb.BinaryWriter.prototype.writePackedUint64String=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++){var U=jspb.arith.UInt64.fromString(B[N]);this.encoder_.writeSplitVarint64(U.lo,U.hi)}this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedSint32=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeZigzagVarint32(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSint32",jspb.BinaryWriter.prototype.writePackedSint32),jspb.BinaryWriter.prototype.writePackedSint64=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeZigzagVarint64(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSint64",jspb.BinaryWriter.prototype.writePackedSint64),jspb.BinaryWriter.prototype.writePackedSint64String=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeZigzagVarintHash64(jspb.utils.decimalStringToHash64(B[N]));this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedSintHash64=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeZigzagVarintHash64(B[N]);this.endDelimited_(A)}},jspb.BinaryWriter.prototype.writePackedFixed32=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*B.length),A=0;A<B.length;A++)this.encoder_.writeUint32(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFixed32",jspb.BinaryWriter.prototype.writePackedFixed32),jspb.BinaryWriter.prototype.writePackedFixed64=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++)this.encoder_.writeUint64(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFixed64",jspb.BinaryWriter.prototype.writePackedFixed64),jspb.BinaryWriter.prototype.writePackedFixed64String=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++){var N=jspb.arith.UInt64.fromString(B[A]);this.encoder_.writeSplitFixed64(N.lo,N.hi)}},jspb.BinaryWriter.prototype.writePackedSfixed32=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*B.length),A=0;A<B.length;A++)this.encoder_.writeInt32(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSfixed32",jspb.BinaryWriter.prototype.writePackedSfixed32),jspb.BinaryWriter.prototype.writePackedSfixed64=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++)this.encoder_.writeInt64(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedSfixed64",jspb.BinaryWriter.prototype.writePackedSfixed64),jspb.BinaryWriter.prototype.writePackedSfixed64String=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++)this.encoder_.writeInt64String(B[A])},jspb.BinaryWriter.prototype.writePackedFloat=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(4*B.length),A=0;A<B.length;A++)this.encoder_.writeFloat(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedFloat",jspb.BinaryWriter.prototype.writePackedFloat),jspb.BinaryWriter.prototype.writePackedDouble=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++)this.encoder_.writeDouble(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedDouble",jspb.BinaryWriter.prototype.writePackedDouble),jspb.BinaryWriter.prototype.writePackedBool=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(B.length),A=0;A<B.length;A++)this.encoder_.writeBool(B[A])},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedBool",jspb.BinaryWriter.prototype.writePackedBool),jspb.BinaryWriter.prototype.writePackedEnum=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeEnum(B[N]);this.endDelimited_(A)}},goog.exportProperty(jspb.BinaryWriter.prototype,"writePackedEnum",jspb.BinaryWriter.prototype.writePackedEnum),jspb.BinaryWriter.prototype.writePackedFixedHash64=function(A,B){if(null!=B&&B.length)for(this.writeFieldHeader_(A,jspb.BinaryConstants.WireType.DELIMITED),this.encoder_.writeUnsignedVarint32(8*B.length),A=0;A<B.length;A++)this.encoder_.writeFixedHash64(B[A])},jspb.BinaryWriter.prototype.writePackedVarintHash64=function(A,B){if(null!=B&&B.length){A=this.beginDelimited_(A);for(var N=0;N<B.length;N++)this.encoder_.writeVarintHash64(B[N]);this.endDelimited_(A)}},jspb.Map=function(A,B){this.arr_=A,this.valueCtor_=B,this.map_={},this.arrClean=!0,0<this.arr_.length&&this.loadFromArray_()},goog.exportSymbol("jspb.Map",jspb.Map),jspb.Map.prototype.loadFromArray_=function(){for(var A=0;A<this.arr_.length;A++){var B=this.arr_[A],N=B[0];this.map_[N.toString()]=new jspb.Map.Entry_(N,B[1])}this.arrClean=!0},jspb.Map.prototype.toArray=function(){if(this.arrClean){if(this.valueCtor_){var A,B=this.map_;for(A in B)if(Object.prototype.hasOwnProperty.call(B,A)){var N=B[A].valueWrapper;N&&N.toArray()}}}else{for(this.arr_.length=0,(B=this.stringKeys_()).sort(),A=0;A<B.length;A++){var U=this.map_[B[A]];(N=U.valueWrapper)&&N.toArray(),this.arr_.push([U.key,U.value])}this.arrClean=!0}return this.arr_},goog.exportProperty(jspb.Map.prototype,"toArray",jspb.Map.prototype.toArray),jspb.Map.prototype.toObject=function(A,B){for(var N=this.toArray(),U=[],H=0;H<N.length;H++){var W=this.map_[N[H][0].toString()];this.wrapEntry_(W);var j=W.valueWrapper;j?(jspb.asserts.assert(B),U.push([W.key,B(A,j)])):U.push([W.key,W.value])}return U},goog.exportProperty(jspb.Map.prototype,"toObject",jspb.Map.prototype.toObject),jspb.Map.fromObject=function(A,B,N){B=new jspb.Map([],B);for(var U=0;U<A.length;U++){var H=A[U][0],W=N(A[U][1]);B.set(H,W)}return B},goog.exportProperty(jspb.Map,"fromObject",jspb.Map.fromObject),jspb.Map.ArrayIteratorIterable_=function(A){this.idx_=0,this.arr_=A},jspb.Map.ArrayIteratorIterable_.prototype.next=function(){return this.idx_<this.arr_.length?{done:!1,value:this.arr_[this.idx_++]}:{done:!0,value:void 0}},"undefined"!=typeof Symbol&&(jspb.Map.ArrayIteratorIterable_.prototype[Symbol.iterator]=function(){return this}),jspb.Map.prototype.getLength=function(){return this.stringKeys_().length},goog.exportProperty(jspb.Map.prototype,"getLength",jspb.Map.prototype.getLength),jspb.Map.prototype.clear=function(){this.map_={},this.arrClean=!1},goog.exportProperty(jspb.Map.prototype,"clear",jspb.Map.prototype.clear),jspb.Map.prototype.del=function(A){A=A.toString();var B=this.map_.hasOwnProperty(A);return delete this.map_[A],this.arrClean=!1,B},goog.exportProperty(jspb.Map.prototype,"del",jspb.Map.prototype.del),jspb.Map.prototype.getEntryList=function(){var A=[],B=this.stringKeys_();B.sort();for(var N=0;N<B.length;N++){var U=this.map_[B[N]];A.push([U.key,U.value])}return A},goog.exportProperty(jspb.Map.prototype,"getEntryList",jspb.Map.prototype.getEntryList),jspb.Map.prototype.entries=function(){var A=[],B=this.stringKeys_();B.sort();for(var N=0;N<B.length;N++){var U=this.map_[B[N]];A.push([U.key,this.wrapEntry_(U)])}return new jspb.Map.ArrayIteratorIterable_(A)},goog.exportProperty(jspb.Map.prototype,"entries",jspb.Map.prototype.entries),jspb.Map.prototype.keys=function(){var A=[],B=this.stringKeys_();B.sort();for(var N=0;N<B.length;N++)A.push(this.map_[B[N]].key);return new jspb.Map.ArrayIteratorIterable_(A)},goog.exportProperty(jspb.Map.prototype,"keys",jspb.Map.prototype.keys),jspb.Map.prototype.values=function(){var A=[],B=this.stringKeys_();B.sort();for(var N=0;N<B.length;N++)A.push(this.wrapEntry_(this.map_[B[N]]));return new jspb.Map.ArrayIteratorIterable_(A)},goog.exportProperty(jspb.Map.prototype,"values",jspb.Map.prototype.values),jspb.Map.prototype.forEach=function(A,B){var N=this.stringKeys_();N.sort();for(var U=0;U<N.length;U++){var H=this.map_[N[U]];A.call(B,this.wrapEntry_(H),H.key,this)}},goog.exportProperty(jspb.Map.prototype,"forEach",jspb.Map.prototype.forEach),jspb.Map.prototype.set=function(A,B){var N=new jspb.Map.Entry_(A);return this.valueCtor_?(N.valueWrapper=B,N.value=B.toArray()):N.value=B,this.map_[A.toString()]=N,this.arrClean=!1,this},goog.exportProperty(jspb.Map.prototype,"set",jspb.Map.prototype.set),jspb.Map.prototype.wrapEntry_=function(A){return this.valueCtor_?(A.valueWrapper||(A.valueWrapper=new this.valueCtor_(A.value)),A.valueWrapper):A.value},jspb.Map.prototype.get=function(A){if(A=this.map_[A.toString()])return this.wrapEntry_(A)},goog.exportProperty(jspb.Map.prototype,"get",jspb.Map.prototype.get),jspb.Map.prototype.has=function(A){return A.toString()in this.map_},goog.exportProperty(jspb.Map.prototype,"has",jspb.Map.prototype.has),jspb.Map.prototype.serializeBinary=function(A,B,N,U,H){var W=this.stringKeys_();W.sort();for(var j=0;j<W.length;j++){var V=this.map_[W[j]];B.beginSubMessage(A),N.call(B,1,V.key),this.valueCtor_?U.call(B,2,this.wrapEntry_(V),H):U.call(B,2,V.value),B.endSubMessage()}},goog.exportProperty(jspb.Map.prototype,"serializeBinary",jspb.Map.prototype.serializeBinary),jspb.Map.deserializeBinary=function(A,B,N,U,H,W,j){for(;B.nextField()&&!B.isEndGroup();){var V=B.getFieldNumber();1==V?W=N.call(B):2==V&&(A.valueCtor_?(jspb.asserts.assert(H),j||(j=new A.valueCtor_),U.call(B,j,H)):j=U.call(B))}jspb.asserts.assert(void 0!=W),jspb.asserts.assert(void 0!=j),A.set(W,j)},goog.exportProperty(jspb.Map,"deserializeBinary",jspb.Map.deserializeBinary),jspb.Map.prototype.stringKeys_=function(){var A,B=this.map_,N=[];for(A in B)Object.prototype.hasOwnProperty.call(B,A)&&N.push(A);return N},jspb.Map.Entry_=function(A,B){this.key=A,this.value=B,this.valueWrapper=void 0},jspb.ExtensionFieldInfo=function(A,B,N,U,H){this.fieldIndex=A,this.fieldName=B,this.ctor=N,this.toObjectFn=U,this.isRepeated=H},goog.exportSymbol("jspb.ExtensionFieldInfo",jspb.ExtensionFieldInfo),jspb.ExtensionFieldBinaryInfo=function(A,B,N,U,H,W){this.fieldInfo=A,this.binaryReaderFn=B,this.binaryWriterFn=N,this.binaryMessageSerializeFn=U,this.binaryMessageDeserializeFn=H,this.isPacked=W},goog.exportSymbol("jspb.ExtensionFieldBinaryInfo",jspb.ExtensionFieldBinaryInfo),jspb.ExtensionFieldInfo.prototype.isMessageType=function(){return!!this.ctor},goog.exportProperty(jspb.ExtensionFieldInfo.prototype,"isMessageType",jspb.ExtensionFieldInfo.prototype.isMessageType),jspb.Message=function(){},goog.exportSymbol("jspb.Message",jspb.Message),jspb.Message.GENERATE_TO_OBJECT=!0,goog.exportProperty(jspb.Message,"GENERATE_TO_OBJECT",jspb.Message.GENERATE_TO_OBJECT),jspb.Message.GENERATE_FROM_OBJECT=!goog.DISALLOW_TEST_ONLY_CODE,goog.exportProperty(jspb.Message,"GENERATE_FROM_OBJECT",jspb.Message.GENERATE_FROM_OBJECT),jspb.Message.GENERATE_TO_STRING=!0,jspb.Message.ASSUME_LOCAL_ARRAYS=!1,jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS=!0,jspb.Message.SUPPORTS_UINT8ARRAY_="function"==typeof Uint8Array,jspb.Message.prototype.getJsPbMessageId=function(){return this.messageId_},goog.exportProperty(jspb.Message.prototype,"getJsPbMessageId",jspb.Message.prototype.getJsPbMessageId),jspb.Message.getIndex_=function(A,B){return B+A.arrayIndexOffset_},jspb.Message.hiddenES6Property_=function(){},jspb.Message.getFieldNumber_=function(A,B){return B-A.arrayIndexOffset_},jspb.Message.initialize=function(A,B,N,U,H,W){if(A.wrappers_=null,B||(B=N?[N]:[]),A.messageId_=N?String(N):void 0,A.arrayIndexOffset_=0===N?-1:0,A.array=B,jspb.Message.initPivotAndExtensionObject_(A,U),A.convertedPrimitiveFields_={},jspb.Message.SERIALIZE_EMPTY_TRAILING_FIELDS||(A.repeatedFields=H),H)for(B=0;B<H.length;B++)(N=H[B])<A.pivot_?(N=jspb.Message.getIndex_(A,N),A.array[N]=A.array[N]||jspb.Message.EMPTY_LIST_SENTINEL_):(jspb.Message.maybeInitEmptyExtensionObject_(A),A.extensionObject_[N]=A.extensionObject_[N]||jspb.Message.EMPTY_LIST_SENTINEL_);if(W&&W.length)for(B=0;B<W.length;B++)jspb.Message.computeOneofCase(A,W[B])},goog.exportProperty(jspb.Message,"initialize",jspb.Message.initialize),jspb.Message.EMPTY_LIST_SENTINEL_=goog.DEBUG&&Object.freeze?Object.freeze([]):[],jspb.Message.isArray_=function(A){return jspb.Message.ASSUME_LOCAL_ARRAYS?A instanceof Array:Array.isArray(A)},jspb.Message.isExtensionObject_=function(A){return null!==A&&"object"==typeof A&&!jspb.Message.isArray_(A)&&!(jspb.Message.SUPPORTS_UINT8ARRAY_&&A instanceof Uint8Array)},jspb.Message.initPivotAndExtensionObject_=function(A,B){var N=A.array.length,U=-1;if(N&&(U=N-1,N=A.array[U],jspb.Message.isExtensionObject_(N))){A.pivot_=jspb.Message.getFieldNumber_(A,U),A.extensionObject_=N;return}-1<B?(A.pivot_=Math.max(B,jspb.Message.getFieldNumber_(A,U+1)),A.extensionObject_=null):A.pivot_=Number.MAX_VALUE},jspb.Message.maybeInitEmptyExtensionObject_=function(A){var B=jspb.Message.getIndex_(A,A.pivot_);A.array[B]||(A.extensionObject_=A.array[B]={})},jspb.Message.toObjectList=function(A,B,N){for(var U=[],H=0;H<A.length;H++)U[H]=B.call(A[H],N,A[H]);return U},goog.exportProperty(jspb.Message,"toObjectList",jspb.Message.toObjectList),jspb.Message.toObjectExtension=function(A,B,N,U,H){for(var W in N){var j=N[W],V=U.call(A,j);if(null!=V){for(var K in j.fieldName)if(j.fieldName.hasOwnProperty(K))break;B[K]=j.toObjectFn?j.isRepeated?jspb.Message.toObjectList(V,j.toObjectFn,H):j.toObjectFn(H,V):V}}},goog.exportProperty(jspb.Message,"toObjectExtension",jspb.Message.toObjectExtension),jspb.Message.serializeBinaryExtensions=function(A,B,N,U){for(var H in N){var W=N[H],j=W.fieldInfo;if(!W.binaryWriterFn)throw Error("Message extension present that was generated without binary serialization support");var V=U.call(A,j);if(null!=V){if(j.isMessageType()){if(W.binaryMessageSerializeFn)W.binaryWriterFn.call(B,j.fieldIndex,V,W.binaryMessageSerializeFn);else throw Error("Message extension present holding submessage without binary support enabled, and message is being serialized to binary format")}else W.binaryWriterFn.call(B,j.fieldIndex,V)}}},goog.exportProperty(jspb.Message,"serializeBinaryExtensions",jspb.Message.serializeBinaryExtensions),jspb.Message.readBinaryExtension=function(A,B,N,U,H){var W=N[B.getFieldNumber()];if(W){if(N=W.fieldInfo,!W.binaryReaderFn)throw Error("Deserializing extension whose generated code does not support binary format");if(N.isMessageType()){var j=new N.ctor;W.binaryReaderFn.call(B,j,W.binaryMessageDeserializeFn)}else j=W.binaryReaderFn.call(B);N.isRepeated&&!W.isPacked?(B=U.call(A,N))?B.push(j):H.call(A,N,[j]):H.call(A,N,j)}else B.skipField()},goog.exportProperty(jspb.Message,"readBinaryExtension",jspb.Message.readBinaryExtension),jspb.Message.getField=function(A,B){if(B<A.pivot_){B=jspb.Message.getIndex_(A,B);var N=A.array[B];return N===jspb.Message.EMPTY_LIST_SENTINEL_?A.array[B]=[]:N}if(A.extensionObject_)return(N=A.extensionObject_[B])===jspb.Message.EMPTY_LIST_SENTINEL_?A.extensionObject_[B]=[]:N},goog.exportProperty(jspb.Message,"getField",jspb.Message.getField),jspb.Message.getRepeatedField=function(A,B){return jspb.Message.getField(A,B)},goog.exportProperty(jspb.Message,"getRepeatedField",jspb.Message.getRepeatedField),jspb.Message.getOptionalFloatingPointField=function(A,B){return null==(A=jspb.Message.getField(A,B))?A:+A},goog.exportProperty(jspb.Message,"getOptionalFloatingPointField",jspb.Message.getOptionalFloatingPointField),jspb.Message.getBooleanField=function(A,B){return null==(A=jspb.Message.getField(A,B))?A:!!A},goog.exportProperty(jspb.Message,"getBooleanField",jspb.Message.getBooleanField),jspb.Message.getRepeatedFloatingPointField=function(A,B){var N=jspb.Message.getRepeatedField(A,B);if(A.convertedPrimitiveFields_||(A.convertedPrimitiveFields_={}),!A.convertedPrimitiveFields_[B]){for(var U=0;U<N.length;U++)N[U]=+N[U];A.convertedPrimitiveFields_[B]=!0}return N},goog.exportProperty(jspb.Message,"getRepeatedFloatingPointField",jspb.Message.getRepeatedFloatingPointField),jspb.Message.getRepeatedBooleanField=function(A,B){var N=jspb.Message.getRepeatedField(A,B);if(A.convertedPrimitiveFields_||(A.convertedPrimitiveFields_={}),!A.convertedPrimitiveFields_[B]){for(var U=0;U<N.length;U++)N[U]=!!N[U];A.convertedPrimitiveFields_[B]=!0}return N},goog.exportProperty(jspb.Message,"getRepeatedBooleanField",jspb.Message.getRepeatedBooleanField),jspb.Message.bytesAsB64=function(A){return null==A||"string"==typeof A?A:jspb.Message.SUPPORTS_UINT8ARRAY_&&A instanceof Uint8Array?goog.crypt.base64.encodeByteArray(A):(jspb.asserts.fail("Cannot coerce to b64 string: "+goog.typeOf(A)),null)},goog.exportProperty(jspb.Message,"bytesAsB64",jspb.Message.bytesAsB64),jspb.Message.bytesAsU8=function(A){return null==A||A instanceof Uint8Array?A:"string"==typeof A?goog.crypt.base64.decodeStringToUint8Array(A):(jspb.asserts.fail("Cannot coerce to Uint8Array: "+goog.typeOf(A)),null)},goog.exportProperty(jspb.Message,"bytesAsU8",jspb.Message.bytesAsU8),jspb.Message.bytesListAsB64=function(A){return jspb.Message.assertConsistentTypes_(A),A.length&&"string"!=typeof A[0]?goog.array.map(A,jspb.Message.bytesAsB64):A},goog.exportProperty(jspb.Message,"bytesListAsB64",jspb.Message.bytesListAsB64),jspb.Message.bytesListAsU8=function(A){return jspb.Message.assertConsistentTypes_(A),!A.length||A[0]instanceof Uint8Array?A:goog.array.map(A,jspb.Message.bytesAsU8)},goog.exportProperty(jspb.Message,"bytesListAsU8",jspb.Message.bytesListAsU8),jspb.Message.assertConsistentTypes_=function(A){if(goog.DEBUG&&A&&1<A.length){var B=goog.typeOf(A[0]);goog.array.forEach(A,function(A){goog.typeOf(A)!=B&&jspb.asserts.fail("Inconsistent type in JSPB repeated field array. Got "+goog.typeOf(A)+" expected "+B)})}},jspb.Message.getFieldWithDefault=function(A,B,N){return null==(A=jspb.Message.getField(A,B))?N:A},goog.exportProperty(jspb.Message,"getFieldWithDefault",jspb.Message.getFieldWithDefault),jspb.Message.getBooleanFieldWithDefault=function(A,B,N){return null==(A=jspb.Message.getBooleanField(A,B))?N:A},goog.exportProperty(jspb.Message,"getBooleanFieldWithDefault",jspb.Message.getBooleanFieldWithDefault),jspb.Message.getFloatingPointFieldWithDefault=function(A,B,N){return null==(A=jspb.Message.getOptionalFloatingPointField(A,B))?N:A},goog.exportProperty(jspb.Message,"getFloatingPointFieldWithDefault",jspb.Message.getFloatingPointFieldWithDefault),jspb.Message.getFieldProto3=jspb.Message.getFieldWithDefault,goog.exportProperty(jspb.Message,"getFieldProto3",jspb.Message.getFieldProto3),jspb.Message.getMapField=function(A,B,N,U){if(A.wrappers_||(A.wrappers_={}),B in A.wrappers_)return A.wrappers_[B];var H=jspb.Message.getField(A,B);if(!H){if(N)return;H=[],jspb.Message.setField(A,B,H)}return A.wrappers_[B]=new jspb.Map(H,U)},goog.exportProperty(jspb.Message,"getMapField",jspb.Message.getMapField),jspb.Message.setField=function(A,B,N){return jspb.asserts.assertInstanceof(A,jspb.Message),B<A.pivot_?A.array[jspb.Message.getIndex_(A,B)]=N:(jspb.Message.maybeInitEmptyExtensionObject_(A),A.extensionObject_[B]=N),A},goog.exportProperty(jspb.Message,"setField",jspb.Message.setField),jspb.Message.setProto3IntField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,0)},goog.exportProperty(jspb.Message,"setProto3IntField",jspb.Message.setProto3IntField),jspb.Message.setProto3FloatField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,0)},goog.exportProperty(jspb.Message,"setProto3FloatField",jspb.Message.setProto3FloatField),jspb.Message.setProto3BooleanField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,!1)},goog.exportProperty(jspb.Message,"setProto3BooleanField",jspb.Message.setProto3BooleanField),jspb.Message.setProto3StringField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,"")},goog.exportProperty(jspb.Message,"setProto3StringField",jspb.Message.setProto3StringField),jspb.Message.setProto3BytesField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,"")},goog.exportProperty(jspb.Message,"setProto3BytesField",jspb.Message.setProto3BytesField),jspb.Message.setProto3EnumField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,0)},goog.exportProperty(jspb.Message,"setProto3EnumField",jspb.Message.setProto3EnumField),jspb.Message.setProto3StringIntField=function(A,B,N){return jspb.Message.setFieldIgnoringDefault_(A,B,N,"0")},goog.exportProperty(jspb.Message,"setProto3StringIntField",jspb.Message.setProto3StringIntField),jspb.Message.setFieldIgnoringDefault_=function(A,B,N,U){return jspb.asserts.assertInstanceof(A,jspb.Message),N!==U?jspb.Message.setField(A,B,N):B<A.pivot_?A.array[jspb.Message.getIndex_(A,B)]=null:(jspb.Message.maybeInitEmptyExtensionObject_(A),delete A.extensionObject_[B]),A},jspb.Message.addToRepeatedField=function(A,B,N,U){return jspb.asserts.assertInstanceof(A,jspb.Message),B=jspb.Message.getRepeatedField(A,B),void 0!=U?B.splice(U,0,N):B.push(N),A},goog.exportProperty(jspb.Message,"addToRepeatedField",jspb.Message.addToRepeatedField),jspb.Message.setOneofField=function(A,B,N,U){return jspb.asserts.assertInstanceof(A,jspb.Message),(N=jspb.Message.computeOneofCase(A,N))&&N!==B&&void 0!==U&&(A.wrappers_&&N in A.wrappers_&&(A.wrappers_[N]=void 0),jspb.Message.setField(A,N,void 0)),jspb.Message.setField(A,B,U)},goog.exportProperty(jspb.Message,"setOneofField",jspb.Message.setOneofField),jspb.Message.computeOneofCase=function(A,B){for(var N,U,H=0;H<B.length;H++){var W=B[H],j=jspb.Message.getField(A,W);null!=j&&(N=W,U=j,jspb.Message.setField(A,W,void 0))}return N?(jspb.Message.setField(A,N,U),N):0},goog.exportProperty(jspb.Message,"computeOneofCase",jspb.Message.computeOneofCase),jspb.Message.getWrapperField=function(A,B,N,U){if(A.wrappers_||(A.wrappers_={}),!A.wrappers_[N]){var H=jspb.Message.getField(A,N);(U||H)&&(A.wrappers_[N]=new B(H))}return A.wrappers_[N]},goog.exportProperty(jspb.Message,"getWrapperField",jspb.Message.getWrapperField),jspb.Message.getRepeatedWrapperField=function(A,B,N){return jspb.Message.wrapRepeatedField_(A,B,N),(B=A.wrappers_[N])==jspb.Message.EMPTY_LIST_SENTINEL_&&(B=A.wrappers_[N]=[]),B},goog.exportProperty(jspb.Message,"getRepeatedWrapperField",jspb.Message.getRepeatedWrapperField),jspb.Message.wrapRepeatedField_=function(A,B,N){if(A.wrappers_||(A.wrappers_={}),!A.wrappers_[N]){for(var U=jspb.Message.getRepeatedField(A,N),H=[],W=0;W<U.length;W++)H[W]=new B(U[W]);A.wrappers_[N]=H}},jspb.Message.setWrapperField=function(A,B,N){jspb.asserts.assertInstanceof(A,jspb.Message),A.wrappers_||(A.wrappers_={});var U=N?N.toArray():N;return A.wrappers_[B]=N,jspb.Message.setField(A,B,U)},goog.exportProperty(jspb.Message,"setWrapperField",jspb.Message.setWrapperField),jspb.Message.setOneofWrapperField=function(A,B,N,U){jspb.asserts.assertInstanceof(A,jspb.Message),A.wrappers_||(A.wrappers_={});var H=U?U.toArray():U;return A.wrappers_[B]=U,jspb.Message.setOneofField(A,B,N,H)},goog.exportProperty(jspb.Message,"setOneofWrapperField",jspb.Message.setOneofWrapperField),jspb.Message.setRepeatedWrapperField=function(A,B,N){jspb.asserts.assertInstanceof(A,jspb.Message),A.wrappers_||(A.wrappers_={}),N=N||[];for(var U=[],H=0;H<N.length;H++)U[H]=N[H].toArray();return A.wrappers_[B]=N,jspb.Message.setField(A,B,U)},goog.exportProperty(jspb.Message,"setRepeatedWrapperField",jspb.Message.setRepeatedWrapperField),jspb.Message.addToRepeatedWrapperField=function(A,B,N,U,H){jspb.Message.wrapRepeatedField_(A,U,B);var W=A.wrappers_[B];return W||(W=A.wrappers_[B]=[]),N=N||new U,A=jspb.Message.getRepeatedField(A,B),void 0!=H?(W.splice(H,0,N),A.splice(H,0,N.toArray())):(W.push(N),A.push(N.toArray())),N},goog.exportProperty(jspb.Message,"addToRepeatedWrapperField",jspb.Message.addToRepeatedWrapperField),jspb.Message.toMap=function(A,B,N,U){for(var H={},W=0;W<A.length;W++)H[B.call(A[W])]=N?N.call(A[W],U,A[W]):A[W];return H},goog.exportProperty(jspb.Message,"toMap",jspb.Message.toMap),jspb.Message.prototype.syncMapFields_=function(){if(this.wrappers_)for(var A in this.wrappers_){var B=this.wrappers_[A];if(Array.isArray(B))for(var N=0;N<B.length;N++)B[N]&&B[N].toArray();else B&&B.toArray()}},jspb.Message.prototype.toArray=function(){return this.syncMapFields_(),this.array},goog.exportProperty(jspb.Message.prototype,"toArray",jspb.Message.prototype.toArray),jspb.Message.GENERATE_TO_STRING&&(jspb.Message.prototype.toString=function(){return this.syncMapFields_(),this.array.toString()}),jspb.Message.prototype.getExtension=function(A){if(this.extensionObject_){this.wrappers_||(this.wrappers_={});var B=A.fieldIndex;if(A.isRepeated){if(A.isMessageType())return this.wrappers_[B]||(this.wrappers_[B]=goog.array.map(this.extensionObject_[B]||[],function(B){return new A.ctor(B)})),this.wrappers_[B]}else if(A.isMessageType())return!this.wrappers_[B]&&this.extensionObject_[B]&&(this.wrappers_[B]=new A.ctor(this.extensionObject_[B])),this.wrappers_[B];return this.extensionObject_[B]}},goog.exportProperty(jspb.Message.prototype,"getExtension",jspb.Message.prototype.getExtension),jspb.Message.prototype.setExtension=function(A,B){this.wrappers_||(this.wrappers_={}),jspb.Message.maybeInitEmptyExtensionObject_(this);var N=A.fieldIndex;return A.isRepeated?(B=B||[],A.isMessageType()?(this.wrappers_[N]=B,this.extensionObject_[N]=goog.array.map(B,function(A){return A.toArray()})):this.extensionObject_[N]=B):A.isMessageType()?(this.wrappers_[N]=B,this.extensionObject_[N]=B?B.toArray():B):this.extensionObject_[N]=B,this},goog.exportProperty(jspb.Message.prototype,"setExtension",jspb.Message.prototype.setExtension),jspb.Message.difference=function(A,B){if(!(A instanceof B.constructor))throw Error("Messages have different types.");var N=A.toArray();B=B.toArray();var U=[],H=0,W=N.length>B.length?N.length:B.length;for(A.getJsPbMessageId()&&(U[0]=A.getJsPbMessageId(),H=1);H<W;H++)jspb.Message.compareFields(N[H],B[H])||(U[H]=B[H]);return new A.constructor(U)},goog.exportProperty(jspb.Message,"difference",jspb.Message.difference),jspb.Message.equals=function(A,B){return A==B||!(!A||!B)&&A instanceof B.constructor&&jspb.Message.compareFields(A.toArray(),B.toArray())},goog.exportProperty(jspb.Message,"equals",jspb.Message.equals),jspb.Message.compareExtensions=function(A,B){B=B||{};var N,U={};for(N in A=A||{})U[N]=0;for(N in B)U[N]=0;for(N in U)if(!jspb.Message.compareFields(A[N],B[N]))return!1;return!0},goog.exportProperty(jspb.Message,"compareExtensions",jspb.Message.compareExtensions),jspb.Message.compareFields=function(A,B){if(A==B)return!0;if(!goog.isObject(A)||!goog.isObject(B))return!!("number"==typeof A&&isNaN(A)||"number"==typeof B&&isNaN(B))&&String(A)==String(B);if(A.constructor!=B.constructor)return!1;if(jspb.Message.SUPPORTS_UINT8ARRAY_&&A.constructor===Uint8Array){if(A.length!=B.length)return!1;for(var N=0;N<A.length;N++)if(A[N]!=B[N])return!1;return!0}if(A.constructor===Array){var U=void 0,H=void 0,W=Math.max(A.length,B.length);for(N=0;N<W;N++){var j=A[N],V=B[N];if(j&&j.constructor==Object&&(jspb.asserts.assert(void 0===U),jspb.asserts.assert(N===A.length-1),U=j,j=void 0),V&&V.constructor==Object&&(jspb.asserts.assert(void 0===H),jspb.asserts.assert(N===B.length-1),H=V,V=void 0),!jspb.Message.compareFields(j,V))return!1}return!U&&!H||(U=U||{},H=H||{},jspb.Message.compareExtensions(U,H))}if(A.constructor===Object)return jspb.Message.compareExtensions(A,B);throw Error("Invalid type in JSPB array")},goog.exportProperty(jspb.Message,"compareFields",jspb.Message.compareFields),jspb.Message.prototype.cloneMessage=function(){return jspb.Message.cloneMessage(this)},goog.exportProperty(jspb.Message.prototype,"cloneMessage",jspb.Message.prototype.cloneMessage),jspb.Message.prototype.clone=function(){return jspb.Message.cloneMessage(this)},goog.exportProperty(jspb.Message.prototype,"clone",jspb.Message.prototype.clone),jspb.Message.clone=function(A){return jspb.Message.cloneMessage(A)},goog.exportProperty(jspb.Message,"clone",jspb.Message.clone),jspb.Message.cloneMessage=function(A){return new A.constructor(jspb.Message.clone_(A.toArray()))},jspb.Message.copyInto=function(A,B){jspb.asserts.assertInstanceof(A,jspb.Message),jspb.asserts.assertInstanceof(B,jspb.Message),jspb.asserts.assert(A.constructor==B.constructor,"Copy source and target message should have the same type."),A=jspb.Message.clone(A);for(var N=B.toArray(),U=A.toArray(),H=N.length=0;H<U.length;H++)N[H]=U[H];B.wrappers_=A.wrappers_,B.extensionObject_=A.extensionObject_},goog.exportProperty(jspb.Message,"copyInto",jspb.Message.copyInto),jspb.Message.clone_=function(A){if(Array.isArray(A)){for(var B=Array(A.length),N=0;N<A.length;N++){var U=A[N];null!=U&&(B[N]="object"==typeof U?jspb.Message.clone_(jspb.asserts.assert(U)):U)}return B}if(jspb.Message.SUPPORTS_UINT8ARRAY_&&A instanceof Uint8Array)return new Uint8Array(A);for(N in B={},A)null!=(U=A[N])&&(B[N]="object"==typeof U?jspb.Message.clone_(jspb.asserts.assert(U)):U);return B},jspb.Message.registerMessageType=function(A,B){B.messageId=A},goog.exportProperty(jspb.Message,"registerMessageType",jspb.Message.registerMessageType),jspb.Message.messageSetExtensions={},jspb.Message.messageSetExtensionsBinary={},jspb.Export={},exports.Map=jspb.Map,exports.Message=jspb.Message,exports.BinaryReader=jspb.BinaryReader,exports.BinaryWriter=jspb.BinaryWriter,exports.ExtensionFieldInfo=jspb.ExtensionFieldInfo,exports.ExtensionFieldBinaryInfo=jspb.ExtensionFieldBinaryInfo,exports.exportSymbol=goog.exportSymbol,exports.inherits=goog.inherits,exports.object={extend:goog.object.extend},exports.typeOf=goog.typeOf},47164:function(A,B){B.read=function(A,B,N,U,H){var W,j,V=8*H-U-1,K=(1<<V)-1,X=K>>1,J=-7,ee=N?H-1:0,et=N?-1:1,er=A[B+ee];for(ee+=et,W=er&(1<<-J)-1,er>>=-J,J+=V;J>0;W=256*W+A[B+ee],ee+=et,J-=8);for(j=W&(1<<-J)-1,W>>=-J,J+=U;J>0;j=256*j+A[B+ee],ee+=et,J-=8);if(0===W)W=1-X;else{if(W===K)return j?NaN:1/0*(er?-1:1);j+=Math.pow(2,U),W-=X}return(er?-1:1)*j*Math.pow(2,W-U)},B.write=function(A,B,N,U,H,W){var j,V,K,X=8*W-H-1,J=(1<<X)-1,ee=J>>1,et=5960464477539062e-23*(23===H),er=U?0:W-1,en=U?1:-1,ei=+(B<0||0===B&&1/B<0);for(isNaN(B=Math.abs(B))||B===1/0?(V=+!!isNaN(B),j=J):(j=Math.floor(Math.log(B)/Math.LN2),B*(K=Math.pow(2,-j))<1&&(j--,K*=2),j+ee>=1?B+=et/K:B+=et*Math.pow(2,1-ee),B*K>=2&&(j++,K/=2),j+ee>=J?(V=0,j=J):j+ee>=1?(V=(B*K-1)*Math.pow(2,H),j+=ee):(V=B*Math.pow(2,ee-1)*Math.pow(2,H),j=0));H>=8;A[N+er]=255&V,er+=en,V/=256,H-=8);for(j=j<<H|V,X+=H;X>0;A[N+er]=255&j,er+=en,j/=256,X-=8);A[N+er-en]|=128*ei}},17476:function(A){function isBuffer(A){return!!A.constructor&&"function"==typeof A.constructor.isBuffer&&A.constructor.isBuffer(A)}function isSlowBuffer(A){return"function"==typeof A.readFloatLE&&"function"==typeof A.slice&&isBuffer(A.slice(0,0))}A.exports=function(A){return null!=A&&(isBuffer(A)||isSlowBuffer(A)||!!A._isBuffer)}},78943:function(A,B,N){var U=null;"undefined"!=typeof WebSocket?U=WebSocket:"undefined"!=typeof MozWebSocket?U=MozWebSocket:void 0!==N.g?U=N.g.WebSocket||N.g.MozWebSocket:"undefined"!=typeof window?U=window.WebSocket||window.MozWebSocket:"undefined"!=typeof self&&(U=self.WebSocket||self.MozWebSocket),A.exports=U},80022:function(A,B,N){A=N.nmd(A);var U=200,H="__lodash_hash_undefined__",W=0x1fffffffffffff,j="[object Arguments]",V="[object Array]",K="[object Boolean]",X="[object Date]",J="[object Error]",ee="[object Function]",et="[object GeneratorFunction]",er="[object Map]",en="[object Number]",ei="[object Object]",eo="[object Promise]",ea="[object RegExp]",es="[object Set]",eu="[object String]",el="[object Symbol]",ec="[object WeakMap]",ef="[object ArrayBuffer]",ed="[object DataView]",ep="[object Float32Array]",eh="[object Float64Array]",eg="[object Int8Array]",ey="[object Int16Array]",em="[object Int32Array]",e_="[object Uint8Array]",ev="[object Uint8ClampedArray]",eS="[object Uint16Array]",eb="[object Uint32Array]",eE=/[\\^$.*+?()[\]{}|]/g,eT=/\w*$/,eA=/^\[object .+?Constructor\]$/,ew=/^(?:0|[1-9]\d*)$/,eO={};eO[j]=eO[V]=eO[ef]=eO[ed]=eO[K]=eO[X]=eO[ep]=eO[eh]=eO[eg]=eO[ey]=eO[em]=eO[er]=eO[en]=eO[ei]=eO[ea]=eO[es]=eO[eu]=eO[el]=eO[e_]=eO[ev]=eO[eS]=eO[eb]=!0,eO[J]=eO[ee]=eO[ec]=!1;var eB="object"==typeof N.g&&N.g&&N.g.Object===Object&&N.g,eI="object"==typeof self&&self&&self.Object===Object&&self,eR=eB||eI||Function("return this")(),eC=B&&!B.nodeType&&B,eL=eC&&A&&!A.nodeType&&A,eN=eL&&eL.exports===eC;function addMapEntry(A,B){return A.set(B[0],B[1]),A}function addSetEntry(A,B){return A.add(B),A}function arrayEach(A,B){for(var N=-1,U=A?A.length:0;++N<U&&!1!==B(A[N],N,A););return A}function arrayPush(A,B){for(var N=-1,U=B.length,H=A.length;++N<U;)A[H+N]=B[N];return A}function arrayReduce(A,B,N,U){var H=-1,W=A?A.length:0;for(U&&W&&(N=A[++H]);++H<W;)N=B(N,A[H],H,A);return N}function baseTimes(A,B){for(var N=-1,U=Array(A);++N<A;)U[N]=B(N);return U}function getValue(A,B){return null==A?void 0:A[B]}function isHostObject(A){var B=!1;if(null!=A&&"function"!=typeof A.toString)try{B=!!(A+"")}catch(A){}return B}function mapToArray(A){var B=-1,N=Array(A.size);return A.forEach(function(A,U){N[++B]=[U,A]}),N}function overArg(A,B){return function(N){return A(B(N))}}function setToArray(A){var B=-1,N=Array(A.size);return A.forEach(function(A){N[++B]=A}),N}var eM=Array.prototype,ex=Function.prototype,eP=Object.prototype,eU=eR["__core-js_shared__"],eD=function(){var A=/[^.]+$/.exec(eU&&eU.keys&&eU.keys.IE_PROTO||"");return A?"Symbol(src)_1."+A:""}(),eF=ex.toString,ek=eP.hasOwnProperty,eH=eP.toString,eW=RegExp("^"+eF.call(ek).replace(eE,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),ej=eN?eR.Buffer:void 0,eV=eR.Symbol,eG=eR.Uint8Array,eY=overArg(Object.getPrototypeOf,Object),e$=Object.create,ez=eP.propertyIsEnumerable,eK=eM.splice,eZ=Object.getOwnPropertySymbols,eX=ej?ej.isBuffer:void 0,eJ=overArg(Object.keys,Object),eQ=getNative(eR,"DataView"),e0=getNative(eR,"Map"),e1=getNative(eR,"Promise"),e2=getNative(eR,"Set"),e3=getNative(eR,"WeakMap"),e6=getNative(Object,"create"),e4=toSource(eQ),e5=toSource(e0),e8=toSource(e1),e7=toSource(e2),e9=toSource(e3),te=eV?eV.prototype:void 0,tt=te?te.valueOf:void 0;function Hash(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function hashClear(){this.__data__=e6?e6(null):{}}function hashDelete(A){return this.has(A)&&delete this.__data__[A]}function hashGet(A){var B=this.__data__;if(e6){var N=B[A];return N===H?void 0:N}return ek.call(B,A)?B[A]:void 0}function hashHas(A){var B=this.__data__;return e6?void 0!==B[A]:ek.call(B,A)}function hashSet(A,B){return this.__data__[A]=e6&&void 0===B?H:B,this}function ListCache(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function listCacheClear(){this.__data__=[]}function listCacheDelete(A){var B=this.__data__,N=assocIndexOf(B,A);return!(N<0)&&(N==B.length-1?B.pop():eK.call(B,N,1),!0)}function listCacheGet(A){var B=this.__data__,N=assocIndexOf(B,A);return N<0?void 0:B[N][1]}function listCacheHas(A){return assocIndexOf(this.__data__,A)>-1}function listCacheSet(A,B){var N=this.__data__,U=assocIndexOf(N,A);return U<0?N.push([A,B]):N[U][1]=B,this}function MapCache(A){var B=-1,N=A?A.length:0;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function mapCacheClear(){this.__data__={hash:new Hash,map:new(e0||ListCache),string:new Hash}}function mapCacheDelete(A){return getMapData(this,A).delete(A)}function mapCacheGet(A){return getMapData(this,A).get(A)}function mapCacheHas(A){return getMapData(this,A).has(A)}function mapCacheSet(A,B){return getMapData(this,A).set(A,B),this}function Stack(A){this.__data__=new ListCache(A)}function stackClear(){this.__data__=new ListCache}function stackDelete(A){return this.__data__.delete(A)}function stackGet(A){return this.__data__.get(A)}function stackHas(A){return this.__data__.has(A)}function stackSet(A,B){var N=this.__data__;if(N instanceof ListCache){var H=N.__data__;if(!e0||H.length<U-1)return H.push([A,B]),this;N=this.__data__=new MapCache(H)}return N.set(A,B),this}function arrayLikeKeys(A,B){var N=ti(A)||isArguments(A)?baseTimes(A.length,String):[],U=N.length,H=!!U;for(var W in A)(B||ek.call(A,W))&&!(H&&("length"==W||isIndex(W,U)))&&N.push(W);return N}function assignValue(A,B,N){var U=A[B];(!(ek.call(A,B)&&eq(U,N))||void 0===N&&!(B in A))&&(A[B]=N)}function assocIndexOf(A,B){for(var N=A.length;N--;)if(eq(A[N][0],B))return N;return -1}function baseAssign(A,B){return A&&copyObject(B,keys(B),A)}function baseClone(A,B,N,U,H,W,V){if(U&&(K=W?U(A,H,W,V):U(A)),void 0!==K)return K;if(!isObject(A))return A;var K,X=ti(A);if(X){if(K=initCloneArray(A),!B)return copyArray(A,K)}else{var J=tn(A),er=J==ee||J==et;if(to(A))return cloneBuffer(A,B);if(J==ei||J==j||er&&!W){if(isHostObject(A))return W?A:{};if(K=initCloneObject(er?{}:A),!B)return copySymbols(A,baseAssign(K,A))}else{if(!eO[J])return W?A:{};K=initCloneByTag(A,J,baseClone,B)}}V||(V=new Stack);var en=V.get(A);if(en)return en;if(V.set(A,K),!X)var eo=N?getAllKeys(A):keys(A);return arrayEach(eo||A,function(H,W){eo&&(H=A[W=H]),assignValue(K,W,baseClone(H,B,N,U,W,A,V))}),K}function baseCreate(A){return isObject(A)?e$(A):{}}function baseGetAllKeys(A,B,N){var U=B(A);return ti(A)?U:arrayPush(U,N(A))}function baseGetTag(A){return eH.call(A)}function baseIsNative(A){return!(!isObject(A)||isMasked(A))&&(isFunction(A)||isHostObject(A)?eW:eA).test(toSource(A))}function baseKeys(A){if(!isPrototype(A))return eJ(A);var B=[];for(var N in Object(A))ek.call(A,N)&&"constructor"!=N&&B.push(N);return B}function cloneBuffer(A,B){if(B)return A.slice();var N=new A.constructor(A.length);return A.copy(N),N}function cloneArrayBuffer(A){var B=new A.constructor(A.byteLength);return new eG(B).set(new eG(A)),B}function cloneDataView(A,B){var N=B?cloneArrayBuffer(A.buffer):A.buffer;return new A.constructor(N,A.byteOffset,A.byteLength)}function cloneMap(A,B,N){return arrayReduce(B?N(mapToArray(A),!0):mapToArray(A),addMapEntry,new A.constructor)}function cloneRegExp(A){var B=new A.constructor(A.source,eT.exec(A));return B.lastIndex=A.lastIndex,B}function cloneSet(A,B,N){return arrayReduce(B?N(setToArray(A),!0):setToArray(A),addSetEntry,new A.constructor)}function cloneSymbol(A){return tt?Object(tt.call(A)):{}}function cloneTypedArray(A,B){var N=B?cloneArrayBuffer(A.buffer):A.buffer;return new A.constructor(N,A.byteOffset,A.length)}function copyArray(A,B){var N=-1,U=A.length;for(B||(B=Array(U));++N<U;)B[N]=A[N];return B}function copyObject(A,B,N,U){N||(N={});for(var H=-1,W=B.length;++H<W;){var j=B[H],V=U?U(N[j],A[j],j,N,A):void 0;assignValue(N,j,void 0===V?A[j]:V)}return N}function copySymbols(A,B){return copyObject(A,tr(A),B)}function getAllKeys(A){return baseGetAllKeys(A,keys,tr)}function getMapData(A,B){var N=A.__data__;return isKeyable(B)?N["string"==typeof B?"string":"hash"]:N.map}function getNative(A,B){var N=getValue(A,B);return baseIsNative(N)?N:void 0}Hash.prototype.clear=hashClear,Hash.prototype.delete=hashDelete,Hash.prototype.get=hashGet,Hash.prototype.has=hashHas,Hash.prototype.set=hashSet,ListCache.prototype.clear=listCacheClear,ListCache.prototype.delete=listCacheDelete,ListCache.prototype.get=listCacheGet,ListCache.prototype.has=listCacheHas,ListCache.prototype.set=listCacheSet,MapCache.prototype.clear=mapCacheClear,MapCache.prototype.delete=mapCacheDelete,MapCache.prototype.get=mapCacheGet,MapCache.prototype.has=mapCacheHas,MapCache.prototype.set=mapCacheSet,Stack.prototype.clear=stackClear,Stack.prototype.delete=stackDelete,Stack.prototype.get=stackGet,Stack.prototype.has=stackHas,Stack.prototype.set=stackSet;var tr=eZ?overArg(eZ,Object):stubArray,tn=baseGetTag;function initCloneArray(A){var B=A.length,N=A.constructor(B);return B&&"string"==typeof A[0]&&ek.call(A,"index")&&(N.index=A.index,N.input=A.input),N}function initCloneObject(A){return"function"!=typeof A.constructor||isPrototype(A)?{}:baseCreate(eY(A))}function initCloneByTag(A,B,N,U){var H=A.constructor;switch(B){case ef:return cloneArrayBuffer(A);case K:case X:return new H(+A);case ed:return cloneDataView(A,U);case ep:case eh:case eg:case ey:case em:case e_:case ev:case eS:case eb:return cloneTypedArray(A,U);case er:return cloneMap(A,U,N);case en:case eu:return new H(A);case ea:return cloneRegExp(A);case es:return cloneSet(A,U,N);case el:return cloneSymbol(A)}}function isIndex(A,B){return!!(B=null==B?W:B)&&("number"==typeof A||ew.test(A))&&A>-1&&A%1==0&&A<B}function isKeyable(A){var B=typeof A;return"string"==B||"number"==B||"symbol"==B||"boolean"==B?"__proto__"!==A:null===A}function isMasked(A){return!!eD&&eD in A}function isPrototype(A){var B=A&&A.constructor;return A===("function"==typeof B&&B.prototype||eP)}function toSource(A){if(null!=A){try{return eF.call(A)}catch(A){}try{return A+""}catch(A){}}return""}function cloneDeep(A){return baseClone(A,!0,!0)}function eq(A,B){return A===B||A!=A&&B!=B}function isArguments(A){return isArrayLikeObject(A)&&ek.call(A,"callee")&&(!ez.call(A,"callee")||eH.call(A)==j)}(eQ&&tn(new eQ(new ArrayBuffer(1)))!=ed||e0&&tn(new e0)!=er||e1&&tn(e1.resolve())!=eo||e2&&tn(new e2)!=es||e3&&tn(new e3)!=ec)&&(tn=function(A){var B=eH.call(A),N=B==ei?A.constructor:void 0,U=N?toSource(N):void 0;if(U)switch(U){case e4:return ed;case e5:return er;case e8:return eo;case e7:return es;case e9:return ec}return B});var ti=Array.isArray;function isArrayLike(A){return null!=A&&isLength(A.length)&&!isFunction(A)}function isArrayLikeObject(A){return isObjectLike(A)&&isArrayLike(A)}var to=eX||stubFalse;function isFunction(A){var B=isObject(A)?eH.call(A):"";return B==ee||B==et}function isLength(A){return"number"==typeof A&&A>-1&&A%1==0&&A<=W}function isObject(A){var B=typeof A;return!!A&&("object"==B||"function"==B)}function isObjectLike(A){return!!A&&"object"==typeof A}function keys(A){return isArrayLike(A)?arrayLikeKeys(A):baseKeys(A)}function stubArray(){return[]}function stubFalse(){return!1}A.exports=cloneDeep},51899:function(A,B,N){var U="Expected a function",H=0/0,W="[object Symbol]",j=/^\s+|\s+$/g,V=/^[-+]0x[0-9a-f]+$/i,K=/^0b[01]+$/i,X=/^0o[0-7]+$/i,J=parseInt,ee="object"==typeof N.g&&N.g&&N.g.Object===Object&&N.g,et="object"==typeof self&&self&&self.Object===Object&&self,er=ee||et||Function("return this")(),en=Object.prototype.toString,ei=Math.max,eo=Math.min,now=function(){return er.Date.now()};function debounce(A,B,N){var H,W,j,V,K,X,J=0,ee=!1,et=!1,er=!0;if("function"!=typeof A)throw TypeError(U);function invokeFunc(B){var N=H,U=W;return H=W=void 0,J=B,V=A.apply(U,N)}function leadingEdge(A){return J=A,K=setTimeout(timerExpired,B),ee?invokeFunc(A):V}function remainingWait(A){var N=A-X,U=A-J,H=B-N;return et?eo(H,j-U):H}function shouldInvoke(A){var N=A-X,U=A-J;return void 0===X||N>=B||N<0||et&&U>=j}function timerExpired(){var A=now();if(shouldInvoke(A))return trailingEdge(A);K=setTimeout(timerExpired,remainingWait(A))}function trailingEdge(A){return(K=void 0,er&&H)?invokeFunc(A):(H=W=void 0,V)}function cancel(){void 0!==K&&clearTimeout(K),J=0,H=X=W=K=void 0}function flush(){return void 0===K?V:trailingEdge(now())}function debounced(){var A=now(),N=shouldInvoke(A);if(H=arguments,W=this,X=A,N){if(void 0===K)return leadingEdge(X);if(et)return K=setTimeout(timerExpired,B),invokeFunc(X)}return void 0===K&&(K=setTimeout(timerExpired,B)),V}return B=toNumber(B)||0,isObject(N)&&(ee=!!N.leading,j=(et="maxWait"in N)?ei(toNumber(N.maxWait)||0,B):j,er="trailing"in N?!!N.trailing:er),debounced.cancel=cancel,debounced.flush=flush,debounced}function isObject(A){var B=typeof A;return!!A&&("object"==B||"function"==B)}function isObjectLike(A){return!!A&&"object"==typeof A}function isSymbol(A){return"symbol"==typeof A||isObjectLike(A)&&en.call(A)==W}function toNumber(A){if("number"==typeof A)return A;if(isSymbol(A))return H;if(isObject(A)){var B="function"==typeof A.valueOf?A.valueOf():A;A=isObject(B)?B+"":B}if("string"!=typeof A)return 0===A?A:+A;A=A.replace(j,"");var N=K.test(A);return N||X.test(A)?J(A.slice(2),N?2:8):V.test(A)?H:+A}A.exports=debounce},84560:function(A,B,N){A=N.nmd(A);var U=200,H="__lodash_hash_undefined__",W=800,j=16,V=0x1fffffffffffff,K="[object Arguments]",X="[object Array]",J="[object AsyncFunction]",ee="[object Boolean]",et="[object Date]",er="[object Error]",en="[object Function]",ei="[object GeneratorFunction]",eo="[object Map]",ea="[object Number]",es="[object Null]",eu="[object Object]",el="[object Proxy]",ec="[object RegExp]",ef="[object Set]",ed="[object String]",ep="[object Undefined]",eh="[object WeakMap]",eg="[object ArrayBuffer]",ey="[object DataView]",em="[object Float64Array]",e_="[object Int8Array]",ev="[object Int16Array]",eS="[object Int32Array]",eb="[object Uint8Array]",eE="[object Uint8ClampedArray]",eT="[object Uint16Array]",eA="[object Uint32Array]",ew=/[\\^$.*+?()[\]{}|]/g,eO=/^\[object .+?Constructor\]$/,eB=/^(?:0|[1-9]\d*)$/,eI={};eI["[object Float32Array]"]=eI[em]=eI[e_]=eI[ev]=eI[eS]=eI[eb]=eI[eE]=eI[eT]=eI[eA]=!0,eI[K]=eI[X]=eI[eg]=eI[ee]=eI[ey]=eI[et]=eI[er]=eI[en]=eI[eo]=eI[ea]=eI[eu]=eI[ec]=eI[ef]=eI[ed]=eI[eh]=!1;var eR="object"==typeof N.g&&N.g&&N.g.Object===Object&&N.g,eC="object"==typeof self&&self&&self.Object===Object&&self,eL=eR||eC||Function("return this")(),eN=B&&!B.nodeType&&B,eM=eN&&A&&!A.nodeType&&A,ex=eM&&eM.exports===eN,eP=ex&&eR.process,eU=function(){try{var A=eM&&eM.require&&eM.require("util").types;if(A)return A;return eP&&eP.binding&&eP.binding("util")}catch(A){}}(),eD=eU&&eU.isTypedArray;function apply(A,B,N){switch(N.length){case 0:return A.call(B);case 1:return A.call(B,N[0]);case 2:return A.call(B,N[0],N[1]);case 3:return A.call(B,N[0],N[1],N[2])}return A.apply(B,N)}function baseTimes(A,B){for(var N=-1,U=Array(A);++N<A;)U[N]=B(N);return U}function baseUnary(A){return function(B){return A(B)}}function getValue(A,B){return null==A?void 0:A[B]}function overArg(A,B){return function(N){return A(B(N))}}var eF=Array.prototype,ek=Function.prototype,eH=Object.prototype,eW=eL["__core-js_shared__"],ej=ek.toString,eV=eH.hasOwnProperty,eG=function(){var A=/[^.]+$/.exec(eW&&eW.keys&&eW.keys.IE_PROTO||"");return A?"Symbol(src)_1."+A:""}(),eY=eH.toString,e$=ej.call(Object),ez=RegExp("^"+ej.call(eV).replace(ew,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),eK=ex?eL.Buffer:void 0,eZ=eL.Symbol,eX=eL.Uint8Array,eJ=eK?eK.allocUnsafe:void 0,eQ=overArg(Object.getPrototypeOf,Object),e0=Object.create,e1=eH.propertyIsEnumerable,e2=eF.splice,e3=eZ?eZ.toStringTag:void 0,e6=function(){try{var A=getNative(Object,"defineProperty");return A({},"",{}),A}catch(A){}}(),e4=eK?eK.isBuffer:void 0,e5=Math.max,e8=Date.now,e7=getNative(eL,"Map"),e9=getNative(Object,"create"),te=function(){function object(){}return function(A){if(!isObject(A))return{};if(e0)return e0(A);object.prototype=A;var B=new object;return object.prototype=void 0,B}}();function Hash(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function hashClear(){this.__data__=e9?e9(null):{},this.size=0}function hashDelete(A){var B=this.has(A)&&delete this.__data__[A];return this.size-=+!!B,B}function hashGet(A){var B=this.__data__;if(e9){var N=B[A];return N===H?void 0:N}return eV.call(B,A)?B[A]:void 0}function hashHas(A){var B=this.__data__;return e9?void 0!==B[A]:eV.call(B,A)}function hashSet(A,B){var N=this.__data__;return this.size+=+!this.has(A),N[A]=e9&&void 0===B?H:B,this}function ListCache(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function listCacheClear(){this.__data__=[],this.size=0}function listCacheDelete(A){var B=this.__data__,N=assocIndexOf(B,A);return!(N<0)&&(N==B.length-1?B.pop():e2.call(B,N,1),--this.size,!0)}function listCacheGet(A){var B=this.__data__,N=assocIndexOf(B,A);return N<0?void 0:B[N][1]}function listCacheHas(A){return assocIndexOf(this.__data__,A)>-1}function listCacheSet(A,B){var N=this.__data__,U=assocIndexOf(N,A);return U<0?(++this.size,N.push([A,B])):N[U][1]=B,this}function MapCache(A){var B=-1,N=null==A?0:A.length;for(this.clear();++B<N;){var U=A[B];this.set(U[0],U[1])}}function mapCacheClear(){this.size=0,this.__data__={hash:new Hash,map:new(e7||ListCache),string:new Hash}}function mapCacheDelete(A){var B=getMapData(this,A).delete(A);return this.size-=+!!B,B}function mapCacheGet(A){return getMapData(this,A).get(A)}function mapCacheHas(A){return getMapData(this,A).has(A)}function mapCacheSet(A,B){var N=getMapData(this,A),U=N.size;return N.set(A,B),this.size+=+(N.size!=U),this}function Stack(A){var B=this.__data__=new ListCache(A);this.size=B.size}function stackClear(){this.__data__=new ListCache,this.size=0}function stackDelete(A){var B=this.__data__,N=B.delete(A);return this.size=B.size,N}function stackGet(A){return this.__data__.get(A)}function stackHas(A){return this.__data__.has(A)}function stackSet(A,B){var N=this.__data__;if(N instanceof ListCache){var H=N.__data__;if(!e7||H.length<U-1)return H.push([A,B]),this.size=++N.size,this;N=this.__data__=new MapCache(H)}return N.set(A,B),this.size=N.size,this}function arrayLikeKeys(A,B){var N=ti(A),U=!N&&tn(A),H=!N&&!U&&to(A),W=!N&&!U&&!H&&ta(A),j=N||U||H||W,V=j?baseTimes(A.length,String):[],K=V.length;for(var X in A)(B||eV.call(A,X))&&!(j&&("length"==X||H&&("offset"==X||"parent"==X)||W&&("buffer"==X||"byteLength"==X||"byteOffset"==X)||isIndex(X,K)))&&V.push(X);return V}function assignMergeValue(A,B,N){(void 0!==N&&!eq(A[B],N)||void 0===N&&!(B in A))&&baseAssignValue(A,B,N)}function assignValue(A,B,N){var U=A[B];(!(eV.call(A,B)&&eq(U,N))||void 0===N&&!(B in A))&&baseAssignValue(A,B,N)}function assocIndexOf(A,B){for(var N=A.length;N--;)if(eq(A[N][0],B))return N;return -1}function baseAssignValue(A,B,N){"__proto__"==B&&e6?e6(A,B,{configurable:!0,enumerable:!0,value:N,writable:!0}):A[B]=N}Hash.prototype.clear=hashClear,Hash.prototype.delete=hashDelete,Hash.prototype.get=hashGet,Hash.prototype.has=hashHas,Hash.prototype.set=hashSet,ListCache.prototype.clear=listCacheClear,ListCache.prototype.delete=listCacheDelete,ListCache.prototype.get=listCacheGet,ListCache.prototype.has=listCacheHas,ListCache.prototype.set=listCacheSet,MapCache.prototype.clear=mapCacheClear,MapCache.prototype.delete=mapCacheDelete,MapCache.prototype.get=mapCacheGet,MapCache.prototype.has=mapCacheHas,MapCache.prototype.set=mapCacheSet,Stack.prototype.clear=stackClear,Stack.prototype.delete=stackDelete,Stack.prototype.get=stackGet,Stack.prototype.has=stackHas,Stack.prototype.set=stackSet;var tt=createBaseFor();function baseGetTag(A){return null==A?void 0===A?ep:es:e3&&e3 in Object(A)?getRawTag(A):objectToString(A)}function baseIsArguments(A){return isObjectLike(A)&&baseGetTag(A)==K}function baseIsNative(A){return!(!isObject(A)||isMasked(A))&&(isFunction(A)?ez:eO).test(toSource(A))}function baseIsTypedArray(A){return isObjectLike(A)&&isLength(A.length)&&!!eI[baseGetTag(A)]}function baseKeysIn(A){if(!isObject(A))return nativeKeysIn(A);var B=isPrototype(A),N=[];for(var U in A)!("constructor"==U&&(B||!eV.call(A,U)))&&N.push(U);return N}function baseMerge(A,B,N,U,H){if(A!==B)tt(B,function(W,j){if(H||(H=new Stack),isObject(W))baseMergeDeep(A,B,j,N,baseMerge,U,H);else{var V=U?U(safeGet(A,j),W,j+"",A,B,H):void 0;void 0===V&&(V=W),assignMergeValue(A,j,V)}},keysIn)}function baseMergeDeep(A,B,N,U,H,W,j){var V=safeGet(A,N),K=safeGet(B,N),X=j.get(K);if(X){assignMergeValue(A,N,X);return}var J=W?W(V,K,N+"",A,B,j):void 0,ee=void 0===J;if(ee){var et=ti(K),er=!et&&to(K),en=!et&&!er&&ta(K);J=K,et||er||en?ti(V)?J=V:isArrayLikeObject(V)?J=copyArray(V):er?(ee=!1,J=cloneBuffer(K,!0)):en?(ee=!1,J=cloneTypedArray(K,!0)):J=[]:isPlainObject(K)||tn(K)?(J=V,tn(V)?J=toPlainObject(V):(!isObject(V)||isFunction(V))&&(J=initCloneObject(K))):ee=!1}ee&&(j.set(K,J),H(J,K,U,W,j),j.delete(K)),assignMergeValue(A,N,J)}function baseRest(A,B){return tr(overRest(A,B,identity),A+"")}function cloneBuffer(A,B){if(B)return A.slice();var N=A.length,U=eJ?eJ(N):new A.constructor(N);return A.copy(U),U}function cloneArrayBuffer(A){var B=new A.constructor(A.byteLength);return new eX(B).set(new eX(A)),B}function cloneTypedArray(A,B){var N=B?cloneArrayBuffer(A.buffer):A.buffer;return new A.constructor(N,A.byteOffset,A.length)}function copyArray(A,B){var N=-1,U=A.length;for(B||(B=Array(U));++N<U;)B[N]=A[N];return B}function copyObject(A,B,N,U){var H=!N;N||(N={});for(var W=-1,j=B.length;++W<j;){var V=B[W],K=U?U(N[V],A[V],V,N,A):void 0;void 0===K&&(K=A[V]),H?baseAssignValue(N,V,K):assignValue(N,V,K)}return N}function createAssigner(A){return baseRest(function(B,N){var U=-1,H=N.length,W=H>1?N[H-1]:void 0,j=H>2?N[2]:void 0;for(W=A.length>3&&"function"==typeof W?(H--,W):void 0,j&&isIterateeCall(N[0],N[1],j)&&(W=H<3?void 0:W,H=1),B=Object(B);++U<H;){var V=N[U];V&&A(B,V,U,W)}return B})}function createBaseFor(A){return function(B,N,U){for(var H=-1,W=Object(B),j=U(B),V=j.length;V--;){var K=j[A?V:++H];if(!1===N(W[K],K,W))break}return B}}function getMapData(A,B){var N=A.__data__;return isKeyable(B)?N["string"==typeof B?"string":"hash"]:N.map}function getNative(A,B){var N=getValue(A,B);return baseIsNative(N)?N:void 0}function getRawTag(A){var B=eV.call(A,e3),N=A[e3];try{A[e3]=void 0;var U=!0}catch(A){}var H=eY.call(A);return U&&(B?A[e3]=N:delete A[e3]),H}function initCloneObject(A){return"function"!=typeof A.constructor||isPrototype(A)?{}:te(eQ(A))}function isIndex(A,B){var N=typeof A;return!!(B=null==B?V:B)&&("number"==N||"symbol"!=N&&eB.test(A))&&A>-1&&A%1==0&&A<B}function isIterateeCall(A,B,N){if(!isObject(N))return!1;var U=typeof B;return("number"==U?!!(isArrayLike(N)&&isIndex(B,N.length)):"string"==U&&B in N)&&eq(N[B],A)}function isKeyable(A){var B=typeof A;return"string"==B||"number"==B||"symbol"==B||"boolean"==B?"__proto__"!==A:null===A}function isMasked(A){return!!eG&&eG in A}function isPrototype(A){var B=A&&A.constructor;return A===("function"==typeof B&&B.prototype||eH)}function nativeKeysIn(A){var B=[];if(null!=A)for(var N in Object(A))B.push(N);return B}function objectToString(A){return eY.call(A)}function overRest(A,B,N){return B=e5(void 0===B?A.length-1:B,0),function(){for(var U=arguments,H=-1,W=e5(U.length-B,0),j=Array(W);++H<W;)j[H]=U[B+H];H=-1;for(var V=Array(B+1);++H<B;)V[H]=U[H];return V[B]=N(j),apply(A,this,V)}}function safeGet(A,B){if(("constructor"!==B||"function"!=typeof A[B])&&"__proto__"!=B)return A[B]}var tr=shortOut(e6?function(A,B){return e6(A,"toString",{configurable:!0,enumerable:!1,value:constant(B),writable:!0})}:identity);function shortOut(A){var B=0,N=0;return function(){var U=e8(),H=j-(U-N);if(N=U,H>0){if(++B>=W)return arguments[0]}else B=0;return A.apply(void 0,arguments)}}function toSource(A){if(null!=A){try{return ej.call(A)}catch(A){}try{return A+""}catch(A){}}return""}function eq(A,B){return A===B||A!=A&&B!=B}var tn=baseIsArguments(function(){return arguments}())?baseIsArguments:function(A){return isObjectLike(A)&&eV.call(A,"callee")&&!e1.call(A,"callee")},ti=Array.isArray;function isArrayLike(A){return null!=A&&isLength(A.length)&&!isFunction(A)}function isArrayLikeObject(A){return isObjectLike(A)&&isArrayLike(A)}var to=e4||stubFalse;function isFunction(A){if(!isObject(A))return!1;var B=baseGetTag(A);return B==en||B==ei||B==J||B==el}function isLength(A){return"number"==typeof A&&A>-1&&A%1==0&&A<=V}function isObject(A){var B=typeof A;return null!=A&&("object"==B||"function"==B)}function isObjectLike(A){return null!=A&&"object"==typeof A}function isPlainObject(A){if(!isObjectLike(A)||baseGetTag(A)!=eu)return!1;var B=eQ(A);if(null===B)return!0;var N=eV.call(B,"constructor")&&B.constructor;return"function"==typeof N&&N instanceof N&&ej.call(N)==e$}var ta=eD?baseUnary(eD):baseIsTypedArray;function toPlainObject(A){return copyObject(A,keysIn(A))}function keysIn(A){return isArrayLike(A)?arrayLikeKeys(A,!0):baseKeysIn(A)}var ts=createAssigner(function(A,B,N){baseMerge(A,B,N)});function constant(A){return function(){return A}}function identity(A){return A}function stubFalse(){return!1}A.exports=ts},14978:function(A,B,N){var U="Expected a function",H=0/0,W="[object Symbol]",j=/^\s+|\s+$/g,V=/^[-+]0x[0-9a-f]+$/i,K=/^0b[01]+$/i,X=/^0o[0-7]+$/i,J=parseInt,ee="object"==typeof N.g&&N.g&&N.g.Object===Object&&N.g,et="object"==typeof self&&self&&self.Object===Object&&self,er=ee||et||Function("return this")(),en=Object.prototype.toString,ei=Math.max,eo=Math.min,now=function(){return er.Date.now()};function debounce(A,B,N){var H,W,j,V,K,X,J=0,ee=!1,et=!1,er=!0;if("function"!=typeof A)throw TypeError(U);function invokeFunc(B){var N=H,U=W;return H=W=void 0,J=B,V=A.apply(U,N)}function leadingEdge(A){return J=A,K=setTimeout(timerExpired,B),ee?invokeFunc(A):V}function remainingWait(A){var N=A-X,U=A-J,H=B-N;return et?eo(H,j-U):H}function shouldInvoke(A){var N=A-X,U=A-J;return void 0===X||N>=B||N<0||et&&U>=j}function timerExpired(){var A=now();if(shouldInvoke(A))return trailingEdge(A);K=setTimeout(timerExpired,remainingWait(A))}function trailingEdge(A){return(K=void 0,er&&H)?invokeFunc(A):(H=W=void 0,V)}function cancel(){void 0!==K&&clearTimeout(K),J=0,H=X=W=K=void 0}function flush(){return void 0===K?V:trailingEdge(now())}function debounced(){var A=now(),N=shouldInvoke(A);if(H=arguments,W=this,X=A,N){if(void 0===K)return leadingEdge(X);if(et)return K=setTimeout(timerExpired,B),invokeFunc(X)}return void 0===K&&(K=setTimeout(timerExpired,B)),V}return B=toNumber(B)||0,isObject(N)&&(ee=!!N.leading,j=(et="maxWait"in N)?ei(toNumber(N.maxWait)||0,B):j,er="trailing"in N?!!N.trailing:er),debounced.cancel=cancel,debounced.flush=flush,debounced}function throttle(A,B,N){var H=!0,W=!0;if("function"!=typeof A)throw TypeError(U);return isObject(N)&&(H="leading"in N?!!N.leading:H,W="trailing"in N?!!N.trailing:W),debounce(A,B,{leading:H,maxWait:B,trailing:W})}function isObject(A){var B=typeof A;return!!A&&("object"==B||"function"==B)}function isObjectLike(A){return!!A&&"object"==typeof A}function isSymbol(A){return"symbol"==typeof A||isObjectLike(A)&&en.call(A)==W}function toNumber(A){if("number"==typeof A)return A;if(isSymbol(A))return H;if(isObject(A)){var B="function"==typeof A.valueOf?A.valueOf():A;A=isObject(B)?B+"":B}if("string"!=typeof A)return 0===A?A:+A;A=A.replace(j,"");var N=K.test(A);return N||X.test(A)?J(A.slice(2),N?2:8):V.test(A)?H:+A}A.exports=throttle},27100:function(A,B,N){var U=1/0,H="[object Symbol]",W="object"==typeof N.g&&N.g&&N.g.Object===Object&&N.g,j="object"==typeof self&&self&&self.Object===Object&&self,V=W||j||Function("return this")(),K=0,X=Object.prototype.toString,J=V.Symbol,ee=J?J.prototype:void 0,et=ee?ee.toString:void 0;function baseToString(A){if("string"==typeof A)return A;if(isSymbol(A))return et?et.call(A):"";var B=A+"";return"0"==B&&1/A==-U?"-0":B}function isObjectLike(A){return!!A&&"object"==typeof A}function isSymbol(A){return"symbol"==typeof A||isObjectLike(A)&&X.call(A)==H}function toString(A){return null==A?"":baseToString(A)}function uniqueId(A){var B=++K;return toString(A)+B}A.exports=uniqueId},5681:function(A,B,N){!function(){var B=N(54274),U=N(29203).utf8,H=N(17476),W=N(29203).bin,md5=function(A,N){A.constructor==String?A=N&&"binary"===N.encoding?W.stringToBytes(A):U.stringToBytes(A):H(A)?A=Array.prototype.slice.call(A,0):!Array.isArray(A)&&A.constructor!==Uint8Array&&(A=A.toString());for(var j=B.bytesToWords(A),V=8*A.length,K=0x67452301,X=-0x10325477,J=-0x67452302,ee=0x10325476,et=0;et<j.length;et++)j[et]=(j[et]<<8|j[et]>>>24)&0xff00ff|(j[et]<<24|j[et]>>>8)&0xff00ff00;j[V>>>5]|=128<<V%32,j[(V+64>>>9<<4)+14]=V;for(var er=md5._ff,en=md5._gg,ei=md5._hh,eo=md5._ii,et=0;et<j.length;et+=16){var ea=K,es=X,eu=J,el=ee;K=er(K,X,J,ee,j[et+0],7,-0x28955b88),ee=er(ee,K,X,J,j[et+1],12,-0x173848aa),J=er(J,ee,K,X,j[et+2],17,0x242070db),X=er(X,J,ee,K,j[et+3],22,-0x3e423112),K=er(K,X,J,ee,j[et+4],7,-0xa83f051),ee=er(ee,K,X,J,j[et+5],12,0x4787c62a),J=er(J,ee,K,X,j[et+6],17,-0x57cfb9ed),X=er(X,J,ee,K,j[et+7],22,-0x2b96aff),K=er(K,X,J,ee,j[et+8],7,0x698098d8),ee=er(ee,K,X,J,j[et+9],12,-0x74bb0851),J=er(J,ee,K,X,j[et+10],17,-42063),X=er(X,J,ee,K,j[et+11],22,-0x76a32842),K=er(K,X,J,ee,j[et+12],7,0x6b901122),ee=er(ee,K,X,J,j[et+13],12,-0x2678e6d),J=er(J,ee,K,X,j[et+14],17,-0x5986bc72),X=er(X,J,ee,K,j[et+15],22,0x49b40821),K=en(K,X,J,ee,j[et+1],5,-0x9e1da9e),ee=en(ee,K,X,J,j[et+6],9,-0x3fbf4cc0),J=en(J,ee,K,X,j[et+11],14,0x265e5a51),X=en(X,J,ee,K,j[et+0],20,-0x16493856),K=en(K,X,J,ee,j[et+5],5,-0x29d0efa3),ee=en(ee,K,X,J,j[et+10],9,0x2441453),J=en(J,ee,K,X,j[et+15],14,-0x275e197f),X=en(X,J,ee,K,j[et+4],20,-0x182c0438),K=en(K,X,J,ee,j[et+9],5,0x21e1cde6),ee=en(ee,K,X,J,j[et+14],9,-0x3cc8f82a),J=en(J,ee,K,X,j[et+3],14,-0xb2af279),X=en(X,J,ee,K,j[et+8],20,0x455a14ed),K=en(K,X,J,ee,j[et+13],5,-0x561c16fb),ee=en(ee,K,X,J,j[et+2],9,-0x3105c08),J=en(J,ee,K,X,j[et+7],14,0x676f02d9),X=en(X,J,ee,K,j[et+12],20,-0x72d5b376),K=ei(K,X,J,ee,j[et+5],4,-378558),ee=ei(ee,K,X,J,j[et+8],11,-0x788e097f),J=ei(J,ee,K,X,j[et+11],16,0x6d9d6122),X=ei(X,J,ee,K,j[et+14],23,-0x21ac7f4),K=ei(K,X,J,ee,j[et+1],4,-0x5b4115bc),ee=ei(ee,K,X,J,j[et+4],11,0x4bdecfa9),J=ei(J,ee,K,X,j[et+7],16,-0x944b4a0),X=ei(X,J,ee,K,j[et+10],23,-0x41404390),K=ei(K,X,J,ee,j[et+13],4,0x289b7ec6),ee=ei(ee,K,X,J,j[et+0],11,-0x155ed806),J=ei(J,ee,K,X,j[et+3],16,-0x2b10cf7b),X=ei(X,J,ee,K,j[et+6],23,0x4881d05),K=ei(K,X,J,ee,j[et+9],4,-0x262b2fc7),ee=ei(ee,K,X,J,j[et+12],11,-0x1924661b),J=ei(J,ee,K,X,j[et+15],16,0x1fa27cf8),X=ei(X,J,ee,K,j[et+2],23,-0x3b53a99b),K=eo(K,X,J,ee,j[et+0],6,-0xbd6ddbc),ee=eo(ee,K,X,J,j[et+7],10,0x432aff97),J=eo(J,ee,K,X,j[et+14],15,-0x546bdc59),X=eo(X,J,ee,K,j[et+5],21,-0x36c5fc7),K=eo(K,X,J,ee,j[et+12],6,0x655b59c3),ee=eo(ee,K,X,J,j[et+3],10,-0x70f3336e),J=eo(J,ee,K,X,j[et+10],15,-1051523),X=eo(X,J,ee,K,j[et+1],21,-0x7a7ba22f),K=eo(K,X,J,ee,j[et+8],6,0x6fa87e4f),ee=eo(ee,K,X,J,j[et+15],10,-0x1d31920),J=eo(J,ee,K,X,j[et+6],15,-0x5cfebcec),X=eo(X,J,ee,K,j[et+13],21,0x4e0811a1),K=eo(K,X,J,ee,j[et+4],6,-0x8ac817e),ee=eo(ee,K,X,J,j[et+11],10,-0x42c50dcb),J=eo(J,ee,K,X,j[et+2],15,0x2ad7d2bb),X=eo(X,J,ee,K,j[et+9],21,-0x14792c6f),K=K+ea>>>0,X=X+es>>>0,J=J+eu>>>0,ee=ee+el>>>0}return B.endian([K,X,J,ee])};md5._ff=function(A,B,N,U,H,W,j){var V=A+(B&N|~B&U)+(H>>>0)+j;return(V<<W|V>>>32-W)+B},md5._gg=function(A,B,N,U,H,W,j){var V=A+(B&U|N&~U)+(H>>>0)+j;return(V<<W|V>>>32-W)+B},md5._hh=function(A,B,N,U,H,W,j){var V=A+(B^N^U)+(H>>>0)+j;return(V<<W|V>>>32-W)+B},md5._ii=function(A,B,N,U,H,W,j){var V=A+(N^(B|~U))+(H>>>0)+j;return(V<<W|V>>>32-W)+B},md5._blocksize=16,md5._digestsize=16,A.exports=function(A,N){if(null==A)throw Error("Illegal argument "+A);var U=B.wordsToBytes(md5(A,N));return N&&N.asBytes?U:N&&N.asString?W.bytesToString(U):B.bytesToHex(U)}}()},88514:function(A){var B=1e3,N=6e4,U=36e5,H=864e5,W=6048e5,j=315576e5;function parse(A){if((A=String(A)).length>100)return;var V=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(A);if(!!V){var K=parseFloat(V[1]);switch((V[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return K*j;case"weeks":case"week":case"w":return K*W;case"days":case"day":case"d":return K*H;case"hours":case"hour":case"hrs":case"hr":case"h":return K*U;case"minutes":case"minute":case"mins":case"min":case"m":return K*N;case"seconds":case"second":case"secs":case"sec":case"s":return K*B;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return K;default:return}}}function fmtShort(A){var W=Math.abs(A);return W>=H?Math.round(A/H)+"d":W>=U?Math.round(A/U)+"h":W>=N?Math.round(A/N)+"m":W>=B?Math.round(A/B)+"s":A+"ms"}function fmtLong(A){var W=Math.abs(A);return W>=H?plural(A,W,H,"day"):W>=U?plural(A,W,U,"hour"):W>=N?plural(A,W,N,"minute"):W>=B?plural(A,W,B,"second"):A+" ms"}function plural(A,B,N,U){var H=B>=1.5*N;return Math.round(A/N)+" "+U+(H?"s":"")}A.exports=function(A,B){B=B||{};var N=typeof A;if("string"===N&&A.length>0)return parse(A);if("number"===N&&isFinite(A))return B.long?fmtLong(A):fmtShort(A);throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(A))}},73656:function(A){var B,N,U,H=A.exports={};function defaultSetTimout(){throw Error("setTimeout has not been defined")}function defaultClearTimeout(){throw Error("clearTimeout has not been defined")}function runTimeout(A){if(B===setTimeout)return setTimeout(A,0);if((B===defaultSetTimout||!B)&&setTimeout)return B=setTimeout,setTimeout(A,0);try{return B(A,0)}catch(N){try{return B.call(null,A,0)}catch(N){return B.call(this,A,0)}}}function runClearTimeout(A){if(N===clearTimeout)return clearTimeout(A);if((N===defaultClearTimeout||!N)&&clearTimeout)return N=clearTimeout,clearTimeout(A);try{return N(A)}catch(B){try{return N.call(null,A)}catch(B){return N.call(this,A)}}}!function(){try{B="function"==typeof setTimeout?setTimeout:defaultSetTimout}catch(A){B=defaultSetTimout}try{N="function"==typeof clearTimeout?clearTimeout:defaultClearTimeout}catch(A){N=defaultClearTimeout}}();var W=[],j=!1,V=-1;function cleanUpNextTick(){if(!!j&&!!U)j=!1,U.length?W=U.concat(W):V=-1,W.length&&drainQueue()}function drainQueue(){if(!j){var A=runTimeout(cleanUpNextTick);j=!0;for(var B=W.length;B;){for(U=W,W=[];++V<B;)U&&U[V].run();V=-1,B=W.length}U=null,j=!1,runClearTimeout(A)}}function Item(A,B){this.fun=A,this.array=B}function noop(){}H.nextTick=function(A){var B=Array(arguments.length-1);if(arguments.length>1)for(var N=1;N<arguments.length;N++)B[N-1]=arguments[N];W.push(new Item(A,B)),1===W.length&&!j&&runTimeout(drainQueue)},Item.prototype.run=function(){this.fun.apply(null,this.array)},H.title="browser",H.browser=!0,H.env={},H.argv=[],H.version="",H.versions={},H.on=noop,H.addListener=noop,H.once=noop,H.off=noop,H.removeListener=noop,H.removeAllListeners=noop,H.emit=noop,H.prependListener=noop,H.prependOnceListener=noop,H.listeners=function(A){return[]},H.binding=function(A){throw Error("process.binding is not supported")},H.cwd=function(){return"/"},H.chdir=function(A){throw Error("process.chdir is not supported")},H.umask=function(){return 0}},31772:function(A,B,N){"use strict";var U=N(25148);function emptyFunction(){}function emptyFunctionWithReset(){}emptyFunctionWithReset.resetWarningCache=emptyFunction,A.exports=function(){function shim(A,B,N,H,W,j){if(j!==U){var V=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw V.name="Invariant Violation",V}}function getShim(){return shim}shim.isRequired=shim;var A={array:shim,bigint:shim,bool:shim,func:shim,number:shim,object:shim,string:shim,symbol:shim,any:shim,arrayOf:getShim,element:shim,elementType:shim,instanceOf:getShim,node:shim,objectOf:getShim,oneOf:getShim,oneOfType:getShim,shape:getShim,exact:getShim,checkPropTypes:emptyFunctionWithReset,resetWarningCache:emptyFunction};return A.PropTypes=A,A}},7862:function(A,B,N){A.exports=N(31772)()},25148:function(A){"use strict";var B="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED";A.exports=B},47658:function(A,B,N){let U=N(47739),H=N(4130),W=N(6650),j=N(4436);function renderCanvas(A,B,N,W,j){let V=[].slice.call(arguments,1),K=V.length,X="function"==typeof V[K-1];if(!X&&!U())throw Error("Callback required as last argument");if(X){if(K<2)throw Error("Too few arguments provided");2===K?(j=N,N=B,B=W=void 0):3===K&&(B.getContext&&void 0===j?(j=W,W=void 0):(j=W,W=N,N=B,B=void 0))}else{if(K<1)throw Error("Too few arguments provided");return 1===K?(N=B,B=W=void 0):2===K&&!B.getContext&&(W=N,N=B,B=void 0),new Promise(function(U,j){try{let j=H.create(N,W);U(A(j,B,W))}catch(A){j(A)}})}try{let U=H.create(N,W);j(null,A(U,B,W))}catch(A){j(A)}}B.create=H.create,B.toCanvas=renderCanvas.bind(null,W.render),B.toDataURL=renderCanvas.bind(null,W.renderToDataURL),B.toString=renderCanvas.bind(null,function(A,B,N){return j.render(A,N)})},47739:function(A){A.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},95150:function(A,B,N){let U=N(14634).getSymbolSize;B.getRowColCoords=function getRowColCoords(A){if(1===A)return[];let B=Math.floor(A/7)+2,N=U(A),H=145===N?26:2*Math.ceil((N-13)/(2*B-2)),W=[N-7];for(let A=1;A<B-1;A++)W[A]=W[A-1]-H;return W.push(6),W.reverse()},B.getPositions=function getPositions(A){let N=[],U=B.getRowColCoords(A),H=U.length;for(let A=0;A<H;A++)for(let B=0;B<H;B++){if((0!==A||0!==B)&&(0!==A||B!==H-1)&&(A!==H-1||0!==B))N.push([U[A],U[B]])}return N}},2276:function(A,B,N){let U=N(25064),H=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function AlphanumericData(A){this.mode=U.ALPHANUMERIC,this.data=A}AlphanumericData.getBitsLength=function getBitsLength(A){return 11*Math.floor(A/2)+A%2*6},AlphanumericData.prototype.getLength=function getLength(){return this.data.length},AlphanumericData.prototype.getBitsLength=function getBitsLength(){return AlphanumericData.getBitsLength(this.data.length)},AlphanumericData.prototype.write=function write(A){let B;for(B=0;B+2<=this.data.length;B+=2){let N=45*H.indexOf(this.data[B]);N+=H.indexOf(this.data[B+1]),A.put(N,11)}this.data.length%2&&A.put(H.indexOf(this.data[B]),6)},A.exports=AlphanumericData},75511:function(A){function BitBuffer(){this.buffer=[],this.length=0}BitBuffer.prototype={get:function(A){let B=Math.floor(A/8);return(this.buffer[B]>>>7-A%8&1)==1},put:function(A,B){for(let N=0;N<B;N++)this.putBit((A>>>B-N-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(A){let B=Math.floor(this.length/8);this.buffer.length<=B&&this.buffer.push(0),A&&(this.buffer[B]|=128>>>this.length%8),this.length++}},A.exports=BitBuffer},82800:function(A){function BitMatrix(A){if(!A||A<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=A,this.data=new Uint8Array(A*A),this.reservedBit=new Uint8Array(A*A)}BitMatrix.prototype.set=function(A,B,N,U){let H=A*this.size+B;this.data[H]=N,U&&(this.reservedBit[H]=!0)},BitMatrix.prototype.get=function(A,B){return this.data[A*this.size+B]},BitMatrix.prototype.xor=function(A,B,N){this.data[A*this.size+B]^=N},BitMatrix.prototype.isReserved=function(A,B){return this.reservedBit[A*this.size+B]},A.exports=BitMatrix},23922:function(A,B,N){let U=N(25064);function ByteData(A){this.mode=U.BYTE,"string"==typeof A?this.data=new TextEncoder().encode(A):this.data=new Uint8Array(A)}ByteData.getBitsLength=function getBitsLength(A){return 8*A},ByteData.prototype.getLength=function getLength(){return this.data.length},ByteData.prototype.getBitsLength=function getBitsLength(){return ByteData.getBitsLength(this.data.length)},ByteData.prototype.write=function(A){for(let B=0,N=this.data.length;B<N;B++)A.put(this.data[B],8)},A.exports=ByteData},34937:function(A,B,N){let U=N(25350),H=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],W=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];B.getBlocksCount=function getBlocksCount(A,B){switch(B){case U.L:return H[(A-1)*4+0];case U.M:return H[(A-1)*4+1];case U.Q:return H[(A-1)*4+2];case U.H:return H[(A-1)*4+3];default:return}},B.getTotalCodewordsCount=function getTotalCodewordsCount(A,B){switch(B){case U.L:return W[(A-1)*4+0];case U.M:return W[(A-1)*4+1];case U.Q:return W[(A-1)*4+2];case U.H:return W[(A-1)*4+3];default:return}}},25350:function(A,B){function fromString(A){if("string"!=typeof A)throw Error("Param is not a string");switch(A.toLowerCase()){case"l":case"low":return B.L;case"m":case"medium":return B.M;case"q":case"quartile":return B.Q;case"h":case"high":return B.H;default:throw Error("Unknown EC Level: "+A)}}B.L={bit:1},B.M={bit:0},B.Q={bit:3},B.H={bit:2},B.isValid=function isValid(A){return A&&void 0!==A.bit&&A.bit>=0&&A.bit<4},B.from=function from(A,N){if(B.isValid(A))return A;try{return fromString(A)}catch(A){return N}}},89886:function(A,B,N){let U=N(14634).getSymbolSize,H=7;B.getPositions=function getPositions(A){let B=U(A);return[[0,0],[B-H,0],[0,B-H]]}},47381:function(A,B,N){let U=N(14634),H=1335,W=21522,j=U.getBCHDigit(H);B.getEncodedBits=function getEncodedBits(A,B){let N=A.bit<<3|B,V=N<<10;for(;U.getBCHDigit(V)-j>=0;)V^=H<<U.getBCHDigit(V)-j;return(N<<10|V)^W}},63643:function(A,B){let N=new Uint8Array(512),U=new Uint8Array(256);!function initTables(){let A=1;for(let B=0;B<255;B++)N[B]=A,U[A]=B,256&(A<<=1)&&(A^=285);for(let A=255;A<512;A++)N[A]=N[A-255]}(),B.log=function log(A){if(A<1)throw Error("log("+A+")");return U[A]},B.exp=function exp(A){return N[A]},B.mul=function mul(A,B){return 0===A||0===B?0:N[U[A]+U[B]]}},3880:function(A,B,N){let U=N(25064),H=N(14634);function KanjiData(A){this.mode=U.KANJI,this.data=A}KanjiData.getBitsLength=function getBitsLength(A){return 13*A},KanjiData.prototype.getLength=function getLength(){return this.data.length},KanjiData.prototype.getBitsLength=function getBitsLength(){return KanjiData.getBitsLength(this.data.length)},KanjiData.prototype.write=function(A){let B;for(B=0;B<this.data.length;B++){let N=H.toSJIS(this.data[B]);if(N>=33088&&N<=40956)N-=33088;else if(N>=57408&&N<=60351)N-=49472;else throw Error("Invalid SJIS character: "+this.data[B]+"\nMake sure your charset is UTF-8");N=(N>>>8&255)*192+(255&N),A.put(N,13)}},A.exports=KanjiData},69670:function(A,B){B.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let N={N1:3,N2:3,N3:40,N4:10};function getMaskAt(A,N,U){switch(A){case B.Patterns.PATTERN000:return(N+U)%2==0;case B.Patterns.PATTERN001:return N%2==0;case B.Patterns.PATTERN010:return U%3==0;case B.Patterns.PATTERN011:return(N+U)%3==0;case B.Patterns.PATTERN100:return(Math.floor(N/2)+Math.floor(U/3))%2==0;case B.Patterns.PATTERN101:return N*U%2+N*U%3==0;case B.Patterns.PATTERN110:return(N*U%2+N*U%3)%2==0;case B.Patterns.PATTERN111:return(N*U%3+(N+U)%2)%2==0;default:throw Error("bad maskPattern:"+A)}}B.isValid=function isValid(A){return null!=A&&""!==A&&!isNaN(A)&&A>=0&&A<=7},B.from=function from(A){return B.isValid(A)?parseInt(A,10):void 0},B.getPenaltyN1=function getPenaltyN1(A){let B=A.size,U=0,H=0,W=0,j=null,V=null;for(let K=0;K<B;K++){H=W=0,j=V=null;for(let X=0;X<B;X++){let B=A.get(K,X);B===j?H++:(H>=5&&(U+=N.N1+(H-5)),j=B,H=1),(B=A.get(X,K))===V?W++:(W>=5&&(U+=N.N1+(W-5)),V=B,W=1)}H>=5&&(U+=N.N1+(H-5)),W>=5&&(U+=N.N1+(W-5))}return U},B.getPenaltyN2=function getPenaltyN2(A){let B=A.size,U=0;for(let N=0;N<B-1;N++)for(let H=0;H<B-1;H++){let B=A.get(N,H)+A.get(N,H+1)+A.get(N+1,H)+A.get(N+1,H+1);(4===B||0===B)&&U++}return U*N.N2},B.getPenaltyN3=function getPenaltyN3(A){let B=A.size,U=0,H=0,W=0;for(let N=0;N<B;N++){H=W=0;for(let j=0;j<B;j++)H=H<<1&2047|A.get(N,j),j>=10&&(1488===H||93===H)&&U++,W=W<<1&2047|A.get(j,N),j>=10&&(1488===W||93===W)&&U++}return U*N.N3},B.getPenaltyN4=function getPenaltyN4(A){let B=0,U=A.data.length;for(let N=0;N<U;N++)B+=A.data[N];return Math.abs(Math.ceil(100*B/U/5)-10)*N.N4},B.applyMask=function applyMask(A,B){let N=B.size;for(let U=0;U<N;U++)for(let H=0;H<N;H++)!B.isReserved(H,U)&&B.xor(H,U,getMaskAt(A,H,U))},B.getBestMask=function getBestMask(A,N){let U=Object.keys(B.Patterns).length,H=0,W=1/0;for(let j=0;j<U;j++){N(j),B.applyMask(j,A);let U=B.getPenaltyN1(A)+B.getPenaltyN2(A)+B.getPenaltyN3(A)+B.getPenaltyN4(A);B.applyMask(j,A),U<W&&(W=U,H=j)}return H}},25064:function(A,B,N){let U=N(28645),H=N(72642);function fromString(A){if("string"!=typeof A)throw Error("Param is not a string");switch(A.toLowerCase()){case"numeric":return B.NUMERIC;case"alphanumeric":return B.ALPHANUMERIC;case"kanji":return B.KANJI;case"byte":return B.BYTE;default:throw Error("Unknown mode: "+A)}}B.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},B.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},B.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},B.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},B.MIXED={bit:-1},B.getCharCountIndicator=function getCharCountIndicator(A,B){if(!A.ccBits)throw Error("Invalid mode: "+A);if(!U.isValid(B))throw Error("Invalid version: "+B);return B>=1&&B<10?A.ccBits[0]:B<27?A.ccBits[1]:A.ccBits[2]},B.getBestModeForData=function getBestModeForData(A){if(H.testNumeric(A))return B.NUMERIC;if(H.testAlphanumeric(A))return B.ALPHANUMERIC;if(H.testKanji(A))return B.KANJI;else return B.BYTE},B.toString=function toString(A){if(A&&A.id)return A.id;throw Error("Invalid mode")},B.isValid=function isValid(A){return A&&A.bit&&A.ccBits},B.from=function from(A,N){if(B.isValid(A))return A;try{return fromString(A)}catch(A){return N}}},34678:function(A,B,N){let U=N(25064);function NumericData(A){this.mode=U.NUMERIC,this.data=A.toString()}NumericData.getBitsLength=function getBitsLength(A){return 10*Math.floor(A/3)+(A%3?A%3*3+1:0)},NumericData.prototype.getLength=function getLength(){return this.data.length},NumericData.prototype.getBitsLength=function getBitsLength(){return NumericData.getBitsLength(this.data.length)},NumericData.prototype.write=function write(A){let B,N,U;for(B=0;B+3<=this.data.length;B+=3)U=parseInt(N=this.data.substr(B,3),10),A.put(U,10);let H=this.data.length-B;H>0&&(U=parseInt(N=this.data.substr(B),10),A.put(U,3*H+1))},A.exports=NumericData},57065:function(A,B,N){let U=N(63643);B.mul=function mul(A,B){let N=new Uint8Array(A.length+B.length-1);for(let H=0;H<A.length;H++)for(let W=0;W<B.length;W++)N[H+W]^=U.mul(A[H],B[W]);return N},B.mod=function mod(A,B){let N=new Uint8Array(A);for(;N.length-B.length>=0;){let A=N[0];for(let H=0;H<B.length;H++)N[H]^=U.mul(B[H],A);let H=0;for(;H<N.length&&0===N[H];)H++;N=N.slice(H)}return N},B.generateECPolynomial=function generateECPolynomial(A){let N=new Uint8Array([1]);for(let H=0;H<A;H++)N=B.mul(N,new Uint8Array([1,U.exp(H)]));return N}},4130:function(A,B,N){let U=N(14634),H=N(25350),W=N(75511),j=N(82800),V=N(95150),K=N(89886),X=N(69670),J=N(34937),ee=N(27654),et=N(56323),er=N(47381),en=N(25064),ei=N(96174);function setupFinderPattern(A,B){let N=A.size,U=K.getPositions(B);for(let B=0;B<U.length;B++){let H=U[B][0],W=U[B][1];for(let B=-1;B<=7;B++)if(!(H+B<=-1)&&!(N<=H+B))for(let U=-1;U<=7;U++)!(W+U<=-1)&&!(N<=W+U)&&(B>=0&&B<=6&&(0===U||6===U)||U>=0&&U<=6&&(0===B||6===B)||B>=2&&B<=4&&U>=2&&U<=4?A.set(H+B,W+U,!0,!0):A.set(H+B,W+U,!1,!0))}}function setupTimingPattern(A){let B=A.size;for(let N=8;N<B-8;N++){let B=N%2==0;A.set(N,6,B,!0),A.set(6,N,B,!0)}}function setupAlignmentPattern(A,B){let N=V.getPositions(B);for(let B=0;B<N.length;B++){let U=N[B][0],H=N[B][1];for(let B=-2;B<=2;B++)for(let N=-2;N<=2;N++)-2===B||2===B||-2===N||2===N||0===B&&0===N?A.set(U+B,H+N,!0,!0):A.set(U+B,H+N,!1,!0)}}function setupVersionInfo(A,B){let N,U,H;let W=A.size,j=et.getEncodedBits(B);for(let B=0;B<18;B++)N=Math.floor(B/3),U=B%3+W-8-3,H=(j>>B&1)==1,A.set(N,U,H,!0),A.set(U,N,H,!0)}function setupFormatInfo(A,B,N){let U,H;let W=A.size,j=er.getEncodedBits(B,N);for(U=0;U<15;U++)H=(j>>U&1)==1,U<6?A.set(U,8,H,!0):U<8?A.set(U+1,8,H,!0):A.set(W-15+U,8,H,!0),U<8?A.set(8,W-U-1,H,!0):U<9?A.set(8,15-U-1+1,H,!0):A.set(8,15-U-1,H,!0);A.set(W-8,8,1,!0)}function setupData(A,B){let N=A.size,U=-1,H=N-1,W=7,j=0;for(let V=N-1;V>0;V-=2)for(6===V&&V--;;){for(let N=0;N<2;N++)if(!A.isReserved(H,V-N)){let U=!1;j<B.length&&(U=(B[j]>>>W&1)==1),A.set(H,V-N,U),-1==--W&&(j++,W=7)}if((H+=U)<0||N<=H){H-=U,U=-U;break}}}function createData(A,B,N){let H=new W;N.forEach(function(B){H.put(B.mode.bit,4),H.put(B.getLength(),en.getCharCountIndicator(B.mode,A)),B.write(H)});let j=U.getSymbolTotalCodewords(A),V=(j-J.getTotalCodewordsCount(A,B))*8;for(H.getLengthInBits()+4<=V&&H.put(0,4);H.getLengthInBits()%8!=0;)H.putBit(0);let K=(V-H.getLengthInBits())/8;for(let A=0;A<K;A++)H.put(A%2?17:236,8);return createCodewords(H,A,B)}function createCodewords(A,B,N){let H,W;let j=U.getSymbolTotalCodewords(B),V=j-J.getTotalCodewordsCount(B,N),K=J.getBlocksCount(B,N),X=j%K,et=K-X,er=Math.floor(j/K),en=Math.floor(V/K),ei=en+1,eo=er-en,ea=new ee(eo),es=0,eu=Array(K),el=Array(K),ec=0,ef=new Uint8Array(A.buffer);for(let A=0;A<K;A++){let B=A<et?en:ei;eu[A]=ef.slice(es,es+B),el[A]=ea.encode(eu[A]),es+=B,ec=Math.max(ec,B)}let ed=new Uint8Array(j),ep=0;for(H=0;H<ec;H++)for(W=0;W<K;W++)H<eu[W].length&&(ed[ep++]=eu[W][H]);for(H=0;H<eo;H++)for(W=0;W<K;W++)ed[ep++]=el[W][H];return ed}function createSymbol(A,B,N,H){let W;if(Array.isArray(A))W=ei.fromArray(A);else if("string"==typeof A){let U=B;if(!U){let B=ei.rawSplit(A);U=et.getBestVersionForData(B,N)}W=ei.fromString(A,U||40)}else throw Error("Invalid data");let V=et.getBestVersionForData(W,N);if(!V)throw Error("The amount of data is too big to be stored in a QR Code");if(B){if(B<V)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+V+".\n")}else B=V;let K=createData(B,N,W),J=new j(U.getSymbolSize(B));return setupFinderPattern(J,B),setupTimingPattern(J),setupAlignmentPattern(J,B),setupFormatInfo(J,N,0),B>=7&&setupVersionInfo(J,B),setupData(J,K),isNaN(H)&&(H=X.getBestMask(J,setupFormatInfo.bind(null,J,N))),X.applyMask(H,J),setupFormatInfo(J,N,H),{modules:J,version:B,errorCorrectionLevel:N,maskPattern:H,segments:W}}B.create=function create(A,B){let N,W;if(void 0===A||""===A)throw Error("No input text");let j=H.M;return void 0!==B&&(j=H.from(B.errorCorrectionLevel,H.M),N=et.from(B.version),W=X.from(B.maskPattern),B.toSJISFunc&&U.setToSJISFunction(B.toSJISFunc)),createSymbol(A,N,j,W)}},27654:function(A,B,N){let U=N(57065);function ReedSolomonEncoder(A){this.genPoly=void 0,this.degree=A,this.degree&&this.initialize(this.degree)}ReedSolomonEncoder.prototype.initialize=function initialize(A){this.degree=A,this.genPoly=U.generateECPolynomial(this.degree)},ReedSolomonEncoder.prototype.encode=function encode(A){if(!this.genPoly)throw Error("Encoder not initialized");let B=new Uint8Array(A.length+this.degree);B.set(A);let N=U.mod(B,this.genPoly),H=this.degree-N.length;if(H>0){let A=new Uint8Array(this.degree);return A.set(N,H),A}return N},A.exports=ReedSolomonEncoder},72642:function(A,B){let N="[0-9]+",U="[A-Z $%*+\\-./:]+",H="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",W="(?:(?![A-Z0-9 $%*+\\-./:]|"+(H=H.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";B.KANJI=RegExp(H,"g"),B.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),B.BYTE=RegExp(W,"g"),B.NUMERIC=RegExp(N,"g"),B.ALPHANUMERIC=RegExp(U,"g");let j=RegExp("^"+H+"$"),V=RegExp("^"+N+"$"),K=RegExp("^[A-Z0-9 $%*+\\-./:]+$");B.testKanji=function testKanji(A){return j.test(A)},B.testNumeric=function testNumeric(A){return V.test(A)},B.testAlphanumeric=function testAlphanumeric(A){return K.test(A)}},96174:function(A,B,N){let U=N(25064),H=N(34678),W=N(2276),j=N(23922),V=N(3880),K=N(72642),X=N(14634),J=N(759);function getStringByteLength(A){return unescape(encodeURIComponent(A)).length}function getSegments(A,B,N){let U;let H=[];for(;null!==(U=A.exec(N));)H.push({data:U[0],index:U.index,mode:B,length:U[0].length});return H}function getSegmentsFromString(A){let B,N;let H=getSegments(K.NUMERIC,U.NUMERIC,A),W=getSegments(K.ALPHANUMERIC,U.ALPHANUMERIC,A);return X.isKanjiModeEnabled()?(B=getSegments(K.BYTE,U.BYTE,A),N=getSegments(K.KANJI,U.KANJI,A)):(B=getSegments(K.BYTE_KANJI,U.BYTE,A),N=[]),H.concat(W,B,N).sort(function(A,B){return A.index-B.index}).map(function(A){return{data:A.data,mode:A.mode,length:A.length}})}function getSegmentBitsLength(A,B){switch(B){case U.NUMERIC:return H.getBitsLength(A);case U.ALPHANUMERIC:return W.getBitsLength(A);case U.KANJI:return V.getBitsLength(A);case U.BYTE:return j.getBitsLength(A)}}function mergeSegments(A){return A.reduce(function(A,B){let N=A.length-1>=0?A[A.length-1]:null;return N&&N.mode===B.mode?(A[A.length-1].data+=B.data,A):(A.push(B),A)},[])}function buildNodes(A){let B=[];for(let N=0;N<A.length;N++){let H=A[N];switch(H.mode){case U.NUMERIC:B.push([H,{data:H.data,mode:U.ALPHANUMERIC,length:H.length},{data:H.data,mode:U.BYTE,length:H.length}]);break;case U.ALPHANUMERIC:B.push([H,{data:H.data,mode:U.BYTE,length:H.length}]);break;case U.KANJI:B.push([H,{data:H.data,mode:U.BYTE,length:getStringByteLength(H.data)}]);break;case U.BYTE:B.push([{data:H.data,mode:U.BYTE,length:getStringByteLength(H.data)}])}}return B}function buildGraph(A,B){let N={},H={start:{}},W=["start"];for(let j=0;j<A.length;j++){let V=A[j],K=[];for(let A=0;A<V.length;A++){let X=V[A],J=""+j+A;K.push(J),N[J]={node:X,lastCount:0},H[J]={};for(let A=0;A<W.length;A++){let j=W[A];N[j]&&N[j].node.mode===X.mode?(H[j][J]=getSegmentBitsLength(N[j].lastCount+X.length,X.mode)-getSegmentBitsLength(N[j].lastCount,X.mode),N[j].lastCount+=X.length):(N[j]&&(N[j].lastCount=X.length),H[j][J]=getSegmentBitsLength(X.length,X.mode)+4+U.getCharCountIndicator(X.mode,B))}}W=K}for(let A=0;A<W.length;A++)H[W[A]].end=0;return{map:H,table:N}}function buildSingleSegment(A,B){let N;let K=U.getBestModeForData(A);if((N=U.from(B,K))!==U.BYTE&&N.bit<K.bit)throw Error('"'+A+'" cannot be encoded with mode '+U.toString(N)+".\n Suggested mode is: "+U.toString(K));switch(N===U.KANJI&&!X.isKanjiModeEnabled()&&(N=U.BYTE),N){case U.NUMERIC:return new H(A);case U.ALPHANUMERIC:return new W(A);case U.KANJI:return new V(A);case U.BYTE:return new j(A)}}B.fromArray=function fromArray(A){return A.reduce(function(A,B){return"string"==typeof B?A.push(buildSingleSegment(B,null)):B.data&&A.push(buildSingleSegment(B.data,B.mode)),A},[])},B.fromString=function fromString(A,N){let U=buildGraph(buildNodes(getSegmentsFromString(A,X.isKanjiModeEnabled())),N),H=J.find_path(U.map,"start","end"),W=[];for(let A=1;A<H.length-1;A++)W.push(U.table[H[A]].node);return B.fromArray(mergeSegments(W))},B.rawSplit=function rawSplit(A){return B.fromArray(getSegmentsFromString(A,X.isKanjiModeEnabled()))}},14634:function(A,B){let N;let U=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];B.getSymbolSize=function getSymbolSize(A){if(!A)throw Error('"version" cannot be null or undefined');if(A<1||A>40)throw Error('"version" should be in range from 1 to 40');return 4*A+17},B.getSymbolTotalCodewords=function getSymbolTotalCodewords(A){return U[A]},B.getBCHDigit=function(A){let B=0;for(;0!==A;)B++,A>>>=1;return B},B.setToSJISFunction=function setToSJISFunction(A){if("function"!=typeof A)throw Error('"toSJISFunc" is not a valid function.');N=A},B.isKanjiModeEnabled=function(){return void 0!==N},B.toSJIS=function toSJIS(A){return N(A)}},28645:function(A,B){B.isValid=function isValid(A){return!isNaN(A)&&A>=1&&A<=40}},56323:function(A,B,N){let U=N(14634),H=N(34937),W=N(25350),j=N(25064),V=N(28645),K=7973,X=U.getBCHDigit(K);function getBestVersionForDataLength(A,N,U){for(let H=1;H<=40;H++)if(N<=B.getCapacity(H,U,A))return H}function getReservedBitsCount(A,B){return j.getCharCountIndicator(A,B)+4}function getTotalBitsFromDataArray(A,B){let N=0;return A.forEach(function(A){let U=getReservedBitsCount(A.mode,B);N+=U+A.getBitsLength()}),N}function getBestVersionForMixedData(A,N){for(let U=1;U<=40;U++)if(getTotalBitsFromDataArray(A,U)<=B.getCapacity(U,N,j.MIXED))return U}B.from=function from(A,B){return V.isValid(A)?parseInt(A,10):B},B.getCapacity=function getCapacity(A,B,N){if(!V.isValid(A))throw Error("Invalid QR Code version");void 0===N&&(N=j.BYTE);let W=U.getSymbolTotalCodewords(A),K=(W-H.getTotalCodewordsCount(A,B))*8;if(N===j.MIXED)return K;let X=K-getReservedBitsCount(N,A);switch(N){case j.NUMERIC:return Math.floor(X/10*3);case j.ALPHANUMERIC:return Math.floor(X/11*2);case j.KANJI:return Math.floor(X/13);case j.BYTE:default:return Math.floor(X/8)}},B.getBestVersionForData=function getBestVersionForData(A,B){let N;let U=W.from(B,W.M);if(Array.isArray(A)){if(A.length>1)return getBestVersionForMixedData(A,U);if(0===A.length)return 1;N=A[0]}else N=A;return getBestVersionForDataLength(N.mode,N.getLength(),U)},B.getEncodedBits=function getEncodedBits(A){if(!V.isValid(A)||A<7)throw Error("Invalid QR Code version");let B=A<<12;for(;U.getBCHDigit(B)-X>=0;)B^=K<<U.getBCHDigit(B)-X;return A<<12|B}},6650:function(A,B,N){let U=N(55655);function clearCanvas(A,B,N){A.clearRect(0,0,B.width,B.height),!B.style&&(B.style={}),B.height=N,B.width=N,B.style.height=N+"px",B.style.width=N+"px"}function getCanvasElement(){try{return document.createElement("canvas")}catch(A){throw Error("You need to specify a canvas element")}}B.render=function render(A,B,N){let H=N,W=B;void 0===H&&(!B||!B.getContext)&&(H=B,B=void 0),!B&&(W=getCanvasElement()),H=U.getOptions(H);let j=U.getImageWidth(A.modules.size,H),V=W.getContext("2d"),K=V.createImageData(j,j);return U.qrToImageData(K.data,A,H),clearCanvas(V,W,j),V.putImageData(K,0,0),W},B.renderToDataURL=function renderToDataURL(A,N,U){let H=U;void 0===H&&(!N||!N.getContext)&&(H=N,N=void 0),!H&&(H={});let W=B.render(A,N,H),j=H.type||"image/png",V=H.rendererOpts||{};return W.toDataURL(j,V.quality)}},4436:function(A,B,N){let U=N(55655);function getColorAttrib(A,B){let N=A.a/255,U=B+'="'+A.hex+'"';return N<1?U+" "+B+'-opacity="'+N.toFixed(2).slice(1)+'"':U}function svgCmd(A,B,N){let U=A+B;return void 0!==N&&(U+=" "+N),U}function qrToPath(A,B,N){let U="",H=0,W=!1,j=0;for(let V=0;V<A.length;V++){let K=Math.floor(V%B),X=Math.floor(V/B);!K&&!W&&(W=!0),A[V]?(j++,!(V>0&&K>0&&A[V-1])&&(U+=W?svgCmd("M",K+N,.5+X+N):svgCmd("m",H,0),H=0,W=!1),!(K+1<B&&A[V+1])&&(U+=svgCmd("h",j),j=0)):H++}return U}B.render=function render(A,B,N){let H=U.getOptions(B),W=A.modules.size,j=A.modules.data,V=W+2*H.margin,K=H.color.light.a?"<path "+getColorAttrib(H.color.light,"fill")+' d="M0 0h'+V+"v"+V+'H0z"/>':"",X="<path "+getColorAttrib(H.color.dark,"stroke")+' d="'+qrToPath(j,W,H.margin)+'"/>',J='viewBox="0 0 '+V+" "+V+'"',ee='<svg xmlns="http://www.w3.org/2000/svg" '+(H.width?'width="'+H.width+'" height="'+H.width+'" ':"")+J+' shape-rendering="crispEdges">'+K+X+"</svg>\n";return"function"==typeof N&&N(null,ee),ee}},55655:function(A,B){function hex2rgba(A){if("number"==typeof A&&(A=A.toString()),"string"!=typeof A)throw Error("Color should be defined as hex string");let B=A.slice().replace("#","").split("");if(B.length<3||5===B.length||B.length>8)throw Error("Invalid hex color: "+A);(3===B.length||4===B.length)&&(B=Array.prototype.concat.apply([],B.map(function(A){return[A,A]}))),6===B.length&&B.push("F","F");let N=parseInt(B.join(""),16);return{r:N>>24&255,g:N>>16&255,b:N>>8&255,a:255&N,hex:"#"+B.slice(0,6).join("")}}B.getOptions=function getOptions(A){!A&&(A={}),!A.color&&(A.color={});let B=void 0===A.margin||null===A.margin||A.margin<0?4:A.margin,N=A.width&&A.width>=21?A.width:void 0,U=A.scale||4;return{width:N,scale:N?4:U,margin:B,color:{dark:hex2rgba(A.color.dark||"#000000ff"),light:hex2rgba(A.color.light||"#ffffffff")},type:A.type,rendererOpts:A.rendererOpts||{}}},B.getScale=function getScale(A,B){return B.width&&B.width>=A+2*B.margin?B.width/(A+2*B.margin):B.scale},B.getImageWidth=function getImageWidth(A,N){let U=B.getScale(A,N);return Math.floor((A+2*N.margin)*U)},B.qrToImageData=function qrToImageData(A,N,U){let H=N.modules.size,W=N.modules.data,j=B.getScale(H,U),V=Math.floor((H+2*U.margin)*j),K=U.margin*j,X=[U.color.light,U.color.dark];for(let B=0;B<V;B++)for(let N=0;N<V;N++){let J=(B*V+N)*4,ee=U.color.light;B>=K&&N>=K&&B<V-K&&N<V-K&&(ee=X[+!!W[Math.floor((B-K)/j)*H+Math.floor((N-K)/j)]]),A[J++]=ee.r,A[J++]=ee.g,A[J++]=ee.b,A[J]=ee.a}}},4659:function(A,B,N){"use strict";let U;N.d(B,{Z:function(){return esm_browser_v4}});var H={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let W=new Uint8Array(16);function rng(){if(!U&&!(U="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return U(W)}let j=[];for(let A=0;A<256;++A)j.push((A+256).toString(16).slice(1));function unsafeStringify(A,B=0){return j[A[B+0]]+j[A[B+1]]+j[A[B+2]]+j[A[B+3]]+"-"+j[A[B+4]]+j[A[B+5]]+"-"+j[A[B+6]]+j[A[B+7]]+"-"+j[A[B+8]]+j[A[B+9]]+"-"+j[A[B+10]]+j[A[B+11]]+j[A[B+12]]+j[A[B+13]]+j[A[B+14]]+j[A[B+15]]}var esm_browser_v4=function v4(A,B,N){if(H.randomUUID&&!B&&!A)return H.randomUUID();let U=(A=A||{}).random||(A.rng||rng)();if(U[6]=15&U[6]|64,U[8]=63&U[8]|128,B){N=N||0;for(let A=0;A<16;++A)B[N+A]=U[A];return B}return unsafeStringify(U)}},2505:function(A,B,N){"use strict";N.d(B,{RR:function(){return flip},X5:function(){return autoPlacement},cv:function(){return offset},dp:function(){return size},oo:function(){return et},uY:function(){return shift}});var U=N(41622),H=N(51606),W=N(70879),j=N(27337),V=N(64593),K=N(75649),X=N(31547),J=N(377);N(87394),N(94941),N(48421),N(97357),N(34333),N(87989),N(6045),N(10364),N(67673),N(43648),N(82427),N(36062),N(57057),N(28636),N(69032),N(93354),N(86651),N(1154),N(80013),N(64961),N(12996),N(59564),N(64091),N(51109),N(42876),N(33933);var ee=N(71492);function computeCoordsFromPlacement(A,B,N){var U,H=A.reference,W=A.floating,j=(0,ee.Qq)(B),V=(0,ee.Wh)(B),K=(0,ee.I4)(V),X=(0,ee.k3)(B),J="y"===j,et=H.x+H.width/2-W.width/2,er=H.y+H.height/2-W.height/2,en=H[K]/2-W[K]/2;switch(X){case"top":U={x:et,y:H.y-W.height};break;case"bottom":U={x:et,y:H.y+H.height};break;case"right":U={x:H.x+H.width,y:er};break;case"left":U={x:H.x-W.width,y:er};break;default:U={x:H.x,y:H.y}}switch((0,ee.hp)(B)){case"start":U[V]-=en*(N&&J?-1:1);break;case"end":U[V]+=en*(N&&J?-1:1)}return U}var et=function(){var A=(0,U._)(function(A,B,N){var U,V,K,ee,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return V=void 0===(U=N.placement)?"bottom":U,ee=void 0===(K=N.strategy)?"absolute":K,er=void 0===(et=N.middleware)?[]:et,en=N.platform,ei=er.filter(Boolean),[4,null==en.isRTL?void 0:en.isRTL(B)];case 1:return eo=J.sent(),[4,en.getElementRects({reference:A,floating:B,strategy:ee})];case 2:eu=(es=computeCoordsFromPlacement(ea=J.sent(),V,eo)).x,el=es.y,ec=V,ef={},ed=0,ep=0,J.label=3;case 3:if(!(ep<ei.length))return[3,11];return eg=(eh=ei[ep]).name,[4,(0,eh.fn)({x:eu,y:el,initialPlacement:V,placement:ec,strategy:ee,middlewareData:ef,rects:ea,platform:en,elements:{reference:A,floating:B}})];case 4:if(em=(ey=J.sent()).x,e_=ey.y,ev=ey.data,eS=ey.reset,eu=null!=em?em:eu,el=null!=e_?e_:el,ef=(0,j._)((0,W._)({},ef),(0,H._)({},eg,(0,W._)({},ef[eg],ev))),!(eS&&ed<=50))return[3,10];if(ed++,(void 0===eS?"undefined":(0,X._)(eS))!=="object")return[3,9];if(eS.placement&&(ec=eS.placement),!eS.rects)return[3,8];if(!0!==eS.rects)return[3,6];return[4,en.getElementRects({reference:A,floating:B,strategy:ee})];case 5:return eb=J.sent(),[3,7];case 6:eb=eS.rects,J.label=7;case 7:ea=eb,J.label=8;case 8:eu=(eE=computeCoordsFromPlacement(ea,ec,eo)).x,el=eE.y,J.label=9;case 9:ep=-1,J.label=10;case 10:return ep++,[3,3];case 11:return[2,{x:eu,y:el,placement:ec,strategy:ee,middlewareData:ef}]}})});return function computePosition(B,N,U){return A.apply(this,arguments)}}();function detectOverflow(A,B){return _detectOverflow.apply(this,arguments)}function _detectOverflow(){return(_detectOverflow=(0,U._)(function(A,B){var N,U,H,W,j,V,K,X,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE,eT,eA;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return void 0===B&&(B={}),U=A.x,H=A.y,W=A.platform,j=A.rects,V=A.elements,K=A.strategy,er=void 0===(et=(X=(0,ee.ku)(B,A)).boundary)?"clippingAncestors":et,ei=void 0===(en=X.rootBoundary)?"viewport":en,ea=void 0===(eo=X.elementContext)?"floating":eo,eu=void 0!==(es=X.altBoundary)&&es,ec=void 0===(el=X.padding)?0:el,ef=(0,ee.yd)(ec),ed="floating"===ea?"reference":"floating",ep=V[eu?ed:ea],eg=W.getClippingRect,ey={},[4,null==W.isElement?void 0:W.isElement(ep)];case 1:if(!(null==(N=J.sent())||N))return[3,2];return em=ep,[3,5];case 2:if(e_=ep.contextElement)return[3,4];return[4,null==W.getDocumentElement?void 0:W.getDocumentElement(V.floating)];case 3:e_=J.sent(),J.label=4;case 4:em=e_,J.label=5;case 5:return[4,eg.apply(W,[(ey.element=em,ey.boundary=er,ey.rootBoundary=ei,ey.strategy=K,ey)])];case 6:return eh=ee.JB.apply(void 0,[J.sent()]),ev="floating"===ea?{x:U,y:H,width:j.floating.width,height:j.floating.height}:j.reference,[4,null==W.getOffsetParent?void 0:W.getOffsetParent(V.floating)];case 7:return eS=J.sent(),[4,null==W.isElement?void 0:W.isElement(eS)];case 8:if(!J.sent())return[3,10];return[4,null==W.getScale?void 0:W.getScale(eS)];case 9:return eE=J.sent()||{x:1,y:1},[3,11];case 10:eE={x:1,y:1},J.label=11;case 11:if(eb=eE,!W.convertOffsetParentRelativeRectToViewportRelativeRect)return[3,13];return[4,W.convertOffsetParentRelativeRectToViewportRelativeRect({elements:V,rect:ev,offsetParent:eS,strategy:K})];case 12:return eA=J.sent(),[3,14];case 13:eA=ev,J.label=14;case 14:return eT=ee.JB.apply(void 0,[eA]),[2,{top:(eh.top-eT.top+ef.top)/eb.y,bottom:(eT.bottom-eh.bottom+ef.bottom)/eb.y,left:(eh.left-eT.left+ef.left)/eb.x,right:(eT.right-eh.right+ef.right)/eb.x}]}})})).apply(this,arguments)}function getPlacementList(A,B,N){return(A?(0,K._)(N.filter(function(B){return(0,ee.hp)(B)===A})).concat((0,K._)(N.filter(function(B){return(0,ee.hp)(B)!==A}))):N.filter(function(A){return(0,ee.k3)(A)===A})).filter(function(N){return!A||(0,ee.hp)(N)===A||!!B&&(0,ee.Go)(N)!==N})}var autoPlacement=function autoPlacement(A){return void 0===A&&(A={}),{name:"autoPlacement",options:A,fn:function(B){return(0,U._)(function(){var N,U,H,W,j,X,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return W=B.rects,j=B.middlewareData,X=B.placement,et=B.platform,er=B.elements,eo=void 0!==(ei=(en=(0,ee.ku)(A,B)).crossAxis)&&ei,ea=en.alignment,eu=void 0===(es=en.allowedPlacements)?ee.Ct:es,ec=void 0===(el=en.autoAlignment)||el,ef=(0,V._)(en,["crossAxis","alignment","allowedPlacements","autoAlignment"]),ed=void 0!==ea||eu===ee.Ct?getPlacementList(ea||null,ec,eu):eu,[4,detectOverflow(B,ef)];case 1:if(ep=J.sent(),null==(eg=ed[eh=(null==(N=j.autoPlacement)?void 0:N.index)||0]))return[2,{}];return em=[eg,W],[4,null==et.isRTL?void 0:et.isRTL(er.floating)];case 2:if(ey=ee.i8.apply(void 0,em.concat([J.sent()])),X!==eg)return[2,{reset:{placement:ed[0]}}];if(e_=[ep[(0,ee.k3)(eg)],ep[ey[0]],ep[ey[1]]],ev=(0,K._)((null==(U=j.autoPlacement)?void 0:U.overflows)||[]).concat([{placement:eg,overflows:e_}]),eS=ed[eh+1])return[2,{data:{index:eh+1,overflows:ev},reset:{placement:eS}}];if((eE=(null==(H=(eb=ev.map(function(A){var B=(0,ee.hp)(A.placement);return[A.placement,B&&eo?A.overflows.slice(0,2).reduce(function(A,B){return A+B},0):A.overflows[0],A.overflows]}).sort(function(A,B){return A[1]-B[1]})).filter(function(A){return A[2].slice(0,(0,ee.hp)(A[0])?2:3).every(function(A){return A<=0})})[0])?void 0:H[0])||eb[0][0])!==X)return[2,{data:{index:eh+1,overflows:ev},reset:{placement:eE}}];return[2,{}]}})})()}}},flip=function flip(A){return void 0===A&&(A={}),{name:"flip",options:A,fn:function(B){return(0,U._)(function(){var N,U,H,W,j,X,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE,eT,eA,ew,eO,eB,eI,eR,eC,eL,eN,eM,ex;return(0,J.Jh)(this,function(J){switch(J.label){case 0:if(H=B.placement,W=B.middlewareData,j=B.rects,X=B.initialPlacement,et=B.platform,er=B.elements,eo=void 0===(ei=(en=(0,ee.ku)(A,B)).mainAxis)||ei,es=void 0===(ea=en.crossAxis)||ea,eu=en.fallbackPlacements,ec=void 0===(el=en.fallbackStrategy)?"bestFit":el,ed=void 0===(ef=en.fallbackAxisSideDirection)?"none":ef,eh=void 0===(ep=en.flipAlignment)||ep,eg=(0,V._)(en,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]),null!=(N=W.arrow)&&N.alignmentOffset)return[2,{}];return ey=(0,ee.k3)(H),em=(0,ee.Qq)(X),e_=(0,ee.k3)(X)===X,[4,null==et.isRTL?void 0:et.isRTL(er.floating)];case 1:return ev=J.sent(),eS=eu||(e_||!eh?[(0,ee.pw)(X)]:(0,ee.gy)(X)),eb="none"!==ed,!eu&&eb&&(eE=eS).push.apply(eE,(0,K._)((0,ee.KX)(X,eh,ed,ev))),eT=[X].concat((0,K._)(eS)),[4,detectOverflow(B,eg)];case 2:if(eA=J.sent(),ew=[],eO=(null==(U=W.flip)?void 0:U.overflows)||[],eo&&ew.push(eA[ey]),es&&(eB=(0,ee.i8)(H,j,ev),ew.push(eA[eB[0]],eA[eB[1]])),eO=(0,K._)(eO).concat([{placement:H,overflows:ew}]),!ew.every(function(A){return A<=0})){if(eL=eT[eC=((null==(eI=W.flip)?void 0:eI.index)||0)+1])return[2,{data:{index:eC,overflows:eO},reset:{placement:eL}}];if(!(eN=null==(eR=eO.filter(function(A){return A.overflows[0]<=0}).sort(function(A,B){return A.overflows[1]-B.overflows[1]})[0])?void 0:eR.placement))switch(ec){case"bestFit":(ex=null==(eM=eO.filter(function(A){if(eb){var B=(0,ee.Qq)(A.placement);return B===em||"y"===B}return!0}).map(function(A){return[A.placement,A.overflows.filter(function(A){return A>0}).reduce(function(A,B){return A+B},0)]}).sort(function(A,B){return A[1]-B[1]})[0])?void 0:eM[0])&&(eN=ex);break;case"initialPlacement":eN=X}if(H!==eN)return[2,{reset:{placement:eN}}]}return[2,{}]}})})()}}};function convertValueToCoords(A,B){return _convertValueToCoords.apply(this,arguments)}function _convertValueToCoords(){return(_convertValueToCoords=(0,U._)(function(A,B){var N,U,H,W,j,V,K,X,et,er,en,ei,eo,ea;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return N=A.placement,U=A.platform,H=A.elements,[4,null==U.isRTL?void 0:U.isRTL(H.floating)];case 1:return W=J.sent(),j=(0,ee.k3)(N),V=(0,ee.hp)(N),K="y"===(0,ee.Qq)(N),X=["left","top"].includes(j)?-1:1,et=W&&K?-1:1,ei=(en="number"==typeof(er=(0,ee.ku)(B,A))?{mainAxis:er,crossAxis:0,alignmentAxis:null}:{mainAxis:er.mainAxis||0,crossAxis:er.crossAxis||0,alignmentAxis:er.alignmentAxis}).mainAxis,eo=en.crossAxis,ea=en.alignmentAxis,V&&"number"==typeof ea&&(eo="end"===V?-1*ea:ea),[2,K?{x:eo*et,y:ei*X}:{x:ei*X,y:eo*et}]}})})).apply(this,arguments)}var offset=function offset(A){return void 0===A&&(A=0),{name:"offset",options:A,fn:function(B){return(0,U._)(function(){var N,U,H,V,K,X,ee;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return H=B.x,V=B.y,K=B.placement,X=B.middlewareData,[4,convertValueToCoords(B,A)];case 1:if(ee=J.sent(),K===(null==(N=X.offset)?void 0:N.placement)&&null!=(U=X.arrow)&&U.alignmentOffset)return[2,{}];return[2,{x:H+ee.x,y:V+ee.y,data:(0,j._)((0,W._)({},ee),{placement:K})}]}})})()}}},shift=function shift(A){return void 0===A&&(A={}),{name:"shift",options:A,fn:function(B){return(0,U._)(function(){var N,U,K,X,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE,eT,eA;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return N=B.x,U=B.y,K=B.placement,er=void 0===(et=(X=(0,ee.ku)(A,B)).mainAxis)||et,ei=void 0!==(en=X.crossAxis)&&en,ea=void 0===(eo=X.limiter)?{fn:function(A){return{x:A.x,y:A.y}}}:eo,es=(0,V._)(X,["mainAxis","crossAxis","limiter"]),eu={x:N,y:U},[4,detectOverflow(B,es)];case 1:return el=J.sent(),ec=(0,ee.Qq)((0,ee.k3)(K)),ed=eu[ef=(0,ee.Rn)(ec)],ep=eu[ec],er&&(eh="y"===ef?"top":"left",eg="y"===ef?"bottom":"right",ey=ed+el[eh],em=ed-el[eg],ed=(0,ee.uZ)(ey,ed,em)),ei&&(e_="y"===ec?"top":"left",ev="y"===ec?"bottom":"right",eS=ep+el[e_],eb=ep-el[ev],ep=(0,ee.uZ)(eS,ep,eb)),eT=ea.fn((0,j._)((0,W._)({},B),(eE={},(0,H._)(eE,ef,ed),(0,H._)(eE,ec,ep),eE))),[2,(0,j._)((0,W._)({},eT),{data:{x:eT.x-N,y:eT.y-U,enabled:(eA={},(0,H._)(eA,ef,er),(0,H._)(eA,ec,ei),eA)}})]}})})()}}},size=function size(A){return void 0===A&&(A={}),{name:"size",options:A,fn:function(B){return(0,U._)(function(){var N,U,H,K,X,et,er,en,ei,eo,ea,es,eu,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE,eT,eA,ew;return(0,J.Jh)(this,function(J){switch(J.label){case 0:return H=B.placement,K=B.rects,X=B.platform,et=B.elements,ei=void 0===(en=(er=(0,ee.ku)(A,B)).apply)?function(){}:en,[4,detectOverflow(B,(0,V._)(er,["apply"]))];case 1:if(eo=J.sent(),ea=(0,ee.k3)(H),es=(0,ee.hp)(H),eu="y"===(0,ee.Qq)(H),ec=(el=K.floating).width,ef=el.height,"top"!==ea&&"bottom"!==ea)return[3,3];return ed=ea,[4,null==X.isRTL?void 0:X.isRTL(et.floating)];case 2:return ep=es===(J.sent()?"start":"end")?"left":"right",[3,4];case 3:ep=ea,ed="end"===es?"top":"bottom",J.label=4;case 4:return eh=ef-eo.top-eo.bottom,eg=ec-eo.left-eo.right,ey=(0,ee.VV)(ef-eo[ed],eh),em=(0,ee.VV)(ec-eo[ep],eg),e_=!B.middlewareData.shift,ev=ey,eS=em,null!=(N=B.middlewareData.shift)&&N.enabled.x&&(eS=eg),null!=(U=B.middlewareData.shift)&&U.enabled.y&&(ev=eh),e_&&!es&&(eb=(0,ee.Fp)(eo.left,0),eE=(0,ee.Fp)(eo.right,0),eT=(0,ee.Fp)(eo.top,0),eA=(0,ee.Fp)(eo.bottom,0),eu?eS=ec-2*(0!==eb||0!==eE?eb+eE:(0,ee.Fp)(eo.left,eo.right)):ev=ef-2*(0!==eT||0!==eA?eT+eA:(0,ee.Fp)(eo.top,eo.bottom))),[4,ei((0,j._)((0,W._)({},B),{availableWidth:eS,availableHeight:ev}))];case 5:return J.sent(),[4,X.getDimensions(et.floating)];case 6:if(ew=J.sent(),ec!==ew.width||ef!==ew.height)return[2,{reset:{rects:!0}}];return[2,{}]}})})()}}}},36877:function(A,B,N){"use strict";N.d(B,{Me:function(){return autoUpdate},oo:function(){return computePosition}});var U=N(41622),H=N(70879),W=N(27337),j=N(44501),V=N(75649),K=N(377);N(9557),N(64091),N(51109),N(87394),N(94941),N(48421),N(97357),N(34333),N(42876),N(33933),N(87989),N(82427),N(36062),N(57057),N(87535),N(75204),N(74093),N(20768),N(41648),N(59339),N(47444),N(34757),N(85908),N(39995),N(22943),N(54767),N(55820),N(93225),N(33708),N(64322),N(47771),N(27461),N(23339);var X=N(71492),J=N(2505);N(99808),N(58486),N(72169),N(6045),N(10364),N(67673);var ee=Math.min,et=Math.max,er=Math.round,en=Math.floor,createCoords=function(A){return{x:A,y:A}};function getNodeName(A){return isNode(A)?(A.nodeName||"").toLowerCase():"#document"}function getWindow(A){var B;return(null==A?void 0:null==(B=A.ownerDocument)?void 0:B.defaultView)||window}function getDocumentElement(A){var B;return null==(B=(isNode(A)?A.ownerDocument:A.document)||window.document)?void 0:B.documentElement}function isNode(A){return A instanceof Node||A instanceof getWindow(A).Node}function isElement(A){return A instanceof Element||A instanceof getWindow(A).Element}function isHTMLElement(A){return A instanceof HTMLElement||A instanceof getWindow(A).HTMLElement}function isShadowRoot(A){return"undefined"!=typeof ShadowRoot&&(A instanceof ShadowRoot||A instanceof getWindow(A).ShadowRoot)}function isOverflowElement(A){var B=getComputedStyle(A),N=B.overflow,U=B.overflowX,H=B.overflowY,W=B.display;return/auto|scroll|overlay|hidden|clip/.test(N+H+U)&&!["inline","contents"].includes(W)}function isTableElement(A){return["table","td","th"].includes(getNodeName(A))}function isContainingBlock(A){var B=isWebKit(),N=getComputedStyle(A);return"none"!==N.transform||"none"!==N.perspective||!!N.containerType&&"normal"!==N.containerType||!B&&!!N.backdropFilter&&"none"!==N.backdropFilter||!B&&!!N.filter&&"none"!==N.filter||["transform","perspective","filter"].some(function(A){return(N.willChange||"").includes(A)})||["paint","layout","strict","content"].some(function(A){return(N.contain||"").includes(A)})}function getContainingBlock(A){for(var B=getParentNode(A);isHTMLElement(B)&&!isLastTraversableNode(B);){if(isContainingBlock(B))return B;B=getParentNode(B)}return null}function isWebKit(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function isLastTraversableNode(A){return["html","body","#document"].includes(getNodeName(A))}function getComputedStyle(A){return getWindow(A).getComputedStyle(A)}function getNodeScroll(A){return isElement(A)?{scrollLeft:A.scrollLeft,scrollTop:A.scrollTop}:{scrollLeft:A.pageXOffset,scrollTop:A.pageYOffset}}function getParentNode(A){if("html"===getNodeName(A))return A;var B=A.assignedSlot||A.parentNode||isShadowRoot(A)&&A.host||getDocumentElement(A);return isShadowRoot(B)?B.host:B}function getNearestOverflowAncestor(A){var B=getParentNode(A);return isLastTraversableNode(B)?A.ownerDocument?A.ownerDocument.body:A.body:isHTMLElement(B)&&isOverflowElement(B)?B:getNearestOverflowAncestor(B)}function getOverflowAncestors(A,B,N){void 0===B&&(B=[]),void 0===N&&(N=!0);var U,H=getNearestOverflowAncestor(A),W=H===(null==(U=A.ownerDocument)?void 0:U.body),j=getWindow(H);return W?B.concat(j,j.visualViewport||[],isOverflowElement(H)?H:[],j.frameElement&&N?getOverflowAncestors(j.frameElement):[]):B.concat(H,getOverflowAncestors(H,[],N))}function getCssDimensions(A){var B=getComputedStyle(A),N=parseFloat(B.width)||0,U=parseFloat(B.height)||0,H=isHTMLElement(A),W=H?A.offsetWidth:N,j=H?A.offsetHeight:U,V=er(N)!==W||er(U)!==j;return V&&(N=W,U=j),{width:N,height:U,$:V}}function unwrapElement(A){return isElement(A)?A:A.contextElement}function getScale(A){var B=unwrapElement(A);if(!isHTMLElement(B))return createCoords(1);var N=B.getBoundingClientRect(),U=getCssDimensions(B),H=U.width,W=U.height,j=U.$,V=(j?er(N.width):N.width)/H,K=(j?er(N.height):N.height)/W;return(!V||!Number.isFinite(V))&&(V=1),(!K||!Number.isFinite(K))&&(K=1),{x:V,y:K}}N(25069),N(1154),N(80013);var ei=createCoords(0);function getVisualOffsets(A){var B=getWindow(A);return isWebKit()&&B.visualViewport?{x:B.visualViewport.offsetLeft,y:B.visualViewport.offsetTop}:ei}function shouldAddVisualOffsets(A,B,N){return void 0===B&&(B=!1),!!N&&(!B||N===getWindow(A))&&B}function getBoundingClientRect(A,B,N,U){void 0===B&&(B=!1),void 0===N&&(N=!1);var H=A.getBoundingClientRect(),W=unwrapElement(A),j=createCoords(1);B&&(U?isElement(U)&&(j=getScale(U)):j=getScale(A));var V=shouldAddVisualOffsets(W,N,U)?getVisualOffsets(W):createCoords(0),K=(H.left+V.x)/j.x,J=(H.top+V.y)/j.y,ee=H.width/j.x,et=H.height/j.y;if(W){for(var er=getWindow(W),en=U&&isElement(U)?getWindow(U):U,ei=er.frameElement;ei&&U&&en!==er;){var eo=getScale(ei),ea=ei.getBoundingClientRect(),es=getComputedStyle(ei),eu=ea.left+(ei.clientLeft+parseFloat(es.paddingLeft))*eo.x,el=ea.top+(ei.clientTop+parseFloat(es.paddingTop))*eo.y;K*=eo.x,J*=eo.y,ee*=eo.x,et*=eo.y,K+=eu,J+=el,ei=getWindow(ei).frameElement}}return(0,X.JB)({width:ee,height:et,x:K,y:J})}function convertOffsetParentRelativeRectToViewportRelativeRect(A){var B=A.rect,N=A.offsetParent,U=A.strategy,H=isHTMLElement(N),W=getDocumentElement(N);if(N===W)return B;var j={scrollLeft:0,scrollTop:0},V=createCoords(1),K=createCoords(0);if((H||!H&&"fixed"!==U)&&(("body"!==getNodeName(N)||isOverflowElement(W))&&(j=getNodeScroll(N)),isHTMLElement(N))){var X=getBoundingClientRect(N);V=getScale(N),K.x=X.x+N.clientLeft,K.y=X.y+N.clientTop}return{width:B.width*V.x,height:B.height*V.y,x:B.x*V.x-j.scrollLeft*V.x+K.x,y:B.y*V.y-j.scrollTop*V.y+K.y}}function getClientRects(A){return Array.from(A.getClientRects())}function getWindowScrollBarX(A){return getBoundingClientRect(getDocumentElement(A)).left+getNodeScroll(A).scrollLeft}function getDocumentRect(A){var B=getDocumentElement(A),N=getNodeScroll(A),U=A.ownerDocument.body,H=et(B.scrollWidth,B.clientWidth,U.scrollWidth,U.clientWidth),W=et(B.scrollHeight,B.clientHeight,U.scrollHeight,U.clientHeight),j=-N.scrollLeft+getWindowScrollBarX(A),V=-N.scrollTop;return"rtl"===getComputedStyle(U).direction&&(j+=et(B.clientWidth,U.clientWidth)-H),{width:H,height:W,x:j,y:V}}function getViewportRect(A,B){var N=getWindow(A),U=getDocumentElement(A),H=N.visualViewport,W=U.clientWidth,j=U.clientHeight,V=0,K=0;if(H){W=H.width,j=H.height;var X=isWebKit();(!X||X&&"fixed"===B)&&(V=H.offsetLeft,K=H.offsetTop)}return{width:W,height:j,x:V,y:K}}function getInnerBoundingClientRect(A,B){var N=getBoundingClientRect(A,!0,"fixed"===B),U=N.top+A.clientTop,H=N.left+A.clientLeft,W=isHTMLElement(A)?getScale(A):createCoords(1);return{width:A.clientWidth*W.x,height:A.clientHeight*W.y,x:H*W.x,y:U*W.y}}function getClientRectFromClippingAncestor(A,B,N){var U;if("viewport"===B)U=getViewportRect(A,N);else if("document"===B)U=getDocumentRect(getDocumentElement(A));else if(isElement(B))U=getInnerBoundingClientRect(B,N);else{var j=getVisualOffsets(A);U=(0,W._)((0,H._)({},B),{x:B.x-j.x,y:B.y-j.y})}return(0,X.JB)(U)}function hasFixedPositionAncestor(A,B){var N=getParentNode(A);return!(N===B||!isElement(N)||isLastTraversableNode(N))&&("fixed"===getComputedStyle(N).position||hasFixedPositionAncestor(N,B))}function getClippingElementAncestors(A,B){var N=B.get(A);if(N)return N;for(var U=getOverflowAncestors(A,[],!1).filter(function(A){return isElement(A)&&"body"!==getNodeName(A)}),H=null,W="fixed"===getComputedStyle(A).position,j=W?getParentNode(A):A;isElement(j)&&!isLastTraversableNode(j);){var V=getComputedStyle(j),K=isContainingBlock(j);!K&&"fixed"===V.position&&(H=null),(W?!K&&!H:!K&&"static"===V.position&&!!H&&["absolute","fixed"].includes(H.position)||isOverflowElement(j)&&!K&&hasFixedPositionAncestor(A,j))?U=U.filter(function(A){return A!==j}):H=V,j=getParentNode(j)}return B.set(A,U),U}function getClippingRect(A){var B=A.element,N=A.boundary,U=A.rootBoundary,H=A.strategy,W="clippingAncestors"===N?getClippingElementAncestors(B,this._c):[].concat(N),j=(0,V._)(W).concat([U]),K=j[0],X=j.reduce(function(A,N){var U=getClientRectFromClippingAncestor(B,N,H);return A.top=et(U.top,A.top),A.right=ee(U.right,A.right),A.bottom=ee(U.bottom,A.bottom),A.left=et(U.left,A.left),A},getClientRectFromClippingAncestor(B,K,H));return{width:X.right-X.left,height:X.bottom-X.top,x:X.left,y:X.top}}function getDimensions(A){return getCssDimensions(A)}function getRectRelativeToOffsetParent(A,B,N){var U=isHTMLElement(B),H=getDocumentElement(B),W="fixed"===N,j=getBoundingClientRect(A,!0,W,B),V={scrollLeft:0,scrollTop:0},K=createCoords(0);if(U||!U&&!W){if(("body"!==getNodeName(B)||isOverflowElement(H))&&(V=getNodeScroll(B)),U){var X=getBoundingClientRect(B,!0,W,B);K.x=X.x+B.clientLeft,K.y=X.y+B.clientTop}else H&&(K.x=getWindowScrollBarX(H))}return{x:j.left+V.scrollLeft-K.x,y:j.top+V.scrollTop-K.y,width:j.width,height:j.height}}function getTrueOffsetParent(A,B){return isHTMLElement(A)&&"fixed"!==getComputedStyle(A).position?B?B(A):A.offsetParent:null}function getOffsetParent(A,B){var N=getWindow(A);if(!isHTMLElement(A))return N;for(var U=getTrueOffsetParent(A,B);U&&isTableElement(U)&&"static"===getComputedStyle(U).position;)U=getTrueOffsetParent(U,B);return U&&("html"===getNodeName(U)||"body"===getNodeName(U)&&"static"===getComputedStyle(U).position&&!isContainingBlock(U))?N:U||getContainingBlock(A)||N}var eo=function(){var A=(0,U._)(function(A){var B,N,U,W,j,V,X,J;return(0,K.Jh)(this,function(K){switch(K.label){case 0:return B=A.reference,N=A.floating,U=A.strategy,W=this.getOffsetParent||getOffsetParent,j=this.getDimensions,V={},X=[B],[4,W(N)];case 1:return V.reference=getRectRelativeToOffsetParent.apply(void 0,X.concat([K.sent(),U])),J=[{x:0,y:0}],[4,j(N)];case 2:return[2,(V.floating=H._.apply(void 0,J.concat([K.sent()])),V)]}})});return function getElementRects(B){return A.apply(this,arguments)}}(),ea={convertOffsetParentRelativeRectToViewportRelativeRect:convertOffsetParentRelativeRectToViewportRelativeRect,getDocumentElement:getDocumentElement,getClippingRect:getClippingRect,getOffsetParent:getOffsetParent,getElementRects:eo,getClientRects:getClientRects,getDimensions:getDimensions,getScale:getScale,isElement:isElement,isRTL:function isRTL(A){return"rtl"===getComputedStyle(A).direction}};function observeMove(A,B){var N,U=null,j=getDocumentElement(A);function cleanup(){clearTimeout(N),U&&U.disconnect(),U=null}function refresh(V,K){void 0===V&&(V=!1),void 0===K&&(K=1),cleanup();var X=A.getBoundingClientRect(),J=X.left,er=X.top,ei=X.width,eo=X.height;if(!V&&B(),!!ei&&!!eo){var ea={rootMargin:-en(er)+"px "+-en(j.clientWidth-(J+ei))+"px "+-en(j.clientHeight-(er+eo))+"px "+-en(J)+"px",threshold:et(0,ee(1,K))||1},es=!0;try{U=new IntersectionObserver(handleObserve,(0,W._)((0,H._)({},ea),{root:j.ownerDocument}))}catch(A){U=new IntersectionObserver(handleObserve,ea)}U.observe(A)}function handleObserve(A){var B=A[0].intersectionRatio;if(B!==K){if(!es)return refresh();B?refresh(!1,B):N=setTimeout(function(){refresh(!1,1e-7)},100)}es=!1}}return refresh(!0),cleanup}function autoUpdate(A,B,N,U){void 0===U&&(U={});var H,W=U.ancestorScroll,K=void 0===W||W,X=U.ancestorResize,J=void 0===X||X,ee=U.elementResize,et=void 0===ee?"function"==typeof ResizeObserver:ee,er=U.layoutShift,en=void 0===er?"function"==typeof IntersectionObserver:er,ei=U.animationFrame,eo=void 0!==ei&&ei,ea=unwrapElement(A),es=K||J?(0,V._)(ea?getOverflowAncestors(ea):[]).concat((0,V._)(getOverflowAncestors(B))):[];es.forEach(function(A){K&&A.addEventListener("scroll",N,{passive:!0}),J&&A.addEventListener("resize",N)});var eu=ea&&en?observeMove(ea,N):null,el=-1,ec=null;et&&(ec=new ResizeObserver(function(A){var U=(0,j._)(A,1)[0];U&&U.target===ea&&ec&&(ec.unobserve(B),cancelAnimationFrame(el),el=requestAnimationFrame(function(){ec&&ec.observe(B)})),N()}),ea&&!eo&&ec.observe(ea),ec.observe(B));var ef=eo?getBoundingClientRect(A):null;function frameLoop(){var B=getBoundingClientRect(A);ef&&(B.x!==ef.x||B.y!==ef.y||B.width!==ef.width||B.height!==ef.height)&&N(),ef=B,H=requestAnimationFrame(frameLoop)}return eo&&frameLoop(),N(),function(){es.forEach(function(A){K&&A.removeEventListener("scroll",N),J&&A.removeEventListener("resize",N)}),eu&&eu(),ec&&ec.disconnect(),ec=null,eo&&cancelAnimationFrame(H)}}var computePosition=function(A,B,N){var U=new Map,j=(0,H._)({platform:ea},N),V=(0,W._)((0,H._)({},j.platform),{_c:U});return(0,J.oo)(A,B,(0,W._)((0,H._)({},j),{platform:V}))}},71492:function(A,B,N){"use strict";N.d(B,{Ct:function(){return W},Fp:function(){return V},Go:function(){return getOppositeAlignmentPlacement},I4:function(){return getAxisLength},JB:function(){return rectToClientRect},KX:function(){return getOppositeAxisPlacements},Qq:function(){return getSideAxis},Rn:function(){return getOppositeAxis},VV:function(){return j},Wh:function(){return getAlignmentAxis},gy:function(){return getExpandedPlacements},hp:function(){return getAlignment},i8:function(){return getAlignmentSides},k3:function(){return getSide},ku:function(){return evaluate},pw:function(){return getOppositePlacement},uZ:function(){return clamp},yd:function(){return getPaddingObject}});var U=N(70879);N(82427),N(36062),N(57057),N(97357),N(34333),N(87989),N(99808),N(58486),N(42876),N(33933),N(72169),N(6045),N(10364),N(67673);var H=["start","end"],W=["top","right","bottom","left"].reduce(function(A,B){return A.concat(B,B+"-"+H[0],B+"-"+H[1])},[]),j=Math.min,V=Math.max,K={left:"right",right:"left",bottom:"top",top:"bottom"},X={start:"end",end:"start"};function clamp(A,B,N){return V(A,j(B,N))}function evaluate(A,B){return"function"==typeof A?A(B):A}function getSide(A){return A.split("-")[0]}function getAlignment(A){return A.split("-")[1]}function getOppositeAxis(A){return"x"===A?"y":"x"}function getAxisLength(A){return"y"===A?"height":"width"}function getSideAxis(A){return["top","bottom"].includes(getSide(A))?"y":"x"}function getAlignmentAxis(A){return getOppositeAxis(getSideAxis(A))}function getAlignmentSides(A,B,N){void 0===N&&(N=!1);var U=getAlignment(A),H=getAlignmentAxis(A),W=getAxisLength(H),j="x"===H?U===(N?"end":"start")?"right":"left":"start"===U?"bottom":"top";return B.reference[W]>B.floating[W]&&(j=getOppositePlacement(j)),[j,getOppositePlacement(j)]}function getExpandedPlacements(A){var B=getOppositePlacement(A);return[getOppositeAlignmentPlacement(A),B,getOppositeAlignmentPlacement(B)]}function getOppositeAlignmentPlacement(A){return A.replace(/start|end/g,function(A){return X[A]})}function getSideList(A,B,N){var U=["left","right"],H=["right","left"],W=["top","bottom"],j=["bottom","top"];switch(A){case"top":case"bottom":if(N)return B?H:U;return B?U:H;case"left":case"right":return B?W:j;default:return[]}}function getOppositeAxisPlacements(A,B,N,U){var H=getAlignment(A),W=getSideList(getSide(A),"start"===N,U);return H&&(W=W.map(function(A){return A+"-"+H}),B&&(W=W.concat(W.map(getOppositeAlignmentPlacement)))),W}function getOppositePlacement(A){return A.replace(/left|right|bottom|top/g,function(A){return K[A]})}function expandPaddingObject(A){return(0,U._)({top:0,right:0,bottom:0,left:0},A)}function getPaddingObject(A){return"number"!=typeof A?expandPaddingObject(A):{top:A,right:A,bottom:A,left:A}}function rectToClientRect(A){var B=A.x,N=A.y,U=A.width,H=A.height;return{width:U,height:H,top:N,left:B,right:B+U,bottom:N+H,x:B,y:N}}},52859:function(A,B,N){"use strict";function getDevtoolsGlobalHook(){return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__}function getTarget(){return"undefined"!=typeof window?window:"undefined"!=typeof globalThis?globalThis:{}}N.d(B,{F1:function(){return setupDevtoolsPlugin}}),N(86651),N(98976);var U,H,W="function"==typeof Proxy,j="devtools-plugin:setup",V="plugin:settings:set",K=N(41622),X=N(46490),J=N(90251),ee=N(75649),et=N(377);function isPerformanceSupported(){var A;return void 0!==U?U:("undefined"!=typeof window&&window.performance?(U=!0,H=window.performance):"undefined"!=typeof globalThis&&(null===(A=globalThis.perf_hooks)||void 0===A?void 0:A.performance)?(U=!0,H=globalThis.perf_hooks.performance):U=!1,U)}function now(){return isPerformanceSupported()?H.now():Date.now()}N(87989),N(58051),N(19077),N(75973),N(7608),N(42876),N(33933),N(36277),N(34333),N(74719),N(13396),N(91313),N(27461),N(23339),N(51109);var er=function(){function ApiProxy(A,B){var N=this;(0,X._)(this,ApiProxy),this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=A,this.hook=B;var U={};if(A.settings)for(var H in A.settings){var W=A.settings[H];U[H]=W.defaultValue}var j="__vue-devtools-plugin-settings__".concat(A.id),K=Object.assign({},U);try{var J=localStorage.getItem(j),et=JSON.parse(J);Object.assign(K,et)}catch(A){}this.fallbacks={getSettings:function(){return K},setSettings:function(A){try{localStorage.setItem(j,JSON.stringify(A))}catch(A){}K=A},now:function(){return now()}},B&&B.on(V,function(A,B){A===N.plugin.id&&N.fallbacks.setSettings(B)}),this.proxiedOn=new Proxy({},{get:function(A,B){if(N.target)return N.target.on[B];var U=N;return function(){for(var A=arguments.length,N=Array(A),H=0;H<A;H++)N[H]=arguments[H];U.onQueue.push({method:B,args:N})}}}),this.proxiedTarget=new Proxy({},{get:function(A,B){if(N.target)return N.target[B];if("on"===B)return N.proxiedOn;if(Object.keys(N.fallbacks).includes(B)){var U=N;return function(){for(var A,N=arguments.length,H=Array(N),W=0;W<N;W++)H[W]=arguments[W];return U.targetQueue.push({method:B,args:H,resolve:function(){}}),(A=U.fallbacks)[B].apply(A,(0,ee._)(H))}}else{var H=N;return function(){for(var A=arguments.length,N=Array(A),U=0;U<A;U++)N[U]=arguments[U];return new Promise(function(A){H.targetQueue.push({method:B,args:N,resolve:A})})}}}})}return(0,J._)(ApiProxy,[{key:"setRealTarget",value:function setRealTarget(A){var B=this;return(0,K._)(function(){var N,U,H,W,j,V,K,X,J,er,en,ei,eo,ea,es,eu;return(0,et.Jh)(this,function(et){switch(et.label){case 0:B.target=A,N=!0,U=!1,H=void 0;try{for(W=B.onQueue[Symbol.iterator]();!(N=(j=W.next()).done);N=!0)V=j.value,(K=B.target.on)[V.method].apply(K,(0,ee._)(V.args))}catch(A){U=!0,H=A}finally{try{!N&&null!=W.return&&W.return()}finally{if(U)throw H}}X=!0,J=!1,er=void 0,et.label=1;case 1:et.trys.push([1,6,7,8]),en=B.targetQueue[Symbol.iterator](),et.label=2;case 2:if(X=(ei=en.next()).done)return[3,5];return es=(eo=ei.value).resolve,[4,(ea=B.target)[eo.method].apply(ea,(0,ee._)(eo.args))];case 3:es.apply(eo,[et.sent()]),et.label=4;case 4:return X=!0,[3,2];case 5:return[3,8];case 6:return eu=et.sent(),J=!0,er=eu,[3,8];case 7:try{!X&&null!=en.return&&en.return()}finally{if(J)throw er}return[7];case 8:return[2]}})})()}}]),ApiProxy}();function setupDevtoolsPlugin(A,B){var N=A,U=getTarget(),H=getDevtoolsGlobalHook(),V=W&&N.enableEarlyProxy;if(H&&(U.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__||!V))H.emit(j,A,B);else{var K=V?new er(N,H):null;(U.__VUE_DEVTOOLS_PLUGINS__=U.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:N,setupFn:B,proxy:K}),K&&B(K.proxiedTarget)}}},53861:function(A,B,N){"use strict";N.d(B,{Iyq:function(){return useMutationObserver},ORN:function(){return useEventListener},PrY:function(){return useScrollLock},VPI:function(){return useClipboard},_aR:function(){return useLocalStorage},h4X:function(){return useElementSize},i9H:function(){return onClickOutside},iPe:function(){return useWindowSize},iiK:function(){return useMouse},jYR:function(){return useMouseInElement},vO3:function(){return useScroll},xKQ:function(){return useDocumentVisibility},y$C:function(){return useStorage},yU7:function(){return useResizeObserver}});var U=N(41622),H=N(70879),W=N(64593),j=N(44501),V=N(75649),K=N(31547),X=N(377);N(36277),N(34333),N(86651),N(97542),N(41593),N(29744),N(34885),N(29273),N(6045),N(10364),N(67673),N(87535),N(75204),N(97357),N(63530),N(17114),N(25001),N(43645),N(64091),N(51109),N(1154),N(80013),N(42876),N(33933),N(87394),N(94941),N(48421),N(74934),N(61047),N(82427),N(36062),N(57057),N(27461),N(23339),N(7608),N(36163),N(74093),N(20768),N(41648),N(59339),N(47444),N(34757),N(85908),N(39995),N(22943),N(54767),N(55820),N(93225),N(33708),N(64322),N(47771),N(63235),N(15368),N(12970),N(8503),N(32207),N(13598),N(23541),N(63827),N(30488),N(82236),N(51938),N(33913),N(94949),N(17891),N(44258),N(38609),N(4787),N(44211),N(29338),N(73405),N(98754),N(31832),N(88402),N(56712),N(76267),N(83257),N(90834),N(907),N(18552),N(13768),N(45697),N(91004),N(18638),N(96336),N(87168),N(14190),N(4137),N(70805),N(5317),N(81167),N(22583),N(89655),N(88598),N(11530),N(16765),N(3398),N(90621),N(35904),N(73982),N(87683),N(59735),N(69167),N(27151),N(95341),N(53395),N(10074),N(31899),N(98398),N(94837),N(53077),N(14340),N(31578),N(46521),N(49932),N(78246),N(62444),N(34076),N(12334),N(68802),N(87989),N(75973),N(58051),N(27798),N(39465),N(91280),N(74719),N(13396),N(91313),N(55947),N(19077),N(98976),N(16755),N(9557),N(96118),N(99808),N(58486),N(23329),N(85203),N(92519),N(25069),N(57745),N(63712),N(69032),N(93354),N(64961),N(12996),N(59564),N(23390),N(69038),N(80156),N(50721),N(29112),N(67275),N(59989),N(7099),N(25037),N(89300),N(49930),N(19990),N(28636),N(95477),N(43648),N(72169),N(67930);var J=N(84409),ee=N(72811);function unrefElement(A){var B,N=(0,J.Tn)(A);return null!=(B=null==N?void 0:N.$el)?B:N}var et=J.C5?window:void 0,er=J.C5?window.document:void 0,en=J.C5?window.navigator:void 0;function useEventListener(){for(var A,B,N,U,W,K,X=arguments.length,er=Array(X),en=0;en<X;en++)er[en]=arguments[en];if("string"==typeof er[0]||Array.isArray(er[0])?(B=(W=(0,j._)(er,3))[0],N=W[1],U=W[2],A=et):(A=(K=(0,j._)(er,4))[0],B=K[1],N=K[2],U=K[3]),!A)return J.ZT;!Array.isArray(B)&&(B=[B]),!Array.isArray(N)&&(N=[N]);var ei=[],cleanup=function(){ei.forEach(function(A){return A()}),ei.length=0},register=function(A,B,N,U){return A.addEventListener(B,N,U),function(){return A.removeEventListener(B,N,U)}},eo=(0,ee.YP)(function(){return[unrefElement(A),(0,J.Tn)(U)]},function(A){var U,W=(0,j._)(A,2),K=W[0],X=W[1];if(cleanup(),K){var ee=(0,J.Kn)(X)?(0,H._)({},X):X;(U=ei).push.apply(U,(0,V._)(B.flatMap(function(A){return N.map(function(B){return register(K,A,B,ee)})})))}},{immediate:!0,flush:"post"}),stop=function(){eo(),cleanup()};return(0,J.IY)(stop),stop}J.C5&&window.location;var ei=!1;function onClickOutside(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.window,H=void 0===U?et:U,W=N.ignore,j=void 0===W?[]:W,V=N.capture,K=void 0===V||V,X=N.detectIframe,ee=void 0!==X&&X;if(!H)return J.ZT;J.gn&&!ei&&(ei=!0,Array.from(H.document.body.children).forEach(function(A){return A.addEventListener("click",J.ZT)}),H.document.documentElement.addEventListener("click",J.ZT));var er=!0,shouldIgnore=function(A){return j.some(function(B){if("string"==typeof B)return Array.from(H.document.querySelectorAll(B)).some(function(B){return B===A.target||A.composedPath().includes(B)});var N=unrefElement(B);return N&&(A.target===N||A.composedPath().includes(N))})},en=[useEventListener(H,"click",function(N){var U=unrefElement(A);if(!(!U||U===N.target||N.composedPath().includes(U))){if(0===N.detail&&(er=!shouldIgnore(N)),!er){er=!0;return}B(N)}},{passive:!0,capture:K}),useEventListener(H,"pointerdown",function(B){var N=unrefElement(A);er=!shouldIgnore(B)&&!!(N&&!B.composedPath().includes(N))},{passive:!0}),ee&&useEventListener(H,"blur",function(N){setTimeout(function(){var U,W=unrefElement(A);(null==(U=H.document.activeElement)?void 0:U.tagName)==="IFRAME"&&!(null==W?void 0:W.contains(H.document.activeElement))&&B(N)},0)})].filter(Boolean);return function(){return en.forEach(function(A){return A()})}}function useMounted(){var A=(0,ee.iH)(!1),B=(0,ee.FN)();return B&&(0,ee.bv)(function(){A.value=!0},ee.$Q?null:B),A}function useSupported(A){var B=useMounted();return(0,ee.Fl)(function(){return B.value,!!A()})}function useMediaQuery(A){var B,N=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},U=N.window,H=void 0===U?et:U,W=useSupported(function(){return H&&"matchMedia"in H&&"function"==typeof H.matchMedia}),j=(0,ee.iH)(!1),handler=function(A){j.value=A.matches},cleanup=function(){B&&("removeEventListener"in B?B.removeEventListener("change",handler):B.removeListener(handler))},V=(0,ee.m0)(function(){W.value&&(cleanup(),B=H.matchMedia((0,J.Tn)(A)),"addEventListener"in B?B.addEventListener("change",handler):B.addListener(handler),j.value=B.matches)});return(0,J.IY)(function(){V(),cleanup(),B=void 0}),j}function usePermission(A){var B,N=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},H=N.controls,W=void 0!==H&&H,j=N.navigator,V=void 0===j?en:j,K=useSupported(function(){return V&&"permissions"in V}),et="string"==typeof A?{name:A}:A,er=(0,ee.iH)(),onChange=function(){B&&(er.value=B.state)},ei=(0,J._m)((0,U._)(function(){var A;return(0,X.Jh)(this,function(N){switch(N.label){case 0:if(!K.value)return[2];if(B)return[3,4];N.label=1;case 1:return N.trys.push([1,3,,4]),[4,V.permissions.query(et)];case 2:return useEventListener(B=N.sent(),"change",onChange),onChange(),[3,4];case 3:return A=N.sent(),er.value="prompt",[3,4];case 4:return[2,B]}})}));return(ei(),W)?{state:er,isSupported:K,query:ei}:er}function useClipboard(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=A.navigator,N=void 0===B?en:B,H=A.read,W=void 0!==H&&H,j=A.source,V=A.copiedDuring,K=void 0===V?1500:V,et=A.legacy,er=void 0!==et&&et,ei=useSupported(function(){return N&&"clipboard"in N}),eo=usePermission("clipboard-read"),ea=usePermission("clipboard-write"),es=(0,ee.Fl)(function(){return ei.value||er}),eu=(0,ee.iH)(""),el=(0,ee.iH)(!1),ec=(0,J.eM)(function(){return el.value=!1},K);function updateText(){ei.value&&isAllowed(eo.value)?N.clipboard.readText().then(function(A){eu.value=A}):eu.value=legacyRead()}function _copy(){return(_copy=(0,U._)(function(){var A,B=arguments;return(0,X.Jh)(this,function(U){switch(U.label){case 0:if(A=B.length>0&&void 0!==B[0]?B[0]:(0,J.Tn)(j),!(es.value&&null!=A))return[3,4];if(!(ei.value&&isAllowed(ea.value)))return[3,2];return[4,N.clipboard.writeText(A)];case 1:return U.sent(),[3,3];case 2:legacyCopy(A),U.label=3;case 3:eu.value=A,el.value=!0,ec.start(),U.label=4;case 4:return[2]}})})).apply(this,arguments)}function legacyCopy(A){var B=document.createElement("textarea");B.value=null!=A?A:"",B.style.position="absolute",B.style.opacity="0",document.body.appendChild(B),B.select(),document.execCommand("copy"),B.remove()}function legacyRead(){var A,B,N;return null!=(N=null==(B=null==(A=null==document?void 0:document.getSelection)?void 0:A.call(document))?void 0:B.toString())?N:""}function isAllowed(A){return"granted"===A||"prompt"===A}return es.value&&W&&useEventListener(["copy","cut"],updateText),{isSupported:es,text:eu,copied:el,copy:function copy(){return _copy.apply(this,arguments)}}}var eo="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==N.g?N.g:"undefined"!=typeof self?self:{},ea="__vueuse_ssr_handlers__",es=getHandlers();function getHandlers(){return!(ea in eo)&&(eo[ea]=eo[ea]||{}),eo[ea]}function getSSRHandler(A,B){return es[A]||B}function guessSerializerType(A){return null==A?"any":A instanceof Set?"set":A instanceof Map?"map":A instanceof Date?"date":"boolean"==typeof A?"boolean":"string"==typeof A?"string":(void 0===A?"undefined":(0,K._)(A))==="object"?"object":Number.isNaN(A)?"any":"number"}var eu={boolean:{read:function(A){return"true"===A},write:function(A){return String(A)}},object:{read:function(A){return JSON.parse(A)},write:function(A){return JSON.stringify(A)}},number:{read:function(A){return Number.parseFloat(A)},write:function(A){return String(A)}},any:{read:function(A){return A},write:function(A){return String(A)}},string:{read:function(A){return A},write:function(A){return String(A)}},map:{read:function(A){return new Map(JSON.parse(A))},write:function(A){return JSON.stringify(Array.from(A.entries()))}},set:{read:function(A){return new Set(JSON.parse(A))},write:function(A){return JSON.stringify(Array.from(A))}},date:{read:function(A){return new Date(A)},write:function(A){return A.toISOString()}}},el="vueuse-storage";function useStorage(A,B,N){var U,W=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},j=W.flush,V=void 0===j?"pre":j,K=W.deep,X=void 0===K||K,er=W.listenToStorageChanges,en=void 0===er||er,ei=W.writeDefaults,eo=void 0===ei||ei,ea=W.mergeDefaults,es=void 0!==ea&&ea,ec=W.shallow,ef=W.window,ed=void 0===ef?et:ef,ep=W.eventFilter,eh=W.onError,eg=void 0===eh?function(A){console.error(A)}:eh,ey=W.initOnMounted,em=(ec?ee.XI:ee.iH)("function"==typeof B?B():B);if(!N)try{N=getSSRHandler("getDefaultStorage",function(){var A;return null==(A=et)?void 0:A.localStorage})()}catch(A){eg(A)}if(!N)return em;var e_=(0,J.Tn)(B),ev=guessSerializerType(e_),eS=null!=(U=W.serializer)?U:eu[ev],eb=(0,J._I)(em,function(){return write(em.value)},{flush:V,deep:X,eventFilter:ep}),eE=eb.pause,eT=eb.resume;function dispatchWriteEvent(B,U){ed&&ed.dispatchEvent(new CustomEvent(el,{detail:{key:A,oldValue:B,newValue:U,storageArea:N}}))}function write(B){try{var U=N.getItem(A);if(null==B)dispatchWriteEvent(U,null),N.removeItem(A);else{var H=eS.write(B);U!==H&&(N.setItem(A,H),dispatchWriteEvent(U,H))}}catch(A){eg(A)}}function read(B){var U=B?B.newValue:N.getItem(A);if(null==U)return eo&&null!=e_&&N.setItem(A,eS.write(e_)),e_;if(!B&&es){var W=eS.read(U);return"function"==typeof es?es(W,e_):"object"!==ev||Array.isArray(W)?W:(0,H._)({},e_,W)}if("string"!=typeof U)return U;else return eS.read(U)}function update(B){if(!B||B.storageArea===N){if(B&&null==B.key){em.value=e_;return}if(!B||B.key===A){eE();try{(null==B?void 0:B.newValue)!==eS.write(em.value)&&(em.value=read(B))}catch(A){eg(A)}finally{B?(0,ee.Y3)(eT):eT()}}}}function updateFromCustomEvent(A){update(A.detail)}return ed&&en&&(0,J.u7)(function(){useEventListener(ed,"storage",update),useEventListener(ed,el,updateFromCustomEvent),ey&&update()}),!ey&&update(),em}function useMutationObserver(A,B){var N,U=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},H=U.window,j=void 0===H?et:H,V=(0,W._)(U,["window"]),K=useSupported(function(){return j&&"MutationObserver"in j}),cleanup=function(){N&&(N.disconnect(),N=void 0)},X=(0,ee.Fl)(function(){var B=(0,J.Tn)(A);return new Set((Array.isArray(B)?B:[B]).map(unrefElement).filter(J.nf))}),er=(0,ee.YP)(function(){return X.value},function(A){cleanup(),K.value&&j&&A.size&&(N=new MutationObserver(B),A.forEach(function(A){return N.observe(A,V)}))},{immediate:!0,flush:"post"}),takeRecords=function(){return null==N?void 0:N.takeRecords()},stop=function(){cleanup(),er()};return(0,J.IY)(stop),{isSupported:K,stop:stop,takeRecords:takeRecords}}function useDocumentVisibility(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=A.document,N=void 0===B?er:B;if(!N)return(0,ee.iH)("visible");var U=(0,ee.iH)(N.visibilityState);return useEventListener(N,"visibilitychange",function(){U.value=N.visibilityState}),U}function useResizeObserver(A,B){var N,U=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},H=U.window,j=void 0===H?et:H,V=(0,W._)(U,["window"]),K=useSupported(function(){return j&&"ResizeObserver"in j}),cleanup=function(){N&&(N.disconnect(),N=void 0)},X=(0,ee.Fl)(function(){return Array.isArray(A)?A.map(function(A){return unrefElement(A)}):[unrefElement(A)]}),er=(0,ee.YP)(X,function(A){if(cleanup(),K.value&&j){N=new ResizeObserver(B);var U=!0,H=!1,W=void 0;try{for(var X,J=A[Symbol.iterator]();!(U=(X=J.next()).done);U=!0){var ee=X.value;ee&&N.observe(ee,V)}}catch(A){H=!0,W=A}finally{try{!U&&null!=J.return&&J.return()}finally{if(H)throw W}}}},{immediate:!0,flush:"post"}),stop=function(){cleanup(),er()};return(0,J.IY)(stop),{isSupported:K,stop:stop}}function useElementSize(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{width:0,height:0},N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.window,H=void 0===U?et:U,W=N.box,V=void 0===W?"content-box":W,K=(0,ee.Fl)(function(){var B,N;return null==(N=null==(B=unrefElement(A))?void 0:B.namespaceURI)?void 0:N.includes("svg")}),X=(0,ee.iH)(B.width),er=(0,ee.iH)(B.height),en=useResizeObserver(A,function(B){var N=(0,j._)(B,1)[0],U="border-box"===V?N.borderBoxSize:"content-box"===V?N.contentBoxSize:N.devicePixelContentBoxSize;if(H&&K.value){var W=unrefElement(A);if(W){var J=H.getComputedStyle(W);X.value=Number.parseFloat(J.width),er.value=Number.parseFloat(J.height)}}else if(U){var ee=Array.isArray(U)?U:[U];X.value=ee.reduce(function(A,B){return A+B.inlineSize},0),er.value=ee.reduce(function(A,B){return A+B.blockSize},0)}else X.value=N.contentRect.width,er.value=N.contentRect.height},N).stop;(0,J.u7)(function(){var N=unrefElement(A);N&&(X.value="offsetWidth"in N?N.offsetWidth:B.width,er.value="offsetHeight"in N?N.offsetHeight:B.height)});var ei=(0,ee.YP)(function(){return unrefElement(A)},function(A){X.value=A?B.width:0,er.value=A?B.height:0});return{width:X,height:er,stop:function stop(){en(),ei()}}}var ec=1;function useScroll(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N=B.throttle,U=void 0===N?0:N,H=B.idle,W=void 0===H?200:H,j=B.onStop,V=void 0===j?J.ZT:j,K=B.onScroll,X=void 0===K?J.ZT:K,er=B.offset,en=void 0===er?{left:0,right:0,top:0,bottom:0}:er,ei=B.eventListenerOptions,eo=void 0===ei?{capture:!1,passive:!0}:ei,ea=B.behavior,es=void 0===ea?"auto":ea,eu=B.window,el=void 0===eu?et:eu,ef=B.onError,ed=void 0===ef?function(A){console.error(A)}:ef,ep=(0,ee.iH)(0),eh=(0,ee.iH)(0),eg=(0,ee.Fl)({get:function(){return ep.value},set:function(A){scrollTo1(A,void 0)}}),ey=(0,ee.Fl)({get:function(){return eh.value},set:function(A){scrollTo1(void 0,A)}});function scrollTo1(B,N){if(!!el){var U,H,W,j=(0,J.Tn)(A);j&&(null==(W=j instanceof Document?el.document.body:j)||W.scrollTo({top:null!=(U=(0,J.Tn)(N))?U:ey.value,left:null!=(H=(0,J.Tn)(B))?H:eg.value,behavior:(0,J.Tn)(es)}))}}var em=(0,ee.iH)(!1),e_=(0,ee.qj)({left:!0,right:!1,top:!0,bottom:!1}),ev=(0,ee.qj)({left:!1,right:!1,top:!1,bottom:!1}),onScrollEnd=function(A){em.value&&(em.value=!1,ev.left=!1,ev.right=!1,ev.top=!1,ev.bottom=!1,V(A))},eS=(0,J.DI)(onScrollEnd,U+W),setArrivedState=function(A){if(el){var B,N=(null==(B=null==A?void 0:A.document)?void 0:B.documentElement)||(null==A?void 0:A.documentElement)||unrefElement(A),U=getComputedStyle(N),H=U.display,W=U.flexDirection,j=N.scrollLeft;ev.left=j<ep.value,ev.right=j>ep.value;var V=Math.abs(j)<=(en.left||0),K=Math.abs(j)+N.clientWidth>=N.scrollWidth-(en.right||0)-ec;"flex"===H&&"row-reverse"===W?(e_.left=K,e_.right=V):(e_.left=V,e_.right=K),ep.value=j;var X=N.scrollTop;A===el.document&&!X&&(X=el.document.body.scrollTop),ev.top=X<eh.value,ev.bottom=X>eh.value;var J=Math.abs(X)<=(en.top||0),ee=Math.abs(X)+N.clientHeight>=N.scrollHeight-(en.bottom||0)-ec;"flex"===H&&"column-reverse"===W?(e_.top=ee,e_.bottom=J):(e_.top=J,e_.bottom=ee),eh.value=X}},onScrollHandler=function(A){if(el){var B,N=null!=(B=A.target.documentElement)?B:A.target;setArrivedState(N),em.value=!0,eS(A),X(A)}};return useEventListener(A,"scroll",U?(0,J.vA)(onScrollHandler,U,!0,!1):onScrollHandler,eo),(0,J.u7)(function(){try{var B=(0,J.Tn)(A);if(!B)return;setArrivedState(B)}catch(A){ed(A)}}),useEventListener(A,"scrollend",onScrollEnd,eo),{x:eg,y:ey,isScrolling:em,arrivedState:e_,directions:ev,measure:function(){var B=(0,J.Tn)(A);el&&B&&setArrivedState(B)}}}function resolveElement(A){return"undefined"!=typeof Window&&A instanceof Window?A.document.documentElement:"undefined"!=typeof Document&&A instanceof Document?A.documentElement:A}function useLocalStorage(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.window,H=void 0===U?et:U;return useStorage(A,B,null==H?void 0:H.localStorage,N)}var ef={page:function(A){return[A.pageX,A.pageY]},client:function(A){return[A.clientX,A.clientY]},screen:function(A){return[A.screenX,A.screenY]},movement:function(A){return A instanceof Touch?null:[A.movementX,A.movementY]}};function useMouse(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=A.type,N=void 0===B?"page":B,U=A.touch,H=void 0===U||U,W=A.resetOnTouchEnds,V=void 0!==W&&W,K=A.initialValue,X=void 0===K?{x:0,y:0}:K,J=A.window,er=void 0===J?et:J,en=A.target,ei=void 0===en?er:en,eo=A.scroll,ea=void 0===eo||eo,es=A.eventFilter,eu=null,el=(0,ee.iH)(X.x),ec=(0,ee.iH)(X.y),ed=(0,ee.iH)(null),ep="function"==typeof N?N:ef[N],mouseHandler=function(A){var B,N=ep(A);eu=A,N&&(B=(0,j._)(N,2),el.value=B[0],ec.value=B[1],ed.value="mouse")},touchHandler=function(A){if(A.touches.length>0){var B,N=ep(A.touches[0]);N&&(B=(0,j._)(N,2),el.value=B[0],ec.value=B[1],ed.value="touch")}},scrollHandler=function(){if(eu&&er){var A=ep(eu);eu instanceof MouseEvent&&A&&(el.value=A[0]+er.scrollX,ec.value=A[1]+er.scrollY)}},reset=function(){el.value=X.x,ec.value=X.y},eh=es?function(A){return es(function(){return mouseHandler(A)},{})}:function(A){return mouseHandler(A)},eg=es?function(A){return es(function(){return touchHandler(A)},{})}:function(A){return touchHandler(A)},ey=es?function(){return es(function(){return scrollHandler()},{})}:function(){return scrollHandler()};if(ei){var em={passive:!0};useEventListener(ei,["mousemove","dragover"],eh,em),H&&"movement"!==N&&(useEventListener(ei,["touchstart","touchmove"],eg,em),V&&useEventListener(ei,"touchend",reset,em)),ea&&"page"===N&&useEventListener(er,"scroll",ey,{passive:!0})}return{x:el,y:ec,sourceType:ed}}function useMouseInElement(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N=B.handleOutside,U=void 0===N||N,H=B.window,W=void 0===H?et:H,j=B.type||"page",V=useMouse(B),K=V.x,X=V.y,J=V.sourceType,er=(0,ee.iH)(null!=A?A:null==W?void 0:W.document.body),en=(0,ee.iH)(0),ei=(0,ee.iH)(0),eo=(0,ee.iH)(0),ea=(0,ee.iH)(0),es=(0,ee.iH)(0),eu=(0,ee.iH)(0),el=(0,ee.iH)(!0),stop=function(){};return W&&(stop=(0,ee.YP)([er,K,X],function(){var A=unrefElement(er);if(A){var B=A.getBoundingClientRect(),N=B.left,H=B.top,V=B.width,J=B.height;eo.value=N+("page"===j?W.pageXOffset:0),ea.value=H+("page"===j?W.pageYOffset:0),es.value=J,eu.value=V;var ee=K.value-eo.value,et=X.value-ea.value;el.value=0===V||0===J||ee<0||et<0||ee>V||et>J,(U||!el.value)&&(en.value=ee,ei.value=et)}},{immediate:!0}),useEventListener(document,"mouseleave",function(){el.value=!0})),{x:K,y:X,sourceType:J,elementX:en,elementY:ei,elementPositionX:eo,elementPositionY:ea,elementHeight:es,elementWidth:eu,isOutside:el,stop:stop}}function checkOverflowScroll(A){var B=window.getComputedStyle(A);if("scroll"===B.overflowX||"scroll"===B.overflowY||"auto"===B.overflowX&&A.clientWidth<A.scrollWidth||"auto"===B.overflowY&&A.clientHeight<A.scrollHeight)return!0;var N=A.parentNode;return!!N&&"BODY"!==N.tagName&&checkOverflowScroll(N)}function preventDefault(A){var B=A||window.event;return!checkOverflowScroll(B.target)&&(!!(B.touches.length>1)||(B.preventDefault&&B.preventDefault(),!1))}var ed=new WeakMap;function useScrollLock(A){var B=arguments.length>1&&void 0!==arguments[1]&&arguments[1],N=(0,ee.iH)(B),U=null;(0,ee.YP)((0,J.Vh)(A),function(A){var B=resolveElement((0,J.Tn)(A));if(B){var U=B;!ed.get(U)&&ed.set(U,U.style.overflow),N.value&&(U.style.overflow="hidden")}},{immediate:!0});var lock=function(){var B=resolveElement((0,J.Tn)(A));B&&!N.value&&(J.gn&&(U=useEventListener(B,"touchmove",function(A){preventDefault(A)},{passive:!1})),B.style.overflow="hidden",N.value=!0)},unlock=function(){var B,H=resolveElement((0,J.Tn)(A));H&&N.value&&(J.gn&&(null==U||U()),H.style.overflow=null!=(B=ed.get(H))?B:"",ed.delete(H),N.value=!1)};return(0,J.IY)(unlock),(0,ee.Fl)({get:function(){return N.value},set:function(A){A?lock():unlock()}})}function useWindowSize(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=A.window,N=void 0===B?et:B,U=A.initialWidth,H=void 0===U?Number.POSITIVE_INFINITY:U,W=A.initialHeight,j=void 0===W?Number.POSITIVE_INFINITY:W,V=A.listenOrientation,K=void 0===V||V,X=A.includeScrollbar,er=void 0===X||X,en=(0,ee.iH)(H),ei=(0,ee.iH)(j),update=function(){N&&(er?(en.value=N.innerWidth,ei.value=N.innerHeight):(en.value=N.document.documentElement.clientWidth,ei.value=N.document.documentElement.clientHeight))};if(update(),(0,J.u7)(update),useEventListener("resize",update,{passive:!0}),K){var eo=useMediaQuery("(orientation: portrait)");(0,ee.YP)(eo,function(){return update()})}return{width:en,height:ei}}},84409:function(A,B,N){"use strict";N.d(B,{$M:function(){return whenever},BE:function(){return createInjectionState},B_:function(){return watchDebounced},C5:function(){return er},DI:function(){return useDebounceFn},Dt:function(){return createSharedComposable},IY:function(){return tryOnScopeDispose},KS:function(){return useTimeout},Kn:function(){return isObject},OT:function(){return useToggle},Tn:function(){return toValue},Vh:function(){return toRef},Wg:function(){return createEventHook},ZT:function(){return noop},_I:function(){return watchPausable},_m:function(){return createSingletonPromise},eM:function(){return useTimeoutFn},f8:function(){return watchOnce},gn:function(){return ei},nf:function(){return notNullish},u7:function(){return tryOnMounted},vA:function(){return useThrottleFn}});var U=N(41622),H=N(70879),W=N(27337),j=N(64593),V=N(44501),K=N(75649),X=N(31547),J=N(377);N(7628),N(63235),N(15368),N(12970),N(8503),N(32207),N(13598),N(23541),N(63827),N(30488),N(82236),N(51938),N(33913),N(94949),N(17891),N(44258),N(38609),N(4787),N(44211),N(29338),N(73405),N(98754),N(31832),N(88402),N(56712),N(27461),N(23339),N(51109),N(34333),N(36277),N(64091),N(6045),N(10364),N(67673),N(89300),N(49930),N(19990),N(41593),N(74719),N(13396),N(97542),N(25037),N(91313),N(58051),N(86651),N(75973),N(47051),N(36163),N(23390),N(69038),N(29650),N(49690),N(97210),N(94284),N(87394),N(94941),N(48421),N(97357),N(42876),N(33933),N(98976),N(55947),N(25069),N(58486),N(72169),N(1154),N(80013),N(95477),N(43648),N(9557),N(96118),N(82427),N(36062),N(57057),N(87535),N(75204),N(57745),N(29744),N(97553),N(69032),N(93354),N(64961),N(12996),N(59564),N(70860),N(99808),N(87989),N(57015);var ee=N(72811);function tryOnScopeDispose(A){return!!(0,ee.nZ)()&&((0,ee.EB)(A),!0)}function createEventHook(){var A=new Set,off=function(B){A.delete(B)};return{on:function(B){A.add(B);var offFn=function(){return off(B)};return tryOnScopeDispose(offFn),{off:offFn}},off:off,trigger:function(){for(var B=arguments.length,N=Array(B),U=0;U<B;U++)N[U]=arguments[U];return Promise.all(Array.from(A).map(function(A){return A.apply(void 0,(0,K._)(N))}))}}}var et=new WeakMap,provideLocal=function(A,B){var N,U=null==(N=(0,ee.FN)())?void 0:N.proxy;if(null==U)throw Error("provideLocal must be called in setup");!et.has(U)&&et.set(U,Object.create(null)),et.get(U)[A]=B,(0,ee.JJ)(A,B)},injectLocal=function(){for(var A,B=arguments.length,N=Array(B),U=0;U<B;U++)N[U]=arguments[U];var H=N[0],W=null==(A=(0,ee.FN)())?void 0:A.proxy;if(null==W)throw Error("injectLocal must be called in setup");return et.has(W)&&H in et.get(W)?et.get(W)[H]:ee.f3.apply(void 0,(0,K._)(N))};function createInjectionState(A,B){var N=(null==B?void 0:B.injectionKey)||Symbol(A.name||"InjectionState");return[function(){for(var B=arguments.length,U=Array(B),H=0;H<B;H++)U[H]=arguments[H];var W=A.apply(void 0,(0,K._)(U));return provideLocal(N,W),W},function(){return injectLocal(N)}]}function createSharedComposable(A){var B,N,U=0,dispose=function(){U-=1,N&&U<=0&&(N.stop(),B=void 0,N=void 0)};return function(){for(var H=arguments.length,W=Array(H),j=0;j<H;j++)W[j]=arguments[j];return U+=1,!B&&(B=(N=(0,ee.B)(!0)).run(function(){return A.apply(void 0,(0,K._)(W))})),tryOnScopeDispose(dispose),B}}function toValue(A){return"function"==typeof A?A():(0,ee.SU)(A)}var er="undefined"!=typeof window,notNullish=function(A){return null!=A},en=Object.prototype.toString,isObject=function(A){return"[object Object]"===en.call(A)},noop=function(){},ei=getIsIOS();function getIsIOS(){var A,B;return er&&(null==(A=null==window?void 0:window.navigator)?void 0:A.userAgent)&&(/iP(ad|hone|od)/.test(window.navigator.userAgent)||(null==(B=null==window?void 0:window.navigator)?void 0:B.maxTouchPoints)>2&&/iPad|Macintosh/.test(null==window?void 0:window.navigator.userAgent))}function createFilterWrapper(A,B){return function wrapper(){for(var N=this,U=arguments.length,H=Array(U),W=0;W<U;W++)H[W]=arguments[W];return new Promise(function(U,W){Promise.resolve(A(function(){return B.apply(N,H)},{fn:B,thisArg:N,args:H})).then(U).catch(W)})}}var bypassFilter=function(A){return A()};function debounceFilter(A){var B,N,U=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},H=noop,_clearTimeout=function(A){clearTimeout(A),H(),H=noop};return function(W){var j=toValue(A),V=toValue(U.maxWait);return(B&&_clearTimeout(B),j<=0||void 0!==V&&V<=0)?(N&&(_clearTimeout(N),N=null),Promise.resolve(W())):new Promise(function(A,K){H=U.rejectOnCancel?K:A,V&&!N&&(N=setTimeout(function(){B&&_clearTimeout(B),N=null,A(W())},V)),B=setTimeout(function(){N&&_clearTimeout(N),N=null,A(W())},j)})}}function throttleFilter(){for(var A,B,N,U,H,W,j,K,J,et,er,en,ei,eo,ea=arguments.length,es=Array(ea),eu=0;eu<ea;eu++)es[eu]=arguments[eu];var el=0,ec=!0,ef=noop;(0,ee.dq)(es[0])||"object"!==(0,X._)(es[0])?(N=(er=(0,V._)(es,4))[0],U=void 0===(en=er[1])||en,H=void 0===(ei=er[2])||ei,W=void 0!==(eo=er[3])&&eo):(N=(j=es[0]).delay,U=void 0===(K=j.trailing)||K,H=void 0===(J=j.leading)||J,W=void 0!==(et=j.rejectOnCancel)&&et);var clear=function(){A&&(clearTimeout(A),A=void 0,ef(),ef=noop)};return function(j){var V=toValue(N),K=Date.now()-el,invoke=function(){return B=j()};return(clear(),V<=0)?(el=Date.now(),invoke()):(K>V&&(H||!ec)?(el=Date.now(),invoke()):U&&(B=new Promise(function(B,N){ef=W?N:B,A=setTimeout(function(){el=Date.now(),ec=!0,B(invoke()),clear()},Math.max(0,V-K))})),!H&&!A&&(A=setTimeout(function(){return ec=!0},V)),ec=!1,B)}}function pausableFilter(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bypassFilter,B=(0,ee.iH)(!0);function pause(){B.value=!1}function resume(){B.value=!0}var eventFilter=function(){for(var N=arguments.length,U=Array(N),H=0;H<N;H++)U[H]=arguments[H];B.value&&A.apply(void 0,(0,K._)(U))};return{isActive:(0,ee.OT)(B),pause:pause,resume:resume,eventFilter:eventFilter}}function cacheStringFunction(A){var B=Object.create(null);return function(N){return B[N]||(B[N]=A(N))}}var eo=/\B([A-Z])/g;cacheStringFunction(function(A){return A.replace(eo,"-$1").toLowerCase()});var ea=/-(\w)/g;function createSingletonPromise(A){var B;function wrapper(){return!B&&(B=A()),B}return wrapper.reset=(0,U._)(function(){var A;return(0,J.Jh)(this,function(N){switch(N.label){case 0:if(A=B,B=void 0,!A)return[3,2];return[4,A];case 1:N.sent(),N.label=2;case 2:return[2]}})}),wrapper}function getLifeCycleTarget(A){return A||(0,ee.FN)()}function toRef(){for(var A=arguments.length,B=Array(A),N=0;N<A;N++)B[N]=arguments[N];if(1!==B.length)return ee.Vh.apply(void 0,(0,K._)(B));var U=B[0];return"function"==typeof U?(0,ee.OT)((0,ee.ZM)(function(){return{get:U,set:noop}})):(0,ee.iH)(U)}function useDebounceFn(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return createFilterWrapper(debounceFilter(B,N),A)}function useThrottleFn(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:200,N=arguments.length>2&&void 0!==arguments[2]&&arguments[2],U=!(arguments.length>3)||void 0===arguments[3]||arguments[3],H=arguments.length>4&&void 0!==arguments[4]&&arguments[4];return createFilterWrapper(throttleFilter(B,N,U,H),A)}function watchWithFilter(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.eventFilter,H=void 0===U?bypassFilter:U,W=(0,j._)(N,["eventFilter"]);return(0,ee.YP)(A,createFilterWrapper(H,B),W)}function watchPausable(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.eventFilter,V=(0,j._)(N,["eventFilter"]),K=pausableFilter(U),X=K.eventFilter,J=K.pause,ee=K.resume,et=K.isActive;return{stop:watchWithFilter(A,B,(0,W._)((0,H._)({},V),{eventFilter:X})),pause:J,resume:ee,isActive:et}}function tryOnMounted(A){var B=!(arguments.length>1)||void 0===arguments[1]||arguments[1],N=arguments.length>2?arguments[2]:void 0;getLifeCycleTarget()?(0,ee.bv)(A,N):B?A():(0,ee.Y3)(A)}function useTimeoutFn(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.immediate,H=void 0===U||U,W=(0,ee.iH)(!1),j=null;function clear(){j&&(clearTimeout(j),j=null)}function stop(){W.value=!1,clear()}function start(){for(var N=arguments.length,U=Array(N),H=0;H<N;H++)U[H]=arguments[H];clear(),W.value=!0,j=setTimeout(function(){W.value=!1,j=null,A.apply(void 0,(0,K._)(U))},toValue(B))}return H&&(W.value=!0,er&&start()),tryOnScopeDispose(stop),{isPending:(0,ee.OT)(W),start:start,stop:stop}}function useTimeout(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1e3,B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N=B.controls,U=void 0!==N&&N,W=B.callback,j=useTimeoutFn(null!=W?W:noop,A,B),V=(0,ee.Fl)(function(){return!j.isPending.value});return U?(0,H._)({ready:V},j):V}function useToggle(){var A=arguments.length>0&&void 0!==arguments[0]&&arguments[0],B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N=B.truthyValue,U=void 0===N||N,H=B.falsyValue,W=void 0!==H&&H,j=(0,ee.dq)(A),V=(0,ee.iH)(A);function toggle(A){if(arguments.length)return V.value=A,V.value;var B=toValue(U);return V.value=V.value===B?toValue(W):B,V.value}return j?toggle:[V,toggle]}function watchDebounced(A,B){var N=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},U=N.debounce,V=void 0===U?0:U,K=N.maxWait,X=void 0===K?void 0:K,J=(0,j._)(N,["debounce","maxWait"]);return watchWithFilter(A,B,(0,W._)((0,H._)({},J),{eventFilter:debounceFilter(V,{maxWait:X})}))}function watchOnce(A,B,N){var U=(0,ee.YP)(A,function(){for(var A=arguments.length,N=Array(A),H=0;H<A;H++)N[H]=arguments[H];return(0,ee.Y3)(function(){return U()}),B.apply(void 0,(0,K._)(N))},N);return U}function whenever(A,B,N){var U=(0,ee.YP)(A,function(A,H,W){A&&((null==N?void 0:N.once)&&(0,ee.Y3)(function(){return U()}),B(A,H,W))},(0,W._)((0,H._)({},N),{once:!1}));return U}cacheStringFunction(function(A){return A.replace(ea,function(A,B){return B?B.toUpperCase():""})})},7448:function(A,B,N){A=N.nmd(A);var U=N(31547);N(18638),N(96336),N(87168),N(14190),N(4137),N(70805),N(5317),N(81167),N(22583),N(89655),N(88598),N(11530),N(16765),N(3398),N(90621),N(35904),N(73982),N(87683),N(59735),N(69167),N(27151),N(95341),N(53395),N(10074),N(31899),N(98398),N(94837),N(53077),N(14340),N(31578),N(46521),N(49932),N(78246),N(34333),N(27461),N(62444),N(34076),N(12334),N(68802),N(76267),N(83257),N(90834),N(907),N(18552),N(82539),N(41593),N(34885),N(55947);var H=function(A){"use strict";Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var B=null;try{B=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(A){}function Long(A,B,N){this.low=0|A,this.high=0|B,this.unsigned=!!N}function isLong(A){return!0===(A&&A.__isLong__)}function ctz32(A){var B=Math.clz32(A&-A);return A?31-B:B}Long.prototype.__isLong__,Object.defineProperty(Long.prototype,"__isLong__",{value:!0}),Long.isLong=isLong;var N={},U={};function fromInt(A,B){var H,W,j;if(B)return(A>>>=0,(j=0<=A&&A<256)&&(W=U[A]))?W:(H=fromBits(A,0,!0),j&&(U[A]=H),H);return(A|=0,(j=-128<=A&&A<128)&&(W=N[A]))?W:(H=fromBits(A,A<0?-1:0,!1),j&&(N[A]=H),H)}function fromNumber(A,B){if(isNaN(A))return B?ee:J;if(B){if(A<0)return ee;if(A>=V)return eo}else{if(A<=-K)return ea;if(A+1>=K)return ei}return A<0?fromNumber(-A,B).neg():fromBits(A%j|0,A/j|0,B)}function fromBits(A,B,N){return new Long(A,B,N)}Long.fromInt=fromInt,Long.fromNumber=fromNumber,Long.fromBits=fromBits;var H=Math.pow;function fromString(A,B,N){if(0===A.length)throw Error("empty string");if("number"==typeof B?(N=B,B=!1):B=!!B,"NaN"===A||"Infinity"===A||"+Infinity"===A||"-Infinity"===A)return B?ee:J;if((N=N||10)<2||36<N)throw RangeError("radix");if((U=A.indexOf("-"))>0)throw Error("interior hyphen");if(0===U)return fromString(A.substring(1),B,N).neg();for(var U,W=fromNumber(H(N,8)),j=J,V=0;V<A.length;V+=8){var K=Math.min(8,A.length-V),X=parseInt(A.substring(V,V+K),N);if(K<8){var et=fromNumber(H(N,K));j=j.mul(et).add(fromNumber(X))}else j=(j=j.mul(W)).add(fromNumber(X))}return j.unsigned=B,j}function fromValue(A,B){return"number"==typeof A?fromNumber(A,B):"string"==typeof A?fromString(A,B):fromBits(A.low,A.high,"boolean"==typeof B?B:A.unsigned)}Long.fromString=fromString,Long.fromValue=fromValue;var W=65536,j=0x100000000,V=0xffffffffffffffff,K=0x8000000000000000,X=fromInt(0x1000000),J=fromInt(0);Long.ZERO=J;var ee=fromInt(0,!0);Long.UZERO=ee;var et=fromInt(1);Long.ONE=et;var er=fromInt(1,!0);Long.UONE=er;var en=fromInt(-1);Long.NEG_ONE=en;var ei=fromBits(-1,0x7fffffff,!1);Long.MAX_VALUE=ei;var eo=fromBits(-1,-1,!0);Long.MAX_UNSIGNED_VALUE=eo;var ea=fromBits(0,-0x80000000,!1);Long.MIN_VALUE=ea;var es=Long.prototype;es.toInt=function toInt(){return this.unsigned?this.low>>>0:this.low},es.toNumber=function toNumber(){return this.unsigned?(this.high>>>0)*j+(this.low>>>0):this.high*j+(this.low>>>0)},es.toString=function toString(A){if((A=A||10)<2||36<A)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(!this.eq(ea))return"-"+this.neg().toString(A);var B=fromNumber(A),N=this.div(B),U=N.mul(B).sub(this);return N.toString(A)+U.toInt().toString(A)}for(var W=fromNumber(H(A,6),this.unsigned),j=this,V="";;){var K=j.div(W),X=(j.sub(K.mul(W)).toInt()>>>0).toString(A);if((j=K).isZero())return X+V;for(;X.length<6;)X="0"+X;V=""+X+V}},es.getHighBits=function getHighBits(){return this.high},es.getHighBitsUnsigned=function getHighBitsUnsigned(){return this.high>>>0},es.getLowBits=function getLowBits(){return this.low},es.getLowBitsUnsigned=function getLowBitsUnsigned(){return this.low>>>0},es.getNumBitsAbs=function getNumBitsAbs(){if(this.isNegative())return this.eq(ea)?64:this.neg().getNumBitsAbs();for(var A=0!=this.high?this.high:this.low,B=31;B>0&&(A&1<<B)==0;B--);return 0!=this.high?B+33:B+1},es.isZero=function isZero(){return 0===this.high&&0===this.low},es.eqz=es.isZero,es.isNegative=function isNegative(){return!this.unsigned&&this.high<0},es.isPositive=function isPositive(){return this.unsigned||this.high>=0},es.isOdd=function isOdd(){return(1&this.low)==1},es.isEven=function isEven(){return(1&this.low)==0},es.equals=function equals(A){return!isLong(A)&&(A=fromValue(A)),(this.unsigned===A.unsigned||this.high>>>31!=1||A.high>>>31!=1)&&this.high===A.high&&this.low===A.low},es.eq=es.equals,es.notEquals=function notEquals(A){return!this.eq(A)},es.neq=es.notEquals,es.ne=es.notEquals,es.lessThan=function lessThan(A){return 0>this.comp(A)},es.lt=es.lessThan,es.lessThanOrEqual=function lessThanOrEqual(A){return 0>=this.comp(A)},es.lte=es.lessThanOrEqual,es.le=es.lessThanOrEqual,es.greaterThan=function greaterThan(A){return this.comp(A)>0},es.gt=es.greaterThan,es.greaterThanOrEqual=function greaterThanOrEqual(A){return this.comp(A)>=0},es.gte=es.greaterThanOrEqual,es.ge=es.greaterThanOrEqual,es.compare=function compare(A){if(!isLong(A)&&(A=fromValue(A)),this.eq(A))return 0;var B=this.isNegative(),N=A.isNegative();return B&&!N?-1:!B&&N?1:this.unsigned?A.high>>>0>this.high>>>0||A.high===this.high&&A.low>>>0>this.low>>>0?-1:1:this.sub(A).isNegative()?-1:1},es.comp=es.compare,es.negate=function negate(){return!this.unsigned&&this.eq(ea)?ea:this.not().add(et)},es.neg=es.negate,es.add=function add(A){!isLong(A)&&(A=fromValue(A));var B=this.high>>>16,N=65535&this.high,U=this.low>>>16,H=65535&this.low,W=A.high>>>16,j=65535&A.high,V=A.low>>>16,K=65535&A.low,X,J,ee=0,et=0;return X=0+((J=0+(H+K))>>>16),J&=65535,X+=U+V,et+=X>>>16,X&=65535,et+=N+j,ee+=et>>>16,et&=65535,ee+=B+W,fromBits(X<<16|J,(ee&=65535)<<16|et,this.unsigned)},es.subtract=function subtract(A){return!isLong(A)&&(A=fromValue(A)),this.add(A.neg())},es.sub=es.subtract,es.multiply=function multiply(A){if(this.isZero())return this;if(!isLong(A)&&(A=fromValue(A)),B)return fromBits(B.mul(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned);if(A.isZero())return this.unsigned?ee:J;if(this.eq(ea))return A.isOdd()?ea:J;if(A.eq(ea))return this.isOdd()?ea:J;if(this.isNegative())return A.isNegative()?this.neg().mul(A.neg()):this.neg().mul(A).neg();if(A.isNegative())return this.mul(A.neg()).neg();if(this.lt(X)&&A.lt(X))return fromNumber(this.toNumber()*A.toNumber(),this.unsigned);var N=this.high>>>16,U=65535&this.high,H=this.low>>>16,W=65535&this.low,j=A.high>>>16,V=65535&A.high,K=A.low>>>16,et=65535&A.low,er,en,ei=0,eo=0;return er=0+((en=0+W*et)>>>16),en&=65535,er+=H*et,eo+=er>>>16,er&=65535,er+=W*K,eo+=er>>>16,er&=65535,eo+=U*et,ei+=eo>>>16,eo&=65535,eo+=H*K,ei+=eo>>>16,eo&=65535,eo+=W*V,ei+=eo>>>16,eo&=65535,ei+=N*et+U*K+H*V+W*j,fromBits(er<<16|en,(ei&=65535)<<16|eo,this.unsigned)},es.mul=es.multiply,es.divide=function divide(A){if(!isLong(A)&&(A=fromValue(A)),A.isZero())throw Error("division by zero");if(B){var N,U,W;if(!this.unsigned&&-0x80000000===this.high&&-1===A.low&&-1===A.high)return this;return fromBits((this.unsigned?B.div_u:B.div_s)(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?ee:J;if(this.unsigned){if(!A.unsigned&&(A=A.toUnsigned()),A.gt(this))return ee;if(A.gt(this.shru(1)))return er;W=ee}else{if(this.eq(ea))return A.eq(et)||A.eq(en)?ea:A.eq(ea)?et:(N=this.shr(1).div(A).shl(1)).eq(J)?A.isNegative()?et:en:(U=this.sub(A.mul(N)),W=N.add(U.div(A)));if(A.eq(ea))return this.unsigned?ee:J;if(this.isNegative())return A.isNegative()?this.neg().div(A.neg()):this.neg().div(A).neg();if(A.isNegative())return this.div(A.neg()).neg();W=J}for(U=this;U.gte(A);){for(var j=Math.ceil(Math.log(N=Math.max(1,Math.floor(U.toNumber()/A.toNumber())))/Math.LN2),V=j<=48?1:H(2,j-48),K=fromNumber(N),X=K.mul(A);X.isNegative()||X.gt(U);)N-=V,X=(K=fromNumber(N,this.unsigned)).mul(A);K.isZero()&&(K=et),W=W.add(K),U=U.sub(X)}return W},es.div=es.divide,es.modulo=function modulo(A){return(!isLong(A)&&(A=fromValue(A)),B)?fromBits((this.unsigned?B.rem_u:B.rem_s)(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned):this.sub(this.div(A).mul(A))},es.mod=es.modulo,es.rem=es.modulo,es.not=function not(){return fromBits(~this.low,~this.high,this.unsigned)},es.countLeadingZeros=function countLeadingZeros(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},es.clz=es.countLeadingZeros,es.countTrailingZeros=function countTrailingZeros(){return this.low?ctz32(this.low):ctz32(this.high)+32},es.ctz=es.countTrailingZeros,es.and=function and(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low&A.low,this.high&A.high,this.unsigned)},es.or=function or(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low|A.low,this.high|A.high,this.unsigned)},es.xor=function xor(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low^A.low,this.high^A.high,this.unsigned)},es.shiftLeft=function shiftLeft(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low<<A,this.high<<A|this.low>>>32-A,this.unsigned):fromBits(0,this.low<<A-32,this.unsigned)},es.shl=es.shiftLeft,es.shiftRight=function shiftRight(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,this.high>>A,this.unsigned):fromBits(this.high>>A-32,this.high>=0?0:-1,this.unsigned)},es.shr=es.shiftRight,es.shiftRightUnsigned=function shiftRightUnsigned(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,this.high>>>A,this.unsigned):32===A?fromBits(this.high,0,this.unsigned):fromBits(this.high>>>A-32,0,this.unsigned)},es.shru=es.shiftRightUnsigned,es.shr_u=es.shiftRightUnsigned,es.rotateLeft=function rotateLeft(A){var B;return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:32===A?fromBits(this.high,this.low,this.unsigned):A<32?(B=32-A,fromBits(this.low<<A|this.high>>>B,this.high<<A|this.low>>>B,this.unsigned)):(A-=32,B=32-A,fromBits(this.high<<A|this.low>>>B,this.low<<A|this.high>>>B,this.unsigned))},es.rotl=es.rotateLeft,es.rotateRight=function rotateRight(A){var B;return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:32===A?fromBits(this.high,this.low,this.unsigned):A<32?(B=32-A,fromBits(this.high<<B|this.low>>>A,this.low<<B|this.high>>>A,this.unsigned)):(A-=32,B=32-A,fromBits(this.low<<B|this.high>>>A,this.high<<B|this.low>>>A,this.unsigned))},es.rotr=es.rotateRight,es.toSigned=function toSigned(){return this.unsigned?fromBits(this.low,this.high,!1):this},es.toUnsigned=function toUnsigned(){return this.unsigned?this:fromBits(this.low,this.high,!0)},es.toBytes=function toBytes(A){return A?this.toBytesLE():this.toBytesBE()},es.toBytesLE=function toBytesLE(){var A=this.high,B=this.low;return[255&B,B>>>8&255,B>>>16&255,B>>>24,255&A,A>>>8&255,A>>>16&255,A>>>24]},es.toBytesBE=function toBytesBE(){var A=this.high,B=this.low;return[A>>>24,A>>>16&255,A>>>8&255,255&A,B>>>24,B>>>16&255,B>>>8&255,255&B]},Long.fromBytes=function fromBytes(A,B,N){return N?Long.fromBytesLE(A,B):Long.fromBytesBE(A,B)},Long.fromBytesLE=function fromBytesLE(A,B){return new Long(A[0]|A[1]<<8|A[2]<<16|A[3]<<24,A[4]|A[5]<<8|A[6]<<16|A[7]<<24,B)},Long.fromBytesBE=function fromBytesBE(A,B){return new Long(A[4]<<24|A[5]<<16|A[6]<<8|A[7],A[0]<<24|A[1]<<16|A[2]<<8|A[3],B)};var eu=Long;return A.default=eu,"default"in A?A.default:A}({});"function"==typeof define&&define.amd?define([],function(){return H}):"object"===U._(A)&&"object"===U._(B)&&(A.exports=H)},2044:function(A,B,N){A=N.nmd(A);var U=N(31547);N(98976),N(16755),N(18638),N(96336),N(87168),N(14190),N(4137),N(70805),N(5317),N(81167),N(22583),N(89655),N(88598),N(11530),N(16765),N(3398),N(90621),N(35904),N(73982),N(87683),N(59735),N(69167),N(27151),N(95341),N(53395),N(10074),N(31899),N(98398),N(94837),N(53077),N(14340),N(31578),N(46521),N(49932),N(78246),N(34333),N(27461),N(62444),N(34076),N(12334),N(68802),N(76267),N(83257),N(90834),N(907),N(18552),N(82539),N(41593),N(34885),N(55947),N(9557),!function(N,H){function unwrapDefault(A){return"default"in A?A.default:A}"function"==typeof define&&define.amd?define([],function(){var A={};return H(A),unwrapDefault(A)}):"object"===U._(B)?(H(B),"object"===U._(A)&&(A.exports=unwrapDefault(B))):!function(){var A={};H(A),N.Long=unwrapDefault(A)}()}("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:this,function(A){"use strict";Object.defineProperty(A,"__esModule",{value:!0}),A.default=void 0;var B=null;try{B=new WebAssembly.Instance(new WebAssembly.Module(new Uint8Array([0,97,115,109,1,0,0,0,1,13,2,96,0,1,127,96,4,127,127,127,127,1,127,3,7,6,0,1,1,1,1,1,6,6,1,127,1,65,0,11,7,50,6,3,109,117,108,0,1,5,100,105,118,95,115,0,2,5,100,105,118,95,117,0,3,5,114,101,109,95,115,0,4,5,114,101,109,95,117,0,5,8,103,101,116,95,104,105,103,104,0,0,10,191,1,6,4,0,35,0,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,126,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,127,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,128,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,129,34,4,66,32,135,167,36,0,32,4,167,11,36,1,1,126,32,0,173,32,1,173,66,32,134,132,32,2,173,32,3,173,66,32,134,132,130,34,4,66,32,135,167,36,0,32,4,167,11])),{}).exports}catch(A){}function Long(A,B,N){this.low=0|A,this.high=0|B,this.unsigned=!!N}function isLong(A){return!0===(A&&A.__isLong__)}function ctz32(A){var B=Math.clz32(A&-A);return A?31-B:B}Long.prototype.__isLong__,Object.defineProperty(Long.prototype,"__isLong__",{value:!0}),Long.isLong=isLong;var N={},H={};function fromInt(A,B){var U,W,j;if(B)return(A>>>=0,(j=0<=A&&A<256)&&(W=H[A]))?W:(U=fromBits(A,0,!0),j&&(H[A]=U),U);return(A|=0,(j=-128<=A&&A<128)&&(W=N[A]))?W:(U=fromBits(A,A<0?-1:0,!1),j&&(N[A]=U),U)}function fromNumber(A,B){if(isNaN(A))return B?et:ee;if(B){if(A<0)return et;if(A>=K)return ea}else{if(A<=-X)return es;if(A+1>=X)return eo}return A<0?fromNumber(-A,B).neg():fromBits(A%V|0,A/V|0,B)}function fromBits(A,B,N){return new Long(A,B,N)}Long.fromInt=fromInt,Long.fromNumber=fromNumber,Long.fromBits=fromBits;var W=Math.pow;function fromString(A,B,N){if(0===A.length)throw Error("empty string");if("number"==typeof B?(N=B,B=!1):B=!!B,"NaN"===A||"Infinity"===A||"+Infinity"===A||"-Infinity"===A)return B?et:ee;if((N=N||10)<2||36<N)throw RangeError("radix");if((U=A.indexOf("-"))>0)throw Error("interior hyphen");if(0===U)return fromString(A.substring(1),B,N).neg();for(var U,H=fromNumber(W(N,8)),j=ee,V=0;V<A.length;V+=8){var K=Math.min(8,A.length-V),X=parseInt(A.substring(V,V+K),N);if(K<8){var J=fromNumber(W(N,K));j=j.mul(J).add(fromNumber(X))}else j=(j=j.mul(H)).add(fromNumber(X))}return j.unsigned=B,j}function fromValue(A,B){return"number"==typeof A?fromNumber(A,B):"string"==typeof A?fromString(A,B):fromBits(A.low,A.high,"boolean"==typeof B?B:A.unsigned)}Long.fromString=fromString,Long.fromValue=fromValue;var j=65536,V=0x100000000,K=0xffffffffffffffff,X=0x8000000000000000,J=fromInt(0x1000000),ee=fromInt(0);Long.ZERO=ee;var et=fromInt(0,!0);Long.UZERO=et;var er=fromInt(1);Long.ONE=er;var en=fromInt(1,!0);Long.UONE=en;var ei=fromInt(-1);Long.NEG_ONE=ei;var eo=fromBits(-1,0x7fffffff,!1);Long.MAX_VALUE=eo;var ea=fromBits(-1,-1,!0);Long.MAX_UNSIGNED_VALUE=ea;var es=fromBits(0,-0x80000000,!1);Long.MIN_VALUE=es;var eu=Long.prototype;eu.toInt=function toInt(){return this.unsigned?this.low>>>0:this.low},eu.toNumber=function toNumber(){return this.unsigned?(this.high>>>0)*V+(this.low>>>0):this.high*V+(this.low>>>0)},eu.toString=function toString(A){if((A=A||10)<2||36<A)throw RangeError("radix");if(this.isZero())return"0";if(this.isNegative()){if(!this.eq(es))return"-"+this.neg().toString(A);var B=fromNumber(A),N=this.div(B),U=N.mul(B).sub(this);return N.toString(A)+U.toInt().toString(A)}for(var H=fromNumber(W(A,6),this.unsigned),j=this,V="";;){var K=j.div(H),X=(j.sub(K.mul(H)).toInt()>>>0).toString(A);if((j=K).isZero())return X+V;for(;X.length<6;)X="0"+X;V=""+X+V}},eu.getHighBits=function getHighBits(){return this.high},eu.getHighBitsUnsigned=function getHighBitsUnsigned(){return this.high>>>0},eu.getLowBits=function getLowBits(){return this.low},eu.getLowBitsUnsigned=function getLowBitsUnsigned(){return this.low>>>0},eu.getNumBitsAbs=function getNumBitsAbs(){if(this.isNegative())return this.eq(es)?64:this.neg().getNumBitsAbs();for(var A=0!=this.high?this.high:this.low,B=31;B>0&&(A&1<<B)==0;B--);return 0!=this.high?B+33:B+1},eu.isSafeInteger=function isSafeInteger(){var A=this.high>>21;return!A||!this.unsigned&&-1===A&&(0!==this.low||-2097152!==this.high)},eu.isZero=function isZero(){return 0===this.high&&0===this.low},eu.eqz=eu.isZero,eu.isNegative=function isNegative(){return!this.unsigned&&this.high<0},eu.isPositive=function isPositive(){return this.unsigned||this.high>=0},eu.isOdd=function isOdd(){return(1&this.low)==1},eu.isEven=function isEven(){return(1&this.low)==0},eu.equals=function equals(A){return!isLong(A)&&(A=fromValue(A)),(this.unsigned===A.unsigned||this.high>>>31!=1||A.high>>>31!=1)&&this.high===A.high&&this.low===A.low},eu.eq=eu.equals,eu.notEquals=function notEquals(A){return!this.eq(A)},eu.neq=eu.notEquals,eu.ne=eu.notEquals,eu.lessThan=function lessThan(A){return 0>this.comp(A)},eu.lt=eu.lessThan,eu.lessThanOrEqual=function lessThanOrEqual(A){return 0>=this.comp(A)},eu.lte=eu.lessThanOrEqual,eu.le=eu.lessThanOrEqual,eu.greaterThan=function greaterThan(A){return this.comp(A)>0},eu.gt=eu.greaterThan,eu.greaterThanOrEqual=function greaterThanOrEqual(A){return this.comp(A)>=0},eu.gte=eu.greaterThanOrEqual,eu.ge=eu.greaterThanOrEqual,eu.compare=function compare(A){if(!isLong(A)&&(A=fromValue(A)),this.eq(A))return 0;var B=this.isNegative(),N=A.isNegative();return B&&!N?-1:!B&&N?1:this.unsigned?A.high>>>0>this.high>>>0||A.high===this.high&&A.low>>>0>this.low>>>0?-1:1:this.sub(A).isNegative()?-1:1},eu.comp=eu.compare,eu.negate=function negate(){return!this.unsigned&&this.eq(es)?es:this.not().add(er)},eu.neg=eu.negate,eu.add=function add(A){!isLong(A)&&(A=fromValue(A));var B=this.high>>>16,N=65535&this.high,U=this.low>>>16,H=65535&this.low,W=A.high>>>16,j=65535&A.high,V=A.low>>>16,K=65535&A.low,X,J,ee=0,et=0;return X=0+((J=0+(H+K))>>>16),J&=65535,X+=U+V,et+=X>>>16,X&=65535,et+=N+j,ee+=et>>>16,et&=65535,ee+=B+W,fromBits(X<<16|J,(ee&=65535)<<16|et,this.unsigned)},eu.subtract=function subtract(A){return!isLong(A)&&(A=fromValue(A)),this.add(A.neg())},eu.sub=eu.subtract,eu.multiply=function multiply(A){if(this.isZero())return this;if(!isLong(A)&&(A=fromValue(A)),B)return fromBits(B.mul(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned);if(A.isZero())return this.unsigned?et:ee;if(this.eq(es))return A.isOdd()?es:ee;if(A.eq(es))return this.isOdd()?es:ee;if(this.isNegative())return A.isNegative()?this.neg().mul(A.neg()):this.neg().mul(A).neg();if(A.isNegative())return this.mul(A.neg()).neg();if(this.lt(J)&&A.lt(J))return fromNumber(this.toNumber()*A.toNumber(),this.unsigned);var N=this.high>>>16,U=65535&this.high,H=this.low>>>16,W=65535&this.low,j=A.high>>>16,V=65535&A.high,K=A.low>>>16,X=65535&A.low,er,en,ei=0,eo=0;return er=0+((en=0+W*X)>>>16),en&=65535,er+=H*X,eo+=er>>>16,er&=65535,er+=W*K,eo+=er>>>16,er&=65535,eo+=U*X,ei+=eo>>>16,eo&=65535,eo+=H*K,ei+=eo>>>16,eo&=65535,eo+=W*V,ei+=eo>>>16,eo&=65535,ei+=N*X+U*K+H*V+W*j,fromBits(er<<16|en,(ei&=65535)<<16|eo,this.unsigned)},eu.mul=eu.multiply,eu.divide=function divide(A){if(!isLong(A)&&(A=fromValue(A)),A.isZero())throw Error("division by zero");if(B){var N,U,H;if(!this.unsigned&&-0x80000000===this.high&&-1===A.low&&-1===A.high)return this;return fromBits((this.unsigned?B.div_u:B.div_s)(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned)}if(this.isZero())return this.unsigned?et:ee;if(this.unsigned){if(!A.unsigned&&(A=A.toUnsigned()),A.gt(this))return et;if(A.gt(this.shru(1)))return en;H=et}else{if(this.eq(es))return A.eq(er)||A.eq(ei)?es:A.eq(es)?er:(N=this.shr(1).div(A).shl(1)).eq(ee)?A.isNegative()?er:ei:(U=this.sub(A.mul(N)),H=N.add(U.div(A)));if(A.eq(es))return this.unsigned?et:ee;if(this.isNegative())return A.isNegative()?this.neg().div(A.neg()):this.neg().div(A).neg();if(A.isNegative())return this.div(A.neg()).neg();H=ee}for(U=this;U.gte(A);){for(var j=Math.ceil(Math.log(N=Math.max(1,Math.floor(U.toNumber()/A.toNumber())))/Math.LN2),V=j<=48?1:W(2,j-48),K=fromNumber(N),X=K.mul(A);X.isNegative()||X.gt(U);)N-=V,X=(K=fromNumber(N,this.unsigned)).mul(A);K.isZero()&&(K=er),H=H.add(K),U=U.sub(X)}return H},eu.div=eu.divide,eu.modulo=function modulo(A){return(!isLong(A)&&(A=fromValue(A)),B)?fromBits((this.unsigned?B.rem_u:B.rem_s)(this.low,this.high,A.low,A.high),B.get_high(),this.unsigned):this.sub(this.div(A).mul(A))},eu.mod=eu.modulo,eu.rem=eu.modulo,eu.not=function not(){return fromBits(~this.low,~this.high,this.unsigned)},eu.countLeadingZeros=function countLeadingZeros(){return this.high?Math.clz32(this.high):Math.clz32(this.low)+32},eu.clz=eu.countLeadingZeros,eu.countTrailingZeros=function countTrailingZeros(){return this.low?ctz32(this.low):ctz32(this.high)+32},eu.ctz=eu.countTrailingZeros,eu.and=function and(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low&A.low,this.high&A.high,this.unsigned)},eu.or=function or(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low|A.low,this.high|A.high,this.unsigned)},eu.xor=function xor(A){return!isLong(A)&&(A=fromValue(A)),fromBits(this.low^A.low,this.high^A.high,this.unsigned)},eu.shiftLeft=function shiftLeft(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low<<A,this.high<<A|this.low>>>32-A,this.unsigned):fromBits(0,this.low<<A-32,this.unsigned)},eu.shl=eu.shiftLeft,eu.shiftRight=function shiftRight(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,this.high>>A,this.unsigned):fromBits(this.high>>A-32,this.high>=0?0:-1,this.unsigned)},eu.shr=eu.shiftRight,eu.shiftRightUnsigned=function shiftRightUnsigned(A){return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:A<32?fromBits(this.low>>>A|this.high<<32-A,this.high>>>A,this.unsigned):32===A?fromBits(this.high,0,this.unsigned):fromBits(this.high>>>A-32,0,this.unsigned)},eu.shru=eu.shiftRightUnsigned,eu.shr_u=eu.shiftRightUnsigned,eu.rotateLeft=function rotateLeft(A){var B;return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:32===A?fromBits(this.high,this.low,this.unsigned):A<32?(B=32-A,fromBits(this.low<<A|this.high>>>B,this.high<<A|this.low>>>B,this.unsigned)):(A-=32,B=32-A,fromBits(this.high<<A|this.low>>>B,this.low<<A|this.high>>>B,this.unsigned))},eu.rotl=eu.rotateLeft,eu.rotateRight=function rotateRight(A){var B;return(isLong(A)&&(A=A.toInt()),0==(A&=63))?this:32===A?fromBits(this.high,this.low,this.unsigned):A<32?(B=32-A,fromBits(this.high<<B|this.low>>>A,this.low<<B|this.high>>>A,this.unsigned)):(A-=32,B=32-A,fromBits(this.low<<B|this.high>>>A,this.high<<B|this.low>>>A,this.unsigned))},eu.rotr=eu.rotateRight,eu.toSigned=function toSigned(){return this.unsigned?fromBits(this.low,this.high,!1):this},eu.toUnsigned=function toUnsigned(){return this.unsigned?this:fromBits(this.low,this.high,!0)},eu.toBytes=function toBytes(A){return A?this.toBytesLE():this.toBytesBE()},eu.toBytesLE=function toBytesLE(){var A=this.high,B=this.low;return[255&B,B>>>8&255,B>>>16&255,B>>>24,255&A,A>>>8&255,A>>>16&255,A>>>24]},eu.toBytesBE=function toBytesBE(){var A=this.high,B=this.low;return[A>>>24,A>>>16&255,A>>>8&255,255&A,B>>>24,B>>>16&255,B>>>8&255,255&B]},Long.fromBytes=function fromBytes(A,B,N){return N?Long.fromBytesLE(A,B):Long.fromBytesBE(A,B)},Long.fromBytesLE=function fromBytesLE(A,B){return new Long(A[0]|A[1]<<8|A[2]<<16|A[3]<<24,A[4]|A[5]<<8|A[6]<<16|A[7]<<24,B)},Long.fromBytesBE=function fromBytesBE(A,B){return new Long(A[4]<<24|A[5]<<16|A[6]<<8|A[7],A[0]<<24|A[1]<<16|A[2]<<8|A[3],B)},"function"==typeof BigInt&&(Long.fromBigInt=function fromBigInt1(A,B){return fromBits(Number(BigInt.asIntN(32,A)),Number(BigInt.asIntN(32,A>>BigInt(32))),B)},Long.fromValue=function fromValueWithBigInt(A,B){return(void 0===A?"undefined":U._(A))==="bigint"?fromBigInt(A,B):fromValue(A,B)},eu.toBigInt=function toBigInt(){var A=BigInt(this.low>>>0);return BigInt(this.unsigned?this.high>>>0:this.high)<<BigInt(32)|A}),A.default=Long})},53512:function(A,B,N){"use strict";N.d(B,{u:function(){return vue_b1b42453_useHead},c:function(){return vue_b1b42453_createHead}});var U,H=N(78607),W=N(41622),j=N(70879),V=N(44501),K=N(75649),X=N(377);N(87535),N(75204),N(97357),N(34333),N(87989),N(42876),N(33933),N(63712),N(86651),N(75973),N(23329),N(87394),N(94941),N(48421),N(97542),N(7608),N(25037),N(6045),N(10364),N(67673),N(64961),N(12996),N(59564),N(43648),N(74719),N(13396),N(91313),N(27461),N(23339),N(51109),N(72169),N(58486),N(28636),N(97553),N(25069);var J=N(46490),ee=N(90251),et=N(31547);function flatHooks(A){var B=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},N=arguments.length>2?arguments[2]:void 0;for(var U in A){var H=A[U],W=N?"".concat(N,":").concat(U):U;(void 0===H?"undefined":(0,et._)(H))==="object"&&null!==H?flatHooks(H,B,W):"function"==typeof H&&(B[W]=H)}return B}N(82427),N(36062),N(57057),N(36277),N(63235),N(15368),N(12970),N(8503),N(32207),N(13598),N(23541),N(63827),N(30488),N(82236),N(51938),N(33913),N(94949),N(17891),N(44258),N(38609),N(4787),N(44211),N(29338),N(73405),N(98754),N(31832),N(88402),N(56712),N(34885),N(29273),N(58051),N(92519),N(29744),N(18241),N(96205);var er={run:function(A){return A()}},_createTask=function(){return er},en=void 0!==console.createTask?console.createTask:_createTask;function serialTaskCaller(A,B){var N=en(B.shift());return A.reduce(function(A,U){return A.then(function(){return N.run(function(){return U.apply(void 0,(0,K._)(B))})})},Promise.resolve())}function parallelTaskCaller(A,B){var N=en(B.shift());return Promise.all(A.map(function(A){return N.run(function(){return A.apply(void 0,(0,K._)(B))})}))}function callEachWith(A,B){var N=!0,U=!1,H=void 0;try{for(var W,j=(0,K._)(A)[Symbol.iterator]();!(N=(W=j.next()).done);N=!0)(0,W.value)(B)}catch(A){U=!0,H=A}finally{try{!N&&null!=j.return&&j.return()}finally{if(U)throw H}}}var ei=function(){function Hookable(){(0,J._)(this,Hookable),this._hooks={},this._before=void 0,this._after=void 0,this._deprecatedMessages=void 0,this._deprecatedHooks={},this.hook=this.hook.bind(this),this.callHook=this.callHook.bind(this),this.callHookWith=this.callHookWith.bind(this)}return(0,ee._)(Hookable,[{key:"hook",value:function hook(A,B){var N,U=this,H=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(!A||"function"!=typeof B)return function(){};for(var W=A;this._deprecatedHooks[A];)A=(N=this._deprecatedHooks[A]).to;if(N&&!H.allowDeprecated){var j=N.message;!j&&(j="".concat(W," hook has been deprecated")+(N.to?", please use ".concat(N.to):"")),!this._deprecatedMessages&&(this._deprecatedMessages=new Set),!this._deprecatedMessages.has(j)&&(console.warn(j),this._deprecatedMessages.add(j))}if(!B.name)try{Object.defineProperty(B,"name",{get:function(){return"_"+A.replace(/\W+/g,"_")+"_hook_cb"},configurable:!0})}catch(A){}return this._hooks[A]=this._hooks[A]||[],this._hooks[A].push(B),function(){B&&(U.removeHook(A,B),B=void 0)}}},{key:"hookOnce",value:function hookOnce(A,B){var N,_function=function(){for(var A=arguments.length,U=Array(A),H=0;H<A;H++)U[H]=arguments[H];return"function"==typeof N&&N(),N=void 0,_function=void 0,B.apply(void 0,(0,K._)(U))};return N=this.hook(A,_function)}},{key:"removeHook",value:function removeHook(A,B){if(this._hooks[A]){var N=this._hooks[A].indexOf(B);-1!==N&&this._hooks[A].splice(N,1),0===this._hooks[A].length&&delete this._hooks[A]}}},{key:"deprecateHook",value:function deprecateHook(A,B){this._deprecatedHooks[A]="string"==typeof B?{to:B}:B;var N=this._hooks[A]||[];delete this._hooks[A];var U=!0,H=!1,W=void 0;try{for(var j,V=N[Symbol.iterator]();!(U=(j=V.next()).done);U=!0){var K=j.value;this.hook(A,K)}}catch(A){H=!0,W=A}finally{try{!U&&null!=V.return&&V.return()}finally{if(H)throw W}}}},{key:"deprecateHooks",value:function deprecateHooks(A){for(var B in Object.assign(this._deprecatedHooks,A),A)this.deprecateHook(B,A[B])}},{key:"addHooks",value:function addHooks(A){var B=this,N=flatHooks(A),U=Object.keys(N).map(function(A){return B.hook(A,N[A])});return function(){var A=!0,B=!1,N=void 0;try{for(var H,W=U.splice(0,U.length)[Symbol.iterator]();!(A=(H=W.next()).done);A=!0)(0,H.value)()}catch(A){B=!0,N=A}finally{try{!A&&null!=W.return&&W.return()}finally{if(B)throw N}}}}},{key:"removeHooks",value:function removeHooks(A){var B=flatHooks(A);for(var N in B)this.removeHook(N,B[N])}},{key:"removeAllHooks",value:function removeAllHooks(){for(var A in this._hooks)delete this._hooks[A]}},{key:"callHook",value:function callHook(A){for(var B=arguments.length,N=Array(B>1?B-1:0),U=1;U<B;U++)N[U-1]=arguments[U];return N.unshift(A),this.callHookWith.apply(this,[serialTaskCaller,A].concat((0,K._)(N)))}},{key:"callHookParallel",value:function callHookParallel(A){for(var B=arguments.length,N=Array(B>1?B-1:0),U=1;U<B;U++)N[U-1]=arguments[U];return N.unshift(A),this.callHookWith.apply(this,[parallelTaskCaller,A].concat((0,K._)(N)))}},{key:"callHookWith",value:function callHookWith(A,B){for(var N=this,U=arguments.length,H=Array(U>2?U-2:0),W=2;W<U;W++)H[W-2]=arguments[W];var j=this._before||this._after?{name:B,args:H,context:{}}:void 0;this._before&&callEachWith(this._before,j);var V=A(B in this._hooks?(0,K._)(this._hooks[B]):[],H);return V instanceof Promise?V.finally(function(){N._after&&j&&callEachWith(N._after,j)}):(this._after&&j&&callEachWith(this._after,j),V)}},{key:"beforeEach",value:function beforeEach(A){var B=this;return this._before=this._before||[],this._before.push(A),function(){if(void 0!==B._before){var N=B._before.indexOf(A);-1!==N&&B._before.splice(N,1)}}}},{key:"afterEach",value:function afterEach(A){var B=this;return this._after=this._after||[],this._after.push(A),function(){if(void 0!==B._after){var N=B._after.indexOf(A);-1!==N&&B._after.splice(N,1)}}}}]),Hookable}();function createHooks(){return new ei}var eo=N(51606),ea=N(27337);function asArray$1(A){return Array.isArray(A)?A:[A]}N(99808),N(19077),N(55947),N(97210),N(94284),N(109),N(54060),N(20266),N(85203),N(95477),N(61047);var es=["title","titleTemplate","script","style","noscript"],eu=["base","meta","link","style","script","noscript"],el=["title","titleTemplate","templateParams","base","htmlAttrs","bodyAttrs","meta","link","style","script","noscript"],ec=["base","title","titleTemplate","bodyAttrs","htmlAttrs","templateParams"],ef=["tagPosition","tagPriority","tagDuplicateStrategy","innerHTML","textContent","processTemplateParams"],ed="undefined"!=typeof window;function dist_defineHeadPlugin(A){return A}function hashCode(A){for(var B=9,N=0;N<A.length;)B=Math.imul(B^A.charCodeAt(N++),0x17179149);return((B^B>>>9)+65536).toString(16).substring(1,8).toLowerCase()}function hashTag(A){return A._h||hashCode(A._d?A._d:"".concat(A.tag,":").concat(A.textContent||A.innerHTML||"",":").concat(Object.entries(A.props).map(function(A){var B=(0,V._)(A,2),N=B[0],U=B[1];return"".concat(N,":").concat(String(U))}).join(",")))}function tagDedupeKey(A,B){var N=A.props,U=A.tag;if(ec.includes(U))return U;if("link"===U&&"canonical"===N.rel)return"canonical";if(N.charset)return"charset";var H=["id"];"meta"===U&&(K=H).push.apply(K,["name","property","http-equiv"]);var W=!0,j=!1,V=void 0;try{for(var K,X,J=H[Symbol.iterator]();!(W=(X=J.next()).done);W=!0){var ee=X.value;if(void 0!==N[ee]){var et=String(N[ee]);if(B&&!B(et))return!1;return"".concat(U,":").concat(ee,":").concat(et)}}}catch(A){j=!0,V=A}finally{try{!W&&null!=J.return&&J.return()}finally{if(j)throw V}}return!1}function resolveTitleTemplate(A,B){return null==A?B||null:"function"==typeof A?A(B):A}var dist_p=function(A){return{keyValue:A,metaKey:"property"}},dist_k=function(A){return{keyValue:A}};dist_p("article:author"),dist_p("article:expiration_time"),dist_p("article:modified_time"),dist_p("article:published_time"),dist_p("article:section"),dist_p("article:tag"),dist_p("book:author"),dist_p("book:isbn"),dist_p("book:release_date"),dist_p("book:tag"),dist_p("fb:app_id"),dist_k("msapplication-Config"),dist_k("msapplication-TileColor"),dist_k("msapplication-TileImage"),dist_p("og:audio:secure_url"),dist_p("og:audio:type"),dist_p("og:audio"),dist_p("og:description"),dist_p("og:determiner"),dist_p("og:image"),dist_p("og:image:alt"),dist_p("og:image:height"),dist_p("og:image:secure_url"),dist_p("og:image:type"),dist_p("og:image"),dist_p("og:image:width"),dist_p("og:locale"),dist_p("og:locale:alternate"),dist_p("og:site_name"),dist_p("og:title"),dist_p("og:type"),dist_p("og:url"),dist_p("og:video"),dist_p("og:video:alt"),dist_p("og:video:height"),dist_p("og:video:secure_url"),dist_p("og:video:type"),dist_p("og:video"),dist_p("og:video:width"),dist_p("profile:first_name"),dist_p("profile:gender"),dist_p("profile:last_name"),dist_p("profile:username"),dist_k("twitter:app:id:googleplay"),dist_k("twitter:app:id:ipad"),dist_k("twitter:app:id:iphone"),dist_k("twitter:app:name:googleplay"),dist_k("twitter:app:name:ipad"),dist_k("twitter:app:name:iphone"),dist_k("twitter:app:url:googleplay"),dist_k("twitter:app:url:ipad"),dist_k("twitter:app:url:iphone"),dist_k("twitter:card"),dist_k("twitter:creator"),dist_k("twitter:creator:id"),dist_k("twitter:data1"),dist_k("twitter:data2"),dist_k("twitter:description"),dist_k("twitter:image"),dist_k("twitter:image:alt"),dist_k("twitter:image:height"),dist_k("twitter:image:type"),dist_k("twitter:image"),dist_k("twitter:image:width"),dist_k("twitter:label1"),dist_k("twitter:label2"),dist_k("twitter:player"),dist_k("twitter:player:height"),dist_k("twitter:player:stream"),dist_k("twitter:player:width"),dist_k("twitter:site"),dist_k("twitter:site:id"),dist_k("twitter:title");function normaliseTag(A,B,N){return _normaliseTag.apply(this,arguments)}function _normaliseTag(){return(_normaliseTag=(0,W._)(function(A,B,N){var U,H;return(0,X.Jh)(this,function(W){switch(W.label){case 0:return H={tag:A},[4,normaliseProps((void 0===B?"undefined":(0,et._)(B))!=="object"||"function"==typeof B||B instanceof Promise?(0,eo._)({},["script","noscript","style"].includes(A)?"innerHTML":"textContent",B):(0,j._)({},B),["templateParams","titleTemplate"].includes(A))];case 1:return H.props=W.sent(),U=H,ef.forEach(function(A){var B=void 0!==U.props[A]?U.props[A]:N[A];void 0!==B&&((!["innerHTML","textContent"].includes(A)||es.includes(U.tag))&&(U[A]=B),delete U.props[A])}),U.props.body&&(U.tagPosition="bodyClose",delete U.props.body),U.props.children&&(U.innerHTML=U.props.children,delete U.props.children),"script"===U.tag&&"object"===(0,et._)(U.innerHTML)?(U.innerHTML=JSON.stringify(U.innerHTML),U.props.type=U.props.type||"application/json"):"script"===U.tag&&U.innerHTML&&(/^(https?:)?\/\//.test(U.innerHTML)||U.innerHTML.startsWith("/"))&&(U.props.src=U.innerHTML,delete U.innerHTML),[2,Array.isArray(U.props.content)?U.props.content.map(function(A){return(0,ea._)((0,j._)({},U),{props:(0,ea._)((0,j._)({},U.props),{content:A})})}):U]}})})).apply(this,arguments)}function normaliseClassProp(A){return(void 0===A?"undefined":(0,et._)(A))==="object"&&!Array.isArray(A)&&(A=Object.keys(A).filter(function(B){return A[B]})),(Array.isArray(A)?A.join(" "):A).split(" ").filter(function(A){return A.trim()}).filter(Boolean).join(" ")}function normaliseProps(A,B){return _normaliseProps.apply(this,arguments)}function _normaliseProps(){return(_normaliseProps=(0,W._)(function(A,B){var N,U,H,W,j,V,K,J,ee;return(0,X.Jh)(this,function(X){switch(X.label){case 0:N=!0,U=!1,H=void 0,X.label=1;case 1:X.trys.push([1,7,8,9]),W=Object.keys(A)[Symbol.iterator](),X.label=2;case 2:if(N=(j=W.next()).done)return[3,6];if("class"===(V=j.value))return A[V]=normaliseClassProp(A[V]),[3,5];if(!(A[V]instanceof Promise))return[3,4];return[4,A[V]];case 3:A[V]=X.sent(),X.label=4;case 4:!B&&!ef.includes(V)&&(K=String(A[V]),J=V.startsWith("data-"),"true"===K||""===K?A[V]=!J||"true":!A[V]&&(J&&"false"===K?A[V]="false":delete A[V])),X.label=5;case 5:return N=!0,[3,2];case 6:return[3,9];case 7:return ee=X.sent(),U=!0,H=ee,[3,9];case 8:try{!N&&null!=W.return&&W.return()}finally{if(U)throw H}return[7];case 9:return[2,A]}})})).apply(this,arguments)}var ep=10;function normaliseEntryTags(A){return _normaliseEntryTags.apply(this,arguments)}function _normaliseEntryTags(){return(_normaliseEntryTags=(0,W._)(function(A){var B;return(0,X.Jh)(this,function(N){switch(N.label){case 0:return B=[],Object.entries(A.resolvedInput).filter(function(A){var B=(0,V._)(A,2),N=B[0];return void 0!==B[1]&&el.includes(N)}).forEach(function(N){var U,H=(0,V._)(N,2),W=H[0],j=asArray$1(H[1]);(U=B).push.apply(U,(0,K._)(j.map(function(B){return normaliseTag(W,B,A)}).flat()))}),[4,Promise.all(B)];case 1:return[2,N.sent().flat().filter(Boolean).map(function(B,N){return B._e=A._i,A.mode&&(B._m=A.mode),B._p=(A._i<<ep)+N,B})]}})})).apply(this,arguments)}var eh={base:-10,title:10},eg={critical:-80,high:-10,low:20};function tagWeight(A){var B=100,N=A.tagPriority;return"number"==typeof N?N:("meta"===A.tag?(A.props.charset&&(B=-20),"content-security-policy"===A.props["http-equiv"]&&(B=0)):"link"===A.tag&&"preconnect"===A.props.rel?B=20:A.tag in eh&&(B=eh[A.tag]),"string"==typeof N&&N in eg)?B+eg[N]:B}var ey=[{prefix:"before:",offset:-1},{prefix:"after:",offset:1}],em="%separator";function processTemplateParams(A,B,N){if("string"!=typeof A||!A.includes("%"))return A;function sub(A){var N;return void 0!==(N=["s","pageTitle"].includes(A)?B.pageTitle:A.includes(".")?A.split(".").reduce(function(A,B){return A&&A[B]||void 0},B):B[A])&&(N||"").replace(/"/g,'\\"')}var U=A;try{U=decodeURI(A)}catch(A){}return(U.match(/%(\w+\.+\w+)|%(\w+)/g)||[]).sort().reverse().forEach(function(B){var N=sub(B.slice(1));"string"==typeof N&&(A=A.replace(RegExp("\\".concat(B,"(\\W|$)"),"g"),function(A,B){return"".concat(N).concat(B)}).trim())}),A.includes(em)&&(A.endsWith(em)&&(A=A.slice(0,-em.length).trim()),A.startsWith(em)&&(A=A.slice(em.length).trim()),A=processTemplateParams(A=A.replace(RegExp("\\".concat(em,"\\s*\\").concat(em),"g"),em),{separator:N},N)),A}function elementToTag(A){return _elementToTag.apply(this,arguments)}function _elementToTag(){return(_elementToTag=(0,W._)(function(A){var B,N;return(0,X.Jh)(this,function(U){switch(U.label){case 0:return N={tag:A.tagName.toLowerCase()},[4,normaliseProps(A.getAttributeNames().reduce(function(B,N){return(0,ea._)((0,j._)({},B),(0,eo._)({},N,A.getAttribute(N)))},{}))];case 1:return N.props=U.sent(),N.innerHTML=A.innerHTML,(B=N)._d=tagDedupeKey(B),[2,B]}})})).apply(this,arguments)}function renderDOMHead(A){return _renderDOMHead.apply(this,arguments)}function _renderDOMHead(){return(_renderDOMHead=(0,W._)(function(A){var B,N,U,H,W,J,ee,et,er,en,ei,eo,ea,es,el,ec,ef,ed,ep,eh,eg,ey,em,e_,ev,eS,eb,eE,eT,eA,ew,eO,eB,eI,eR,eC,eL,eN,eM,ex,eP,eU=arguments;function track(A,B,N){var U="".concat(A,":").concat(B);H.sideEffects[U]=N,delete H.pendingSideEffects[U]}function trackCtx(A){var B=A.id,N=A.$el,U=A.tag,W=U.tag.endsWith("Attrs");H.elMap[B]=N,!W&&(["textContent","innerHTML"].forEach(function(A){U[A]&&U[A]!==N[A]&&(N[A]=U[A])}),track(B,"el",function(){H.elMap[B].remove(),delete H.elMap[B]})),Object.entries(U.props).forEach(function(A){var U=(0,V._)(A,2),H=U[0],j=U[1],K="attr:".concat(H);if("class"===H){var X=!0,J=!1,ee=void 0;try{for(var et,_loop=function(){var A=et.value;W&&track(B,"".concat(K,":").concat(A),function(){return N.classList.remove(A)}),N.classList.contains(A)||N.classList.add(A)},er=(j||"").split(" ").filter(Boolean)[Symbol.iterator]();!(X=(et=er.next()).done);X=!0)_loop()}catch(A){J=!0,ee=A}finally{try{!X&&null!=er.return&&er.return()}finally{if(J)throw ee}}}else N.getAttribute(H)!==j&&N.setAttribute(H,!0===j?"":String(j)),W&&track(B,K,function(){return N.removeAttribute(H)})})}return(0,X.Jh)(this,function(V){switch(V.label){case 0:if(!(B=(eU.length>1&&void 0!==eU[1]?eU[1]:{}).document||A.resolvedOptions.document))return[2];return N={shouldRender:A.dirty,tags:[]},[4,A.hooks.callHook("dom:beforeRender",N)];case 1:if(V.sent(),!N.shouldRender)return[2];return[4,A.resolveTags()];case 2:if(U=V.sent().map(function(A){return{tag:A,id:eu.includes(A.tag)?hashTag(A):A.tag,shouldRender:!0}}),H=A._dom)return[3,13];H={elMap:{htmlAttrs:B.documentElement,bodyAttrs:B.body}},W=0,J=["body","head"],V.label=3;case 3:if(!(W<J.length))return[3,13];ee=J[W],er=null==B?void 0:null===(et=B[ee])||void 0===et?void 0:et.children,en=!0,ei=!1,eo=void 0,V.label=4;case 4:V.trys.push([4,10,11,12]),ea=(0,K._)(er).filter(function(A){return eu.includes(A.tagName.toLowerCase())})[Symbol.iterator](),V.label=5;case 5:if(en=(es=ea.next()).done)return[3,9];if(ec=(el=es.value).getAttribute("data-hid"))return[3,7];return[4,elementToTag(el)];case 6:ec=hashTag.apply(void 0,[V.sent()]),V.label=7;case 7:H.elMap[ec]=el,V.label=8;case 8:return en=!0,[3,5];case 9:return[3,12];case 10:return ef=V.sent(),ei=!0,eo=ef,[3,12];case 11:try{!en&&null!=ea.return&&ea.return()}finally{if(ei)throw eo}return[7];case 12:return W++,[3,3];case 13:H.pendingSideEffects=(0,j._)({},H.sideEffects||{}),H.sideEffects={},ed=[],ep={bodyClose:void 0,bodyOpen:void 0,head:void 0},eh=!0,eg=!1,ey=void 0;try{for(em=U[Symbol.iterator]();!(eh=(e_=em.next()).done);eh=!0)if(eS=(ev=e_.value).tag,eb=ev.shouldRender,eE=ev.id,eb){if("title"===eS.tag){B.title=eS.textContent;continue}ev.$el=ev.$el||H.elMap[eE],ev.$el?trackCtx(ev):eu.includes(eS.tag)&&ed.push(ev)}}catch(A){eg=!0,ey=A}finally{try{!eh&&null!=em.return&&em.return()}finally{if(eg)throw ey}}eT=!0,eA=!1,ew=void 0;try{for(eO=ed[Symbol.iterator]();!(eT=(eB=eO.next()).done);eT=!0)eR=(eI=eB.value).tag.tagPosition||"head",eI.$el=B.createElement(eI.tag.tag),trackCtx(eI),ep[eR]=ep[eR]||B.createDocumentFragment(),ep[eR].appendChild(eI.$el)}catch(A){eA=!0,ew=A}finally{try{!eT&&null!=eO.return&&eO.return()}finally{if(eA)throw ew}}eC=!0,eL=!1,eN=void 0,V.label=14;case 14:V.trys.push([14,19,20,21]),eM=U[Symbol.iterator](),V.label=15;case 15:if(eC=(ex=eM.next()).done)return[3,18];return eP=ex.value,[4,A.hooks.callHook("dom:renderTag",eP,B,track)];case 16:V.sent(),V.label=17;case 17:return eC=!0,[3,15];case 18:return[3,21];case 19:return ef=V.sent(),eL=!0,eN=ef,[3,21];case 20:try{!eC&&null!=eM.return&&eM.return()}finally{if(eL)throw eN}return[7];case 21:return ep.head&&B.head.appendChild(ep.head),ep.bodyOpen&&B.body.insertBefore(ep.bodyOpen,B.body.firstChild),ep.bodyClose&&B.body.appendChild(ep.bodyClose),Object.values(H.pendingSideEffects).forEach(function(A){return A()}),A._dom=H,A.dirty=!1,[4,A.hooks.callHook("dom:rendered",{renders:U})];case 22:return V.sent(),[2]}})})).apply(this,arguments)}function debouncedRenderDOMHead(A){return _debouncedRenderDOMHead.apply(this,arguments)}function _debouncedRenderDOMHead(){return(_debouncedRenderDOMHead=(0,W._)(function(A){var B,N,U=arguments;return(0,X.Jh)(this,function(H){return N=(B=U.length>1&&void 0!==U[1]?U[1]:{}).delayFn||function(A){return setTimeout(A,10)},[2,A._domUpdatePromise=A._domUpdatePromise||new Promise(function(U){return N((0,W._)(function(){return(0,X.Jh)(this,function(N){switch(N.label){case 0:return[4,renderDOMHead(A,B)];case 1:return N.sent(),delete A._domUpdatePromise,U(),[2]}})}))})]})})).apply(this,arguments)}function DomPlugin(A){return dist_defineHeadPlugin(function(B){var N,U,H=(null===(U=B.resolvedOptions.document)||void 0===U?void 0:null===(N=U.head.querySelector('script[id="unhead:payload"]'))||void 0===N?void 0:N.innerHTML)||!1;return H&&B.push(JSON.parse(H)),{mode:"client",hooks:{"entries:updated":function(B){debouncedRenderDOMHead(B,A)}}}})}var e_=["templateParams","htmlAttrs","bodyAttrs"],ev=dist_defineHeadPlugin({hooks:{"tag:normalise":function(A){var B=A.tag;["hid","vmid","key"].forEach(function(A){B.props[A]&&(B.key=B.props[A],delete B.props[A])});var N=tagDedupeKey(B)||!!B.key&&"".concat(B.tag,":").concat(B.key);N&&(B._d=N)},"tags:resolve":function(A){var B={};A.tags.forEach(function(A){var N=(A.key?"".concat(A.tag,":").concat(A.key):A._d)||A._p,U=B[N];if(U){var H=null==A?void 0:A.tagDuplicateStrategy;if(!H&&e_.includes(A.tag)&&(H="merge"),"merge"===H){var W=U.props;["class","style"].forEach(function(B){A.props[B]&&W[B]&&("style"===B&&!W[B].endsWith(";")&&(W[B]+=";"),A.props[B]="".concat(W[B]," ").concat(A.props[B]))}),B[N].props=(0,j._)({},W,A.props);return}if(A._e===U._e){U._duped=U._duped||[],A._d="".concat(U._d,":").concat(U._duped.length+1),U._duped.push(A);return}else if(tagWeight(A)>tagWeight(U))return}var V=Object.keys(A.props).length+ +!!A.innerHTML+ +!!A.textContent;if(eu.includes(A.tag)&&0===V){delete B[N];return}B[N]=A});var N=[];Object.values(B).forEach(function(A){var B,U=A._duped;delete A._duped,N.push(A),U&&(B=N).push.apply(B,(0,K._)(U))}),A.tags=N}}}),eS=dist_defineHeadPlugin({mode:"server",hooks:{"tags:resolve":function(A){var B={};A.tags.filter(function(A){return["titleTemplate","templateParams","title"].includes(A.tag)&&"server"===A._m}).forEach(function(A){B[A.tag]=A.tag.startsWith("title")?A.textContent:A.props}),Object.keys(B).length&&A.tags.push({tag:"script",innerHTML:JSON.stringify(B),props:{id:"unhead:payload",type:"application/json"}})}}}),eb=["script","link","bodyAttrs"];function stripEventHandlers(A){var B={},N={};return Object.entries(A.props).forEach(function(A){var U=(0,V._)(A,2),H=U[0],W=U[1];H.startsWith("on")&&"function"==typeof W?N[H]=W:B[H]=W}),{props:B,eventHandlers:N}}var eE=dist_defineHeadPlugin({hooks:{"ssr:render":function(A){A.tags=A.tags.map(function(A){return eb.includes(A.tag)&&Object.entries(A.props).find(function(A){var B=(0,V._)(A,2),N=B[0],U=B[1];return N.startsWith("on")&&"function"==typeof U})?(A.props=stripEventHandlers(A).props,A):A})},"tags:resolve":function(A){A.tags=A.tags.map(function(A){if(!eb.includes(A.tag))return A;var B=stripEventHandlers(A),N=B.props,U=B.eventHandlers;return Object.keys(U).length&&(A.props=N,A._eventHandlers=U),A})},"dom:renderTag":function(A,B,N){if(A.tag._eventHandlers){var U="bodyAttrs"===A.tag.tag?B.defaultView:A.$el;Object.entries(A.tag._eventHandlers).forEach(function(B){var H=(0,V._)(B,2),W=H[0],j=H[1],K="".concat(A.tag._d||A.tag._p,":").concat(W),X=W.slice(2).toLowerCase(),J="data-h-".concat(X);if(N(A.id,K,function(){}),!A.$el.hasAttribute(J)){var ee=j;A.$el.setAttribute(J,""),U.addEventListener(X,ee),A.entry&&N(A.id,K,function(){U.removeEventListener(X,ee),A.$el.removeAttribute(J)})}})}}}}),eT=["link","style","script","noscript"],eA=dist_defineHeadPlugin({hooks:{"tag:normalise":function(A){var B=A.tag;B.key&&eT.includes(B.tag)&&(B.props["data-hid"]=B._h=hashCode(B.key))}}}),ew=dist_defineHeadPlugin({hooks:{"tags:resolve":function(A){var tagPositionForKey=function(B){var N;return null===(N=A.tags.find(function(A){return A._d===B}))||void 0===N?void 0:N._p},B=!0,N=!1,U=void 0;try{for(var H,_loop=function(){var B=H.value,N=B.prefix,U=B.offset,W=!0,j=!1,V=void 0;try{for(var K,X=A.tags.filter(function(A){return"string"==typeof A.tagPriority&&A.tagPriority.startsWith(N)})[Symbol.iterator]();!(W=(K=X.next()).done);W=!0){var J=K.value,ee=tagPositionForKey(J.tagPriority.replace(N,""));void 0!==ee&&(J._p=ee+U)}}catch(A){j=!0,V=A}finally{try{!W&&null!=X.return&&X.return()}finally{if(j)throw V}}},W=ey[Symbol.iterator]();!(B=(H=W.next()).done);B=!0)_loop()}catch(A){N=!0,U=A}finally{try{!B&&null!=W.return&&W.return()}finally{if(N)throw U}}A.tags.sort(function(A,B){return A._p-B._p}).sort(function(A,B){return tagWeight(A)-tagWeight(B)})}}}),eO=dist_defineHeadPlugin({hooks:{"tags:resolve":function(A){var B=A.tags,N=null===(X=B.find(function(A){return"title"===A.tag}))||void 0===X?void 0:X.textContent,U=B.findIndex(function(A){return"templateParams"===A.tag}),H=-1!==U?B[U].props:{},W=H.separator||"|";delete H.separator,H.pageTitle=processTemplateParams(H.pageTitle||N||"",H,W);var j=!0,V=!1,K=void 0;try{for(var X,J,ee=B[Symbol.iterator]();!(j=(J=ee.next()).done);j=!0){var et=J.value;!1!==et.processTemplateParams&&(["titleTemplate","title"].includes(et.tag)&&"string"==typeof et.textContent?et.textContent=processTemplateParams(et.textContent,H,W):"meta"===et.tag&&"string"==typeof et.props.content?et.props.content=processTemplateParams(et.props.content,H,W):"link"===et.tag&&"string"==typeof et.props.href?et.props.href=processTemplateParams(et.props.href,H,W):!0===et.processTemplateParams&&(et.innerHTML?et.innerHTML=processTemplateParams(et.innerHTML,H,W):et.textContent&&(et.textContent=processTemplateParams(et.textContent,H,W))))}}catch(A){V=!0,K=A}finally{try{!j&&null!=ee.return&&ee.return()}finally{if(V)throw K}}A.tags=B.filter(function(A){return"templateParams"!==A.tag})}}}),eB=dist_defineHeadPlugin({hooks:{"tags:resolve":function(A){var B=A.tags,N=B.findIndex(function(A){return"titleTemplate"===A.tag}),U=B.findIndex(function(A){return"title"===A.tag});if(-1!==U&&-1!==N){var H=resolveTitleTemplate(B[N].textContent,B[U].textContent);null!==H?B[U].textContent=H||B[U].textContent:delete B[U]}else if(-1!==N){var W=resolveTitleTemplate(B[N].textContent);null!==W&&(B[N].textContent=W,B[N].tag="title",N=-1)}-1!==N&&delete B[N],A.tags=B.filter(Boolean)}}});function createHead(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=createHeadCore(A);return B.use(DomPlugin()),U=B}function filterMode(A,B){return!A||"server"===A&&B||"client"===A&&!B}function createHeadCore(){var A=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},B=createHooks();B.addHooks(A.hooks||{}),A.document=A.document||(ed?document:void 0);var N=!A.document;A.plugins=[ev,eS,eE,eA,ew,eO,eB].concat((0,K._)((null==A?void 0:A.plugins)||[]));var updated=function(){V.dirty=!0,B.callHook("entries:updated",V)},U=0,H=[],V={dirty:!1,resolvedOptions:A,hooks:B,headEntries:function(){return H},use:function(A){var U="function"==typeof A?A(V):A;filterMode(U.mode,N)&&B.addHooks(U.hooks||{})},push:function(A,W){null==W||delete W.head;var K=(0,j._)({_i:U++,input:A},W);return filterMode(K.mode,N)&&(H.push(K),updated()),{dispose:function(){H=H.filter(function(A){return A._i!==K._i}),B.callHook("entries:updated",V),updated()},patch:function(A){H=H.map(function(B){return B._i===K._i&&(B.input=K.input=A),B}),updated()}}},resolveTags:function(){return(0,W._)(function(){var A,N,U,W,j,J,ee,et,er,en,ei,eo,ea,es,eu;return(0,X.Jh)(this,function(X){switch(X.label){case 0:return A={tags:[],entries:(0,K._)(H)},[4,B.callHook("entries:resolve",A)];case 1:X.sent(),N=!0,U=!1,W=void 0,X.label=2;case 2:X.trys.push([2,15,16,17]),j=A.entries[Symbol.iterator](),X.label=3;case 3:if(N=(J=j.next()).done)return[3,14];return et=(ee=J.value).resolvedInput||ee.input,[4,ee.transform?ee.transform(et):et];case 4:if(ee.resolvedInput=X.sent(),!ee.resolvedInput)return[3,13];er=!0,en=!1,ei=void 0,X.label=5;case 5:return X.trys.push([5,11,12,13]),[4,normaliseEntryTags(ee)];case 6:eo=X.sent()[Symbol.iterator](),X.label=7;case 7:if(er=(ea=eo.next()).done)return[3,10];return es={tag:ea.value,entry:ee,resolvedOptions:V.resolvedOptions},[4,B.callHook("tag:normalise",es)];case 8:X.sent(),A.tags.push(es.tag),X.label=9;case 9:return er=!0,[3,7];case 10:return[3,13];case 11:return eu=X.sent(),en=!0,ei=eu,[3,13];case 12:try{!er&&null!=eo.return&&eo.return()}finally{if(en)throw ei}return[7];case 13:return N=!0,[3,3];case 14:return[3,17];case 15:return eu=X.sent(),U=!0,W=eu,[3,17];case 16:try{!N&&null!=j.return&&j.return()}finally{if(U)throw W}return[7];case 17:return[4,B.callHook("tags:beforeResolve",A)];case 18:return X.sent(),[4,B.callHook("tags:resolve",A)];case 19:return X.sent(),[2,A.tags]}})})()},ssr:N};return A.plugins.forEach(function(A){return V.use(A)}),V.hooks.callHook("init",V),V}function getActiveHead(){return U}let eI=H.i8.startsWith("3");function resolveUnref(A){return"function"==typeof A?A():(0,H.SU)(A)}function resolveUnrefHeadInput(A,B=""){if(A instanceof Promise)return A;let N=resolveUnref(A);return A&&N?Array.isArray(N)?N.map(A=>resolveUnrefHeadInput(A,B)):"object"==typeof N?Object.fromEntries(Object.entries(N).map(([A,B])=>"titleTemplate"===A||A.startsWith("on")?[A,(0,H.SU)(B)]:[A,resolveUnrefHeadInput(B,A)])):N:N}let eR=dist_defineHeadPlugin({hooks:{"entries:resolve":function(A){for(let B of A.entries)B.resolvedInput=resolveUnrefHeadInput(B.input)}}}),eC="usehead";function vueInstall(A){return({install(B){eI&&(B.config.globalProperties.$unhead=A,B.config.globalProperties.$head=A,B.provide(eC,A))}}).install}function vue_b1b42453_createHead(A={}){A.domDelayFn=A.domDelayFn||(A=>(0,H.Y3)(()=>setTimeout(()=>A(),0)));let B=createHead(A);return B.use(eR),B.install=vueInstall(B),B}let eL="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},eN="__unhead_injection_handler__";function injectHead(){if(eN in eL)return eL[eN]();let A=(0,H.f3)(eC);return A||getActiveHead()}function vue_b1b42453_useHead(A,B={}){let N=B.head||injectHead();if(N)return N.ssr?N.push(A,B):clientUseHead(N,A,B)}function clientUseHead(A,B,N={}){let U=(0,H.iH)(!1),W=(0,H.iH)({});(0,H.m0)(()=>{W.value=U.value?{}:resolveUnrefHeadInput(B)});let j=A.push(W.value,N);return(0,H.YP)(W,A=>{j.patch(A)}),(0,H.FN)()&&((0,H.Jd)(()=>{j.dispose()}),(0,H.se)(()=>{U.value=!0}),(0,H.dl)(()=>{U.value=!1})),j}},51401:function(A,B,N){"use strict";function assign(A){for(var B=1;B<arguments.length;B++){var N=arguments[B];for(var U in N)A[U]=N[U]}return A}function init(A,B){function set(N,U,H){"number"==typeof(H=assign({},B,H)).expires&&(H.expires=new Date(Date.now()+864e5*H.expires)),H.expires&&(H.expires=H.expires.toUTCString()),N=encodeURIComponent(N).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var W="";for(var j in H){if(!H[j])continue;if(W+="; "+j,!0!==H[j])W+="="+H[j].split(";")[0]}return document.cookie=N+"="+A.write(U,N)+W}return Object.create({set,get:function get(B){if(!arguments.length||!!B){for(var N=document.cookie?document.cookie.split("; "):[],U={},H=0;H<N.length;H++){var W=N[H].split("="),j=W.slice(1).join("=");try{var V=decodeURIComponent(W[0]);if(U[V]=A.read(j,V),B===V)break}catch(A){}}return B?U[B]:U}},remove:function(A,B){set(A,"",assign({},B,{expires:-1}))},withAttributes:function(A){return init(this.converter,assign({},this.attributes,A))},withConverter:function(A){return init(assign({},this.converter,A),this.attributes)}},{attributes:{value:Object.freeze(B)},converter:{value:Object.freeze(A)}})}N.d(B,{Z:function(){return U}});var U=init({read:function(A){return'"'===A[0]&&(A=A.slice(1,-1)),A.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(A){return encodeURIComponent(A).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})},86058:function(A,B,N){"use strict";function __WEBPACK_DEFAULT_EXPORT__(A){return{all:A=A||new Map,on:function(B,N){var U=A.get(B);U?U.push(N):A.set(B,[N])},off:function(B,N){var U=A.get(B);U&&(N?U.splice(U.indexOf(N)>>>0,1):A.set(B,[]))},emit:function(B,N){var U=A.get(B);U&&U.slice().map(function(A){A(N)}),(U=A.get("*"))&&U.slice().map(function(A){A(B,N)})}}}N.d(B,{Z:function(){return __WEBPACK_DEFAULT_EXPORT__}})},72811:function(A,B,N){"use strict";N.d(B,{$Q:function(){return H},B:function(){return U.B},BK:function(){return U.BK},EB:function(){return U.EB},FN:function(){return U.FN},Fl:function(){return U.Fl},IU:function(){return U.IU},JJ:function(){return U.JJ},OT:function(){return U.OT},PG:function(){return U.PG},SU:function(){return U.SU},Vh:function(){return U.Vh},XI:function(){return U.XI},Xl:function(){return U.Xl},Y3:function(){return U.Y3},YP:function(){return U.YP},ZM:function(){return U.ZM},bv:function(){return U.bv},dq:function(){return U.dq},f3:function(){return U.f3},iH:function(){return U.iH},m0:function(){return U.m0},nZ:function(){return U.nZ},qj:function(){return U.qj},t8:function(){return set}});var U=N(78607),H=!1;function set(A,B,N){return Array.isArray(A)?(A.length=Math.max(A.length,B),A.splice(B,1,N),N):(A[B]=N,N)}},57078:function(A,B,N){"use strict";N.d(B,{NO:function(){return ne},Yn:function(){return $},a4:function(){return I},mw:function(){return F}});var U,H,W,j,V,a=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},o=function(A){if("loading"===document.readyState)return"loading";var B=a();if(B){if(A<B.domInteractive)return"loading";if(0===B.domContentLoadedEventStart||A<B.domContentLoadedEventStart)return"dom-interactive";if(0===B.domComplete||A<B.domComplete)return"dom-content-loaded"}return"complete"},u=function(A){var B=A.nodeName;return 1===A.nodeType?B.toLowerCase():B.toUpperCase().replace(/^#/,"")},c=function(A,B){var N="";try{for(;A&&9!==A.nodeType;){var U=A,H=U.id?"#"+U.id:u(U)+(U.classList&&U.classList.value&&U.classList.value.trim()&&U.classList.value.trim().length?"."+U.classList.value.trim().replace(/\s+/g,"."):"");if(N.length+H.length>(B||100)-1)return N||H;if(N=N?H+">"+N:H,U.id)break;A=U.parentNode}}catch(A){}return N},K=-1,f=function(){return K},d=function(A){addEventListener("pageshow",function(B){B.persisted&&(K=B.timeStamp,A(B))},!0)},l=function(){var A=a();return A&&A.activationStart||0},m=function(A,B){var N=a(),U="navigate";return f()>=0?U="back-forward-cache":N&&(document.prerendering||l()>0?U="prerender":document.wasDiscarded?U="restore":N.type&&(U=N.type.replace(/_/g,"-"))),{name:A,value:void 0===B?-1:B,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:U}},v=function(A,B,N){try{if(PerformanceObserver.supportedEntryTypes.includes(A)){var U=new PerformanceObserver(function(A){Promise.resolve().then(function(){B(A.getEntries())})});return U.observe(Object.assign({type:A,buffered:!0},N||{})),U}}catch(A){}},p=function(A,B,N,U){var H,W;return function(j){B.value>=0&&(j||U)&&((W=B.value-(H||0))||void 0===H)&&(H=B.value,B.delta=W,B.rating=function(A,B){return A>B[1]?"poor":A>B[0]?"needs-improvement":"good"}(B.value,N),A(B))}},h=function(A){requestAnimationFrame(function(){return requestAnimationFrame(function(){return A()})})},g=function(A){var t=function(B){"pagehide"!==B.type&&"hidden"!==document.visibilityState||A(B)};addEventListener("visibilitychange",t,!0),addEventListener("pagehide",t,!0)},T=function(A){var B=!1;return function(N){B||(A(N),B=!0)}},X=-1,E=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},S=function(A){"hidden"===document.visibilityState&&X>-1&&(X="visibilitychange"===A.type?A.timeStamp:0,b())},L=function(){addEventListener("visibilitychange",S,!0),addEventListener("prerenderingchange",S,!0)},b=function(){removeEventListener("visibilitychange",S,!0),removeEventListener("prerenderingchange",S,!0)},C=function(){return X<0&&(X=E(),L(),d(function(){setTimeout(function(){X=E(),L()},0)})),{get firstHiddenTime(){return X}}},w=function(A){document.prerendering?addEventListener("prerenderingchange",function(){return A()},!0):A()},J=[1800,3e3],x=function(A,B){B=B||{},w(function(){var N,U=C(),H=m("FCP"),W=v("paint",function(A){A.forEach(function(A){"first-contentful-paint"===A.name&&(W.disconnect(),A.startTime<U.firstHiddenTime&&(H.value=Math.max(A.startTime-l(),0),H.entries.push(A),N(!0)))})});W&&(N=p(A,H,J,B.reportAllChanges),d(function(U){N=p(A,H=m("FCP"),J,B.reportAllChanges),h(function(){H.value=performance.now()-U.timeStamp,N(!0)})}))})},ee=[.1,.25],F=function(A,B){!function(A,B){B=B||{},x(T(function(){var N,U=m("CLS",0),H=0,W=[],o=function(A){A.forEach(function(A){if(!A.hadRecentInput){var B=W[0],N=W[W.length-1];H&&A.startTime-N.startTime<1e3&&A.startTime-B.startTime<5e3?(H+=A.value,W.push(A)):(H=A.value,W=[A])}}),H>U.value&&(U.value=H,U.entries=W,N())},j=v("layout-shift",o);j&&(N=p(A,U,ee,B.reportAllChanges),g(function(){o(j.takeRecords()),N(!0)}),d(function(){H=0,N=p(A,U=m("CLS",0),ee,B.reportAllChanges),h(function(){return N()})}),setTimeout(N,0))}))}(function(B){!function(A){if(A.entries.length){var B,N=A.entries.reduce(function(A,B){return A&&A.value>B.value?A:B});if(N&&N.sources&&N.sources.length){var U=(B=N.sources).find(function(A){return A.node&&1===A.node.nodeType})||B[0];if(U)return void(A.attribution={largestShiftTarget:c(U.node),largestShiftTime:N.startTime,largestShiftValue:N.value,largestShiftSource:U,largestShiftEntry:N,loadState:o(N.startTime)})}}A.attribution={}}(B),A(B)},B)},I=function(A,B){x(function(B){!function(A){if(A.entries.length){var B=a(),N=A.entries[A.entries.length-1];if(B){var U=B.activationStart||0,H=Math.max(0,B.responseStart-U);return void(A.attribution={timeToFirstByte:H,firstByteToFCP:A.value-H,loadState:o(A.entries[0].startTime),navigationEntry:B,fcpEntry:N})}}A.attribution={timeToFirstByte:0,firstByteToFCP:A.value,loadState:o(f())}}(B),A(B)},B)},et=null,er=new Date,P=function(A,B){U||(U=B,H=A,W=new Date,q(removeEventListener),k())},k=function(){if(H>=0&&H<W-er){var A={entryType:"first-input",name:U.type,target:U.target,cancelable:U.cancelable,startTime:U.timeStamp,processingStart:U.timeStamp+H};j.forEach(function(B){B(A)}),j=[]}},R=function(A){if(A.cancelable){var B=(A.timeStamp>1e12?new Date:performance.now())-A.timeStamp;"pointerdown"==A.type?function(A,B){var n=function(){P(A,B),i()},r=function(){i()},i=function(){removeEventListener("pointerup",n,et),removeEventListener("pointercancel",r,et)};addEventListener("pointerup",n,et),addEventListener("pointercancel",r,et)}(B,A):P(B,A)}},q=function(A){["mousedown","keydown","touchstart","pointerdown"].forEach(function(B){return A(B,R,et)})},en=0,ei=1/0,eo=0,_=function(A){A.forEach(function(A){A.interactionId&&(ei=Math.min(ei,A.interactionId),en=(eo=Math.max(eo,A.interactionId))?(eo-ei)/7+1:0)})},z=function(){return V?en:performance.interactionCount||0},G=function(){"interactionCount"in performance||V||(V=v("event",_,{type:"event",buffered:!0,durationThreshold:0}))},ea=[200,500],es=0,Q=function(){return z()-es},eu=[],el={},Y=function(A){var B=eu[eu.length-1],N=el[A.interactionId];if(N||eu.length<10||A.duration>B.latency){if(N)N.entries.push(A),N.latency=Math.max(N.latency,A.duration);else{var U={id:A.interactionId,latency:A.duration,entries:[A]};el[U.id]=U,eu.push(U)}eu.sort(function(A,B){return B.latency-A.latency}),eu.splice(10).forEach(function(A){delete el[A.id]})}},Z=function(A,B){B=B||{},w(function(){G();var N,U=m("INP"),i=function(A){A.forEach(function(A){A.interactionId&&Y(A),"first-input"!==A.entryType||eu.some(function(B){return B.entries.some(function(B){return A.duration===B.duration&&A.startTime===B.startTime})})||Y(A)});var B,H=(B=Math.min(eu.length-1,Math.floor(Q()/50)),eu[B]);H&&H.latency!==U.value&&(U.value=H.latency,U.entries=H.entries,N())},H=v("event",i,{durationThreshold:B.durationThreshold||40});N=p(A,U,ea,B.reportAllChanges),H&&(H.observe({type:"first-input",buffered:!0}),g(function(){i(H.takeRecords()),U.value<0&&Q()>0&&(U.value=0,U.entries=[]),N(!0)}),d(function(){eu=[],es=z(),N=p(A,U=m("INP"),ea,B.reportAllChanges)}))})},$=function(A,B){Z(function(B){!function(A){if(A.entries.length){var B=A.entries.sort(function(A,B){return B.duration-A.duration||B.processingEnd-B.processingStart-(A.processingEnd-A.processingStart)})[0];A.attribution={eventTarget:c(B.target),eventType:B.name,eventTime:B.startTime,eventEntry:B,loadState:o(B.startTime)}}else A.attribution={}}(B),A(B)},B)},ec=[2500,4e3],ef={},ne=function(A,B){!function(A,B){B=B||{},w(function(){var N,U=C(),H=m("LCP"),a=function(A){var B=A[A.length-1];B&&B.startTime<U.firstHiddenTime&&(H.value=Math.max(B.startTime-l(),0),H.entries=[B],N())},W=v("largest-contentful-paint",a);if(W){N=p(A,H,ec,B.reportAllChanges);var j=T(function(){ef[H.id]||(a(W.takeRecords()),W.disconnect(),ef[H.id]=!0,N(!0))});["keydown","click"].forEach(function(A){addEventListener(A,j,!0)}),g(j),d(function(U){N=p(A,H=m("LCP"),ec,B.reportAllChanges),h(function(){H.value=performance.now()-U.timeStamp,ef[H.id]=!0,N(!0)})})}})}(function(B){!function(A){if(A.entries.length){var B=a();if(B){var N=B.activationStart||0,U=A.entries[A.entries.length-1],H=U.url&&performance.getEntriesByType("resource").filter(function(A){return A.name===U.url})[0],W=Math.max(0,B.responseStart-N),j=Math.max(W,H?(H.requestStart||H.startTime)-N:0),V=Math.max(j,H?H.responseEnd-N:0),K=Math.max(V,U?U.startTime-N:0),X={element:c(U.element),timeToFirstByte:W,resourceLoadDelay:j-W,resourceLoadTime:V-j,elementRenderDelay:K-V,navigationEntry:B,lcpEntry:U};return U.url&&(X.url=U.url),H&&(X.lcpResourceEntry=H),void(A.attribution=X)}}A.attribution={timeToFirstByte:0,resourceLoadDelay:0,resourceLoadTime:0,elementRenderDelay:A.value}}(B),A(B)},B)}},50037:function(A,B,N){"use strict";N.d(B,{Fu:function(){return x},mr:function(){return Z}});var U,H,W,j,V=-1,o=function(A){addEventListener("pageshow",function(B){B.persisted&&(V=B.timeStamp,A(B))},!0)},c=function(){return window.performance&&performance.getEntriesByType&&performance.getEntriesByType("navigation")[0]},u=function(){var A=c();return A&&A.activationStart||0},f=function(A,B){var N=c(),U="navigate";return V>=0?U="back-forward-cache":N&&(document.prerendering||u()>0?U="prerender":document.wasDiscarded?U="restore":N.type&&(U=N.type.replace(/_/g,"-"))),{name:A,value:void 0===B?-1:B,rating:"good",delta:0,entries:[],id:"v3-".concat(Date.now(),"-").concat(Math.floor(0x82f79cd8fff*Math.random())+1e12),navigationType:U}},s=function(A,B,N){try{if(PerformanceObserver.supportedEntryTypes.includes(A)){var U=new PerformanceObserver(function(A){Promise.resolve().then(function(){B(A.getEntries())})});return U.observe(Object.assign({type:A,buffered:!0},N||{})),U}}catch(A){}},d=function(A,B,N,U){var H,W;return function(j){B.value>=0&&(j||U)&&((W=B.value-(H||0))||void 0===H)&&(H=B.value,B.delta=W,B.rating=function(A,B){return A>B[1]?"poor":A>B[0]?"needs-improvement":"good"}(B.value,N),A(B))}},p=function(A){var n=function(B){"pagehide"!==B.type&&"hidden"!==document.visibilityState||A(B)};addEventListener("visibilitychange",n,!0),addEventListener("pagehide",n,!0)},v=function(A){var B=!1;return function(N){B||(A(N),B=!0)}},K=-1,h=function(){return"hidden"!==document.visibilityState||document.prerendering?1/0:0},g=function(A){"hidden"===document.visibilityState&&K>-1&&(K="visibilitychange"===A.type?A.timeStamp:0,T())},y=function(){addEventListener("visibilitychange",g,!0),addEventListener("prerenderingchange",g,!0)},T=function(){removeEventListener("visibilitychange",g,!0),removeEventListener("prerenderingchange",g,!0)},E=function(){return K<0&&(K=h(),y(),o(function(){setTimeout(function(){K=h(),y()},0)})),{get firstHiddenTime(){return K}}},C=function(A){document.prerendering?addEventListener("prerenderingchange",function(){return A()},!0):A()},X={passive:!0,capture:!0},J=new Date,P=function(A,B){U||(U=B,H=A,W=new Date,k(removeEventListener),F())},F=function(){if(H>=0&&H<W-J){var A={entryType:"first-input",name:U.type,target:U.target,cancelable:U.cancelable,startTime:U.timeStamp,processingStart:U.timeStamp+H};j.forEach(function(B){B(A)}),j=[]}},M=function(A){if(A.cancelable){var B=(A.timeStamp>1e12?new Date:performance.now())-A.timeStamp;"pointerdown"==A.type?function(A,B){var t=function(){P(A,B),i()},r=function(){i()},i=function(){removeEventListener("pointerup",t,X),removeEventListener("pointercancel",r,X)};addEventListener("pointerup",t,X),addEventListener("pointercancel",r,X)}(B,A):P(B,A)}},k=function(A){["mousedown","keydown","touchstart","pointerdown"].forEach(function(B){return A(B,M,X)})},ee=[100,300],x=function(A,B){B=B||{},C(function(){var N,W=E(),V=f("FID"),l=function(A){A.startTime<W.firstHiddenTime&&(V.value=A.processingStart-A.startTime,V.entries.push(A),N(!0))},m=function(A){A.forEach(l)},K=s("first-input",m);N=d(A,V,ee,B.reportAllChanges),K&&p(v(function(){m(K.takeRecords()),K.disconnect()})),K&&o(function(){var W;N=d(A,V=f("FID"),ee,B.reportAllChanges),j=[],H=-1,U=null,k(addEventListener),W=l,j.push(W),F()})})},et=[800,1800],Y=function e(A){document.prerendering?C(function(){return e(A)}):"complete"!==document.readyState?addEventListener("load",function(){return e(A)},!0):setTimeout(A,0)},Z=function(A,B){B=B||{};var N=f("TTFB"),U=d(A,N,et,B.reportAllChanges);Y(function(){var H=c();if(H){var W=H.responseStart;if(W<=0||W>performance.now())return;N.value=Math.max(W-u(),0),N.entries=[H],U(!0),o(function(){(U=d(A,N=f("TTFB",0),et,B.reportAllChanges))(!0)})}})}}}]);
//# sourceMappingURL=https://picasso-private-1251524319.cos.ap-shanghai.myqcloud.com/data/formula-static/formula/xhs-pc-web/vendor.621a7319.js.map