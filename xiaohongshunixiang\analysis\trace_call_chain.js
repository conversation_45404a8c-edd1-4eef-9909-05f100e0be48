// 🔍 追踪 x-s-common 生成的完整调用链

(() => {
    console.log('🚀 开始追踪 x-s-common 生成调用链...');
    
    // 调用链追踪器
    const callChain = [];
    let isTracing = false;
    
    // Hook 所有函数调用
    const hookAllFunctions = () => {
        // 获取所有全局函数
        const globalFunctions = [];
        
        for (let key in window) {
            try {
                if (typeof window[key] === 'function' && key !== 'hookAllFunctions') {
                    globalFunctions.push(key);
                }
            } catch (e) {}
        }
        
        console.log(`📊 找到 ${globalFunctions.length} 个全局函数`);
        
        // Hook 重要的函数
        const importantFunctions = globalFunctions.filter(name => 
            name.includes('webmsxyw') ||
            name.includes('sign') ||
            name.includes('encrypt') ||
            name.includes('encode') ||
            name.length === 1 || // 单字母函数名（通常是混淆后的）
            name.length === 2    // 双字母函数名
        );
        
        console.log('🎯 重要函数:', importantFunctions);
        
        importantFunctions.forEach(funcName => {
            try {
                const originalFunc = window[funcName];
                if (typeof originalFunc === 'function') {
                    window[funcName] = function(...args) {
                        if (isTracing) {
                            callChain.push({
                                function: funcName,
                                args: args.map(arg => {
                                    if (typeof arg === 'string' && arg.length > 100) {
                                        return arg.substring(0, 50) + '...';
                                    }
                                    return arg;
                                }),
                                timestamp: Date.now()
                            });
                            
                            console.log(`🔗 调用: ${funcName}`, args);
                        }
                        
                        return originalFunc.apply(this, args);
                    };
                }
            } catch (e) {
                console.log(`❌ Hook ${funcName} 失败:`, e.message);
            }
        });
    };
    
    // 开始追踪
    const startTracing = () => {
        console.log('🎬 开始追踪...');
        callChain.length = 0;
        isTracing = true;
        
        // 模拟一个 API 调用来触发签名生成
        setTimeout(() => {
            console.log('🔥 触发签名生成...');
            
            // 尝试调用 _webmsxyw
            if (typeof window._webmsxyw === 'function') {
                try {
                    const result = window._webmsxyw('/api/sns/web/v1/feed', {});
                    console.log('📤 _webmsxyw 结果:', result);
                } catch (e) {
                    console.log('❌ _webmsxyw 调用失败:', e.message);
                }
            }
            
            // 停止追踪
            setTimeout(() => {
                isTracing = false;
                console.log('🛑 追踪结束');
                console.log('📋 调用链:', callChain);
                
                // 分析调用链
                analyzeCallChain();
            }, 1000);
        }, 500);
    };
    
    // 分析调用链
    const analyzeCallChain = () => {
        console.log('🔍 分析调用链...');
        
        // 查找可能的签名生成函数
        const suspiciousCalls = callChain.filter(call => {
            const funcName = call.function;
            const args = call.args;
            
            // 查找处理字符串或对象的函数
            return args.some(arg => 
                (typeof arg === 'string' && arg.length > 10) ||
                (typeof arg === 'object' && arg !== null)
            ) || funcName.length <= 2; // 短函数名通常是混淆后的核心函数
        });
        
        console.log('🎯 可疑的签名生成调用:', suspiciousCalls);
        
        // 保存结果
        window.traceResults = {
            fullCallChain: callChain,
            suspiciousCalls: suspiciousCalls,
            timestamp: Date.now()
        };
        
        console.log('💾 追踪结果已保存到 window.traceResults');
    };
    
    // 执行 Hook
    hookAllFunctions();
    
    // 延迟开始追踪，让页面完全加载
    setTimeout(startTracing, 2000);
    
    return {
        message: '调用链追踪器已启动',
        status: 'waiting_for_page_load'
    };
})();
