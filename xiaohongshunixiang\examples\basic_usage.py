#!/usr/bin/env python3
"""
小红书 x-s-common 基础使用示例
"""

import requests
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class XhsSignatureClient:
    def __init__(self, server_url='http://localhost:5108'):
        self.server_url = server_url
        
    def check_server(self):
        """检查服务器状态"""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def generate_signature(self, uri, data=None):
        """生成签名"""
        try:
            response = requests.post(
                f"{self.server_url}/sign",
                json={
                    'uri': uri,
                    'data': data
                },
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"签名生成失败: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"签名生成异常: {e}")
            return None
    
    def make_request(self, uri, data=None, base_url='https://edith.xiaohongshu.com'):
        """发起带签名的请求"""
        # 生成签名
        signature = self.generate_signature(uri, data)
        if not signature:
            return None
        
        # 构建请求头
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Content-Type': 'application/json',
            'x-s': signature['x-s'],
            'x-t': signature['x-t'],
            'x-s-common': signature['x-s-common']
        }
        
        # 发起请求
        try:
            if data:
                response = requests.post(
                    f"{base_url}{uri}",
                    json=data,
                    headers=headers,
                    timeout=15
                )
            else:
                response = requests.get(
                    f"{base_url}{uri}",
                    headers=headers,
                    timeout=15
                )
            
            return response
            
        except Exception as e:
            print(f"请求失败: {e}")
            return None

def example_get_note():
    """示例：获取笔记详情"""
    print("📝 示例：获取笔记详情")
    print("-" * 40)
    
    client = XhsSignatureClient()
    
    # 检查服务器
    if not client.check_server():
        print("❌ 签名服务器不可用，请先启动服务器")
        print("   python tools/signature_server.py")
        return
    
    # 请求参数
    uri = '/api/sns/web/v1/feed'
    data = {
        'source_note_id': '683bc963000000002100e293',
        'image_formats': ['jpg', 'webp', 'avif'],
        'extra': {'need_body_topic': 1},
        'xsec_source': 'pc_feed',
        'xsec_token': 'ABxZkgpidfXAhaSNfetqgmAv9Qq2HsCQoJ_DNVQ3BZ7s0='
    }
    
    print(f"🔧 生成签名...")
    signature = client.generate_signature(uri, data)
    
    if signature:
        print(f"✅ 签名生成成功:")
        print(f"   x-s: {signature['x-s'][:30]}...")
        print(f"   x-t: {signature['x-t']}")
        print(f"   x-s-common: {signature['x-s-common'][:30]}...")
        
        print(f"\n📡 发起请求...")
        response = client.make_request(uri, data)
        
        if response:
            print(f"✅ 请求成功: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"📋 响应数据: {json.dumps(result, ensure_ascii=False, indent=2)[:200]}...")
                except:
                    print(f"📋 响应内容: {response.text[:200]}...")
            else:
                print(f"❌ 请求失败: {response.text[:200]}...")
        else:
            print(f"❌ 请求失败")
    else:
        print(f"❌ 签名生成失败")

def example_search():
    """示例：搜索笔记"""
    print("\n🔍 示例：搜索笔记")
    print("-" * 40)
    
    client = XhsSignatureClient()
    
    if not client.check_server():
        print("❌ 签名服务器不可用")
        return
    
    uri = '/api/sns/web/v1/search/notes'
    data = {
        'keyword': '美食',
        'page': 1,
        'page_size': 20,
        'search_id': '',
        'sort': 'general',
        'note_type': 0
    }
    
    print(f"🔧 生成搜索签名...")
    signature = client.generate_signature(uri, data)
    
    if signature:
        print(f"✅ 签名生成成功")
        print(f"📡 模拟搜索请求...")
        
        # 这里只是演示签名生成，实际请求可能需要更多参数
        print(f"   URI: {uri}")
        print(f"   关键词: {data['keyword']}")
        print(f"   签名: {signature['x-s'][:20]}...")
    else:
        print(f"❌ 搜索签名生成失败")

def example_batch_signature():
    """示例：批量生成签名"""
    print("\n📦 示例：批量生成签名")
    print("-" * 40)
    
    client = XhsSignatureClient()
    
    if not client.check_server():
        print("❌ 签名服务器不可用")
        return
    
    # 批量请求
    batch_requests = [
        {
            'uri': '/api/sns/web/v1/feed',
            'data': {'source_note_id': f'test_id_{i}'}
        }
        for i in range(3)
    ]
    
    try:
        response = requests.post(
            f"{client.server_url}/batch_sign",
            json={'requests': batch_requests},
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get('success'):
                print(f"✅ 批量签名成功: {result['count']} 个")
                
                for i, item in enumerate(result['results']):
                    print(f"   请求 {i+1}: {item['uri']}")
                    print(f"     x-s: {item['headers']['x-s'][:20]}...")
            else:
                print(f"❌ 批量签名失败: {result}")
        else:
            print(f"❌ 批量请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 批量签名异常: {e}")

def example_signature_validation():
    """示例：签名验证"""
    print("\n🔍 示例：签名验证")
    print("-" * 40)
    
    client = XhsSignatureClient()
    
    if not client.check_server():
        print("❌ 签名服务器不可用")
        return
    
    # 生成一个签名
    signature = client.generate_signature('/api/test', {'test': 'data'})
    
    if signature:
        xs_common = signature['x-s-common']
        print(f"🔧 验证签名: {xs_common[:30]}...")
        
        try:
            response = requests.post(
                f"{client.server_url}/validate",
                json={'x-s-common': xs_common},
                timeout=5
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result.get('valid'):
                    print(f"✅ 签名验证通过")
                    print(f"   解码内容: {result.get('decoded', '')[:50]}...")
                else:
                    print(f"❌ 签名验证失败: {result.get('error')}")
            else:
                print(f"❌ 验证请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 验证异常: {e}")
    else:
        print(f"❌ 无法生成测试签名")

def main():
    """主函数"""
    print("🚀 小红书 x-s-common 基础使用示例")
    print("=" * 60)
    
    # 运行示例
    example_get_note()
    example_search()
    example_batch_signature()
    example_signature_validation()
    
    print("\n" + "=" * 60)
    print("📋 示例完成")
    print("\n💡 使用说明:")
    print("1. 确保签名服务器运行在 http://localhost:5108")
    print("2. 根据实际需求修改请求参数")
    print("3. 注意请求频率，避免触发限制")
    print("4. 实际使用时需要有效的 Cookie")

if __name__ == '__main__':
    main()
