#!/usr/bin/env python3
"""
JavaScript文件爬取工具使用示例
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.js_crawler_gui import JSCrawlerGUI
import tkinter as tk


def main():
    """运行JavaScript爬取工具"""
    print("🔍 启动JavaScript文件爬取工具...")
    print("="*50)
    print("功能说明:")
    print("1. 输入目标网站URL")
    print("2. 配置爬取选项（深度、保存设置等）")
    print("3. 点击'开始爬取'按钮")
    print("4. 查看爬取日志和结果")
    print("5. 可选择导出结果到文件")
    print("="*50)
    
    # 创建GUI应用
    root = tk.Tk()
    app = JSCrawlerGUI(root)
    
    # 运行应用
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == '__main__':
    main()
