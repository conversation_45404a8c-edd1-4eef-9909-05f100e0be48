#!/usr/bin/env python3
"""
设备指纹分析工具
分析小红书设备指纹的特性和生成规律
"""

import base64
import json
import hashlib
import time
import re

class FingerprintAnalyzer:
    def __init__(self):
        # 我们已知的真实设备指纹
        self.real_fingerprint = "I38rHdgsjopgIvesdVwgIC+oIELmBZ5e3VwXLgFTIxS3bqwErFeexd0ekncAzMFYnqthIhJeSnMDKutRI3KsYorWHPtGrbV0P9WfIi/eWc6eYqtyQApPI37ekmR1QL+5Ii6sdneeSfqYHqwl2qt5B0DBIx+PGDi/sVtkIxdsxuwr4qtiIhuaIE3e3LV0I3VTIC7e0utl2ADmsLveDSKsSPw5IEvsiVtJOqw8BuwfPpdeTFWOIx4TIiu6ZPwrPut5IvlaLbgs3qtxIxes1VwHIkumIkIyejgsY/WTge7eSqte/D7sDcpipedeYrDtIC6eDVw2IENsSqtlnlSuNjVtIvoekqt3cZ7sVo4gIESyIhE2QfquIxhnqz8gIkIfoqwkICqWG73sdlOeVPw3IvAe0fgedfDQIi5s3MHM2utAIiKsidvekZNeTPt4nAOeWPwEIvT8zeveSVwAg9osfPwZI34rIxE5Luwwaqw+rekrPI5eDo/eVPwmIhJsSnAekmuvIiAsfI/sxBidIkve3PwlIhQk2VtqOqt1IxesTVtjIk0siqwdIh/sjut3wutnsPw5ICclI3l4wA4jwIAsWVw4IE4qIhOsSqtZBbTt/A0ejjp1IkGPGutPoqwhIvveVPtf+Dee3l5s1rELIE0s6edsiPtzcPwrICJefVwfIkgs60WrICKedo/eWVt3I37eVqwf8BYrIhQIIvKeVL3e60vejcge1qteIEqXICSEpPw8Ii+AIk6e1ImMJ7defVweIkPIgPwhOYNefW=="
        
    def analyze_fingerprint_structure(self):
        """分析指纹结构"""
        print("🔍 分析设备指纹结构")
        print("=" * 60)
        
        fingerprint = self.real_fingerprint
        
        print(f"📊 基本信息:")
        print(f"   长度: {len(fingerprint)} 字符")
        print(f"   开头: {fingerprint[:20]}...")
        print(f"   结尾: ...{fingerprint[-20:]}")
        
        # 分析字符集
        chars = set(fingerprint)
        print(f"\n🔤 字符集分析:")
        print(f"   唯一字符数: {len(chars)}")
        print(f"   字符集: {sorted(chars)}")
        
        # 检查是否是Base64
        import string
        base64_chars = set(string.ascii_letters + string.digits + '+/=')
        is_base64_like = chars.issubset(base64_chars)
        print(f"   疑似Base64: {'是' if is_base64_like else '否'}")
        
        # 尝试Base64解码
        if is_base64_like:
            try:
                decoded_bytes = base64.b64decode(fingerprint)
                print(f"\n📦 Base64解码:")
                print(f"   解码成功: 是")
                print(f"   解码后长度: {len(decoded_bytes)} 字节")
                print(f"   前20字节: {decoded_bytes[:20]}")
                print(f"   后20字节: {decoded_bytes[-20:]}")
                
                # 尝试解析为文本
                try:
                    decoded_text = decoded_bytes.decode('utf-8')
                    print(f"   UTF-8解码: 成功")
                    print(f"   文本内容: {decoded_text[:100]}...")
                except:
                    print(f"   UTF-8解码: 失败（可能是二进制数据）")
                    
            except Exception as e:
                print(f"   Base64解码: 失败 ({e})")
        
        return fingerprint
    
    def analyze_fingerprint_stability(self):
        """分析指纹稳定性"""
        print(f"\n🔒 设备指纹稳定性分析")
        print("=" * 60)
        
        # 计算指纹的哈希值
        fingerprint = self.real_fingerprint
        
        print(f"📈 稳定性指标:")
        
        # MD5哈希
        md5_hash = hashlib.md5(fingerprint.encode()).hexdigest()
        print(f"   MD5: {md5_hash}")
        
        # SHA256哈希
        sha256_hash = hashlib.sha256(fingerprint.encode()).hexdigest()
        print(f"   SHA256: {sha256_hash[:32]}...")
        
        # CRC32哈希
        import zlib
        crc32_hash = zlib.crc32(fingerprint.encode()) & 0xffffffff
        print(f"   CRC32: {crc32_hash}")
        
        print(f"\n💡 稳定性评估:")
        print(f"   ✅ 指纹长度固定: {len(fingerprint)} 字符")
        print(f"   ✅ 格式一致: Base64编码")
        print(f"   ✅ 内容复杂: 包含多种字符")
        print(f"   ⚠️  需要验证: 是否在不同时间/会话中保持一致")
        
        return {
            'md5': md5_hash,
            'sha256': sha256_hash,
            'crc32': crc32_hash,
            'length': len(fingerprint)
        }
    
    def test_fingerprint_variations(self):
        """测试指纹变化情况"""
        print(f"\n🧪 测试指纹变化情况")
        print("=" * 60)
        
        base_fingerprint = self.real_fingerprint
        
        print(f"🔬 变化测试:")
        
        # 测试1: 时间戳变化
        print(f"\n1️⃣ 时间戳相关测试:")
        current_time = int(time.time())
        print(f"   当前时间戳: {current_time}")
        
        # 检查指纹中是否包含时间戳
        time_patterns = [
            str(current_time),
            str(current_time - 3600),  # 1小时前
            str(current_time - 86400), # 1天前
        ]
        
        contains_timestamp = False
        for pattern in time_patterns:
            if pattern in base_fingerprint:
                print(f"   ⚠️  发现时间戳: {pattern}")
                contains_timestamp = True
        
        if not contains_timestamp:
            print(f"   ✅ 未发现明显的时间戳模式")
        
        # 测试2: 随机性分析
        print(f"\n2️⃣ 随机性分析:")
        
        # 分析字符分布
        char_counts = {}
        for char in base_fingerprint:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # 计算熵
        import math
        total_chars = len(base_fingerprint)
        entropy = 0
        for count in char_counts.values():
            probability = count / total_chars
            entropy -= probability * math.log2(probability)
        
        print(f"   信息熵: {entropy:.2f} bits")
        print(f"   字符分布均匀度: {'高' if entropy > 5.0 else '中' if entropy > 3.0 else '低'}")
        
        # 测试3: 重复模式
        print(f"\n3️⃣ 重复模式分析:")
        
        # 查找重复的子字符串
        repeated_patterns = []
        for length in [3, 4, 5, 6]:
            for i in range(len(base_fingerprint) - length):
                pattern = base_fingerprint[i:i+length]
                if base_fingerprint.count(pattern) > 1:
                    repeated_patterns.append((pattern, base_fingerprint.count(pattern)))
        
        if repeated_patterns:
            print(f"   发现重复模式:")
            for pattern, count in repeated_patterns[:5]:  # 只显示前5个
                print(f"     '{pattern}' 出现 {count} 次")
        else:
            print(f"   ✅ 未发现明显的重复模式")
        
        return {
            'entropy': entropy,
            'contains_timestamp': contains_timestamp,
            'repeated_patterns': repeated_patterns
        }
    
    def generate_fingerprint_recommendations(self):
        """生成指纹使用建议"""
        print(f"\n💡 设备指纹使用建议")
        print("=" * 60)
        
        print(f"🎯 关键发现:")
        print(f"   1. 设备指纹长度: {len(self.real_fingerprint)} 字符")
        print(f"   2. 编码格式: Base64")
        print(f"   3. 内容复杂度: 高")
        
        print(f"\n📋 使用策略:")
        print(f"   ✅ 推荐: 使用固定的真实设备指纹")
        print(f"   ✅ 原因: 已验证可以成功通过API验证")
        print(f"   ✅ 稳定性: 相对稳定，适合长期使用")
        
        print(f"\n⚠️  注意事项:")
        print(f"   1. 定期验证指纹有效性")
        print(f"   2. 监控API响应，及时发现指纹失效")
        print(f"   3. 准备备用指纹策略")
        
        print(f"\n🔄 更新策略:")
        print(f"   • 保守策略: 长期使用同一指纹")
        print(f"   • 积极策略: 定期更新指纹（需要重新验证）")
        print(f"   • 混合策略: 主要使用固定指纹，备用动态生成")
        
        return {
            'recommendation': 'use_fixed_real_fingerprint',
            'stability': 'high',
            'update_frequency': 'low'
        }

def main():
    """主函数"""
    print("🎯 小红书设备指纹分析")
    print("=" * 80)
    
    analyzer = FingerprintAnalyzer()
    
    # 1. 分析指纹结构
    analyzer.analyze_fingerprint_structure()
    
    # 2. 分析稳定性
    stability_info = analyzer.analyze_fingerprint_stability()
    
    # 3. 测试变化情况
    variation_info = analyzer.test_fingerprint_variations()
    
    # 4. 生成建议
    recommendations = analyzer.generate_fingerprint_recommendations()
    
    print(f"\n🎉 分析完成!")
    print(f"建议: {recommendations['recommendation']}")
    print(f"稳定性: {recommendations['stability']}")
    print(f"更新频率: {recommendations['update_frequency']}")

if __name__ == '__main__':
    main()
