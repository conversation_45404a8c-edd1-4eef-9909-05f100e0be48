#!/usr/bin/env python3
"""
JavaScript文件爬取工具 - GUI版本
爬取指定URL的所有JavaScript文件
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import requests
import os
import threading
from datetime import datetime
import urllib3
from urllib.parse import urljoin, urlparse, unquote
import re
from bs4 import BeautifulSoup
import json

# 禁用 SSL 警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class JSCrawlerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 JavaScript文件爬取工具")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)
        
        # 配置变量
        self.ssl_verify = tk.BooleanVar(value=False)
        self.use_proxy = tk.BooleanVar(value=False)
        self.proxy_url = tk.StringVar(value="http://127.0.0.1:8080")
        self.save_files = tk.BooleanVar(value=True)
        self.max_depth = tk.IntVar(value=1)
        self.follow_redirects = tk.BooleanVar(value=True)
        
        # 数据存储
        self.js_files = []
        self.crawl_stats = {
            'total_found': 0,
            'downloaded': 0,
            'failed': 0
        }
        
        self.setup_ui()
        
        # 默认URL示例
        self.url_entry.insert(0, "https://www.xiaohongshu.com")

    def get_request_config(self):
        """获取请求配置"""
        config = {
            'verify': self.ssl_verify.get(),
            'timeout': 30,
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        }
        
        if self.use_proxy.get() and self.proxy_url.get():
            proxy = self.proxy_url.get()
            config['proxies'] = {
                'http': proxy,
                'https': proxy
            }
        
        return config

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🔍 JavaScript文件爬取工具", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # URL输入区域
        ttk.Label(main_frame, text="目标URL:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.url_entry = ttk.Entry(main_frame, width=80)
        self.url_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5, padx=(5, 0))
        
        # 爬取选项区域
        options_frame = ttk.LabelFrame(main_frame, text="🔧 爬取选项", padding="5")
        options_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 第一行选项
        options_row1 = ttk.Frame(options_frame)
        options_row1.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(options_row1, text="保存JS文件到本地", variable=self.save_files).pack(side=tk.LEFT)
        ttk.Checkbutton(options_row1, text="跟随重定向", variable=self.follow_redirects).pack(side=tk.LEFT, padx=(20, 0))
        
        # 第二行选项
        options_row2 = ttk.Frame(options_frame)
        options_row2.pack(fill=tk.X)
        
        ttk.Label(options_row2, text="爬取深度:").pack(side=tk.LEFT)
        depth_spinbox = ttk.Spinbox(options_row2, from_=1, to=3, width=5, textvariable=self.max_depth)
        depth_spinbox.pack(side=tk.LEFT, padx=(5, 20))
        
        # 网络配置区域
        config_frame = ttk.LabelFrame(main_frame, text="🌐 网络配置", padding="5")
        config_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # SSL 验证选项
        ssl_frame = ttk.Frame(config_frame)
        ssl_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Checkbutton(ssl_frame, text="启用SSL证书验证", variable=self.ssl_verify).pack(side=tk.LEFT)
        ttk.Label(ssl_frame, text="(关闭此选项可解决代理SSL问题)", foreground="gray").pack(side=tk.LEFT, padx=(10, 0))
        
        # 代理配置
        proxy_frame = ttk.Frame(config_frame)
        proxy_frame.pack(fill=tk.X)
        
        ttk.Checkbutton(proxy_frame, text="使用代理:", variable=self.use_proxy).pack(side=tk.LEFT)
        proxy_entry = ttk.Entry(proxy_frame, textvariable=self.proxy_url, width=30)
        proxy_entry.pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)
        
        self.crawl_btn = ttk.Button(button_frame, text="🚀 开始爬取", 
                                   command=self.start_crawling)
        self.crawl_btn.pack(side=tk.LEFT, padx=5)
        
        self.stop_btn = ttk.Button(button_frame, text="⏹️ 停止爬取", 
                                  command=self.stop_crawling, state='disabled')
        self.stop_btn.pack(side=tk.LEFT, padx=5)
        
        self.clear_btn = ttk.Button(button_frame, text="🗑️ 清空日志", 
                                   command=self.clear_log)
        self.clear_btn.pack(side=tk.LEFT, padx=5)
        
        self.export_btn = ttk.Button(button_frame, text="💾 导出结果", 
                                    command=self.export_results)
        self.export_btn.pack(side=tk.LEFT, padx=5)
        
        self.choose_dir_btn = ttk.Button(button_frame, text="📁 选择保存目录", 
                                        command=self.choose_save_directory)
        self.choose_dir_btn.pack(side=tk.LEFT, padx=5)
        
        # 统计信息
        stats_frame = ttk.Frame(main_frame)
        stats_frame.grid(row=5, column=0, columnspan=3, pady=5)
        
        self.stats_label = ttk.Label(stats_frame, text="📊 统计: 找到 0 个JS文件 | 下载 0 个 | 失败 0 个")
        self.stats_label.pack()
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="📝 爬取日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=20, width=120)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        
        # 保存目录
        self.save_directory = os.path.join(os.getcwd(), "js_files")
        
        # 停止标志
        self.stop_flag = False

    def log(self, message, level="INFO"):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        level_emoji = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌",
            "DEBUG": "🔍"
        }
        emoji = level_emoji.get(level, "ℹ️")
        self.log_text.insert(tk.END, f"[{timestamp}] {emoji} {message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def update_stats(self):
        """更新统计信息"""
        stats_text = f"📊 统计: 找到 {self.crawl_stats['total_found']} 个JS文件 | 下载 {self.crawl_stats['downloaded']} 个 | 失败 {self.crawl_stats['failed']} 个"
        self.stats_label.config(text=stats_text)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        self.js_files.clear()
        self.crawl_stats = {'total_found': 0, 'downloaded': 0, 'failed': 0}
        self.update_stats()

    def choose_save_directory(self):
        """选择保存目录"""
        directory = filedialog.askdirectory(title="选择JS文件保存目录")
        if directory:
            self.save_directory = directory
            self.log(f"保存目录设置为: {directory}")

    def stop_crawling(self):
        """停止爬取"""
        self.stop_flag = True
        self.log("用户请求停止爬取...", "WARNING")

    def start_crawling(self):
        """开始爬取"""
        url = self.url_entry.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入目标URL")
            return
            
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_entry.delete(0, tk.END)
            self.url_entry.insert(0, url)
        
        self.stop_flag = False
        self.crawl_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.progress.start()
        
        # 清空之前的结果
        self.js_files.clear()
        self.crawl_stats = {'total_found': 0, 'downloaded': 0, 'failed': 0}
        
        def crawl():
            try:
                self.log("🚀 开始JavaScript文件爬取...")
                self.log("="*80)
                self.log(f"目标URL: {url}")
                self.log(f"爬取深度: {self.max_depth.get()}")
                self.log(f"保存文件: {'是' if self.save_files.get() else '否'}")
                
                if self.save_files.get():
                    os.makedirs(self.save_directory, exist_ok=True)
                    self.log(f"保存目录: {self.save_directory}")
                
                # 开始爬取
                self.crawl_js_files(url, depth=0)
                
                if not self.stop_flag:
                    self.log("🎉 爬取完成！", "SUCCESS")
                    messagebox.showinfo("完成", f"爬取完成！\n找到 {self.crawl_stats['total_found']} 个JS文件")
                else:
                    self.log("⏹️ 爬取已停止", "WARNING")
                    
            except Exception as e:
                self.log(f"爬取过程出错: {e}", "ERROR")
                messagebox.showerror("错误", f"爬取过程出错: {e}")
            finally:
                self.root.after(0, lambda: [
                    self.progress.stop(),
                    self.crawl_btn.config(state='normal'),
                    self.stop_btn.config(state='disabled')
                ])
                
        threading.Thread(target=crawl, daemon=True).start()

    def crawl_js_files(self, url, depth=0):
        """爬取JavaScript文件"""
        if self.stop_flag or depth > self.max_depth.get():
            return

        try:
            self.log(f"🔍 正在分析页面: {url} (深度: {depth})")

            config = self.get_request_config()
            response = requests.get(url, **config)

            if response.status_code != 200:
                self.log(f"页面访问失败: {response.status_code}", "ERROR")
                return

            # 解析HTML内容
            soup = BeautifulSoup(response.text, 'html.parser')
            base_url = response.url

            # 查找所有JavaScript文件
            js_urls = set()

            # 1. 查找 <script src="..."> 标签
            for script in soup.find_all('script', src=True):
                src = script.get('src')
                if src:
                    full_url = urljoin(base_url, src)
                    js_urls.add(full_url)

            # 2. 查找内联JavaScript中的动态加载
            for script in soup.find_all('script'):
                if script.string:
                    # 查找可能的JS文件URL模式
                    js_patterns = [
                        r'["\']([^"\']*\.js(?:\?[^"\']*)?)["\']',
                        r'import\s*\(\s*["\']([^"\']*\.js(?:\?[^"\']*)?)["\']',
                        r'require\s*\(\s*["\']([^"\']*\.js(?:\?[^"\']*)?)["\']'
                    ]

                    for pattern in js_patterns:
                        matches = re.findall(pattern, script.string)
                        for match in matches:
                            if match.startswith(('http://', 'https://', '//')):
                                js_urls.add(match)
                            elif match.startswith('/'):
                                js_urls.add(urljoin(base_url, match))

            # 3. 查找CSS文件中可能引用的JS文件
            for link in soup.find_all('link', rel='stylesheet', href=True):
                css_url = urljoin(base_url, link.get('href'))
                try:
                    css_response = requests.get(css_url, **config)
                    if css_response.status_code == 200:
                        # 在CSS中查找JS文件引用
                        js_in_css = re.findall(r'url\s*\(\s*["\']?([^"\']*\.js(?:\?[^"\']*)?)["\']?\s*\)', css_response.text)
                        for js_file in js_in_css:
                            js_urls.add(urljoin(css_url, js_file))
                except:
                    pass

            # 处理找到的JS文件
            for js_url in js_urls:
                if self.stop_flag:
                    break

                self.process_js_file(js_url)

            # 如果需要深度爬取，查找页面中的链接
            if depth < self.max_depth.get():
                links = set()
                for link in soup.find_all('a', href=True):
                    href = link.get('href')
                    if href and not href.startswith(('#', 'javascript:', 'mailto:')):
                        full_link = urljoin(base_url, href)
                        # 只爬取同域名的链接
                        if urlparse(full_link).netloc == urlparse(base_url).netloc:
                            links.add(full_link)

                # 限制链接数量，避免无限爬取
                for link in list(links)[:10]:  # 最多爬取10个子页面
                    if self.stop_flag:
                        break
                    self.crawl_js_files(link, depth + 1)

        except Exception as e:
            self.log(f"分析页面时出错: {e}", "ERROR")

    def process_js_file(self, js_url):
        """处理单个JavaScript文件"""
        try:
            # 检查是否已经处理过
            if any(js['url'] == js_url for js in self.js_files):
                return

            self.log(f"📄 发现JS文件: {js_url}")

            # 获取文件信息
            config = self.get_request_config()
            head_response = requests.head(js_url, **config)

            file_info = {
                'url': js_url,
                'filename': self.get_filename_from_url(js_url),
                'size': head_response.headers.get('content-length', 'Unknown'),
                'content_type': head_response.headers.get('content-type', 'Unknown'),
                'status': 'Found',
                'local_path': None
            }

            # 如果需要保存文件
            if self.save_files.get() and head_response.status_code == 200:
                try:
                    # 下载文件内容
                    response = requests.get(js_url, **config)
                    if response.status_code == 200:
                        # 保存文件
                        local_path = os.path.join(self.save_directory, file_info['filename'])

                        # 确保文件名唯一
                        counter = 1
                        original_path = local_path
                        while os.path.exists(local_path):
                            name, ext = os.path.splitext(original_path)
                            local_path = f"{name}_{counter}{ext}"
                            counter += 1

                        with open(local_path, 'w', encoding='utf-8', errors='ignore') as f:
                            f.write(response.text)

                        file_info['local_path'] = local_path
                        file_info['status'] = 'Downloaded'
                        self.crawl_stats['downloaded'] += 1
                        self.log(f"✅ 已保存: {file_info['filename']}", "SUCCESS")
                    else:
                        file_info['status'] = f'Download Failed ({response.status_code})'
                        self.crawl_stats['failed'] += 1
                        self.log(f"❌ 下载失败: {js_url} ({response.status_code})", "ERROR")

                except Exception as e:
                    file_info['status'] = f'Error: {str(e)}'
                    self.crawl_stats['failed'] += 1
                    self.log(f"❌ 处理文件时出错: {e}", "ERROR")

            self.js_files.append(file_info)
            self.crawl_stats['total_found'] += 1
            self.update_stats()

        except Exception as e:
            self.log(f"处理JS文件时出错: {e}", "ERROR")

    def get_filename_from_url(self, url):
        """从URL提取文件名"""
        try:
            # 解析URL
            parsed = urlparse(url)
            path = unquote(parsed.path)

            # 提取文件名
            filename = os.path.basename(path)

            # 如果没有文件名或不是JS文件，生成一个
            if not filename or not filename.endswith('.js'):
                # 使用URL的hash作为文件名
                url_hash = str(hash(url))[-8:]
                filename = f"script_{url_hash}.js"

            # 清理文件名中的非法字符
            filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

            return filename

        except Exception:
            # 如果解析失败，生成随机文件名
            import random
            return f"script_{random.randint(1000, 9999)}.js"

    def export_results(self):
        """导出爬取结果"""
        if not self.js_files:
            messagebox.showwarning("警告", "没有可导出的结果")
            return

        try:
            # 选择保存文件
            filename = filedialog.asksaveasfilename(
                title="保存爬取结果",
                defaultextension=".json",
                filetypes=[("JSON文件", "*.json"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if filename:
                if filename.endswith('.json'):
                    # 导出为JSON格式
                    export_data = {
                        'crawl_time': datetime.now().isoformat(),
                        'target_url': self.url_entry.get(),
                        'statistics': self.crawl_stats,
                        'js_files': self.js_files
                    }

                    with open(filename, 'w', encoding='utf-8') as f:
                        json.dump(export_data, f, indent=2, ensure_ascii=False)

                else:
                    # 导出为文本格式
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(f"JavaScript文件爬取结果\n")
                        f.write(f"爬取时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                        f.write(f"目标URL: {self.url_entry.get()}\n")
                        f.write(f"统计信息: 找到 {self.crawl_stats['total_found']} 个文件\n")
                        f.write("="*80 + "\n\n")

                        for i, js_file in enumerate(self.js_files, 1):
                            f.write(f"{i}. {js_file['filename']}\n")
                            f.write(f"   URL: {js_file['url']}\n")
                            f.write(f"   大小: {js_file['size']}\n")
                            f.write(f"   状态: {js_file['status']}\n")
                            if js_file['local_path']:
                                f.write(f"   本地路径: {js_file['local_path']}\n")
                            f.write("\n")

                self.log(f"✅ 结果已导出到: {filename}", "SUCCESS")
                messagebox.showinfo("成功", f"结果已导出到:\n{filename}")

        except Exception as e:
            self.log(f"导出结果时出错: {e}", "ERROR")
            messagebox.showerror("错误", f"导出失败: {e}")


def main():
    """主函数"""
    root = tk.Tk()
    app = JSCrawlerGUI(root)
    root.mainloop()


if __name__ == '__main__':
    main()
