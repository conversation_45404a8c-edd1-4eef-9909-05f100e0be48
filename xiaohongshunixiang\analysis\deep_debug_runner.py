#!/usr/bin/env python3
"""
深度调试脚本运行器 - 自动执行 deep_debug_script.js
"""

import asyncio
import json
import time
import os
from playwright.async_api import async_playwright

class DeepDebugRunner:
    def __init__(self):
        self.browser = None
        self.page = None
        self.playwright = None
        
    async def init(self):
        """初始化浏览器"""
        print("🚀 启动深度调试环境...")
        
        try:
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                headless=False,  # 显示浏览器窗口
                devtools=True,   # 自动打开开发者工具
                args=[
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--no-sandbox',
                    '--disable-dev-shm-usage'
                ]
            )
            
            self.page = await self.browser.new_page()
            
            # 设置用户代理
            await self.page.set_extra_http_headers({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            })
            
            print("✅ 浏览器环境初始化完成")
            
        except Exception as e:
            print(f"❌ 浏览器初始化失败: {e}")
            raise
            
    async def load_debug_script(self):
        """加载深度调试脚本"""
        script_path = "xiaohongshunixiang/analysis/deep_debug_script.js"
        
        if not os.path.exists(script_path):
            print(f"❌ 找不到调试脚本: {script_path}")
            return None
            
        with open(script_path, 'r', encoding='utf-8') as f:
            script_content = f.read()
            
        print(f"📜 已加载调试脚本: {script_path}")
        return script_content
        
    async def run_deep_debug(self):
        """运行深度调试"""
        print("\n🔍 开始深度调试...")
        
        # 访问小红书
        print("📱 访问小红书页面...")
        await self.page.goto('https://www.xiaohongshu.com/explore', wait_until='networkidle')
        await asyncio.sleep(3)
        
        # 加载并执行调试脚本
        debug_script = await self.load_debug_script()
        if not debug_script:
            return
            
        print("🚀 执行深度调试脚本...")
        
        try:
            # 执行调试脚本
            result = await self.page.evaluate(debug_script)
            print(f"📊 调试脚本执行结果: {result}")

            # 等待一段时间让 Hook 生效
            await asyncio.sleep(2)

            print("✅ 调试脚本已成功注入并运行")
            print("� Hook 已设置，现在可以手动触发 API 请求来观察结果")

        except Exception as e:
            print(f"⚠️ 获取调试结果时出现问题: {e}")
            print("✅ 但调试脚本可能已经成功注入，请查看浏览器控制台")
            
    def display_results_summary(self, results):
        """显示结果摘要"""
        print("\n📋 调试结果摘要:")
        
        algorithms = results.get('algorithms', [])
        if algorithms:
            print(f"\n🔧 找到 {len(algorithms)} 个潜在算法函数:")
            for i, alg in enumerate(algorithms[:5]):  # 只显示前5个
                print(f"   {i+1}. {alg['name']}")
                
        script_code = results.get('scriptCode', [])
        if script_code:
            print(f"\n📜 找到 {len(script_code)} 个相关代码段:")
            for i, code in enumerate(script_code[:5]):  # 只显示前5个
                print(f"   {i+1}. 脚本 {code['scriptIndex']}: {code['code'][:50]}...")
                
    async def interactive_debug(self):
        """交互式调试"""
        print("\n🎮 进入交互式调试模式...")
        print("浏览器将保持打开状态，您可以:")
        print("1. 在开发者工具中查看 Hook 输出")
        print("2. 手动触发 API 请求")
        print("3. 输入命令进行操作")
        print("\n可用命令:")
        print("  test    - 测试 _webmsxyw 函数")
        print("  results - 获取最新调试结果")
        print("  save    - 保存当前结果")
        print("  quit    - 退出")
        
        while True:
            try:
                command = input("\n> ").strip().lower()
                
                if command == 'quit':
                    break
                elif command == 'test':
                    await self.test_webmsxyw()
                elif command == 'results':
                    await self.get_latest_results()
                elif command == 'save':
                    await self.save_current_results()
                else:
                    print("未知命令。可用命令: test, results, save, quit")
                    
            except KeyboardInterrupt:
                print("\n👋 用户中断，退出调试...")
                break
            except Exception as e:
                print(f"❌ 命令执行失败: {e}")
                
    async def test_webmsxyw(self):
        """测试 _webmsxyw 函数"""
        print("🧪 测试 _webmsxyw 函数...")
        
        test_result = await self.page.evaluate("""
            (() => {
                if (typeof window._webmsxyw === 'function') {
                    try {
                        console.log('🧪 开始测试 _webmsxyw...');
                        const result = window._webmsxyw('/api/sns/web/v1/feed', {test: 'data'});
                        console.log('🧪 测试结果:', result);
                        return result;
                    } catch (e) {
                        console.log('❌ 测试失败:', e);
                        return {error: e.message};
                    }
                }
                return {error: '_webmsxyw not found'};
            })();
        """)
        
        print(f"📊 测试结果: {json.dumps(test_result, ensure_ascii=False, indent=2)}")
        
    async def get_latest_results(self):
        """获取最新调试结果"""
        print("📊 获取最新调试结果...")
        
        results = await self.page.evaluate("window.deepDebugResults || null")
        if results:
            self.display_results_summary(results)
        else:
            print("⚠️ 暂无调试结果")
            
    async def save_current_results(self):
        """保存当前结果"""
        print("💾 保存当前调试结果...")
        
        results = await self.page.evaluate("window.deepDebugResults || null")
        if results:
            timestamp = int(time.time())
            output_file = f"xiaohongshunixiang/output/debug_results_manual_{timestamp}.json"
            
            os.makedirs("xiaohongshunixiang/output", exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
                
            print(f"✅ 结果已保存到: {output_file}")
        else:
            print("⚠️ 暂无结果可保存")
            
    async def cleanup(self):
        """清理资源"""
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()

async def main():
    """主函数"""
    runner = DeepDebugRunner()
    
    try:
        await runner.init()
        await runner.run_deep_debug()
        await runner.interactive_debug()
    finally:
        await runner.cleanup()

if __name__ == '__main__':
    print("🔍 深度调试脚本运行器")
    print("=" * 50)
    asyncio.run(main())
