{"functions": [{"name": "getUserInfoPromise", "content": "function getUserInfoPromise(){if(s)return s;var e=getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}", "keyword": "hash"}, {"name": "ShadowPerformanceEntry", "content": "function ShadowPerformanceEntry(e,r,i,a){this.name=e,this.duration=a,this.entryType=r,this.startTime=i}", "keyword": "sha"}, {"name": "HttpTracker", "content": "function HttpTracker(){(0,f._)(this,HttpTracker),this.entryHash={}", "keyword": "hash"}, {"name": "resetEntryHash", "content": "function resetEntryHash(){this.entryHash={}", "keyword": "hash"}, {"name": "track", "content": "function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,g._)({}", "keyword": "hash"}, {"name": "measure", "content": "function measure(e){var r=this.entryHash[e];if(!r)return{}", "keyword": "hash"}, {"name": "getResourceTimingData", "content": "function getResourceTimingData(e){var r=[];return e.forEach(function(e){if(M.includes(e.initiatorType)){var i=Math.round(e.domainLookupEnd-e.domainLookupStart)||0,a=Math.round(e.connectEnd-e.connectStart)||0,s=Math.round(e.responseEnd-e.requestStart)||0,u=Math.round(e.responseEnd-e.responseStart)||0,c=Math.round(e.redirectEnd-e.redirectStart)||0,l=Math.round(e.responseStart-e.requestStart)||0,d=e.duration||Math.round(e.responseEnd-e.startTime)||0,p=0===e.transferSize?eG.webViewStrongCache:0!==e.transferSize&&0===e.encodedBodySize?eG.webViewNeogationCache:eG.webViewRequest,f=+!![eG.webViewStrongCache,eG.webViewNeogationCache].includes(p);r.push({dnsTime:i,tcpTime:a,responseTime:s,contentTime:u,redirectTime:c,serverTime:l,collectTime:String(Date.now()),name:e.name,encodedBodySize:e.encodedBodySize,isHitCache:f,hitCacheType:p,initiatorType:e.initiatorType,duration:d,transferSize:e.transferSize||0}", "keyword": "encode"}, {"name": "InteractLaggyObserver", "content": "function InteractLaggyObserver(e,r){(0,f._)(this,InteractLaggyObserver),(0,h._)(this,\"_state\",void 0),(0,h._)(this,\"_callback\",void 0),(0,h._)(this,\"_observeOptions\",void 0),(0,h._)(this,\"_entries\",[]),(0,h._)(this,\"_interactEventsHandler\",void 0),(0,h._)(this,\"getFps\",void 0),this._state=\"initialization\",this._callback=e,this._interactEventsHandler=debounce(this.handleInteractEvent.bind(this),200,!0),this.getFps=r}", "keyword": "sha"}, {"name": "startListenInteractEvent", "content": "function startListenInteractEvent(){var e=this,r=this.getObserveOptions();r&&r.interactionEventCollectType.forEach(function(r){window.addEventListener(r,e._interactEventsHandler,{capture:!0}", "keyword": "sha"}, {"name": "removeInteractEventListener", "content": "function removeInteractEventListener(){var e=this,r=this.getObserveOptions();r&&r.interactionEventCollectType.forEach(function(r){window.removeEventListener(r,e._interactEventsHandler,!0)}", "keyword": "sha"}, {"name": "assignValue", "content": "function assignValue(e,r){\"object\"===(0,d._)(a[r])&&(void 0===e?\"undefined\":(0,d._)(e))===\"object\"?a[r]=merge(a[r],e):a[r]=e}", "keyword": "sign"}, {"name": "encryptToken", "content": "function encryptToken(e,r,i=\"X\"){let{url:a=\"\"}", "keyword": "encrypt"}, {"name": "spamNeedReload", "content": "function spamNeedReload(e){var r=!1;return!window.spamTimer&&(window.spamTimer=window.setTimeout(function(){window.sessionStorage.removeItem(\"spam_font_counter\"),window.sessionStorage.removeItem(\"spam_sign_counter\")}", "keyword": "sign"}, {"name": "crc32", "content": "function crc32(e){for(var r,i=[],a=0;a<256;a++){r=a;for(var s=0;s<8;s++)r=1&r?0xedb88320^r>>>1:r>>>1;i[a]=r}", "keyword": "crc"}, {"name": "tripletToBase64", "content": "function tripletToBase64(e){return P[e>>18&63]+P[e>>12&63]+P[e>>6&63]+P[63&e]}", "keyword": "base64"}, {"name": "encodeChunk", "content": "function encodeChunk(e,r,i){for(var a,s=[],u=r;u<i;u+=3)a=(e[u]<<16&0xff0000)+(e[u+1]<<8&65280)+(255&e[u+2]),s.push(tripletToBase64(a));return s.join(\"\")}", "keyword": "encode"}, {"name": "encodeUtf8", "content": "function encodeUtf8(e){for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if(\"%\"===s){var u=parseInt(r.charAt(a+1)+r.charAt(a+2),16);i.push(u),a+=2}", "keyword": "encode"}, {"name": "b64Encode", "content": "function b64Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+16383));return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+\"==\")):2===a&&(r=(e[i-2]<<8)+e[i-1],s.push(P[r>>10]+P[r>>4&63]+P[r<<2&63]+\"=\")),s.join(\"\")}", "keyword": "encode"}, {"name": "stringToBytes", "content": "function stringToBytes(e){return i.bin.stringToBytes(unescape(encodeURIComponent(e)))}", "keyword": "encode"}, {"name": "bytesToString", "content": "function bytesToString(e){return decodeURIComponent(escape(i.bin.bytesToString(e)))}", "keyword": "decode"}, {"name": "bytesToBase64", "content": "function bytesToBase64(e){for(var r=[],a=0;a<e.length;a+=3)for(var s=e[a]<<16|e[a+1]<<8|e[a+2],u=0;u<4;u++)8*a+6*u<=8*e.length?r.push(i.charAt(s>>>6*(3-u)&63)):r.push(\"=\");return r.join(\"\")}", "keyword": "base64"}, {"name": "base64ToBytes", "content": "function base64ToBytes(e){e=e.replace(/[^A-Z0-9+\\/]/gi,\"\");for(var r=[],a=0,s=0;a<e.length;s=++a%4)0!=s&&r.push((i.indexOf(e.charAt(a-1))&Math.pow(2,-2*s+8)-1)<<2*s|i.indexOf(e.charAt(a))>>>6-2*s);return r}", "keyword": "base64"}, {"name": "encrypt_sign", "content": "function encrypt_sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\\r\\n/g,\"\\n\");for(var r=\"\",i=0;i<e.length;i++){var a=e.charCodeAt(i);a<128?r+=String.fromCharCode(a):(a>127&&a<2048?r+=String.fromCharCode(a>>6|192):(r+=String.fromCharCode(a>>12|224),r+=String.fromCharCode(a>>6&63|128)),r+=String.fromCharCode(63&a|128))}", "keyword": "sign"}, {"name": "encode", "content": "function encode(e){var r,i,s,u,c,l,d,p=\"\",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),i=e.charCodeAt(f++),s=e.charCodeAt(f++),u=r>>2,c=(3&r)<<4|i>>4,l=(15&i)<<2|s>>6,d=63&s,isNaN(i)?l=d=64:isNaN(s)&&(d=64),p=p+a.charAt(u)+a.charAt(c)+a.charAt(l)+a.charAt(d);return p}", "keyword": "encode"}, {"name": "utils_shouldSign", "content": "function utils_shouldSign(e){var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf(\"sit.xiaohongshu.com\")>-1?r:(g.some(function(i){if(e.indexOf(i)>-1)return r=!1,!0}", "keyword": "sign"}, {"name": "SignReload", "content": "function SignReload(){this.count=1,this.time=+new Date}", "keyword": "sign"}, {"name": "shouldSignReload", "content": "function shouldSignReload(){try{var e=+new Date,r=JSON.stringify(localStorage.getItem(T)||{}", "keyword": "sign"}, {"name": "generateLocalId", "content": "function generateLocalId(e){var r=getPlatformCode(e),i=\"\".concat((+new Date).toString(16)).concat(genRandomString(30)).concat(r).concat(\"0\").concat(\"000\"),a=encrypt_crc32(i);return\"\".concat(i).concat(a).substring(0,52)}", "keyword": "encrypt"}, {"name": "tokenCheck", "content": "function tokenCheck(e){return token_awaiter(this,void 0,void 0,function(){return token_generator(this,function(r){switch(r.label){case 0:return[4,updateSign(e)];case 1:return r.sent(),[2]}", "keyword": "sign"}, {"name": "updateSign", "content": "function updateSign(e){return token_awaiter(this,void 0,void 0,function(){var r,i,a,s,u,c,d,v;return token_generator(this,function(v){switch(v.label){case 0:if(r=6e4,i=e.isHidden,a=e.callFrom,s=token_shouldUpdate(),!(\"visible\"===i&&s))return[3,5];v.label=1;case 1:return v.trys.push([1,3,,4]),u=\"seccallback\",c=\"\",window[u]=function(e){l.Z.set(p,e,{domain:\"xiaohongshu.com\",expires:3}", "keyword": "sign"}, {"name": "getGid", "content": "function getGid(e,r){return reportBroswerInfo_awaiter(this,void 0,void 0,function(){var i,a,s,u;return reportBroswerInfo_generator(this,function(c){switch(c.label){case 0:return i=utils_getHost(),a=r.reportUrl,s=Object.assign({}", "keyword": "sign"}, {"name": "signAdaptor", "content": "function signAdaptor(e,r){var i;return signAdaptor_awaiter(this,void 0,void 0,function(){var a,s;return signAdaptor_generator(this,function(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,r):xhsSign(e,r),\"function\"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,r):xsCommon(e,r),\"function\"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,r):xhsToken(e,r),logSec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}", "keyword": "sign"}, {"name": "xhsSign", "content": "function xhsSign(e,r){var i=r.url,a=r.params,s=r.paramsSerializer,u=r.data,c=e.configInit,l=e.xsIgnore,d=e.autoReload;if(!(!l.some(function(e){return i.indexOf(e)>=0}", "keyword": "sign"}, {"name": "xhsToken", "content": "function xhsToken(e,r){var i=r.url;r.params,r.paramsSerializer,r.data,e.configInit;var a=e.xsIgnore;if(e.autoReload,!(!a.some(function(e){return i.indexOf(e)>=0}", "keyword": "sign"}, {"name": "signLackReload", "content": "function signLackReload(e){if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),Error(\"网络连接不可用，请刷新重试。\")}", "keyword": "sign"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "content": "function setCanvas(e,r,i,a){var s=this;return window.xhsFingerprint.getV18(function(u,c,l){return genDeviceFingerprint_awaiter(s,void 0,void 0,function(){var s;return genDeviceFingerprint_generator(this,function(c){switch(c.label){case 0:s={sign:l,id:u}", "keyword": "sign"}, {"name": "ui_changeTitle", "content": "function ui_changeTitle(e){if(c.YF.isHarmony)return(0,H.dw)(\"changeTitle\",e);logDeprecated_warnDeprecated(\"changeTitle\",\"document.title = 'my title'\");var r={argsT:V().string.isRequired}", "keyword": "sha"}, {"name": "confirmAntiSpam", "content": "function confirmAntiSpam(){return c.YF.isHarmony?(0,H.dw)(\"confirmAntiSpam\"):bridgeAdapter_adapter(\"confirmAntiSpam\")}", "keyword": "sha"}, {"name": "getDeviceInfoOld", "content": "function getDeviceInfoOld(){if(c.YF.isHarmony)return(0,H.dw)(\"getDeviceInfo\");function imeiType(){return version_appVersionLt(\"5.24\")?{}", "keyword": "sha"}, {"name": "getCaptchaUrl", "content": "function getCaptchaUrl(e,r,i,a,s,u){var c=encodeURIComponent(e),l=\"\".concat(getErrorPageHost(),\"/web-login/captcha?redirectPath=\").concat(c,\"&callFrom=\").concat(r);return i&&(l=\"\".concat(l,\"&biz=\").concat(i)),a&&(l=\"\".concat(l,\"&verifyUuid=\").concat(a)),s&&(l=\"\".concat(l,\"&verifyType=\").concat(s)),u&&(l=\"\".concat(l,\"&verifyBiz=\").concat(u)),l}", "keyword": "encode"}, {"name": "install", "content": "function install(e,r,i){var a=r.http.interceptors.spam,s=r.http.interceptors.dispatch;ec.appId=\"xhs-pc-web\";var u=Object.assign({}", "keyword": "sign"}, {"name": "ShadowPerformanceEntry", "content": "function ShadowPerformanceEntry(e,r,i,a){this.name=e,this.duration=a,this.entryType=r,this.startTime=i}", "keyword": "sha"}, {"name": "HttpTracker", "content": "function HttpTracker(){(0,A._)(this,HttpTracker),this.entryHash={}", "keyword": "hash"}, {"name": "resetEntryHash", "content": "function resetEntryHash(){this.entryHash={}", "keyword": "hash"}, {"name": "track", "content": "function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,v._)({}", "keyword": "hash"}, {"name": "measure", "content": "function measure(e){var r=this.entryHash[e];if(!r)return{}", "keyword": "hash"}, {"name": "isIgnoreErrors", "content": "function isIgnoreErrors(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=\"\".concat(null==e?void 0:e.errorType,\": \").concat(null==e?void 0:e.errorMessage);return r.includes(i)}", "keyword": "sign"}, {"name": "collector_jsError", "content": "function collector_jsError(e,r,i,a){for(var s,u,c,l,d,sendInfraJsError=function sendInfraJsError(e){if(!(isIgnoreErrors(e,f)||!0===g&&ignoreDefaultError(e)))(null==h||!h(e))&&(window.apm_blankScreen_error={errorType:\"JSError\",content:e.errorMessage}", "keyword": "sign"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "function getBufferLength(e){return ek.writeUnsignedVarint32(e)}", "keyword": "sign"}, {"name": "encodeData", "content": "function encodeData(e,r){if(void 0===r)return[];var i=[];if(\"String\"===e&&\"string\"==typeof r)i=ek.writeString(r);else if(\"Int64\"===e&&\"number\"==typeof r)i=ek.writeInt64(r);else if(\"Int32\"===e&&\"number\"==typeof r)i=ek.writeSignedVarint32(r);else if(\"Enum\"===e&&\"number\"==typeof r)i=ek.writeUnsignedVarint32(r);else if(\"Boolean\"===e&&\"boolean\"==typeof r)i=ek.writeBoolean(r);else throw Error(\"not support \".concat(e,\":\").concat(r));return i}", "keyword": "sign"}, {"name": "buildBlock", "content": "function buildBlock(e,r,i){var a,s=[],u=builder_getData(e.pop(),r,i),c=u.value,l=u.dataType;if(void 0===c)return[];if(\"RepeatedString\"===l)e.pop(),a=e,c.forEach(function(e){var r=encodeData(\"String\",e);r.length>0&&(a.forEach(function(e){return s.push(e)}", "keyword": "encode"}, {"name": "LiteEaglet", "content": "function LiteEaglet(e){var r=e.name,i=e.emitter,a=e.trackerEnums,s=e.versionHash;if((0,A._)(this,LiteEaglet),(0,f._)(this,\"name\",void 0),(0,f._)(this,\"emitter\",void 0),(0,f._)(this,\"versionHash\",void 0),(0,f._)(this,\"trackerEnums\",void 0),!r)throw Error(\"missing name\");if(!i)throw Error(\"missing emitter\");if(!a)throw Error(\"missing trackerEnums\");if(!s)throw Error(\"missing versionHash\");this.name=r,this.emitter=i,this.versionHash=s,this.trackerEnums=a}", "keyword": "hash"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "content": "function argsCheck(e){if(!e.versionHash)throw Error(\"[Eaglet Emitter Exception] args.versionHash is required\");if(!e.endpoint)throw Error(\"[Eaglet Emitter Exception] args.endpoint is required\")}", "keyword": "hash"}, {"name": "EmitterBase", "content": "function EmitterBase(e){(0,A._)(this,EmitterBase),(0,f._)(this,\"buffer\",[]),argsCheck(e),this.versionHash=e.versionHash,this.endpoint=e.endpoint}", "keyword": "hash"}, {"name": "serializeBinary", "content": "function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}", "keyword": "sign"}, {"name": "serializeBinary", "content": "function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}", "keyword": "sign"}, {"name": "flushApm", "content": "function flushApm(e){var r=this;eA.then(function(i){var a,s,u=i||r.localDebug?(null===(a=r.apmXrayTrackerEndPoint)||void 0===a?void 0:a.development)||ex.ENDPOINT.development:(null===(s=r.apmXrayTrackerEndPoint)||void 0===s?void 0:s.production)||ex.ENDPOINT.production;e.measurement_name&&sendToXrayByFetch(u,e)}", "keyword": "sha"}, {"name": "createTracker", "content": "function createTracker(e,r){var i,a,s,u,c,l,d,p,f=r.enableNativeEmitter,g=r.enableBatchRequest,m=r.customEndPoint,_=e.NAME===eN.NAME,y={versionHash:e.PROTOBUF_HASH,debug:e.debug,preferNative:f}", "keyword": "hash"}, {"name": "serializeBinary", "content": "function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}", "keyword": "sign"}, {"name": "createSendBeaconTracker_createTracker", "content": "function createSendBeaconTracker_createTracker(e,r){var i,a,s=e.NAME===eN.NAME,u={versionHash:e.PROTOBUF_HASH,debug:e.debug}", "keyword": "hash"}, {"name": "_xhrByBridgeAdapter", "content": "function _xhrByBridgeAdapter(){return(_xhrByBridgeAdapter=(0,l._)(function(e){var r,i,a,s,u,l;return(0,E.Jh)(this,function(d){switch(d.label){case 0:if(c=Date.now(),r=e.method,a=void 0!==(i=e.preferBridge)&&i,u=void 0!==(s=e.useBridge)&&s,!a&&!u)return[2,O()(e)];d.label=1;case 1:if(d.trys.push([1,4,,5]),a)return[2,sendClientRequestV3Adapter(e)];if(!u)return[3,3];return console.warn(\"[Launcher Http] useBridge was deprecated, please use preferBridge\"),[4,isSupportV3Invoke()];case 2:if(d.sent()){if((\"post\"===r||\"put\"===r||\"delete\"===r)&&e.headers)return e.headers[\"Content-Type\"]=\"application/x-www-form-urlencoded\",[2,sendClientRequestV3Adapter(e)];if(\"get\"===r)return[2,sendClientRequestV3Adapter(e)]}", "keyword": "encode"}, {"name": "configure", "content": "function configure(e){var r=e.transport,i=e.fields;isPlainObject(r)&&(d.transport=Object.assign(d.transport,r)),isPlainObject(i)&&p.forEach(function(e){e in i&&(d.fields[e]=i[e])}", "keyword": "sign"}, {"name": "showTrack", "content": "function showTrack(e){return bridgeAdapter_adapter(\"showTrack\",e,{argsT:c().shape({title:c().string,content:c().string,isNewTrack:c().bool}", "keyword": "sha"}, {"name": "showApmTrack", "content": "function showApmTrack(e){return bridgeAdapter_adapter(\"showApmTrack\",e,{argsT:c().shape({content:c().string}", "keyword": "sha"}, {"name": "getTrackEnvOld", "content": "function getTrackEnvOld(){var e={resT:c().shape({isTestEnv:c().bool,uploadOneByOne:c().bool,sessionId:c().string}", "keyword": "sha"}, {"name": "addCalledJsListener", "content": "function addCalledJsListener(e,r){window.XHSHandler=window.XHSHandler||{}", "keyword": "sha"}, {"name": "generateCalledJsCallback", "content": "function generateCalledJsCallback(e){return function(r){var i;((null===(i=window.XHSHandler)||void 0===i?void 0:i[\"\".concat(e,\"CallbackList\")])||[]).forEach(function(e){e(r)}", "keyword": "sha"}, {"name": "setShareInfo", "content": "function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e}", "keyword": "sha"}, {"name": "show<PERSON><PERSON><PERSON>", "content": "function showAlert(e){var r=\"showalertV2\";checkArgs(e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,callback:eG().func}", "keyword": "sha"}, {"name": "showActionSheet", "content": "function showActionSheet(e){return adapter(\"showActionSheet\",e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,value:eG().string.isRequired}", "keyword": "sha"}, {"name": "setNavigationHidden", "content": "function setNavigationHidden(){return adapter(\"setNavigationHidden\",{resT:eG().shape({result:eX}", "keyword": "sha"}, {"name": "showNavigationRightBarButtonItem", "content": "function showNavigationRightBarButtonItem(e){var r={argsT:eG().shape({visible:eG().bool,buttonTitle:eG().string,buttonIcon:eG().string,handler:eG().func}", "keyword": "sha"}, {"name": "ui_setShareInfo", "content": "function ui_setShareInfo(e){var r={argsT:eG().shape({contentType:eG().string,title:eG().string,content:eG().string,linkurl:urlType,imageurl:urlType,type:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,thumb:eG().string,username:eG().string}", "keyword": "sha"}, {"name": "showShareMenu", "content": "function showShareMenu(){return adapter(\"showShareMenu\")}", "keyword": "sha"}, {"name": "shareContent", "content": "function shareContent(e){var r,i={argsT:eG().shape({type:eG().string.isRequired,contentType:eG().oneOf([\"link\",\"image\",\"text\"]),title:eG().string,content:eG().string,linkurl:urlType,imageurl:urlType,base64string:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,thumb:eG().string,username:eG().string}", "keyword": "base64"}, {"name": "alipayClient", "content": "function alipayClient(e){return adapter(\"alipayClient\",e,{argsT:eG().string.isRequired,resT:eG().shape({result:eX,orderid:eG().string.isRequired}", "keyword": "sha"}, {"name": "openURLByWechat", "content": "function openURLByWechat(e){return adapter(\"openURLByWechat\",e,{argsT:eG().shape({url:eG().string.isRequired}", "keyword": "sha"}, {"name": "wechatPayClient", "content": "function wechatPayClient(e){return adapter(\"wechatPayClient\",e,{argsT:eG().string.isRequired,resT:eG().shape({result:eX,orderid:eG().string.isRequired}", "keyword": "sha"}, {"name": "setPasteBoard", "content": "function setPasteBoard(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX}", "keyword": "sha"}, {"name": "showTrack", "content": "function showTrack(e){return adapter(\"showTrack\",e,{argsT:eG().shape({title:eG().string,content:eG().string,isNewTrack:eG().bool}", "keyword": "sha"}, {"name": "showApmTrack", "content": "function showApmTrack(e){return adapter(\"showApmTrack\",e,{argsT:eG().shape({content:eG().string}", "keyword": "sha"}, {"name": "openMapWithLocation", "content": "function openMapWithLocation(e){return adapter(\"openMapWithLocation\",e,{argsT:eG().shape({lat:eG().number,long:eG().number,direction:eG().bool,name:eG().string,coordinate:eG().shape({wgs84:eG().shape({long:eG().number,lat:eG().number}", "keyword": "sha"}, {"name": "addComment", "content": "function addComment(e){return adapter(\"addComment\",e,{argsT:eG().shape({placeholder:eG().string,uid:eG().string}", "keyword": "sha"}, {"name": "checkLoginWithAction", "content": "function checkLoginWithAction(e){return adapter(\"checkLoginWithAction\",e,{argsT:eG().shape({type:eG().number.isRequired}", "keyword": "sha"}, {"name": "isAppInstalled", "content": "function isAppInstalled(e){return adapter(\"isAppInstalled\",e,{argsT:eG().shape({iOS:eG().string,Android:eG().string}", "keyword": "sha"}, {"name": "getAppInfo", "content": "function getAppInfo(){return adapter(\"getAppInfo\",{resT:eG().shape({result:eX,version:eG().string.isRequired,build:eG().string.isRequired,jsversion:eG().string.isRequired,package:eG().oneOf([\"com.xingin.discover\",\"com.xingin.xhs\"]).isRequired}", "keyword": "sha"}, {"name": "getTrackEnv", "content": "function getTrackEnv(){var e={resT:eG().shape({isTestEnv:eG().bool,uploadOneByOne:eG().bool,sessionId:eG().string}", "keyword": "sha"}, {"name": "lowPowerModeEnabled", "content": "function lowPowerModeEnabled(){return adapter(\"lowPowerModeEnabled\",{resT:eG().shape({result:eX,value:eG().bool.isRequired}", "keyword": "sha"}, {"name": "requestNotificationPermission", "content": "function requestNotificationPermission(e){return adapter(\"requestNotificationPermission\",e,{argsT:eG().shape({engaingType:eG().number.isRequired,engaingMessage:eG().string}", "keyword": "sha"}, {"name": "saveImage", "content": "function saveImage(e){return adapter(\"saveImage\",e,{argsT:eG().shape({url:urlType,base64string:eG().string,type:eG().string.isRequired}", "keyword": "base64"}, {"name": "sendClientRequest", "content": "function sendClientRequest(e){console.warn(\"[OzoneBridge] sendClientRequest was deprecated, please use [Launcher http](https://code.devops.xiaohongshu.com/formula/launcher/tree/master/src/http)\");var r={argsT:eG().shape({url:eG().string,type:eG().oneOf([\"GET\",\"POST\",\"PUT\",\"DELETE\"]).isRequired,data:eG().oneOfType([eG().object]),transform:eG().oneOfType([eG().bool,eG().shape({separateNumber:eG().bool}", "keyword": "sha"}, {"name": "getItemOld", "content": "function getItemOld(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX,value:eG().string}", "keyword": "sha"}, {"name": "removeItemOld", "content": "function removeItemOld(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX}", "keyword": "sha"}, {"name": "broadcastNative", "content": "function broadcastNative(e){return adapter(\"broadcastNative\",e,{argsT:eG().shape({key:eG().string.isRequired,data:eG().string.isRequired}", "keyword": "sha"}, {"name": "getMessageStatusIOS", "content": "function getMessageStatusIOS(){return adapter(\"getMessageStatus\",{resT:eG().shape({result:eX,status:eG().oneOf([0,1]).isRequired}", "keyword": "sha"}, {"name": "getThird<PERSON>uth", "content": "function getThirdAuth(e){return adapter(\"getThirdAuth\",e,{argsT:eG().oneOf([\"weixin\"]).isRequired,resT:eG().shape({result:eX,value:eG().oneOfType([eG().object])}", "keyword": "sha"}, {"name": "checkAppPermission", "content": "function checkAppPermission(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,state:eG().oneOf([\"denied\",\"granted\",\"undeterminated\"])}", "keyword": "sha"}, {"name": "areNotificationsEnabledAndroid", "content": "function areNotificationsEnabledAndroid(){return adapter(\"areNotificationsEnabled\",{resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,state:eG().oneOf([\"denied\",\"granted\"])}", "keyword": "sha"}, {"name": "_getFileUrlFromLocalServerIOS", "content": "function _getFileUrlFromLocalServerIOS(){return(_getFileUrlFromLocalServerIOS=(0,eq._)(function(e){var r;return(0,ej.Jh)(this,function(i){switch(i.label){case 0:return[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:eG().shape({url:urlType.isRequired}", "keyword": "sha"}, {"name": "setShareInfo", "content": "function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e}", "keyword": "sha"}, {"name": "show<PERSON><PERSON><PERSON>", "content": "function showAlert(e){var r=\"showalertV2\";checkArgs(e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,callback:O().func}", "keyword": "sha"}, {"name": "showActionSheet", "content": "function showActionSheet(e){return adapter(\"showActionSheet\",e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,value:O().string.isRequired}", "keyword": "sha"}, {"name": "setNavigationHidden", "content": "function setNavigationHidden(){return adapter(\"setNavigationHidden\",{resT:O().shape({result:L}", "keyword": "sha"}, {"name": "showNavigationRightBarButtonItem", "content": "function showNavigationRightBarButtonItem(e){var r={argsT:O().shape({visible:O().bool,buttonTitle:O().string,buttonIcon:O().string,handler:O().func}", "keyword": "sha"}, {"name": "ui_setShareInfo", "content": "function ui_setShareInfo(e){var r={argsT:O().shape({contentType:O().string,title:O().string,content:O().string,linkurl:urlType,imageurl:urlType,type:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb:O().string,username:O().string}", "keyword": "sha"}, {"name": "showShareMenu", "content": "function showShareMenu(){return adapter(\"showShareMenu\")}", "keyword": "sha"}, {"name": "shareContent", "content": "function shareContent(e){var r,i={argsT:O().shape({type:O().string.isRequired,contentType:O().oneOf([\"link\",\"image\",\"text\"]),title:O().string,content:O().string,linkurl:urlType,imageurl:urlType,base64string:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb:O().string,username:O().string}", "keyword": "base64"}, {"name": "alipayClient", "content": "function alipayClient(e){return adapter(\"alipayClient\",e,{argsT:O().string.isRequired,resT:O().shape({result:L,orderid:O().string.isRequired}", "keyword": "sha"}, {"name": "openURLByWechat", "content": "function openURLByWechat(e){return adapter(\"openURLByWechat\",e,{argsT:O().shape({url:O().string.isRequired}", "keyword": "sha"}, {"name": "wechatPayClient", "content": "function wechatPayClient(e){return adapter(\"wechatPayClient\",e,{argsT:O().string.isRequired,resT:O().shape({result:L,orderid:O().string.isRequired}", "keyword": "sha"}, {"name": "setPasteBoard", "content": "function setPasteBoard(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L}", "keyword": "sha"}, {"name": "showTrack", "content": "function showTrack(e){return adapter(\"showTrack\",e,{argsT:O().shape({title:O().string,content:O().string,isNewTrack:O().bool}", "keyword": "sha"}, {"name": "showApmTrack", "content": "function showApmTrack(e){return adapter(\"showApmTrack\",e,{argsT:O().shape({content:O().string}", "keyword": "sha"}, {"name": "openMapWithLocation", "content": "function openMapWithLocation(e){return adapter(\"openMapWithLocation\",e,{argsT:O().shape({lat:O().number,long:O().number,direction:O().bool,name:O().string,coordinate:O().shape({wgs84:O().shape({long:O().number,lat:O().number}", "keyword": "sha"}, {"name": "addComment", "content": "function addComment(e){return adapter(\"addComment\",e,{argsT:O().shape({placeholder:O().string,uid:O().string}", "keyword": "sha"}, {"name": "checkLoginWithAction", "content": "function checkLoginWithAction(e){return adapter(\"checkLoginWithAction\",e,{argsT:O().shape({type:O().number.isRequired}", "keyword": "sha"}, {"name": "isAppInstalled", "content": "function isAppInstalled(e){return adapter(\"isAppInstalled\",e,{argsT:O().shape({iOS:O().string,Android:O().string}", "keyword": "sha"}, {"name": "getAppInfo", "content": "function getAppInfo(){return adapter(\"getAppInfo\",{resT:O().shape({result:L,version:O().string.isRequired,build:O().string.isRequired,jsversion:O().string.isRequired,package:O().oneOf([\"com.xingin.discover\",\"com.xingin.xhs\"]).isRequired}", "keyword": "sha"}, {"name": "getTrackEnv", "content": "function getTrackEnv(){var e={resT:O().shape({isTestEnv:O().bool,uploadOneByOne:O().bool,sessionId:O().string}", "keyword": "sha"}, {"name": "lowPowerModeEnabled", "content": "function lowPowerModeEnabled(){return adapter(\"lowPowerModeEnabled\",{resT:O().shape({result:L,value:O().bool.isRequired}", "keyword": "sha"}, {"name": "requestNotificationPermission", "content": "function requestNotificationPermission(e){return adapter(\"requestNotificationPermission\",e,{argsT:O().shape({engaingType:O().number.isRequired,engaingMessage:O().string}", "keyword": "sha"}, {"name": "saveImage", "content": "function saveImage(e){return adapter(\"saveImage\",e,{argsT:O().shape({url:urlType,base64string:O().string,type:O().string.isRequired}", "keyword": "base64"}, {"name": "sendClientRequest", "content": "function sendClientRequest(e){console.warn(\"[OzoneBridge] sendClientRequest was deprecated, please use [Launcher http](https://code.devops.xiaohongshu.com/formula/launcher/tree/master/src/http)\");var r={argsT:O().shape({url:O().string,type:O().oneOf([\"GET\",\"POST\",\"PUT\",\"DELETE\"]).isRequired,data:O().oneOfType([O().object]),transform:O().oneOfType([O().bool,O().shape({separateNumber:O().bool}", "keyword": "sha"}, {"name": "getItemOld", "content": "function getItemOld(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L,value:O().string}", "keyword": "sha"}, {"name": "removeItemOld", "content": "function removeItemOld(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L}", "keyword": "sha"}, {"name": "broadcastNative", "content": "function broadcastNative(e){return adapter(\"broadcastNative\",e,{argsT:O().shape({key:O().string.isRequired,data:O().string.isRequired}", "keyword": "sha"}, {"name": "getMessageStatusIOS", "content": "function getMessageStatusIOS(){return adapter(\"getMessageStatus\",{resT:O().shape({result:L,status:O().oneOf([0,1]).isRequired}", "keyword": "sha"}, {"name": "getThird<PERSON>uth", "content": "function getThirdAuth(e){return adapter(\"getThirdAuth\",e,{argsT:O().oneOf([\"weixin\"]).isRequired,resT:O().shape({result:L,value:O().oneOfType([O().object])}", "keyword": "sha"}, {"name": "checkAppPermission", "content": "function checkAppPermission(e){var r={argsT:O().string.isRequired,resT:O().shape({result:O().oneOf([0,-1]).isRequired,state:O().oneOf([\"denied\",\"granted\",\"undeterminated\"])}", "keyword": "sha"}, {"name": "areNotificationsEnabledAndroid", "content": "function areNotificationsEnabledAndroid(){return adapter(\"areNotificationsEnabled\",{resT:O().shape({result:O().oneOf([0,-1]).isRequired,state:O().oneOf([\"denied\",\"granted\"])}", "keyword": "sha"}, {"name": "_getFileUrlFromLocalServerIOS", "content": "function _getFileUrlFromLocalServerIOS(){return(_getFileUrlFromLocalServerIOS=(0,A._)(function(e){var r;return(0,R.Jh)(this,function(i){switch(i.label){case 0:return[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:O().shape({url:urlType.isRequired}", "keyword": "sha"}, {"name": "decodeString", "content": "function decodeString(e,r){try{return JSON.parse(e)}", "keyword": "decode"}, {"name": "endcodeAsBinary", "content": "function endcodeAsBinary(e){return d(\"encode %O to buffer\",e),a.from([])}", "keyword": "encode"}, {"name": "decodeBinary", "content": "function decodeBinary(e){return d(\"decodeBinary %O to packet\",e),null}", "keyword": "decode"}, {"name": "encodePacket", "content": "function encodePacket(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.EPacketFormat.STRING,i=arguments.length>2?arguments[2]:void 0;return r===l.EPacketFormat.BINARY?endcodeAsBinary(e):endcodeAsString(e,i)}", "keyword": "encode"}, {"name": "decodePacket", "content": "function decodePacket(e,r){if((void 0===e?\"undefined\":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}", "keyword": "decode"}, {"name": "createPayload", "content": "function createPayload(e,r,i){var a,s=(0,P.createPacket)(e,this,r,i);if(this.sdkConfig.protocol===E.EProtocols.Websocket){var u=_.default.encodePacket(s,this.sdkConfig.packetFormat,this);return{packet:s,payload:u}", "keyword": "encode"}, {"name": "onData", "content": "function onData(e){this.emit(S.EVENTS.DATA,e);var r=_.default.decodePacket(e,this);return this.onPacket(r),this}", "keyword": "decode"}], "keyword_matches": {"x-s-common": [{"position": 201350, "context": "void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O(\"\".concat(\"\").concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stri"}, {"position": 201417, "context": "concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number("}], "X-S-Common": [{"position": 201350, "context": "void 0===a||a.getCurMiniUa(function(e){v.x8=e,v.x9=O(\"\".concat(\"\").concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stri"}, {"position": 201417, "context": "concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number("}], "sign": [{"position": 14494, "context": "return null;if(1===e.length)return e[0].t;let r=e[0];for(let i=1;i<e.length;i++){let a=e[i];a.layoutSignificance>r.layoutSignificance&&(r=a)}return r.t}static get(){return new Promise(e=>{if(window.__FULL"}, {"position": 14515, "context": "length)return e[0].t;let r=e[0];for(let i=1;i<e.length;i++){let a=e[i];a.layoutSignificance>r.layoutSignificance&&(r=a)}return r.t}static get(){return new Promise(e=>{if(window.__FULLY_LOADED__&&window.__"}, {"position": 45554, "context": ".isDetected=!0})}}}]),WhiteScreenDetect}()),isHTTPError=function(e){return[I,A,R].includes(e||\"\")},isIgnoreErrors=function(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];if(e){var i=\"\""}, {"position": 45999, "context": "=a.ignoreErrors,u=a.ignoreError,c=a.enableIgnoreDefaultError;if(ep.pushJsError(e.errorMessage||\"\"),isIgnoreErrors(e,s))return;if(!(!0===c&&((i=e).errorType&&isHTTPError(i.errorType)||\"AbortError\"===i.erro"}, {"position": 82954, "context": "return{value:e[0].t,element:e[0].lastElement};for(var r=e[0],i=1;i<e.length;i++){var a=e[i];a.layoutSignificance>r.layoutSignificance&&(r=a)}return{value:r.t,element:r.lastElement}},stopFMPDispatch=functi"}, {"position": 82975, "context": "lement:e[0].lastElement};for(var r=e[0],i=1;i<e.length;i++){var a=e[i];a.layoutSignificance>r.layoutSignificance&&(r=a)}return{value:r.t,element:r.lastElement}},stopFMPDispatch=function(){var e;(null===(e"}, {"position": 112626, "context": "portJsError=function(e,r,i,a){var s=getJsErrorReportMetrics(e,r.options.jsError);s&&(i&&a&&Object.assign(s,{context_artifactName:i,context_artifactVersion:a}),r.flush(s))};function sendVueError(e,r,i,a){t"}, {"position": 114814, "context": ".options.http||{},u=s.enableResponseData,c=s.enableRequestPayload,report=function(a){r&&i&&Object.assign(a,{context_artifactName:r,context_artifactVersion:i}),[R,I,A].includes(a.measurement_data.errorType"}, {"position": 144003, "context": "ion merge(){for(var e=arguments.length,r=Array(e),i=0;i<e;i++)r[i]=arguments[i];var a={};function assignValue(e,r){\"object\"===(0,d._)(a[r])&&(void 0===e?\"undefined\":(0,d._)(e))===\"object\"?a[r]=merge(a[r],"}, {"position": 144329, "context": "throw Error(\"[Http Exception] config must be a plain object\");for(var r in e)e.hasOwnProperty(r)&&assignValue(e[r],r)}}),a}function isFormData(e){return\"undefined\"!=typeof FormData&&e instanceof FormData}"}, {"position": 148213, "context": "is empty\"));if(a?(p=l||{}).data=c:(p=c||{},(void 0===l?\"undefined\":(0,d._)(l))===\"object\"&&Object.assign(p,l)),p.method=e,p.url=makeUri(u,p),p.matchedPath=getMatchedPath(u),!p.headers&&(p.headers={}),!p.h"}, {"position": 153176, "context": "tion(){window.sessionStorage.removeItem(\"spam_font_counter\"),window.sessionStorage.removeItem(\"spam_sign_counter\")},36e5)),(\"3\"===e||\"1\"===e)&&(r=getStorageItem(\"spam_sign_counter\")),\"4\"===e&&(r=getStorag"}, {"position": 153243, "context": ".sessionStorage.removeItem(\"spam_sign_counter\")},36e5)),(\"3\"===e||\"1\"===e)&&(r=getStorageItem(\"spam_sign_counter\")),\"4\"===e&&(r=getStorageItem(\"spam_font_counter\")),r}var d={name:\"crawler-spam\",installed:"}, {"position": 153486, "context": "lled&&!!r.isBrowser)r.http.interceptors.dispatch.use(function(e){e.url.indexOf(c)>-1&&(e.headers[\"X-Sign\"]=encryptToken(e,r.http.buildURL));var i,s=e.url,u=void 0===s?\"\":s;if((null===(i=window)||void 0==="}, {"position": 153607, "context": ",r.http.buildURL));var i,s=e.url,u=void 0===s?\"\":s;if((null===(i=window)||void 0===i?void 0:i.shouldSign)&&window.shouldSign(u)){var l=u;if(0===u.indexOf(\"//\")&&(u=\"\".concat(window.location.protocol).conc"}, {"position": 153627, "context": "ar i,s=e.url,u=void 0===s?\"\":s;if((null===(i=window)||void 0===i?void 0:i.shouldSign)&&window.shouldSign(u)){var l=u;if(0===u.indexOf(\"//\")&&(u=\"\".concat(window.location.protocol).concat(u)),/^https?:/.te"}, {"position": 153871, "context": "ace(d.origin,\"\")}catch(e){l=u}var p=e.params,f=e.paramsSerializer,v=r.http.buildURL(l,p,f),h=window.sign(v,e.data)||{},g=window.f&&\"function\"==typeof window.f?window.f():{};e.headers=(0,a._)({},e.headers,"}, {"position": 154084, "context": "rn e}),r.http.interceptors.spam.use(function(e){var r=(null==e?void 0:e.headers)&&e.headers[\"x-kong-sign\"];return 406===e.status?r&&\"2\"===r?(alert(\"系统时间错误\"),(0,l.info)(\"systemTimeError\",void 0,\"system_tim"}, {"position": 168282, "context": "sec/v1/scripting\",y=\"/api/sec/v1/sbtsource\",w=\"sdt_source_storage_key\",E=\"last_tiga_update_time\",T=\"sign_lack_info\",S=[\"fe_api/burdock/v2/user/keyInfo\",\"fe_api/burdock/v2/shield/profile\",\"fe_api/burdock/v"}, {"position": 176344, "context": "e){return null!=e&&(t(e)||o(e)||!!e._isBuffer)}},function(e,r,i){e.exports=i(1)}]);function encrypt_sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\\r\\n/g,\"\\n\");for(var r=\"\",i=0;i<e.lengt"}, {"position": 180796, "context": "(i))}})})}function sleep(e){return new Promise(function(r,i){setTimeout(r,e)})}function utils_shouldSign(e){var r=!0;return e.indexOf(window.location.host)>-1||e.indexOf(\"sit.xiaohongshu.com\")>-1?r:(g.som"}, {"position": 181105, "context": "\",a[a.Android=2]=\"Android\",a[a.MacOs=3]=\"MacOs\",a[a.Linux=4]=\"Linux\",a[a.other=5]=\"other\";var utils_SignReload=function SignReload(){this.count=1,this.time=+new Date};function shouldSignReload(){try{var e"}, {"position": 181125, "context": "droid\",a[a.MacOs=3]=\"MacOs\",a[a.Linux=4]=\"Linux\",a[a.other=5]=\"other\";var utils_SignReload=function SignReload(){this.count=1,this.time=+new Date};function shouldSignReload(){try{var e=+new Date,r=JSON.st"}, {"position": 181187, "context": "\"other\";var utils_SignReload=function SignReload(){this.count=1,this.time=+new Date};function shouldSignReload(){try{var e=+new Date,r=JSON.stringify(localStorage.getItem(T)||{}),i=!!(r&&r.count),a=r&&r.t"}, {"position": 181338, "context": "ocalStorage.getItem(T)||{}),i=!!(r&&r.count),a=r&&r.time&&e-r.time<36e5;if(!(i&&a)){var s=new utils_SignReload;return localStorage.setItem(T,JSON.stringify(s)),!0}if(r.count>3)return!1;return r.count=r.co"}, {"position": 186826, "context": "d 0,void 0,function(){return token_generator(this,function(r){switch(r.label){case 0:return[4,updateSign(e)];case 1:return r.sent(),[2]}})})}function updateSign(e){return token_awaiter(this,void 0,void 0,"}, {"position": 186882, "context": "on(r){switch(r.label){case 0:return[4,updateSign(e)];case 1:return r.sent(),[2]}})})}function updateSign(e){return token_awaiter(this,void 0,void 0,function(){var r,i,a,s,u,c,d,v;return token_generator(th"}, {"position": 187618, "context": "updateTokenTs(),[3,4];case 3:return v.sent(),[3,4];case 4:return setTimeout(function(){return updateSign(e)},5*r),[3,6];case 5:setTimeout(function(){return updateSign(e)},r/12),v.label=6;case 6:return[2]}"}, {"position": 187680, "context": "rn setTimeout(function(){return updateSign(e)},5*r),[3,6];case 5:setTimeout(function(){return updateSign(e)},r/12),v.label=6;case 6:return[2]}})})}function getScripting(e){return token_awaiter(this,void 0"}, {"position": 192578, "context": "pt(r.xhsTokenUrl,function(){});try{s=0;(u=function(){var i=document.createElement(\"script\");i.src=r.signUrl,i.type=\"text/javascript\",i.crossOrigin=\"anonymous\",i.dataset.formulaAsset=\"1\",i.dataset.formulaC"}, {"position": 192817, "context": "s<3&&(document.body.removeChild(i),u()),logSec({name:\"anti_spam_js_fail\",data:{url:null==r?void 0:r.signUrl,type:\"sign\"}})},i.onload=function(){e.configInit=!0},document.body.appendChild(i)})()}catch(e){}"}, {"position": 192831, "context": ".body.removeChild(i),u()),logSec({name:\"anti_spam_js_fail\",data:{url:null==r?void 0:r.signUrl,type:\"sign\"}})},i.onload=function(){e.configInit=!0},document.body.appendChild(i)})()}catch(e){}return[2,new P"}, {"position": 194266, "context": "generator(this,function(c){switch(c.label){case 0:return i=utils_getHost(),a=r.reportUrl,s=Object.assign({},r),delete s.reportUrl,[4,e.http.post(\"\".concat(i).concat(a),s,{baseURL:i,withCredentials:!0,tran"}, {"position": 197055, "context": "irectPath=\".concat(e,\"&error_code=\").concat(r,\"&error_msg=\").concat(i)}i(109),i(54060),i(20266);var signAdaptor_awaiter=function(e,r,i,a){function adopt(e){return e instanceof i?e:new i(function(r){r(e)})"}, {"position": 197427, "context": "p(e){e.done?i(e.value):adopt(e.value).then(fulfilled,rejected)}step((a=a.apply(e,r||[])).next())})},signAdaptor_generator=function(e,r){var i,a,s,u,c={label:0,sent:function sent(){if(1&s[0])throw s[1];ret"}, {"position": 198562, "context": "e){l=[6,e],a=0}finally{i=s=0}if(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}};function signAdaptor(e,r){var i;return signAdaptor_awaiter(this,void 0,void 0,function(){var a,s;return signAdapt"}, {"position": 198592, "context": "f(5&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}};function signAdaptor(e,r){var i;return signAdaptor_awaiter(this,void 0,void 0,function(){var a,s;return signAdaptor_generator(this,function(u){"}, {"position": 198657, "context": "tion signAdaptor(e,r){var i;return signAdaptor_awaiter(this,void 0,void 0,function(){var a,s;return signAdaptor_generator(this,function(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSi"}, {"position": 198743, "context": "ar a,s;return signAdaptor_generator(this,function(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,r):xhsSign(e,r),\"function\"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsC"}, {"position": 198759, "context": "gnAdaptor_generator(this,function(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,r):xhsSign(e,r),\"function\"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,r):xsCom"}, {"position": 198772, "context": "erator(this,function(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,r):xhsSign(e,r),\"function\"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,r):xsCommon(e,r),\"fun"}, {"position": 198785, "context": "unction(u){return a=Date.now(),\"function\"==typeof e.shouldSign?(s=e.shouldSign(r))&&xhsSign(e,r):xhsSign(e,r),\"function\"==typeof e.shouldFp?(s=e.shouldFp(r))&&xsCommon(e,r):xsCommon(e,r),\"function\"==typeo"}, {"position": 198979, "context": "on\"==typeof e.shouldToken?(s=e.shouldToken(r))&&xhsToken(e,r):xhsToken(e,r),logSec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",sou"}, {"position": 199059, "context": "ec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}}),[2,r]}"}, {"position": 199070, "context": "nti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}}),[2,r]})})}functio"}, {"position": 199179, "context": ",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}}),[2,r]})})}function xhsSign(e,r){var i=r.url,a=r.params,s=r.paramsSerializer,u=r.data,c=e.configInit,l=e.xsIgnore,d=e.autoReloa"}, {"position": 199262, "context": "})})}function xhsSign(e,r){var i=r.url,a=r.params,s=r.paramsSerializer,u=r.data,c=e.configInit,l=e.xsIgnore,d=e.autoReload;if(!(!l.some(function(e){return i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;"}, {"position": 199348, "context": "nfigInit,l=e.xsIgnore,d=e.autoReload;if(!(!l.some(function(e){return i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._web"}, {"position": 199369, "context": ",d=e.autoReload;if(!(!l.some(function(e){return i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._web"}, {"position": 199424, "context": "Of(e)>=0})&&utils_shouldSign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],r.hea"}, {"position": 199997, "context": "eturn r}function xhsToken(e,r){var i=r.url;r.params,r.paramsSerializer,r.data,e.configInit;var a=e.xsIgnore;if(e.autoReload,!(!a.some(function(e){return i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;tr"}, {"position": 200081, "context": "igInit;var a=e.xsIgnore;if(e.autoReload,!(!a.some(function(e){return i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;try{r.headers[\"Sc-T\"]=window.__xhs_sc__.getXHSToken()||\"\"}catch(e){}return r}function "}, {"position": 200185, "context": "(i)))return r;try{r.headers[\"Sc-T\"]=window.__xhs_sc__.getXHSToken()||\"\"}catch(e){}return r}function signLackReload(e){if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),Err"}, {"position": 200247, "context": "ken()||\"\"}catch(e){}return r}function signLackReload(e){if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),Error(\"网络连接不可用，请刷新重试。\")}function getRealUrl(e,r,i){var a=[\"%27\"],"}, {"position": 200757, "context": "=r.url;if(S.map(function(e){return new RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r;var c=r.headers[\"X-Sign\"]||\"\",d=getSigCount(c),p=localStorage.getItem(\"b1\"),f=localStor"}, {"position": 200793, "context": "ew RegExp(e)}).some(function(e){return e.test(u)}),!utils_shouldSign(u))return r;var c=r.headers[\"X-Sign\"]||\"\",d=getSigCount(c),p=localStorage.getItem(\"b1\"),f=localStorage.getItem(\"b1b1\")||\"1\",v={s0:getPl"}, {"position": 203406, "context": "0,function(){var s;return genDeviceFingerprint_generator(this,function(c){switch(c.label){case 0:s={sign:l,id:u},c.label=1;case 1:return c.trys.push([1,3,,4]),[4,i.post(\"/fe_api/burdock/v2/shield/register"}, {"position": 213433, "context": "(getRedHost()).concat(\"/api/redcaptcha/v2/getconfig\"),{},{withCredentials:!0,transform:!1})}var __assign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;i<a;i++)for("}, {"position": 213460, "context": "/redcaptcha/v2/getconfig\"),{},{withCredentials:!0,transform:!1})}var __assign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;i<a;i++)for(var s in r=arguments[i],r)O"}, {"position": 213474, "context": "/getconfig\"),{},{withCredentials:!0,transform:!1})}var __assign=function(){return(__assign=Object.assign||function(e){for(var r,i=1,a=arguments.length;i<a;i++)for(var s in r=arguments[i],r)Object.prototyp"}, {"position": 217587, "context": "||l.<PERSON>sultPolicy||l.resultpolicy))return(p=e,f=d||\"\",v=l.Riskuuid||l.RiskUuid||l.riskuuid||\"\",h=__assign({},i),delete h.headers,delete h.config,g=encodeURIComponent(JSON.stringify(h)),m=localStorage.getIt"}, {"position": 217776, "context": "alStorage.getItem(\"xhs-pc-theme\")||\"\",null===(c=r.spamInterception)||void 0===c?void 0:c.call(r,__assign(__assign({},i),{uuid:v,verifySdkInit:verifySdkInit})))?[2,i.data.code>=300035&&i.data.code<=300039?"}, {"position": 217785, "context": ".getItem(\"xhs-pc-theme\")||\"\",null===(c=r.spamInterception)||void 0===c?void 0:c.call(r,__assign(__assign({},i),{uuid:v,verifySdkInit:verifySdkInit})))?[2,i.data.code>=300035&&i.data.code<=300039?{headers:"}, {"position": 220862, "context": "urn S.sent(),[3,5];case 5:return r.headers[\"xy-common-params\"]=g.toString(),logSec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-l,type:\"xCommonParams\",source:null==r?void 0:r.url}}),[2,r]}})})}var ri"}, {"position": 226768, "context": "cess461Response:!0,captchaInCurrent:!1,is<PERSON><PERSON><PERSON>:\"visible\",platform:\"Windows\",forceLoginHook:void 0,xsIgnore:[],configInit:!1,autoReload:!1,disableMns:!1,spamCallback:void 0,onCaptchaClose:void 0,onReadyTo"}, {"position": 227054, "context": "{var a=r.http.interceptors.spam,s=r.http.interceptors.dispatch;ec.appId=\"xhs-pc-web\";var u=Object.assign({},ec,i);if(r.isBrowser){!0===window.sdt_source_init&&(u.configInit=!0),a.use(riskLogin.bind(null,e"}, {"position": 227335, "context": "use(triggerSpam.bind(null,e,u)),a.use(riskHandler.bind(null,e));var p=(0,c.Ij)();u.platform=p,s.use(signAdaptor.bind(this,u)),r.http.interceptors.request.use(xCommonParams.bind(this,u)),reportBroswerInfo("}, {"position": 269698, "context": "&isHTTPError(e.errorType))||\"AbortError\"===e.errorType&&\"AbortError\"===e.errorMessage||!1}function isIgnoreErrors(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],i=\"\".concat(null==e?voi"}, {"position": 271082, "context": "on collector_jsError(e,r,i,a){for(var s,u,c,l,d,sendInfraJsError=function sendInfraJsError(e){if(!(isIgnoreErrors(e,f)||!0===g&&ignoreDefaultError(e)))(null==h||!h(e))&&(window.apm_blankScreen_error={erro"}, {"position": 279210, "context": "){var r=[];return r.push(e>>>0&255),r.push(e>>>8&255),r.push(e>>>16&255),r.push(e>>>24&255),r},writeSignedVarint32:function(e){if(e>=0)return this.writeUnsignedVarint32(e);for(var r=[],i=e,a=0;a<9;a++)r.p"}, {"position": 279264, "context": "r.push(e>>>16&255),r.push(e>>>24&255),r},writeSignedVarint32:function(e){if(e>=0)return this.writeUnsignedVarint32(e);for(var r=[],i=e,a=0;a<9;a++)r.push(127&i|128),i>>=7;return r.push(1),r},writeUnsigned"}, {"position": 279362, "context": "UnsignedVarint32(e);for(var r=[],i=e,a=0;a<9;a++)r.push(127&i|128),i>>=7;return r.push(1),r},writeUnsignedVarint32:function(e){for(var r=[],i=e;i>127;)r.push(127&i|128),i>>>=7;return r.push(i),r},writeBoo"}, {"position": 279800, "context": "h(eS[d],eS[p],eS[f]||\"\",eS[v]||\"\")}return r.join(\"\")}};function getBufferLength(e){return ek.writeUnsignedVarint32(e)}function builder_getData(e,r,i){var a,s=e.match(/\\{(\\w+):(\\w+)\\.(\\w+)\\}/),u=s[1],c=s[2"}, {"position": 280403, "context": "Int64\"===e&&\"number\"==typeof r)i=ek.writeInt64(r);else if(\"Int32\"===e&&\"number\"==typeof r)i=ek.writeSignedVarint32(r);else if(\"Enum\"===e&&\"number\"==typeof r)i=ek.writeUnsignedVarint32(r);else if(\"Boolean\""}, {"position": 280472, "context": "&&\"number\"==typeof r)i=ek.writeSignedVarint32(r);else if(\"Enum\"===e&&\"number\"==typeof r)i=ek.writeUnsignedVarint32(r);else if(\"Boolean\"===e&&\"boolean\"==typeof r)i=ek.writeBoolean(r);else throw Error(\"not "}, {"position": 285368, "context": "ializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i."}, {"position": 291367, "context": "ializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i."}, {"position": 296742, "context": "ializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i."}, {"position": 349816, "context": "ogType\"];function configure(e){var r=e.transport,i=e.fields;isPlainObject(r)&&(d.transport=Object.assign(d.transport,r)),isPlainObject(i)&&p.forEach(function(e){e in i&&(d.fields[e]=i[e])})}function shoul"}, {"position": 530324, "context": "e:c.measurement_name,measurement_data:p._(d._({},e),{env:this.ctx.env,extra:JSON.stringify(Object.assign(null!==(u=e.extra)&&void 0!==u?u:{},{traceid:(0,_.getFetchCtx)()}))})},g=this.prodUrl;(null===this|"}], "encrypt": [{"position": 152639, "context": "59989),i(7099),i(27461),i(23339),i(51109),i(72169);var s=i(5681),u=i.n(s);let c=\"/fe_api/\";function encryptToken(e,r,i=\"X\"){let{url:a=\"\"}=e,{params:s,paramsSerializer:l}=e;return(a=a.slice(a.indexOf(c),a.len"}, {"position": 153493, "context": "!r.isBrowser)r.http.interceptors.dispatch.use(function(e){e.url.indexOf(c)>-1&&(e.headers[\"X-Sign\"]=encryptToken(e,r.http.buildURL));var i,s=e.url,u=void 0===s?\"\":s;if((null===(i=window)||void 0===i?void 0:i"}, {"position": 169070, "context": "erbBoHQtNP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5\",R=0,I=A.length;R<I;++R)P[R]=A[R];var encrypt_crc32=function crc32(e){for(var r,i=[],a=0;a<256;a++){r=a;for(var s=0;s<8;s++)r=1&r?0xedb88320^r>>>"}, {"position": 176336, "context": "unction(e){return null!=e&&(t(e)||o(e)||!!e._isBuffer)}},function(e,r,i){e.exports=i(1)}]);function encrypt_sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\\r\\n/g,\"\\n\");for(var r=\"\",i=0;i<e."}, {"position": 182666, "context": ".concat((+new Date).toString(16)).concat(genRandomString(30)).concat(r).concat(\"0\").concat(\"000\"),a=encrypt_crc32(i);return\"\".concat(i).concat(a).substring(0,52)}var L=i(65266),log_awaiter=function(e,r,i,a){"}, {"position": 199416, "context": " i.indexOf(e)>=0})&&utils_shouldSign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],"}], "encode": [{"position": 58310, "context": "rguments[1]:{};return Object.keys(r).reduce(function(e,i){var a=r[i];if(!a)return e;var s=\"\".concat(encodeURIComponent(i),\"=\").concat(encodeURIComponent(a));return -1===e.indexOf(\"?\")?e+=\"?\".concat(s):e+=\"&"}, {"position": 58344, "context": "r).reduce(function(e,i){var a=r[i];if(!a)return e;var s=\"\".concat(encodeURIComponent(i),\"=\").concat(encodeURIComponent(a));return -1===e.indexOf(\"?\")?e+=\"?\".concat(s):e+=\"&\".concat(s),e},e)}var eL=function("}, {"position": 70552, "context": "(e.responseEnd-e.startTime)||0,p=0===e.transferSize?eG.webViewStrongCache:0!==e.transferSize&&0===e.encodedBodySize?eG.webViewNeogationCache:eG.webViewRequest,f=+!![eG.webViewStrongCache,eG.webViewNeogation"}, {"position": 70805, "context": "responseTime:s,contentTime:u,redirectTime:c,serverTime:l,collectTime:String(Date.now()),name:e.name,encodedBodySize:e.encodedBodySize,isHitCache:f,hitCacheType:p,initiatorType:e.initiatorType,duration:d,tra"}, {"position": 70823, "context": "tentTime:u,redirectTime:c,serverTime:l,collectTime:String(Date.now()),name:e.name,encodedBodySize:e.encodedBodySize,isHitCache:f,hitCacheType:p,initiatorType:e.initiatorType,duration:d,transferSize:e.transf"}, {"position": 169364, "context": "n(-1^u)>>>0};function tripletToBase64(e){return P[e>>18&63]+P[e>>12&63]+P[e>>6&63]+P[63&e]}function encodeChunk(e,r,i){for(var a,s=[],u=r;u<i;u+=3)a=(e[u]<<16&0xff0000)+(e[u+1]<<8&65280)+(255&e[u+2]),s.push"}, {"position": 169518, "context": "<<16&0xff0000)+(e[u+1]<<8&65280)+(255&e[u+2]),s.push(tripletToBase64(a));return s.join(\"\")}function encodeUtf8(e){for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if(\"%\"===s){var u"}, {"position": 169542, "context": "8&65280)+(255&e[u+2]),s.push(tripletToBase64(a));return s.join(\"\")}function encodeUtf8(e){for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a++){var s=r.charAt(a);if(\"%\"===s){var u=parseInt(r.charAt(a+1)+"}, {"position": 169731, "context": "nt(r.charAt(a+1)+r.charAt(a+2),16);i.push(u),a+=2}else i.push(s.charCodeAt(0))}return i}function b64Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+1638"}, {"position": 169803, "context": "At(0))}return i}function b64Encode(e){for(var r,i=e.length,a=i%3,s=[],u=0,c=i-a;u<c;u+=16383)s.push(encodeChunk(e,u,u+16383>c?c:u+16383));return 1===a?(r=e[i-1],s.push(P[r>>2]+P[r<<4&63]+\"==\")):2===a&&(r=(e"}, {"position": 170894, "context": "ction(e,r){var i={utf8:{stringToBytes:function stringToBytes(e){return i.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function bytesToString(e){return decodeURIComponent(escape(i.bin.by"}, {"position": 176364, "context": "t(e)||o(e)||!!e._isBuffer)}},function(e,r,i){e.exports=i(1)}]);function encrypt_sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\\r\\n/g,\"\\n\");for(var r=\"\",i=0;i<e.length;i++){var a=e.charCod"}, {"position": 176386, "context": "fer)}},function(e,r,i){e.exports=i(1)}]);function encrypt_sign(e,r){var _utf8_encode=function _utf8_encode(e){e=e.replace(/\\r\\n/g,\"\\n\");for(var r=\"\",i=0;i<e.length;i++){var a=e.charCodeAt(i);a<128?r+=String"}, {"position": 176912, "context": "indow;return void 0!==c&&c&&c.navigator&&c.navigator.userAgent&&c.alert&&(s=\"test\"),{\"X-s\":function encode(e){var r,i,s,u,c,l,d,p=\"\",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),i=e.charCodeAt("}, {"position": 176961, "context": "tor.userAgent&&c.alert&&(s=\"test\"),{\"X-s\":function encode(e){var r,i,s,u,c,l,d,p=\"\",f=0;for(e=_utf8_encode(e);f<e.length;)r=e.charCodeAt(f++),i=e.charCodeAt(f++),s=e.charCodeAt(f++),u=r>>2,c=(3&r)<<4|i>>4,l"}, {"position": 201366, "context": "tCurMiniUa(function(e){v.x8=e,v.x9=O(\"\".concat(\"\").concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}c"}, {"position": 201373, "context": "iUa(function(e){v.x8=e,v.x9=O(\"\".concat(\"\").concat(\"\").concat(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e)"}, {"position": 201433, "context": "t(e)),r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStora"}, {"position": 201440, "context": ".headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}):r.headers[\"X-S-Common\"]=b64Encode(encodeUtf8(JSON.stringify(v)))}catch(e){}return r}function getSigCount(e){var r=Number(sessionStorage.getI"}, {"position": 215184, "context": "&l[0])throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}};function getCaptchaUrl(e,r,i,a,s,u){var c=encodeURIComponent(e),l=\"\".concat(getErrorPageHost(),\"/web-login/captcha?redirectPath=\").concat(c,\"&callFr"}, {"position": 216146, "context": "urn P=(C=window.location).host,A=C.href,clientRedirect(\"//\".concat(P,\"/login?redirectPath=\").concat(encodeURIComponent(A))),[2,i];if(\"302\"===b)return clientRedirect(location.origin),[2,i];if(R=getCaptchaUrl"}, {"position": 217633, "context": "=d||\"\",v=l.Riskuuid||l.RiskUuid||l.riskuuid||\"\",h=__assign({},i),delete h.headers,delete h.config,g=encodeURIComponent(JSON.stringify(h)),m=localStorage.getItem(\"xhs-pc-theme\")||\"\",null===(c=r.spamIntercept"}, {"position": 225117, "context": "urn f=(p=window.location).host,v=p.href,clientRedirect(\"//\".concat(f,\"/login?redirectPath=\").concat(encodeURIComponent(v))),[2,e];if(\"302\"===l)return clientRedirect(location.origin),[2,e];try{h=new utils_Tr"}, {"position": 248886, "context": "rguments[1]:{};return Object.keys(r).reduce(function(e,i){var a=r[i];if(!a)return e;var s=\"\".concat(encodeURIComponent(i),\"=\").concat(encodeURIComponent(a));return -1===e.indexOf(\"?\")?e+=\"?\".concat(s):e+=\"&"}, {"position": 248920, "context": "r).reduce(function(e,i){var a=r[i];if(!a)return e;var s=\"\".concat(encodeURIComponent(i),\"=\").concat(encodeURIComponent(a));return -1===e.indexOf(\"?\")?e+=\"?\".concat(s):e+=\"&\".concat(s),e},e)}i(74719),i(13396"}, {"position": 279497, "context": "r=[],i=e;i>127;)r.push(127&i|128),i>>>=7;return r.push(i),r},writeBoolean:function(e){return[+!!e]},encodeByteArray:function(e){for(var r=[],i=0;i<e.length;i+=3){var a=e[i],s=i+1<e.length,u=s?e[i+1]:0,c=i+2"}, {"position": 280190, "context": "Error(\"not support \".concat(e))}return{value:d,dataType:u}}return{value:void 0,dataType:u}}function encodeData(e,r){if(void 0===r)return[];var i=[];if(\"String\"===e&&\"string\"==typeof r)i=ek.writeString(r);el"}, {"position": 280799, "context": "l=u.dataType;if(void 0===c)return[];if(\"RepeatedString\"===l)e.pop(),a=e,c.forEach(function(e){var r=encodeData(\"String\",e);r.length>0&&(a.forEach(function(e){return s.push(e)}),getBufferLength(r.length).for"}, {"position": 281025, "context": "urn s.push(e)}),r.forEach(function(e){return s.push(e)}))});else if(\"String\"===l){e.pop(),a=e;var d=encodeData(\"String\",c);d.length>0&&(a.forEach(function(e){return s.push(e)}),getBufferLength(d.length).for"}, {"position": 281258, "context": "ush(e)}),d.for<PERSON>ach(function(e){return s.push(e)}))}else(a=e).forEach(function(e){return s.push(e)}),encodeData(l,c).forEach(function(e){return s.push(e)});return s}function buildBuffer(e,r,i,a){var s=[];r.f"}, {"position": 281859, "context": "r l=new Uint8Array(u+=c.length),d=0,p=0;p<s.length;p++){var f=s[p];l.set(f,d),d+=f.length}return ek.encodeByteArray(l)}function titleCase(e){return e.slice(0,1).toUpperCase()+e.slice(1)}var eC=function(){fu"}, {"position": 285354, "context": "!0}},{key:\"serializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes"}, {"position": 285401, "context": "alizeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.re"}, {"position": 285441, "context": "zeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",value:function"}, {"position": 291353, "context": "!0}},{key:\"serializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes"}, {"position": 291400, "context": "alizeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.re"}, {"position": 291440, "context": "zeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendLocalMetric\",value:f"}, {"position": 296728, "context": "ter,[{key:\"serializeBinary\",value:function serializeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes"}, {"position": 296775, "context": "alizeBinary(e,r){var i=new r,a=e.serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.re"}, {"position": 296815, "context": "zeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",value:function"}, {"position": 333292, "context": ",f=r.toUpperCase(),v=(l||{})[\"Content-Type\"],\"GET\"!==f&&(!(v||\"\").match(/application\\/x-www-form-urlencoded|application\\/json/)||u instanceof FormData))return[2,O()(e)];E.label=1;case 1:if(E.trys.push([1,6,"}, {"position": 333603, "context": "()(e)];if(w=L()(i,a,s),T={method:f,url:w,headers:l},S=null,\"GET\"!==f&&(S=\"application/x-www-form-urlencoded\"===v?transformToSearchParams(_):JSON.stringify(_),T.body=S),!e.timeout)return[3,3];return[4,Promis"}, {"position": 336338, "context": "=r||\"put\"===r||\"delete\"===r)&&e.headers)return e.headers[\"Content-Type\"]=\"application/x-www-form-urlencoded\",[2,sendClientRequestV3Adapter(e)];if(\"get\"===r)return[2,sendClientRequestV3Adapter(e)]}return[2,s"}, {"position": 361510, "context": "pperCase(),a=r[\"Content-Type\"],s=(0,I._)(r,[\"Content-Type\"]);\"GET\"===i||[\"application/x-www-form-urlencoded\",\"application/json\"].includes(a)?\"GET\"===i&&a&&(r=s):r[\"Content-Type\"]=\"application/json\",Object.k"}, {"position": 375909, "context": "e(),s=i[\"Content-Type\"],u=(0,eD._)(i,[\"Content-Type\"]);return\"GET\"===a||[\"application/x-www-form-urlencoded\",\"application/json\"].includes(s)?\"GET\"===a&&s&&(i=u):i[\"Content-Type\"]=\"application/json\",Object.k"}, {"position": 407109, "context": "it_profile_preview_data\",(y=er||(er={})).ApplicationJSON=\"application/json\",y.ApplicationXWWWFormUrlencoded=\"application/x-www-form-urlencoded\",y.ImagePNG=\"image/png\",(w=en||(en={})).Delete=\"DELETE\",w.Get=\""}, {"position": 407144, "context": "={})).ApplicationJSON=\"application/json\",y.ApplicationXWWWFormUrlencoded=\"application/x-www-form-urlencoded\",y.ImagePNG=\"image/png\",(w=en||(en={})).Delete=\"DELETE\",w.Get=\"GET\",w.Post=\"POST\",w.Put=\"PUT\",(E=e"}, {"position": 421173, "context": "_profile_preview_data\",w);var ef=((E={}).ApplicationJSON=\"application/json\",E.ApplicationXWWWFormUrlencoded=\"application/x-www-form-urlencoded\",E.ImagePNG=\"image/png\",E);var ev=((T={}).Delete=\"DELETE\",T.Get"}, {"position": 421208, "context": "E={}).ApplicationJSON=\"application/json\",E.ApplicationXWWWFormUrlencoded=\"application/x-www-form-urlencoded\",E.ImagePNG=\"image/png\",E);var ev=((T={}).Delete=\"DELETE\",T.Get=\"GET\",T.Post=\"POST\",T.Put=\"PUT\",T)"}, {"position": 428415, "context": "se(),s=i[\"Content-Type\"],u=(0,E._)(i,[\"Content-Type\"]);return\"GET\"===a||[\"application/x-www-form-urlencoded\",\"application/json\"].includes(s)?\"GET\"===a&&s&&(i=u):i[\"Content-Type\"]=\"application/json\",Object.k"}, {"position": 475513, "context": "rn e&&e.__esModule?e:{default:e}};Object.defineProperty(r,\"__esModule\",{value:!0}),r.decodePacket=r.encodePacket=void 0;var c=u(i(88960)),l=i(36189),d=(0,c.default)(\"longlink parser\");function endcodeAsStri"}, {"position": 475888, "context": "ecode error\",e),Error(\"接收 packet 字符串非法, \".concat(e.message))}}function endcodeAsBinary(e){return d(\"encode %O to buffer\",e),a.from([])}function decodeBinary(e){return d(\"decodeBinary %O to packet\",e),null}f"}, {"position": 476002, "context": "uffer\",e),a.from([])}function decodeBinary(e){return d(\"decodeBinary %O to packet\",e),null}function encodePacket(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.EPacketFormat.STRING,i=argu"}, {"position": 476357, "context": "e?\"undefined\":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:de"}, {"position": 476370, "context": ":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},"}, {"position": 476422, "context": "ry(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function(e,r,i){\"use strict\";var a=i(41622),s="}, {"position": 476435, "context": "n decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function(e,r,i){\"use strict\";var a=i(41622),s=i(35329),u=i("}, {"position": 492529, "context": "(0,P.createPacket)(e,this,r,i);if(this.sdkConfig.protocol===E.EProtocols.Websocket){var u=_.default.encodePacket(s,this.sdkConfig.packetFormat,this);return{packet:s,payload:u}}var c=this.sdkConfig,l=c.sendT"}, {"position": 525869, "context": "(74794);(a=r.EMetricsSdkEventName||(r.EMetricsSdkEventName={})).MISS_PROPERTY=\"sdk_miss_property\",a.ENCODE_FAILED=\"sdk_encode_failed\",a.DECODE_FAILED=\"sdk_decode_failed\",a.WRONG_TYPE=\"sdk_wrong_type\",a.<PERSON>IT"}, {"position": 525888, "context": "csSdkEventName||(r.EMetricsSdkEventName={})).MISS_PROPERTY=\"sdk_miss_property\",a.ENCODE_FAILED=\"sdk_encode_failed\",a.DECODE_FAILED=\"sdk_decode_failed\",a.WRONG_TYPE=\"sdk_wrong_type\",a.WRITE_FAILED=\"sdk_write"}], "decode": [{"position": 17411, "context": "=function(e){try{!/^https?:\\/\\//.test(e)&&(e=\"https://c.xiaohongshu.com\"+e);var r=new URL(e);return decodeURIComponent(r.pathname).split(\"/\").map(function(e){var r,i;if(\"\"!==e&&!Number.isNaN(Number(e)))retu"}, {"position": 126047, "context": "__ClientResourceError__(e){try{if(\"string\"==typeof e){var r=JSON.parse(e),i={};i.errorMsg=r.message?decodeURIComponent(r.message):\"\",i.errorCode=r.errorCode||0;var a=r.url;if(!a)return;var s=window.__APM__C"}, {"position": 170966, "context": ".bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function bytesToString(e){return decodeURIComponent(escape(i.bin.bytesToString(e)))}},bin:{stringToBytes:function stringToBytes(e){for(var "}, {"position": 475498, "context": "unction(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(r,\"__esModule\",{value:!0}),r.decodePacket=r.encodePacket=void 0;var c=u(i(88960)),l=i(36189),d=(0,c.default)(\"longlink parser\");functio"}, {"position": 475726, "context": "{try{return JSON.stringify(e)}catch(e){throw Error(\"输入的 packet 格式非法, \".concat(e.message))}}function decodeString(e,r){try{return JSON.parse(e)}catch(e){throw d(\"decode error\",e),Error(\"接收 packet 字符串非法, \".co"}, {"position": 475787, "context": "t 格式非法, \".concat(e.message))}}function decodeString(e,r){try{return JSON.parse(e)}catch(e){throw d(\"decode error\",e),Error(\"接收 packet 字符串非法, \".concat(e.message))}}function endcodeAsBinary(e){return d(\"encod"}, {"position": 475932, "context": "ncat(e.message))}}function endcodeAsBinary(e){return d(\"encode %O to buffer\",e),a.from([])}function decodeBinary(e){return d(\"decodeBinary %O to packet\",e),null}function encodePacket(e){var r=arguments.leng"}, {"position": 475958, "context": " endcodeAsBinary(e){return d(\"encode %O to buffer\",e),a.from([])}function decodeBinary(e){return d(\"decodeBinary %O to packet\",e),null}function encodePacket(e){var r=arguments.length>1&&void 0!==arguments[1"}, {"position": 476226, "context": "uments[2]:void 0;return r===l.EPacketFormat.BINARY?endcodeAsBinary(e):endcodeAsString(e,i)}function decodePacket(e,r){if((void 0===e?\"undefined\":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;re"}, {"position": 476312, "context": "e,i)}function decodePacket(e,r){if((void 0===e?\"undefined\":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.defau"}, {"position": 476337, "context": "t(e,r){if((void 0===e?\"undefined\":s._(e))===\"object\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePa"}, {"position": 476385, "context": "ect\")return e instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function("}, {"position": 476398, "context": " instanceof a?decodeBinary(e):e;return decodeString(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function(e,r,i){\"use s"}, {"position": 476448, "context": "g(e,r)}r.encodePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function(e,r,i){\"use strict\";var a=i(41622),s=i(35329),u=i(46490),c=i(90"}, {"position": 476461, "context": "dePacket=encodePacket,r.decodePacket=decodePacket,r.default={encodePacket:encodePacket,decodePacket:decodePacket}},95260:function(e,r,i){\"use strict\";var a=i(41622),s=i(35329),u=i(46490),c=i(90251),l=i(2729"}, {"position": 493979, "context": "sError(e,!0,!1)}},{key:\"onData\",value:function onData(e){this.emit(S.EVENTS.DATA,e);var r=_.default.decodePacket(e,this);return this.onPacket(r),this}},{key:\"onPacket\",value:function onPacket(e){var r,i=nul"}, {"position": 525905, "context": "r.EMetricsSdkEventName={})).MISS_PROPERTY=\"sdk_miss_property\",a.ENCODE_FAILED=\"sdk_encode_failed\",a.DECODE_FAILED=\"sdk_decode_failed\",a.WRONG_TYPE=\"sdk_wrong_type\",a.WRITE_FAILED=\"sdk_write_failed\",(s=c=r.E"}, {"position": 525924, "context": "ame={})).MISS_PROPERTY=\"sdk_miss_property\",a.ENCODE_FAILED=\"sdk_encode_failed\",a.DECODE_FAILED=\"sdk_decode_failed\",a.WRONG_TYPE=\"sdk_wrong_type\",a.WRITE_FAILED=\"sdk_write_failed\",(s=c=r.EMetricsEventName||("}], "webmsxyw": [{"position": 199048, "context": "n(e,r),logSec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}})"}, {"position": 199449, "context": "ign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],r.headers[\"X-s\"]=v[\"X-s\"]}catch(e)"}, {"position": 199470, "context": "ignLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],r.headers[\"X-s\"]=v[\"X-s\"]}catch(e){}if(!0!==e.disableMn"}, {"position": 200226, "context": "w.__xhs_sc__.getXHSToken()||\"\"}catch(e){}return r}function signLackReload(e){if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),Error(\"网络连接不可用，请刷新重试。\")}function getRealUrl(e,r,"}], "_webmsxyw": [{"position": 199047, "context": "en(e,r),logSec({name:\"anti_spam_sign_cost\",data:{cost:Date.now()-a,type:(null==window?void 0:window._webmsxyw)?\"sign_new\":\"sign_old\",source:null!==(i=null==r?void 0:r.url)&&void 0!==i?i:window.location.href}})"}, {"position": 199448, "context": "Sign(i)))return r;d&&signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],r.headers[\"X-s\"]=v[\"X-s\"]}catch(e)"}, {"position": 199469, "context": "signLackReload();try{var p=getRealUrl(i,a,s),f=encrypt_sign;c&&void 0!==window._webmsxyw&&(f=window._webmsxyw);var v=f(p,u)||{};r.headers[\"X-t\"]=v[\"X-t\"],r.headers[\"X-s\"]=v[\"X-s\"]}catch(e){}if(!0!==e.disableMn"}, {"position": 200225, "context": "ow.__xhs_sc__.getXHSToken()||\"\"}catch(e){}return r}function signLackReload(e){if(e&&void 0!==window._webmsxyw)throw shouldSignReload()&&window.location.reload(),Error(\"网络连接不可用，请刷新重试。\")}function getRealUrl(e,r,"}], "base64": [{"position": 169295, "context": "(var u=-1,c=0;c<e.length;c++)u=u>>>8^i[255&(u^e.charCodeAt(c))];return(-1^u)>>>0};function tripletToBase64(e){return P[e>>18&63]+P[e>>12&63]+P[e>>6&63]+P[63&e]}function encodeChunk(e,r,i){for(var a,s=[],u=r"}, {"position": 169480, "context": "i){for(var a,s=[],u=r;u<i;u+=3)a=(e[u]<<16&0xff0000)+(e[u+1]<<8&65280)+(255&e[u+2]),s.push(tripletToBase64(a));return s.join(\"\")}function encodeUtf8(e){for(var r=encodeURIComponent(e),i=[],a=0;a<r.length;a+"}, {"position": 175574, "context": " hexToBytes(e){for(var r=[],i=0;i<e.length;i+=2)r.push(parseInt(e.substr(i,2),16));return r},bytesToBase64:function bytesToBase64(e){for(var r=[],a=0;a<e.length;a+=3)for(var s=e[a]<<16|e[a+1]<<8|e[a+2],u=0;"}, {"position": 175597, "context": "r=[],i=0;i<e.length;i+=2)r.push(parseInt(e.substr(i,2),16));return r},bytesToBase64:function bytesToBase64(e){for(var r=[],a=0;a<e.length;a+=3)for(var s=e[a]<<16|e[a+1]<<8|e[a+2],u=0;u<4;u++)8*a+6*u<=8*e.le"}, {"position": 175772, "context": "+2],u=0;u<4;u++)8*a+6*u<=8*e.length?r.push(i.charAt(s>>>6*(3-u)&63)):r.push(\"=\");return r.join(\"\")},base64ToBytes:function base64ToBytes(e){e=e.replace(/[^A-Z0-9+\\/]/gi,\"\");for(var r=[],a=0,s=0;a<e.length;s"}, {"position": 175795, "context": "<=8*e.length?r.push(i.charAt(s>>>6*(3-u)&63)):r.push(\"=\");return r.join(\"\")},base64ToBytes:function base64ToBytes(e){e=e.replace(/[^A-Z0-9+\\/]/gi,\"\");for(var r=[],a=0,s=0;a<e.length;s=++a%4)0!=s&&r.push((i."}, {"position": 285481, "context": "4(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",value:function sendData(e){sendByDefault(u||this.local"}, {"position": 291480, "context": "4(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendLocalMetric\",value:function sendLocalMetric(e){if(this.local"}, {"position": 296855, "context": "4(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",value:function sendData(e){var r,i=l||this.localDebug?"}, {"position": 385036, "context": "eOf([\"link\",\"image\",\"text\"]),title:eG().string,content:eG().string,linkurl:urlType,imageurl:urlType,base64string:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,"}, {"position": 394190, "context": "e:eG().string})})}function saveImage(e){return adapter(\"saveImage\",e,{argsT:eG().shape({url:urlType,base64string:eG().string,type:eG().string.isRequired}),resT:eG().shape({result:eX})})}function basicSendCl"}, {"position": 436740, "context": "oneOf([\"link\",\"image\",\"text\"]),title:O().string,content:O().string,linkurl:urlType,imageurl:urlType,base64string:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpa"}, {"position": 445721, "context": "age:O().string})})}function saveImage(e){return adapter(\"saveImage\",e,{argsT:O().shape({url:urlType,base64string:O().string,type:O().string.isRequired}),resT:O().shape({result:L})})}function basicSendClient"}], "sha": [{"position": 19978, "context": "(e){}return{}},judgePlatform=function(){if(E.YF.isIOS)return E.YF.isXHS?\"iOS\":\"iOSBrowser\";if(E.YF.isHarmonyArk)return E.YF.isXHS?\"Harmony\":\"HarmonyBrowser\";if(E.YF.isAndroid||E.YF.isHarmony)return E.YF."}, {"position": 20060, "context": "Browser\";if(E.YF.isHarmonyArk)return E.YF.isXHS?\"Harmony\":\"HarmonyBrowser\";if(E.YF.isAndroid||E.YF.isHarmony)return E.YF.isXHS?\"Android\":\"AndroidBrowser\";return\"PC\"},j={appVersion:\"0.0.0\",systemVersion:\""}, {"position": 52148, "context": "ion(i){return i.name!==r||i.entryType!==e});var i=eb[r];i&&i.entryType===e&&(eb[r]=void 0)}function ShadowPerformanceEntry(e,r,i,a){this.name=e,this.duration=a,this.entryType=r,this.startTime=i}function "}, {"position": 52687, "context": "at(e,\"' is part of the PerformanceTiming interface, and cannot be used as a mark name.\"));var r=new ShadowPerformanceEntry(e,\"mark\",performance.now(),0);ek.push(r),eb[e]=r}),setupPolyfill(\"measure\",funct"}, {"position": 53153, "context": "\"Failed to execute 'measure' on 'Performance': The mark '\".concat(i,\"' dose not exist.\"));var c=new ShadowPerformanceEntry(e,\"measure\",a,s-a);ek.push(c),eb[e]=c}),setupPolyfill(\"clearMarks\",function(e){r"}, {"position": 53931, "context": "rmance.setupPolyfill=setupPolyfill,e.performance.setNavigationStart=function(e){eC=e},e.performance}ShadowPerformanceEntry.prototype.toJSON=function toJSON(){return{name:this.name,duration:this.duration,"}, {"position": 104037, "context": "d 0),(0,h._)(this,\"_observeOptions\",void 0),(0,h._)(this,\"_entries\",[]),(0,h._)(this,\"_interactEventsHandler\",void 0),(0,h._)(this,\"getFps\",void 0),this._state=\"initialization\",this._callback=e,this._int"}, {"position": 104150, "context": "d 0),(0,h._)(this,\"getFps\",void 0),this._state=\"initialization\",this._callback=e,this._interactEventsHandler=debounce(this.handleInteractEvent.bind(this),200,!0),this.getFps=r}return(0,v._)(InteractLaggy"}, {"position": 106480, "context": "ns();r&&r.interactionEventCollectType.forEach(function(r){window.addEventListener(r,e._interactEventsHandler,{capture:!0})})}},{key:\"removeInteractEventListener\",value:function removeInteractEventListene"}, {"position": 106723, "context": ");r&&r.interactionEventCollectType.forEach(function(r){window.removeEventListener(r,e._interactEventsHandler,!0)})}},{key:\"observe\",value:function observe(e){this._state=\"observe\",this._observeOptions=e,"}, {"position": 208484, "context": "Deprecated_warnDeprecated(e,r,i){(0,Q.ZK)(deprecatedStr(e,r,i))}function ui_changeTitle(e){if(c.YF.isHarmony)return(0,H.dw)(\"changeTitle\",e);logDeprecated_warnDeprecated(\"changeTitle\",\"document.title = '"}, {"position": 208810, "context": "\",e,r)}function closeWindow(){return(0,H.dw)(\"closeWindow\")}function confirmAntiSpam(){return c.YF.isHarmony?(0,H.dw)(\"confirmAntiSpam\"):bridgeAdapter_adapter(\"confirmAntiSpam\")}function completeUrl_comp"}, {"position": 210035, "context": "tGetDeviceInfo(e){return c.YF.isIOS&&(e.deviceId=e.uniqueId),e}function getDeviceInfoOld(){if(c.YF.isHarmony)return(0,H.dw)(\"getDeviceInfo\");function imeiType(){return version_appVersionLt(\"5.24\")?{}:c.Y"}, {"position": 210247, "context": "idfa:V().string.isRequired,idfv:V().string.isRequired}:{imei:V().string.isRequired}}var e={resT:V().shape((0,en._)({result:et,appMarket:V().string.isRequired,appVersion:V().string.isRequired,buildNumber:"}, {"position": 234030, "context": "\"6.5\",!version_appVersionLt(\"6.5\"))||y.YF.isIOS&&(r=\"6.9\",!version_appVersionLt(\"6.9\")),s={resT:E().shape({result:M,response:E().shape((0,v._)({userId:E().string.isRequired,nickname:E().string.isRequired"}, {"position": 234059, "context": "6.5\"))||y.YF.isIOS&&(r=\"6.9\",!version_appVersionLt(\"6.9\")),s={resT:E().shape({result:M,response:E().shape((0,v._)({userId:E().string.isRequired,nickname:E().string.isRequired,gender:E().number.isRequired"}, {"position": 234272, "context": ":E().string.isRequired,image:validators_urlType.isRequired,location:E().string.isRequired,flags:E().shape({fulishe:E().oneOfType([E().object]).isRequired,shequ:E().oneOfType([E().object]).isRequired})},v"}, {"position": 242768, "context": "ction(i){return i.name!==r||i.entryType!==e});var i=Y[r];i&&i.entryType===e&&(Y[r]=void 0)}function ShadowPerformanceEntry(e,r,i,a){this.name=e,this.duration=a,this.entryType=r,this.startTime=i}function "}, {"position": 243303, "context": "at(e,\"' is part of the PerformanceTiming interface, and cannot be used as a mark name.\"));var r=new ShadowPerformanceEntry(e,\"mark\",performance.now(),0);z.push(r),Y[e]=r}),setupPolyfill(\"measure\",functio"}, {"position": 243763, "context": "\"Failed to execute 'measure' on 'Performance': The mark '\".concat(i,\"' dose not exist.\"));var c=new ShadowPerformanceEntry(e,\"measure\",a,s-a);z.push(c),Y[e]=c}),setupPolyfill(\"clearMarks\",function(e){rem"}, {"position": 244537, "context": "ormance.setupPolyfill=setupPolyfill,e.performance.setNavigationStart=function(e){K=e},e.performance}ShadowPerformanceEntry.prototype.toJSON=function toJSON(){return{name:this.name,duration:this.duration,"}, {"position": 294122, "context": "oObject?e.sendData(e.serializeBinary(s,a.BinaryWriter),s.toObject()):e.sendData(s,s)}})}}},{key:\"flushApm\",value:function flushApm(e){var r=this;eA.then(function(i){var a,s,u=i||r.localDebug?(null===(a=r"}, {"position": 294147, "context": "alizeBinary(s,a.BinaryWriter),s.toObject()):e.sendData(s,s)}})}}},{key:\"flushApm\",value:function flushApm(e){var r=this;eA.then(function(i){var a,s,u=i||r.localDebug?(null===(a=r.apmXrayTrackerEndPoint)|"}, {"position": 296218, "context": "):r.measurement_name&&(null===(a=d.emitter)||void 0===a||a.push(r))},flush:function(){d.flush()},flushApm:function(e){var r;null===(r=d.emitter)||void 0===r||r.flushApm(e)},destroy:function(){null==d||d."}, {"position": 296281, "context": "(r))},flush:function(){d.flush()},flushApm:function(e){var r;null===(r=d.emitter)||void 0===r||r.flushApm(e)},destroy:function(){null==d||d.destroy()}}}var eF=i(94287),eV=i.n(eF),eH=function(e){function "}, {"position": 303933, "context": "\"})})}),device_judgePlatform=function(){if(y.YF.isIOS)return y.YF.isXHS?\"iOS\":\"iOSBrowser\";if(y.YF.isHarmonyArk)return y.YF.isXHS?\"Harmony\":\"HarmonyBrowser\";if(y.YF.isAndroid||y.YF.isHarmony)return y.YF."}, {"position": 304015, "context": "Browser\";if(y.YF.isHarmonyArk)return y.YF.isXHS?\"Harmony\":\"HarmonyBrowser\";if(y.YF.isAndroid||y.YF.isHarmony)return y.YF.isXHS?\"Android\":\"AndroidBrowser\";return\"PC\"},ez={appVersion:\"0.0.0\",systemVersion:"}, {"position": 319796, "context": "ingify(g.templateBuffers))}:r),[2]}})})),[2]}})}),function(e,r){return i.apply(this,arguments)}),flushApm:(a=(0,p._)(function(e,r){var i;return(0,_.Jh)(this,function(a){if(\"ApmXrayTracker\"===r){if(isUpgr"}, {"position": 320112, "context": "Time:Date.now()},e9[ex.NAME],e);\"function\"==typeof(null===(r=e6.ApmTracker)||void 0===r?void 0:r.flushApm)&&(null===(i=e6.ApmTracker)||void 0===i||i.flushApm(a))})}return[2]})}),function(e,r){return a.ap"}, {"position": 320164, "context": "ull===(r=e6.ApmTracker)||void 0===r?void 0:r.flushApm)&&(null===(i=e6.ApmTracker)||void 0===i||i.flushApm(a))})}return[2]})}),function(e,r){return a.apply(this,arguments)}),flush:(s=(0,p._)(function(e,r)"}, {"position": 356023, "context": "_adapter(\"closeWindow\")}function showTrack(e){return bridgeAdapter_adapter(\"showTrack\",e,{argsT:c().shape({title:c().string,content:c().string,isNewTrack:c().bool})})}function showApmTrack(e){return brid"}, {"position": 356172, "context": "rack:c().bool})})}function showApmTrack(e){return bridgeAdapter_adapter(\"showApmTrack\",e,{argsT:c().shape({content:c().string})})}function emitTrack(e){return bridgeAdapter_adapter(\"emitTrack\",e,{argsT:c"}, {"position": 357763, "context": "idfa:c().string.isRequired,idfv:c().string.isRequired}:{imei:c().string.isRequired}}var e={resT:c().shape((0,A._)({result:C,appMarket:c().string.isRequired,appVersion:c().string.isRequired,buildNumber:c("}, {"position": 359053, "context": "Legacy(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{extractValue:!1},r={resT:c().shape({result:C,value:c().oneOf([\"WiFi\",\"4G\",\"3G+\",\"3G\",\"2G\",\"NONE\"])})};return bridgeAdapter_adapter(\""}, {"position": 360028, "context": "rn[2,getNetworkTypeLegacy(e)]}})})).apply(this,arguments)}function getTrackEnvOld(){var e={resT:c().shape({isTestEnv:c().bool,uploadOneByOne:c().bool,sessionId:c().string})};return bridgeAdapter_adapter("}, {"position": 362168, "context": "droid:\"5.7.0\"},viewDisappear:{ios:\"5.6.0\",android:\"5.7.0\"},keyboardChange:{ios:\"5.7.0\",namespace:\"XHS<PERSON><PERSON><PERSON>\"},afterLogin:{ios:\"5.22.0\",android:\"5.22.0\",namespace:\"XHS<PERSON><PERSON>ler\"},dataTrafficChange:{ios:\"5."}, {"position": 362234, "context": "dChange:{ios:\"5.7.0\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},afterLogin:{ios:\"5.22.0\",android:\"5.22.0\",namespace:\"XHS<PERSON><PERSON><PERSON>\"},dataTrafficChange:{ios:\"5.42\",android:\"5.42\",namespace:\"XHSHandler\"},themeTypeChange:{ios:\"6"}, {"position": 362303, "context": ",android:\"5.22.0\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},dataTrafficChange:{ios:\"5.42\",android:\"5.42\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},themeTypeChange:{ios:\"6.13\",android:\"6.12\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},faceRecognitionResult:{io"}, {"position": 362370, "context": ".42\",android:\"5.42\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},themeTypeChange:{ios:\"6.13\",android:\"6.12\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},faceRecognitionResult:{ios:\"6.58\",android:\"6.58\",namespace:\"XHS<PERSON><PERSON><PERSON>\"},onBomLotteryStart:{"}, {"position": 362443, "context": "ndroid:\"6.12\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},faceRecognitionResult:{ios:\"6.58\",android:\"6.58\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},onBomLotteryStart:{ios:\"6.63\",android:\"6.63\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},onBomLotteryEnd:{ios:\"6"}, {"position": 362512, "context": "8\",android:\"6.58\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},onBomLotteryStart:{ios:\"6.63\",android:\"6.63\",namespace:\"<PERSON><PERSON><PERSON><PERSON><PERSON>\"},onBomLotteryEnd:{ios:\"6.63\",android:\"6.63\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},onHitCouponFinish:{ios:\"6"}, {"position": 362579, "context": ".63\",android:\"6.63\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},onBomLotteryEnd:{ios:\"6.63\",android:\"6.63\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},onHitCouponFinish:{ios:\"6.65\",android:\"6.65\",namespace:\"XHSHandler\"},receiveTrickleMessage:{"}, {"position": 362648, "context": "3\",android:\"6.63\",namespace:\"<PERSON>HS<PERSON><PERSON><PERSON>\"},onHitCouponFinish:{ios:\"6.65\",android:\"6.65\",namespace:\"XHS<PERSON><PERSON><PERSON>\"},receiveTrickleMessage:{android:\"6.81.0\",namespace:\"XHS<PERSON><PERSON>ler\"}};function supportEvent(e){va"}, {"position": 362712, "context": ":\"6.65\",android:\"6.65\",namespace:\"XHS<PERSON>andler\"},receiveTrickleMessage:{android:\"6.81.0\",namespace:\"XHSHandler\"}};function supportEvent(e){var r=L[e];if(!r)return!1;return s.YF.isIOS&&r.ios&&!appVersionLt("}, {"position": 362918, "context": "os)||s.YF.isAndroid&&r.android&&!appVersionLt(r.android)}function addCalledJsListener(e,r){window.XHSHandler=window.XHSHandler||{},window.XHSHandler[\"\".concat(e,\"CallbackList\")]=window.XHSHandler[\"\".conc"}, {"position": 362936, "context": "d&&r.android&&!appVersionLt(r.android)}function addCalledJsListener(e,r){window.XHSHandler=window.XHSHandler||{},window.XHSHandler[\"\".concat(e,\"CallbackList\")]=window.XHSHandler[\"\".concat(e,\"CallbackList"}, {"position": 362958, "context": "ionLt(r.android)}function addCalledJsListener(e,r){window.XHSHandler=window.XHSHandler||{},window.XHSHandler[\"\".concat(e,\"CallbackList\")]=window.XHSHandler[\"\".concat(e,\"CallbackList\")]||[];for(var i=wind"}, {"position": 363005, "context": ",r){window.XHSHandler=window.XHSHandler||{},window.XHSHandler[\"\".concat(e,\"CallbackList\")]=window.XHSHandler[\"\".concat(e,\"CallbackList\")]||[];for(var i=window.XHSHandler[\"\".concat(e,\"CallbackList\")],a=0;"}, {"position": 363066, "context": "[\"\".concat(e,\"CallbackList\")]=window.XHSHandler[\"\".concat(e,\"CallbackList\")]||[];for(var i=window.XHSHandler[\"\".concat(e,\"CallbackList\")],a=0;a<i.length;a++)if(i[a]===r)return;i.push(r)}function generate"}, {"position": 363235, "context": "return;i.push(r)}function generateCalledJsCallback(e){return function(r){var i;((null===(i=window.XHSHandler)||void 0===i?void 0:i[\"\".concat(e,\"CallbackList\")])||[]).forEach(function(e){e(r)})}}function "}, {"position": 369767, "context": "sFullscreen||_&&m.isNavi<PERSON>id<PERSON>||C&&m.isFullscreen}return(0,s._)((0,a._)({},d),{isIOS:_,isAndroid:y,isHarmony:w,isHarmonyArk:E,isXHS:k,isFullscreen:I,isWeixin:\"micromessenger\"===v,isAlipay:\"alipay\"===v,is"}, {"position": 369779, "context": "|_&&m.isNavi<PERSON>idden||C&&m.isFullscreen}return(0,s._)((0,a._)({},d),{isIOS:_,isAndroid:y,isHarmony:w,isHarmonyArk:E,isXHS:k,isFullscreen:I,isWeixin:\"micromessenger\"===v,isAlipay:\"alipay\"===v,isWeibo:\"weibo"}, {"position": 381218, "context": "iProgram.postMessage({data:{methodName:r,data:i}}),Promise.resolve()):Promise.resolve()}function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e})}function showToast(e){var r=argument"}, {"position": 381265, "context": "}}),Promise.resolve()):Promise.resolve()}function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e})}function showToast(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"}, {"position": 381630, "context": "(\"toast\",(0,ex._)({message:e},r))}function showAlert(e){var r=\"showalertV2\";checkArgs(e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,callback:eG"}, {"position": 381698, "context": "lertV2\";checkArgs(e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,callback:eG().func}))})}.argsT,\"showAlert\");var i=(0,eB._)((0,ex._)({},e),{des:"}, {"position": 382111, "context": "i.actions=a,adapter(r,i)}function showActionSheet(e){return adapter(\"showActionSheet\",e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,value:eG()."}, {"position": 382179, "context": "\"showActionSheet\",e,{argsT:eG().shape({title:eG().string,desc:eG().string,actions:eG().arrayOf(eG().shape({name:eG().string,value:eG().string.isRequired}))}),resT:eG().shape({result:eX,value:eG().string}"}, {"position": 382247, "context": "tring,actions:eG().arrayOf(eG().shape({name:eG().string,value:eG().string.isRequired}))}),resT:eG().shape({result:eX,value:eG().string})})}function setNavigationHidden(){return adapter(\"setNavigationHidd"}, {"position": 382365, "context": "alue:eG().string})})}function setNavigationHidden(){return adapter(\"setNavigationHidden\",{resT:eG().shape({result:eX})})}function showNavigationRightBarButtonItem(e){var r={argsT:eG().shape({visible:eG()"}, {"position": 382449, "context": "den\",{resT:eG().shape({result:eX})})}function showNavigationRightBarButtonItem(e){var r={argsT:eG().shape({visible:eG().bool,buttonTitle:eG().string,buttonIcon:eG().string,handler:eG().func}),resT:eG().s"}, {"position": 382551, "context": "ape({visible:eG().bool,buttonTitle:eG().string,buttonIcon:eG().string,handler:eG().func}),resT:eG().shape({result:eX})};e.button_title&&logDeprecated_warnDeprecated(\"args.button_title\",\"args.buttonTitle\""}, {"position": 382893, "context": ";var i=e.handler,a=(0,eD._)(e,[\"handler\"]),s=(0,eJ.cJ)(a);return i&&\"function\"==typeof i&&(window.XHSHandler=(0,eB._)((0,ex._)({},window.XHSHandler),{navigationRightBarButtonItem:i})),adapter(\"showNaviga"}, {"position": 382932, "context": "\"]),s=(0,eJ.cJ)(a);return i&&\"function\"==typeof i&&(window.XHSHandler=(0,eB._)((0,ex._)({},window.XHSHandler),{navigationRightBarButtonItem:i})),adapter(\"showNavigationRightBarButtonItemV2\",s,{resT:r.res"}, {"position": 383609, "context": "0===arguments[1]||arguments[1];return adapter(\"alwaysBounceIOS\",{direction:e,bounces:r},{argsT:eG().shape({direction:eG().oneOf([\"vertical\",\"horizontal\"]).isRequired,bounces:eG().bool.isRequired})})}func"}, {"position": 383723, "context": "on:eG().oneOf([\"vertical\",\"horizontal\"]).isRequired,bounces:eG().bool.isRequired})})}function ui_setShareInfo(e){var r={argsT:eG().shape({contentType:eG().string,title:eG().string,content:eG().string,lin"}, {"position": 383754, "context": "zontal\"]).isRequired,bounces:eG().bool.isRequired})})}function ui_setShareInfo(e){var r={argsT:eG().shape({contentType:eG().string,title:eG().string,content:eG().string,linkurl:urlType,imageurl:urlType,t"}, {"position": 383888, "context": "le:eG().string,content:eG().string,linkurl:urlType,imageurl:urlType,type:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,t"}, {"position": 383912, "context": "G().string,linkurl:urlType,imageurl:urlType,type:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,thumb:eG().string,usernam"}, {"position": 384071, "context": "e,path:eG().string,thumb:eG().string,username:eG().string}),friend:eG().oneOfType([eG().string,eG().shape({source:eG().string,type:eG().oneOf([\"general\",\"goodsDetail\",\"center\",\"topic\",\"event\"]),title:eG("}, {"position": 384381, "context": "().string,brandName:eG().string,id:eG().string,desc:eG().string,price:eG().number})])})}),resT:eG().shape({result:eX})},i=e;if(i){(i=(0,ex._)({},i)).type&&!eZ&&(i.type=i.type.replace(/WXMiniProgram/,\"Wei"}, {"position": 384739, "context": "a=i.extension)||void 0===a?void 0:a.friend);s&&(i.extension.friend=s)}return ek.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}funct"}, {"position": 384764, "context": "a?void 0:a.friend);s&&(i.extension.friend=s)}return ek.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r"}, {"position": 384793, "context": "nsion.friend=s)}return ek.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:eG().shape({type:eG"}, {"position": 384825, "context": "iniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:eG().shape({type:eG().string.isRequired,contentType"}, {"position": 384846, "context": "fo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:eG().shape({type:eG().string.isRequired,contentType:eG().oneOf([\"link\",\""}, {"position": 384882, "context": "nction showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:eG().shape({type:eG().string.isRequired,contentType:eG().oneOf([\"link\",\"image\",\"text\"]),title:eG().string,co"}, {"position": 385076, "context": "string,content:eG().string,linkurl:urlType,imageurl:urlType,base64string:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,t"}, {"position": 385100, "context": "ng,linkurl:urlType,imageurl:urlType,base64string:eG().string,extension:eG().shape({miniprogram:eG().shape({title:eG().string,desc:eG().string,webpageurl:urlType,path:eG().string,thumb:eG().string,usernam"}, {"position": 385553, "context": "G().string,brandName:eG().string,id:eG().string,desc:eG().string,price:eG().number}])})}),resT:eG().shape({result:eX})},a=(0,ex._)({},e);a.type&&!eZ&&(a.type=e.type.replace(/WXMiniProgram/,\"WeixinSession"}, {"position": 385885, "context": "riend(null===(r=a.extension)||void 0===r?void 0:r.friend);return s&&(a.extension.friend=s),adapter(\"shareContentV2\",a,i)}function pay(e){return adapter(\"pay\",e)}function alipayClient(e){return adapter(\"a"}, {"position": 386043, "context": "e)}function alipayClient(e){return adapter(\"alipayClient\",e,{argsT:eG().string.isRequired,resT:eG().shape({result:eX,orderid:eG().string.isRequired})})}function openURLByWechat(e){return adapter(\"openURL"}, {"position": 386170, "context": "().string.isRequired})})}function openURLByWechat(e){return adapter(\"openURLByWechat\",e,{argsT:eG().shape({url:eG().string.isRequired})})}function wechatPayClient(e){return adapter(\"wechatPayClient\",e,{a"}, {"position": 386311, "context": "ction wechatPayClient(e){return adapter(\"wechatPayClient\",e,{argsT:eG().string.isRequired,resT:eG().shape({result:eX,orderid:eG().string.isRequired})})}function supportApplePayIOS(){return adapter(\"suppo"}, {"position": 387783, "context": "})})).apply(this,arguments)}function setPasteBoard(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX})};return checkArgs(e,r.argsT,\"setPasteBoard\"),adapter(\"setPasteBoard\",{string:e},{res"}, {"position": 387960, "context": "asteBoard\",{string:e},{resT:r.resT})}function showTrack(e){return adapter(\"showTrack\",e,{argsT:eG().shape({title:eG().string,content:eG().string,isNewTrack:eG().bool})})}function showApmTrack(e){return a"}, {"position": 388099, "context": "tring,isNewTrack:eG().bool})})}function showApmTrack(e){return adapter(\"showApmTrack\",e,{argsT:eG().shape({content:eG().string})})}function webtrack(e){return adapter(\"webtrack\",e,{argsT:eG().object})}fu"}, {"position": 388455, "context": "ing.isRequired})}function openMapWithLocation(e){return adapter(\"openMapWithLocation\",e,{argsT:eG().shape({lat:eG().number,long:eG().number,direction:eG().bool,name:eG().string,coordinate:eG().shape({wgs"}, {"position": 388548, "context": "T:eG().shape({lat:eG().number,long:eG().number,direction:eG().bool,name:eG().string,coordinate:eG().shape({wgs84:eG().shape({long:eG().number,lat:eG().number}),gcj02:eG().shape({long:eG().number,lat:eG()"}, {"position": 388566, "context": "eG().number,long:eG().number,direction:eG().bool,name:eG().string,coordinate:eG().shape({wgs84:eG().shape({long:eG().number,lat:eG().number}),gcj02:eG().shape({long:eG().number,lat:eG().number}),bd09:eG("}, {"position": 388619, "context": ":eG().string,coordinate:eG().shape({wgs84:eG().shape({long:eG().number,lat:eG().number}),gcj02:eG().shape({long:eG().number,lat:eG().number}),bd09:eG().shape({long:eG().number,lat:eG().number})})}),resT:"}, {"position": 388671, "context": "({long:eG().number,lat:eG().number}),gcj02:eG().shape({long:eG().number,lat:eG().number}),bd09:eG().shape({long:eG().number,lat:eG().number})})}),resT:eG().shape({result:eX,type:eG().oneOf([\"apple\",\"baid"}, {"position": 388727, "context": "ong:eG().number,lat:eG().number}),bd09:eG().shape({long:eG().number,lat:eG().number})})}),resT:eG().shape({result:eX,type:eG().oneOf([\"apple\",\"baidu\",\"amap\",\"google\"]).isRequired})})}function confirmAnti"}, {"position": 388936, "context": "{return adapter(\"confirmAntiSpam\")}function addComment(e){return adapter(\"addComment\",e,{argsT:eG().shape({placeholder:eG().string,uid:eG().string}),resT:eG().shape({uid:eG().string,is_send:eG().bool,dat"}, {"position": 388995, "context": "eturn adapter(\"addComment\",e,{argsT:eG().shape({placeholder:eG().string,uid:eG().string}),resT:eG().shape({uid:eG().string,is_send:eG().bool,data:eG().shape({content:eG().string,at_users:eG().arrayOf(eG("}, {"position": 389046, "context": "ceholder:eG().string,uid:eG().string}),resT:eG().shape({uid:eG().string,is_send:eG().bool,data:eG().shape({content:eG().string,at_users:eG().arrayOf(eG().shape({userid:eG().string,nickname:eG().string}))"}, {"position": 389100, "context": "({uid:eG().string,is_send:eG().bool,data:eG().shape({content:eG().string,at_users:eG().arrayOf(eG().shape({userid:eG().string,nickname:eG().string}))})})})}function openXhsSystemSettings(){return _openXh"}, {"position": 389643, "context": "penFansPanel\")}function checkLoginWithAction(e){return adapter(\"checkLoginWithAction\",e,{argsT:eG().shape({type:eG().number.isRequired}),resT:eG().shape({result:eX})})}function logout(){return adapter(\"l"}, {"position": 389690, "context": "{return adapter(\"checkLoginWithAction\",e,{argsT:eG().shape({type:eG().number.isRequired}),resT:eG().shape({result:eX})})}function logout(){return adapter(\"logout\")}function isAppInstalled(e){return adapt"}, {"position": 389827, "context": "){return adapter(\"logout\")}function isAppInstalled(e){return adapter(\"isAppInstalled\",e,{argsT:eG().shape({iOS:eG().string,Android:eG().string}),resT:eG().shape({result:eX,value:eG().bool.isRequired})})}"}, {"position": 389882, "context": "eturn adapter(\"isAppInstalled\",e,{argsT:eG().shape({iOS:eG().string,Android:eG().string}),resT:eG().shape({result:eX,value:eG().bool.isRequired})})}function getAppInfo(){return adapter(\"getAppInfo\",{resT"}, {"position": 389991, "context": "sult:eX,value:eG().bool.isRequired})})}function getAppInfo(){return adapter(\"getAppInfo\",{resT:eG().shape({result:eX,version:eG().string.isRequired,build:eG().string.isRequired,jsversion:eG().string.isRe"}, {"position": 390369, "context": ":eG().string.isRequired,idfv:eG().string.isRequired}:{imei:eG().string.isRequired}}var e={resT:eG().shape((0,ex._)({result:eX,appMarket:eG().string.isRequired,appVersion:eG().string.isRequired,buildNumbe"}, {"position": 391189, "context": "egacy(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{extractValue:!1},r={resT:eG().shape({result:eX,value:eG().oneOf([\"WiFi\",\"4G\",\"3G+\",\"3G\",\"2G\",\"NONE\"])})};return adapter(\"getNetworkTy"}, {"position": 391863, "context": "fo see readme\");var i=ek.YF.isAndroid&&(0,ek.S8)(\"6.5\")||ek.YF.isIOS&&(0,ek.S8)(\"6.9\"),a={resT:eG().shape({result:eX,response:eG().shape((0,ex._)({userId:eG().string.isRequired,nickname:eG().string.isReq"}, {"position": 391894, "context": "ndroid&&(0,ek.S8)(\"6.5\")||ek.YF.isIOS&&(0,ek.S8)(\"6.9\"),a={resT:eG().shape({result:eX,response:eG().shape((0,ex._)({userId:eG().string.isRequired,nickname:eG().string.isRequired,gender:eG().number.isRequ"}, {"position": 392103, "context": "serToken:eG().string.isRequired,image:urlType.isRequired,location:eG().string.isRequired,flags:eG().shape({fulishe:eG().oneOfType([eG().object]).isRequired,shequ:eG().oneOfType([eG().object]).isRequired}"}, {"position": 393591, "context": "\"getUserInfoOld\",timing:Date.now()-r,logType:<PERSON><PERSON>}),s})}function getTrackEnv(){var e={resT:eG().shape({isTestEnv:eG().bool,uploadOneByOne:eG().bool,sessionId:eG().string})};return adapter(\"getTrackEn"}, {"position": 393884, "context": "eckRes(i,e.resT),i})}function lowPowerModeEnabled(){return adapter(\"lowPowerModeEnabled\",{resT:eG().shape({result:eX,value:eG().bool.isRequired})})}function requestNotificationPermission(e){return adapte"}, {"position": 394035, "context": "ction requestNotificationPermission(e){return adapter(\"requestNotificationPermission\",e,{argsT:eG().shape({engaingType:eG().number.isRequired,engaingMessage:eG().string})})}function saveImage(e){return a"}, {"position": 394171, "context": "uired,engaingMessage:eG().string})})}function saveImage(e){return adapter(\"saveImage\",e,{argsT:eG().shape({url:urlType,base64string:eG().string,type:eG().string.isRequired}),resT:eG().shape({result:eX})}"}, {"position": 394255, "context": "\",e,{argsT:eG().shape({url:urlType,base64string:eG().string,type:eG().string.isRequired}),resT:eG().shape({result:eX})})}function basicSendClientRequest(e){return adapter(\"sendClientRequest\",e)}function "}, {"position": 394563, "context": "ttp](https://code.devops.xiaohongshu.com/formula/launcher/tree/master/src/http)\");var r={argsT:eG().shape({url:eG().string,type:eG().oneOf([\"GET\",\"POST\",\"PUT\",\"DELETE\"]).isRequired,data:eG().oneOfType([e"}, {"position": 394720, "context": "\",\"DELETE\"]).isRequired,data:eG().oneOfType([eG().object]),transform:eG().oneOfType([eG().bool,eG().shape({separateNumber:eG().bool}),eG().func])}),resT:eG().shape({result:eX,response:eG().oneOfType([eG("}, {"position": 394778, "context": ",transform:eG().oneOfType([eG().bool,eG().shape({separateNumber:eG().bool}),eG().func])}),resT:eG().shape({result:eX,response:eG().oneOfType([eG().object]),status:eG().number})};checkArgs(e,r.argsT,\"send"}, {"position": 396035, "context": "er,eG().object,eG().string])})}function getItemOld(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX,value:eG().string})};return checkArgs(e,r.argsT,\"getItem\"),adapter(\"getItem\",{key:e},{"}, {"position": 396429, "context": "apply(this,arguments)}function setItemOld(e,r){return adapter(\"setItem\",{key:e,value:r},{argsT:eG().shape({key:eG().string.isRequired,value:eG().string.isRequired}),resT:eG().shape({result:eX})})}functio"}, {"position": 396504, "context": "y:e,value:r},{argsT:eG().shape({key:eG().string.isRequired,value:eG().string.isRequired}),resT:eG().shape({result:eX})})}function setItem(e,r){return _setItem.apply(this,arguments)}function _setItem(){re"}, {"position": 396802, "context": "})})).apply(this,arguments)}function removeItemOld(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eX})};return checkArgs(e,r.argsT,\"removeItem\"),adapter(\"removeItem\",{key:e},{resT:r.resT}"}, {"position": 397248, "context": "n adapter(\"broadcast\",e)}function broadcastNative(e){return adapter(\"broadcastNative\",e,{argsT:eG().shape({key:eG().string.isRequired,data:eG().string.isRequired})})}function getMessageStatusIOS(){return"}, {"position": 397390, "context": ").string.isRequired})})}function getMessageStatusIOS(){return adapter(\"getMessageStatus\",{resT:eG().shape({result:eX,status:eG().oneOf([0,1]).isRequired})})}function getThirdAuth(e){return adapter(\"getTh"}, {"position": 397555, "context": " getThirdAuth(e){return adapter(\"getThirdAuth\",e,{argsT:eG().oneOf([\"weixin\"]).isRequired,resT:eG().shape({result:eX,value:eG().oneOfType([eG().object])})})}function getCurrentGeolocation(){return adapte"}, {"position": 397762, "context": "etCurrentGeolocation\")}function checkAppPermission(e){var r={argsT:eG().string.isRequired,resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,state:eG().oneOf([\"denied\",\"granted\",\"undeterminated\"])})},"}, {"position": 398023, "context": ":i,r)}function areNotificationsEnabledAndroid(){return adapter(\"areNotificationsEnabled\",{resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,state:eG().oneOf([\"denied\",\"granted\"])})})}function getFile"}, {"position": 398430, "context": "(this,function(i){switch(i.label){case 0:return[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:eG().shape({url:urlType.isRequired}),resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,value:urlType,mes"}, {"position": 398472, "context": "eturn[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:eG().shape({url:urlType.isRequired}),resT:eG().shape({result:eG().oneOf([0,-1]).isRequired,value:urlType,message:eG().string})})];case 1:if(0===(r=i.s"}, {"position": 399996, "context": ":showNavigationRightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePa"}, {"position": 400011, "context": "RightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApp"}, {"position": 400024, "context": "nItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,appl"}, {"position": 400037, "context": "igationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,applePayClient:ap"}, {"position": 400613, "context": "etStatusBarTextColor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat"}, {"position": 400629, "context": "olor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat"}, {"position": 400643, "context": "BarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClie"}, {"position": 400657, "context": "etStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClient:wechatPayCl"}, {"position": 410734, "context": "_rn_card\",<PERSON><PERSON>backAction=\"seller-feedback-action\",<PERSON><PERSON>ProfileEntries=\"setProfileEntries\",<PERSON>.ShareLiveTrailerCard=\"share_live_trailer_card\",<PERSON><PERSON>ShowTopicReadTask=\"show_topic_read_task\",<PERSON><PERSON>"}, {"position": 410756, "context": "backAction=\"seller-feedback-action\",<PERSON><PERSON>ProfileEntries=\"setProfileEntries\",E.ShareLiveTrailerCard=\"share_live_trailer_card\",E<PERSON>ShowTopicReadTask=\"show_topic_read_task\",<PERSON><PERSON>ntered=\"size-been-entere"}, {"position": 411516, "context": "ionStart\",T.RouterStart=\"routerStart\",T.ViewRenderEnd=\"viewRenderEnd\",(S=ea||(ea={})).More=\"more\",S.Share=\"share\",(b=es||(es={})).Center=\"center\",b.Event=\"event\",b.General=\"general\",b.GoodsDetail=\"goodsD"}, {"position": 411523, "context": "t\",T.RouterStart=\"routerStart\",T.ViewRenderEnd=\"viewRenderEnd\",(S=ea||(ea={})).More=\"more\",S.Share=\"share\",(b=es||(es={})).Center=\"center\",b.Event=\"event\",b.General=\"general\",b.GoodsDetail=\"goodsDetail\","}, {"position": 412566, "context": "rEvent:function(){return findCalendarEvent},invokeAurora:function(){return xhsaurora_invoke_invoke},ShareToFriendType:function(){return e_},subscribeAurora:function(){return xhsaurora_invoke_subscribe},O"}, {"position": 424802, "context": "_rn_card\",<PERSON><PERSON>backAction=\"seller-feedback-action\",<PERSON><PERSON>ProfileEntries=\"setProfileEntries\",S.ShareLiveTrailerCard=\"share_live_trailer_card\",S<PERSON>ShowTopicReadTask=\"show_topic_read_task\",<PERSON><PERSON>nt"}, {"position": 424824, "context": "backAction=\"seller-feedback-action\",<PERSON><PERSON>SetProfileEntries=\"setProfileEntries\",S.ShareLiveTrailerCard=\"share_live_trailer_card\",S.ShowTopicReadTask=\"show_topic_read_task\",<PERSON><PERSON>Entered=\"size-been-entere"}, {"position": 425588, "context": "nStart\",b.RouterStart=\"routerStart\",b.ViewRenderEnd=\"viewRenderEnd\",b);var em=((k={}).More=\"more\",k.Share=\"share\",k);var e_=((C={}).Center=\"center\",C.Event=\"event\",C.<PERSON>=\"general\",C.<PERSON>sDetail=\"good"}, {"position": 425595, "context": ",b.RouterStart=\"routerStart\",b.ViewRenderEnd=\"viewRenderEnd\",b);var em=((k={}).More=\"more\",k.Share=\"share\",k);var e_=((C={}).Center=\"center\",C.Event=\"event\",C.<PERSON>=\"general\",C.GoodsDetail=\"goodsDetail"}, {"position": 432998, "context": "iProgram.postMessage({data:{methodName:r,data:i}}),Promise.resolve()):Promise.resolve()}function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e})}function showToast(e){var r=argument"}, {"position": 433045, "context": "}}),Promise.resolve()):Promise.resolve()}function setShareInfo(e){return postNotice({methodName:\"setShareInfo\",data:e})}function showToast(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"}, {"position": 433408, "context": "er(\"toast\",(0,y._)({message:e},r))}function showAlert(e){var r=\"showalertV2\";checkArgs(e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,callback:O().fun"}, {"position": 433472, "context": "showalertV2\";checkArgs(e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,callback:O().func}))})}.argsT,\"showAlert\");var i=(0,w._)((0,y._)({},e),{des:e.de"}, {"position": 433879, "context": ",i.actions=a,adapter(r,i)}function showActionSheet(e){return adapter(\"showActionSheet\",e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,value:O().string"}, {"position": 433943, "context": "pter(\"showActionSheet\",e,{argsT:O().shape({title:O().string,desc:O().string,actions:O().arrayOf(O().shape({name:O().string,value:O().string.isRequired}))}),resT:O().shape({result:L,value:O().string})})}f"}, {"position": 434008, "context": "O().string,actions:O().arrayOf(O().shape({name:O().string,value:O().string.isRequired}))}),resT:O().shape({result:L,value:O().string})})}function setNavigationHidden(){return adapter(\"setNavigationHidden"}, {"position": 434123, "context": ",value:O().string})})}function setNavigationHidden(){return adapter(\"setNavigationHidden\",{resT:O().shape({result:L})})}function showNavigationRightBarButtonItem(e){var r={argsT:O().shape({visible:O().bo"}, {"position": 434205, "context": "Hidden\",{resT:O().shape({result:L})})}function showNavigationRightBarButtonItem(e){var r={argsT:O().shape({visible:O().bool,buttonTitle:O().string,buttonIcon:O().string,handler:O().func}),resT:O().shape("}, {"position": 434302, "context": "().shape({visible:O().bool,buttonTitle:O().string,buttonIcon:O().string,handler:O().func}),resT:O().shape({result:L})};e.button_title&&logDeprecated_warnDeprecated(\"args.button_title\",\"args.buttonTitle\")"}, {"position": 434641, "context": "\");var i=e.handler,a=(0,E._)(e,[\"handler\"]),s=(0,N.cJ)(a);return i&&\"function\"==typeof i&&(window.XHSHandler=(0,w._)((0,y._)({},window.XHSHandler),{navigationRightBarButtonItem:i})),adapter(\"showNavigati"}, {"position": 434678, "context": "ler\"]),s=(0,N.cJ)(a);return i&&\"function\"==typeof i&&(window.XHSHandler=(0,w._)((0,y._)({},window.XHSHandler),{navigationRightBarButtonItem:i})),adapter(\"showNavigationRightBarButtonItemV2\",s,{resT:r.res"}, {"position": 435353, "context": " 0===arguments[1]||arguments[1];return adapter(\"alwaysBounceIOS\",{direction:e,bounces:r},{argsT:O().shape({direction:O().oneOf([\"vertical\",\"horizontal\"]).isRequired,bounces:O().bool.isRequired})})}functi"}, {"position": 435465, "context": "tion:O().oneOf([\"vertical\",\"horizontal\"]).isRequired,bounces:O().bool.isRequired})})}function ui_setShareInfo(e){var r={argsT:O().shape({contentType:O().string,title:O().string,content:O().string,linkurl"}, {"position": 435495, "context": "rizontal\"]).isRequired,bounces:O().bool.isRequired})})}function ui_setShareInfo(e){var r={argsT:O().shape({contentType:O().string,title:O().string,content:O().string,linkurl:urlType,imageurl:urlType,type"}, {"position": 435624, "context": ",title:O().string,content:O().string,linkurl:urlType,imageurl:urlType,type:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb"}, {"position": 435647, "context": "nt:O().string,linkurl:urlType,imageurl:urlType,type:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb:O().string,username:O("}, {"position": 435798, "context": "urlType,path:O().string,thumb:O().string,username:O().string}),friend:O().oneOfType([O().string,O().shape({source:O().string,type:O().oneOf([\"general\",\"goodsDetail\",\"center\",\"topic\",\"event\"]),title:O().s"}, {"position": 436094, "context": "tar:O().string,brandName:O().string,id:O().string,desc:O().string,price:O().number})])})}),resT:O().shape({result:L})},i=e;if(i){(i=(0,y._)({},i)).type&&!D&&(i.type=i.type.replace(/WXMiniProgram/,\"Weixin"}, {"position": 436448, "context": "(a=i.extension)||void 0===a?void 0:a.friend);u&&(i.extension.friend=u)}return s.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}funct"}, {"position": 436473, "context": "=a?void 0:a.friend);u&&(i.extension.friend=u)}return s.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r"}, {"position": 436502, "context": "ension.friend=u)}return s.YF.isMiniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:O().shape({type:O()"}, {"position": 436534, "context": "iniprogram?setShareInfo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:O().shape({type:O().string.isRequired,contentType:O"}, {"position": 436555, "context": "fo(i):adapter(\"setShareInfo\",i,r)}function showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:O().shape({type:O().string.isRequired,contentType:O().oneOf([\"link\",\"ima"}, {"position": 436590, "context": "unction showShareMenu(){return adapter(\"showShareMenu\")}function shareContent(e){var r,i={argsT:O().shape({type:O().string.isRequired,contentType:O().oneOf([\"link\",\"image\",\"text\"]),title:O().string,conte"}, {"position": 436778, "context": "().string,content:O().string,linkurl:urlType,imageurl:urlType,base64string:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb"}, {"position": 436801, "context": "tring,linkurl:urlType,imageurl:urlType,base64string:O().string,extension:O().shape({miniprogram:O().shape({title:O().string,desc:O().string,webpageurl:urlType,path:O().string,thumb:O().string,username:O("}, {"position": 437233, "context": "atar:O().string,brandName:O().string,id:O().string,desc:O().string,price:O().number}])})}),resT:O().shape({result:L})},a=(0,y._)({},e);a.type&&!D&&(a.type=e.type.replace(/WXMiniProgram/,\"WeixinSession\"))"}, {"position": 437562, "context": "riend(null===(r=a.extension)||void 0===r?void 0:r.friend);return s&&(a.extension.friend=s),adapter(\"shareContentV2\",a,i)}function pay(e){return adapter(\"pay\",e)}function alipayClient(e){return adapter(\"a"}, {"position": 437718, "context": "\",e)}function alipayClient(e){return adapter(\"alipayClient\",e,{argsT:O().string.isRequired,resT:O().shape({result:L,orderid:O().string.isRequired})})}function openURLByWechat(e){return adapter(\"openURLBy"}, {"position": 437842, "context": "O().string.isRequired})})}function openURLByWechat(e){return adapter(\"openURLByWechat\",e,{argsT:O().shape({url:O().string.isRequired})})}function wechatPayClient(e){return adapter(\"wechatPayClient\",e,{ar"}, {"position": 437980, "context": "unction wechatPayClient(e){return adapter(\"wechatPayClient\",e,{argsT:O().string.isRequired,resT:O().shape({result:L,orderid:O().string.isRequired})})}function supportApplePayIOS(){return adapter(\"support"}, {"position": 439434, "context": ")]})})).apply(this,arguments)}function setPasteBoard(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L})};return checkArgs(e,r.argsT,\"setPasteBoard\"),adapter(\"setPasteBoard\",{string:e},{resT"}, {"position": 439609, "context": "PasteBoard\",{string:e},{resT:r.resT})}function showTrack(e){return adapter(\"showTrack\",e,{argsT:O().shape({title:O().string,content:O().string,isNewTrack:O().bool})})}function showApmTrack(e){return adap"}, {"position": 439744, "context": ".string,isNewTrack:O().bool})})}function showApmTrack(e){return adapter(\"showApmTrack\",e,{argsT:O().shape({content:O().string})})}function webtrack(e){return adapter(\"webtrack\",e,{argsT:O().object})}func"}, {"position": 440095, "context": "ring.isRequired})}function openMapWithLocation(e){return adapter(\"openMapWithLocation\",e,{argsT:O().shape({lat:O().number,long:O().number,direction:O().bool,name:O().string,coordinate:O().shape({wgs84:O("}, {"position": 440183, "context": ",{argsT:O().shape({lat:O().number,long:O().number,direction:O().bool,name:O().string,coordinate:O().shape({wgs84:O().shape({long:O().number,lat:O().number}),gcj02:O().shape({long:O().number,lat:O().numbe"}, {"position": 440200, "context": "({lat:O().number,long:O().number,direction:O().bool,name:O().string,coordinate:O().shape({wgs84:O().shape({long:O().number,lat:O().number}),gcj02:O().shape({long:O().number,lat:O().number}),bd09:O().shap"}, {"position": 440250, "context": "l,name:O().string,coordinate:O().shape({wgs84:O().shape({long:O().number,lat:O().number}),gcj02:O().shape({long:O().number,lat:O().number}),bd09:O().shape({long:O().number,lat:O().number})})}),resT:O().s"}, {"position": 440299, "context": ".shape({long:O().number,lat:O().number}),gcj02:O().shape({long:O().number,lat:O().number}),bd09:O().shape({long:O().number,lat:O().number})})}),resT:O().shape({result:L,type:O().oneOf([\"apple\",\"baidu\",\"a"}, {"position": 440352, "context": "ape({long:O().number,lat:O().number}),bd09:O().shape({long:O().number,lat:O().number})})}),resT:O().shape({result:L,type:O().oneOf([\"apple\",\"baidu\",\"amap\",\"google\"]).isRequired})})}function confirmAntiSp"}, {"position": 440558, "context": "){return adapter(\"confirmAntiSpam\")}function addComment(e){return adapter(\"addComment\",e,{argsT:O().shape({placeholder:O().string,uid:O().string}),resT:O().shape({uid:O().string,is_send:O().bool,data:O()"}, {"position": 440614, "context": "e){return adapter(\"addComment\",e,{argsT:O().shape({placeholder:O().string,uid:O().string}),resT:O().shape({uid:O().string,is_send:O().bool,data:O().shape({content:O().string,at_users:O().arrayOf(O().shap"}, {"position": 440662, "context": "e({placeholder:O().string,uid:O().string}),resT:O().shape({uid:O().string,is_send:O().bool,data:O().shape({content:O().string,at_users:O().arrayOf(O().shape({userid:O().string,nickname:O().string}))})})}"}, {"position": 440713, "context": ".shape({uid:O().string,is_send:O().bool,data:O().shape({content:O().string,at_users:O().arrayOf(O().shape({userid:O().string,nickname:O().string}))})})})}function openXhsSystemSettings(){return _openXhsS"}, {"position": 441251, "context": "openFansPanel\")}function checkLoginWithAction(e){return adapter(\"checkLoginWithAction\",e,{argsT:O().shape({type:O().number.isRequired}),resT:O().shape({result:L})})}function logout(){return adapter(\"logo"}, {"position": 441296, "context": "(e){return adapter(\"checkLoginWithAction\",e,{argsT:O().shape({type:O().number.isRequired}),resT:O().shape({result:L})})}function logout(){return adapter(\"logout\")}function isAppInstalled(e){return adapte"}, {"position": 441431, "context": "(){return adapter(\"logout\")}function isAppInstalled(e){return adapter(\"isAppInstalled\",e,{argsT:O().shape({iOS:O().string,Android:O().string}),resT:O().shape({result:L,value:O().bool.isRequired})})}funct"}, {"position": 441483, "context": "e){return adapter(\"isAppInstalled\",e,{argsT:O().shape({iOS:O().string,Android:O().string}),resT:O().shape({result:L,value:O().bool.isRequired})})}function getAppInfo(){return adapter(\"getAppInfo\",{resT:O"}, {"position": 441589, "context": "{result:L,value:O().bool.isRequired})})}function getAppInfo(){return adapter(\"getAppInfo\",{resT:O().shape({result:L,version:O().string.isRequired,build:O().string.isRequired,jsversion:O().string.isRequir"}, {"position": 441956, "context": "idfa:O().string.isRequired,idfv:O().string.isRequired}:{imei:O().string.isRequired}}var e={resT:O().shape((0,y._)({result:L,appMarket:O().string.isRequired,appVersion:O().string.isRequired,buildNumber:O("}, {"position": 442759, "context": "Legacy(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{extractValue:!1},r={resT:O().shape({result:L,value:O().oneOf([\"WiFi\",\"4G\",\"3G+\",\"3G\",\"2G\",\"NONE\"])})};return adapter(\"getNetworkType"}, {"position": 443426, "context": "re info see readme\");var i=s.YF.isAndroid&&(0,s.S8)(\"6.5\")||s.YF.isIOS&&(0,s.S8)(\"6.9\"),a={resT:O().shape({result:L,response:O().shape((0,y._)({userId:O().string.isRequired,nickname:O().string.isRequired"}, {"position": 443455, "context": "YF.isAndroid&&(0,s.S8)(\"6.5\")||s.YF.isIOS&&(0,s.S8)(\"6.9\"),a={resT:O().shape({result:L,response:O().shape((0,y._)({userId:O().string.isRequired,nickname:O().string.isRequired,gender:O().number.isRequired"}, {"position": 443657, "context": "d,userToken:O().string.isRequired,image:urlType.isRequired,location:O().string.isRequired,flags:O().shape({fulishe:O().oneOfType([O().object]).isRequired,shequ:O().oneOfType([O().object]).isRequired})},("}, {"position": 445133, "context": "etUserInfoOld\",timing:Date.now()-r,logType:l.tM.Timing}),s})}function getTrackEnv(){var e={resT:O().shape({isTestEnv:O().bool,uploadOneByOne:O().bool,sessionId:O().string})};return adapter(\"getTrackEnv\")"}, {"position": 445421, "context": "heckRes(i,e.resT),i})}function lowPowerModeEnabled(){return adapter(\"lowPowerModeEnabled\",{resT:O().shape({result:L,value:O().bool.isRequired})})}function requestNotificationPermission(e){return adapter("}, {"position": 445569, "context": "nction requestNotificationPermission(e){return adapter(\"requestNotificationPermission\",e,{argsT:O().shape({engaingType:O().number.isRequired,engaingMessage:O().string})})}function saveImage(e){return ada"}, {"position": 445702, "context": "equired,engaingMessage:O().string})})}function saveImage(e){return adapter(\"saveImage\",e,{argsT:O().shape({url:urlType,base64string:O().string,type:O().string.isRequired}),resT:O().shape({result:L})})}fu"}, {"position": 445783, "context": "mage\",e,{argsT:O().shape({url:urlType,base64string:O().string,type:O().string.isRequired}),resT:O().shape({result:L})})}function basicSendClientRequest(e){return adapter(\"sendClientRequest\",e)}function s"}, {"position": 446089, "context": "http](https://code.devops.xiaohongshu.com/formula/launcher/tree/master/src/http)\");var r={argsT:O().shape({url:O().string,type:O().oneOf([\"GET\",\"POST\",\"PUT\",\"DELETE\"]).isRequired,data:O().oneOfType([O()."}, {"position": 446239, "context": ",\"PUT\",\"DELETE\"]).isRequired,data:O().oneOfType([O().object]),transform:O().oneOfType([O().bool,O().shape({separateNumber:O().bool}),O().func])}),resT:O().shape({result:L,response:O().oneOfType([O().obje"}, {"position": 446294, "context": "ject]),transform:O().oneOfType([O().bool,O().shape({separateNumber:O().bool}),O().func])}),resT:O().shape({result:L,response:O().oneOfType([O().object]),status:O().number})};checkArgs(e,r.argsT,\"sendClie"}, {"position": 447531, "context": "number,O().object,O().string])})}function getItemOld(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L,value:O().string})};return checkArgs(e,r.argsT,\"getItem\"),adapter(\"getItem\",{key:e},{re"}, {"position": 447920, "context": ".apply(this,arguments)}function setItemOld(e,r){return adapter(\"setItem\",{key:e,value:r},{argsT:O().shape({key:O().string.isRequired,value:O().string.isRequired}),resT:O().shape({result:L})})}function se"}, {"position": 447992, "context": ",{key:e,value:r},{argsT:O().shape({key:O().string.isRequired,value:O().string.isRequired}),resT:O().shape({result:L})})}function setItem(e,r){return _setItem.apply(this,arguments)}function _setItem(){ret"}, {"position": 448285, "context": ")]})})).apply(this,arguments)}function removeItemOld(e){var r={argsT:O().string.isRequired,resT:O().shape({result:L})};return checkArgs(e,r.argsT,\"removeItem\"),adapter(\"removeItem\",{key:e},{resT:r.resT})"}, {"position": 448727, "context": "rn adapter(\"broadcast\",e)}function broadcastNative(e){return adapter(\"broadcastNative\",e,{argsT:O().shape({key:O().string.isRequired,data:O().string.isRequired})})}function getMessageStatusIOS(){return a"}, {"position": 448866, "context": "().string.isRequired})})}function getMessageStatusIOS(){return adapter(\"getMessageStatus\",{resT:O().shape({result:L,status:O().oneOf([0,1]).isRequired})})}function getThirdAuth(e){return adapter(\"getThir"}, {"position": 449027, "context": "on getThirdAuth(e){return adapter(\"getThirdAuth\",e,{argsT:O().oneOf([\"weixin\"]).isRequired,resT:O().shape({result:L,value:O().oneOfType([O().object])})})}function getCurrentGeolocation(){return adapter(\""}, {"position": 449229, "context": "\"getCurrentGeolocation\")}function checkAppPermission(e){var r={argsT:O().string.isRequired,resT:O().shape({result:O().oneOf([0,-1]).isRequired,state:O().oneOf([\"denied\",\"granted\",\"undeterminated\"])})},i="}, {"position": 449487, "context": "\":i,r)}function areNotificationsEnabledAndroid(){return adapter(\"areNotificationsEnabled\",{resT:O().shape({result:O().oneOf([0,-1]).isRequired,state:O().oneOf([\"denied\",\"granted\"])})})}function getFileUr"}, {"position": 449889, "context": ")(this,function(i){switch(i.label){case 0:return[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:O().shape({url:urlType.isRequired}),resT:O().shape({result:O().oneOf([0,-1]).isRequired,value:urlType,messa"}, {"position": 449930, "context": ":return[4,adapter(\"getFileUrlFromLocalServer\",e,{argsT:O().shape({url:urlType.isRequired}),resT:O().shape({result:O().oneOf([0,-1]).isRequired,value:urlType,message:O().string})})];case 1:if(0===(r=i.sen"}, {"position": 451448, "context": ":showNavigationRightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePa"}, {"position": 451463, "context": "RightBarButtonItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApp"}, {"position": 451476, "context": "nItem,showNavigationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,appl"}, {"position": 451489, "context": "igationRightBarButtonItem:showNavigationRightBarButtonItem,shareContentV2:shareContent,shareContent:shareContent,supportApplePay:supportApplePayIOS,supportApplePayIOS:supportApplePayIOS,applePayClient:ap"}, {"position": 452065, "context": "etStatusBarTextColor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat"}, {"position": 452081, "context": "olor,setStatusBarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat"}, {"position": 452095, "context": "BarTextColor:setStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClie"}, {"position": 452109, "context": "etStatusBarTextColor,alwaysBounceIOS:alwaysBounceIOS,setShareInfo:ui_setShareInfo,showShareMenu:showShareMenu,pay:pay,alipayClient:alipayClient,openURLByWechat:openURLByWechat,wechatPayClient:wechatPayCl"}, {"position": 550871, "context": "2017cde8304bc3b2234ffd298d1\"}')}}]);\n//# sourceMappingURL=https://picasso-private-1251524319.cos.ap-shanghai.myqcloud.com/data/formula-static/formula/xhs-pc-web/vendor-dynamic.f0f5c43a.js.map"}], "crc": [{"position": 169078, "context": "NP+wOcza/LpngG8yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5\",R=0,I=A.length;R<I;++R)P[R]=A[R];var encrypt_crc32=function crc32(e){for(var r,i=[],a=0;a<256;a++){r=a;for(var s=0;s<8;s++)r=1&r?0xedb88320^r>>>1:r>"}, {"position": 169093, "context": "yJq42KWYj0DSfdikx3VT16IlUAFM97hECvuRX5\",R=0,I=A.length;R<I;++R)P[R]=A[R];var encrypt_crc32=function crc32(e){for(var r,i=[],a=0;a<256;a++){r=a;for(var s=0;s<8;s++)r=1&r?0xedb88320^r>>>1:r>>>1;i[a]=r}for("}, {"position": 182674, "context": "(+new Date).toString(16)).concat(genRandomString(30)).concat(r).concat(\"0\").concat(\"000\"),a=encrypt_crc32(i);return\"\".concat(i).concat(a).substring(0,52)}var L=i(65266),log_awaiter=function(e,r,i,a){func"}], "hash": [{"position": 28558, "context": "getNQELevel(e){return a.apply(this,arguments)});i(67930);var Z={userId:\"\",userToken:\"\",sessionId:\"\",hashExp:\"\",flags:{}},setDefaultABInfo=function(e){var r=e.userId,i=e.userToken,a=e.sessionId,s=e.hashExp"}, {"position": 28655, "context": "\"\",hashExp:\"\",flags:{}},setDefaultABInfo=function(e){var r=e.userId,i=e.userToken,a=e.sessionId,s=e.hashExp;return r&&(Z.userId=r),i&&(Z.userToken=i),a&&(Z.sessionId=a),s&&\"string\"==typeof s&&(Z.hashExp=s"}, {"position": 28750, "context": ",s=e.hashExp;return r&&(Z.userId=r),i&&(Z.userToken=i),a&&(Z.sessionId=a),s&&\"string\"==typeof s&&(Z.hashExp=s),Z};function getABInfoByBridge(){var e;return D.value&&(null===(e=D.value)||void 0===e?void 0:"}, {"position": 29368, "context": "f(s)return s;var e=getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}});return s=e,e}var Q=function(){funct"}, {"position": 29424, "context": "ser:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}});return s=e,e}var Q=function(){function Seq(){(0,f._)(this,Seq)}return(0,v._)(Seq,null,[{key"}, {"position": 58511, "context": "e+=\"&\".concat(s),e},e)}var eL=function(){function HttpTracker(){(0,f._)(this,HttpTracker),this.entryHash={}}return(0,v._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash="}, {"position": 58562, "context": "ttpTracker(){(0,f._)(this,HttpTracker),this.entryHash={}}return(0,v._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)t"}, {"position": 58593, "context": "racker),this.entryHash={}}return(0,v._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark "}, {"position": 58610, "context": "yHash={}}return(0,v._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\""}, {"position": 58732, "context": ",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,g._)({},i,r)):(performanc"}, {"position": 58800, "context": "e is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,g._)({},i,r)):(performance.mark(\"\".concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\","}, {"position": 58876, "context": "at(e,\"_end\")),this.entryHash[e]=(0,g._)({},i,r)):(performance.mark(\"\".concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\",value:function measure(e){var r=this.entryHash[e];if(!r)return{};try{perform"}, {"position": 58946, "context": "concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\",value:function measure(e){var r=this.entryHash[e];if(!r)return{};try{performance.measure(e,\"\".concat(e,\"_start\"),\"\".concat(e,\"_end\"))}catch(e){ret"}, {"position": 124971, "context": "ion(){(l=parseUrl(window.location.href))!==c&&reportPV({initiatorType:tl.routerAfterEach}),c=l};if(\"hash\"===_)window.addEventListener(\"hashChage\",reportHistoryStatePv,!1);else{var y=registerEventHandler(w"}, {"position": 125006, "context": "ref))!==c&&reportPV({initiatorType:tl.routerAfterEach}),c=l};if(\"hash\"===_)window.addEventListener(\"hashChage\",reportHistoryStatePv,!1);else{var y=registerEventHandler(window.history);window.history.pushS"}, {"position": 234495, "context": "\"5.24\")?{}:{lat:E().number.isRequired,lon:E().number.isRequired,sessionId:E().string.isRequired},a?{hashExp:E().string.isRequired}:{}))})};return bridgeAdapter_adapter(\"getUserInfo\").then(function(e){var "}, {"position": 235579, "context": "ULT_\".concat(r.result),\"res.result is not 0\")}return r})}let B={userId:\"\",userToken:\"\",sessionId:\"\",hashExp:\"\",flags:{}};function getABInfoByBridge(){return data_getUserInfo({extractValue:!0}).then(e=>{le"}, {"position": 235718, "context": "ByBridge(){return data_getUserInfo({extractValue:!0}).then(e=>{let{userId:r,userToken:i,sessionId:a,hashExp:s,flags:u}=e;return r&&(B.userId=r),i&&(B.userToken=i),a&&(B.sessionId=a),s&&\"string\"==typeof s&"}, {"position": 235826, "context": "s,flags:u}=e;return r&&(B.userId=r),i&&(B.userToken=i),a&&(B.sessionId=a),s&&\"string\"==typeof s&&(B.hashExp=s),u&&(B.flags=u),B})}function getABInfo(){return getABInfoByBridge().catch(()=>B)}function getF"}, {"position": 249114, "context": "719),i(13396),i(91313);var er=function(){function HttpTracker(){(0,A._)(this,HttpTracker),this.entryHash={}}return(0,D._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash="}, {"position": 249165, "context": "ttpTracker(){(0,A._)(this,HttpTracker),this.entryHash={}}return(0,D._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)t"}, {"position": 249196, "context": "racker),this.entryHash={}}return(0,D._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark "}, {"position": 249213, "context": "yHash={}}return(0,D._)(HttpTracker,[{key:\"resetEntryHash\",value:function resetEntryHash(){this.entryHash={}}},{key:\"track\",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\""}, {"position": 249335, "context": ",value:function track(e,r){if(!e)throw Error(\"[apm-metrics] mark name is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,v._)({},i,r)):(performanc"}, {"position": 249403, "context": "e is required\");var i=this.entryHash[e];void 0!==i?(performance.mark(\"\".concat(e,\"_end\")),this.entryHash[e]=(0,v._)({},i,r)):(performance.mark(\"\".concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\","}, {"position": 249479, "context": "at(e,\"_end\")),this.entryHash[e]=(0,v._)({},i,r)):(performance.mark(\"\".concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\",value:function measure(e){var r=this.entryHash[e];if(!r)return{};try{perform"}, {"position": 249549, "context": "concat(e,\"_start\")),this.entryHash[e]=r)}},{key:\"measure\",value:function measure(e){var r=this.entryHash[e];if(!r)return{};try{performance.measure(e,\"\".concat(e,\"_start\"),\"\".concat(e,\"_end\"))}catch(e){ret"}, {"position": 282039, "context": "ce(1)}var eC=function(){function LiteEaglet(e){var r=e.name,i=e.emitter,a=e.trackerEnums,s=e.versionHash;if((0,A._)(this,LiteEaglet),(0,f._)(this,\"name\",void 0),(0,f._)(this,\"emitter\",void 0),(0,f._)(this"}, {"position": 282152, "context": "_)(this,<PERSON><PERSON><PERSON><PERSON><PERSON>),(0,f._)(this,\"name\",void 0),(0,f._)(this,\"emitter\",void 0),(0,f._)(this,\"versionHash\",void 0),(0,f._)(this,\"trackerEnums\",void 0),!r)throw Error(\"missing name\");if(!i)throw Error(\"miss"}, {"position": 282346, "context": "rror(\"missing emitter\");if(!a)throw <PERSON>rror(\"missing trackerEnums\");if(!s)throw Error(\"missing versionHash\");this.name=r,this.emitter=i,this.versionHash=s,this.trackerEnums=a}return(0,D._)(LiteEaglet,[{key:"}, {"position": 282392, "context": "sing trackerEnums\");if(!s)throw Error(\"missing versionHash\");this.name=r,this.emitter=i,this.versionHash=s,this.trackerEnums=a}return(0,D._)(Li<PERSON>Eaglet,[{key:\"push\",value:function push(e){var r=e.data,i=e"}, {"position": 282622, "context": "ct.keys(r).forEach(function(e){a[titleCase(e)]=r[e]}),this.emitter.push(buildBuffer(a,i,this.versionHash,this.trackerEnums))}},{key:\"flush\",value:function flush(){this.emitter.flush()}}]),LiteEaglet}();fu"}, {"position": 282873, "context": "itter Exception] __proto__.\".concat(e,\" should be implemented\"))}function argsCheck(e){if(!e.versionHash)throw Error(\"[Eaglet Emitter Exception] args.versionHash is required\");if(!e.endpoint)throw Error(\""}, {"position": 282930, "context": "nted\"))}function argsCheck(e){if(!e.versionHash)throw Error(\"[Eaglet Emitter Exception] args.versionHash is required\");if(!e.endpoint)throw Error(\"[Eaglet Emitter Exception] args.endpoint is required\")}va"}, {"position": 283151, "context": "unction EmitterBase(e){(0,A._)(this,EmitterBase),(0,f._)(this,\"buffer\",[]),argsCheck(e),this.versionHash=e.versionHash,this.endpoint=e.endpoint}return(0,D._)(EmitterBase,[{key:\"shouldFlushBuffer\",value:fu"}, {"position": 283165, "context": "rBase(e){(0,A._)(this,EmitterBase),(0,f._)(this,\"buffer\",[]),argsCheck(e),this.versionHash=e.versionHash,this.endpoint=e.endpoint}return(0,D._)(EmitterBase,[{key:\"shouldFlushBuffer\",value:function shouldF"}, {"position": 285433, "context": ".serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",valu"}, {"position": 291432, "context": ".serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendLocalMetri"}, {"position": 294676, "context": ",d,p,f=r.enableNativeEmitter,g=r.enableBatchRequest,m=r.customEndPoint,_=e.NAME===eN.NAME,y={versionHash:e.PROTOBUF_HASH,debug:e.debug,preferNative:f};return _?y.endpoint={development:(null==m?void 0:null"}, {"position": 294692, "context": "ativeEmitter,g=r.enableBatchRequest,m=r.customEndPoint,_=e.NAME===eN.NAME,y={versionHash:e.PROTOBUF_HASH,debug:e.debug,preferNative:f};return _?y.endpoint={development:(null==m?void 0:null===(i=m.defaultT"}, {"position": 295699, "context": "v._)({},y),{isLite:!0})),trackerEnums:null===(p=e.tracker)||void 0===p?void 0:p.trackerEnums,versionHash:e.PROTOBUF_HASH}):new eE({name:e.NAME,builder:e.builder,emitter:_?new eR(y):new eD((0,h._)((0,v._)("}, {"position": 295715, "context": "te:!0})),trackerEnums:null===(p=e.tracker)||void 0===p?void 0:p.trackerEnums,versionHash:e.PROTOBUF_HASH}):new eE({name:e.NAME,builder:e.builder,emitter:_?new eR(y):new eD((0,h._)((0,v._)({},y),{apmXrayTr"}, {"position": 296807, "context": ".serializeBinary();i.encoder_.writeSignedVarint64(a.byteLength+4),i.encoder_.writeInt32(this.versionHash),i.encoder_.writeBytes(a);var s=i.getResultBase64String();return i.reset(),s}},{key:\"sendData\",valu"}, {"position": 297556, "context": "itter}(eP);function createSendBeaconTracker_createTracker(e,r){var i,a,s=e.NAME===eN.NAME,u={versionHash:e.PROTOBUF_HASH,debug:e.debug};s&&(u.endpoint={development:(null==r?void 0:null===(i=r.defaultTrack"}, {"position": 297572, "context": "ion createSendBeaconTracker_createTracker(e,r){var i,a,s=e.NAME===eN.NAME,u={versionHash:e.PROTOBUF_HASH,debug:e.debug};s&&(u.endpoint={development:(null==r?void 0:null===(i=r.defaultTracker)||void 0===i?"}, {"position": 306227, "context": "ion(){return d?d:d=getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}})},purgeUserInfo=function(){return ge"}, {"position": 306283, "context": "ser:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}})},purgeUserInfo=function(){return getABInfo().then(function(e){return{user:{type:\"User\",valu"}, {"position": 306410, "context": "=function(){return getABInfo().then(function(e){return{user:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}})};function getPageInfo(){var e,r,i=a"}, {"position": 306466, "context": "ser:{type:\"User\",value:{userId:e.userId||\"\",hashUserId:e.userToken,wxOpenid:getOpenId()||\"\",expV4:e.hashExp}}}})};function getPageInfo(){var e,r,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}"}, {"position": 314701, "context": "ers.liteTracker=T}S?this.trackers.apmTracker=S:this.trackers.apmTracker={NAME:\"ApmTracker\",PROTOBUF_HASH:0x5c17126e,dataType:\"json\",VERSION:\"0.1.33\",builder:{build:function(e,r){return r}},jspb:{}},void 0"}, {"position": 392322, "context": "24\")?{lat:eG().number.isRequired,lon:eG().number.isRequired,sessionId:eG().string.isRequired}:{},i?{hashExp:eG().string.isRequired}:{}))})};return adapter(\"getUserInfo\").then(function(i){var s=(0,eJ.Lg)(i"}, {"position": 443868, "context": "\"5.24\")?{lat:O().number.isRequired,lon:O().number.isRequired,sessionId:O().string.isRequired}:{},i?{hashExp:O().string.isRequired}:{}))})};return adapter(\"getUserInfo\").then(function(i){var s=(0,N.Lg)(i);"}]}}